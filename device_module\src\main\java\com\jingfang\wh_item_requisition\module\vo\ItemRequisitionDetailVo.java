package com.jingfang.wh_item_requisition.module.vo;

import com.jingfang.wh_item_requisition.module.entity.ItemRequisitionOperationLog;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 物品领用单详情VO
 */
@Data
public class ItemRequisitionDetailVo extends ItemRequisitionVo implements Serializable {
    
    /**
     * 领用单明细列表（包含物品详细信息）
     */
    private List<ItemRequisitionDetailItemVo> details;
    
    /**
     * 操作日志列表
     */
    private List<ItemRequisitionOperationLog> operationLogs;
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 领用单明细项VO（包含物品详细信息）
     */
    @Data
    public static class ItemRequisitionDetailItemVo implements Serializable {
        
        /**
         * 明细ID
         */
        private Long detailId;
        
        /**
         * 领用单ID
         */
        private String requisitionId;
        
        /**
         * 物品ID
         */
        private String itemId;
        
        /**
         * 物品名称（关联查询item_base_info表获取）
         */
        private String itemName;
        
        /**
         * 物品编码（关联查询item_base_info表获取）
         */
        private String itemCode;
        
        /**
         * 规格型号（关联查询item_base_info表获取）
         */
        private String specModel;
        
        /**
         * 单位（关联查询item_base_info表获取）
         */
        private String unit;
        
        /**
         * 仓库ID
         */
        private Integer warehouseId;
        
        /**
         * 仓库名称（通过数据字典获取）
         */
        private String warehouseName;
        
        /**
         * 货架位置
         */
        private String shelfLocation;
        
        /**
         * 申请数量
         */
        private BigDecimal requisitionQuantity;
        
        /**
         * 批准数量
         */
        private BigDecimal approvedQuantity;
        
        /**
         * 实际领用数量
         */
        private BigDecimal actualQuantity;
        
        /**
         * 备注
         */
        private String remark;
        
        /**
         * 创建时间
         */
        private Date createTime;
        
        /**
         * 更新时间
         */
        private Date updateTime;
        
        private static final long serialVersionUID = 1L;
    }
} 