<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.wh_item.mapper.ItemInboundMapper">

    <resultMap id="BaseResultMap" type="com.jingfang.wh_item.module.entity.ItemInbound">
        <id property="inboundId" column="inbound_id" jdbcType="VARCHAR"/>
        <result property="businessDate" column="business_date" jdbcType="DATE"/>
        <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
        <result property="inboundType" column="inbound_type" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="handlerId" column="handler_id" jdbcType="BIGINT"/>
        <result property="handleTime" column="handle_time" jdbcType="TIMESTAMP"/>
        <result property="handleRemark" column="handle_remark" jdbcType="VARCHAR"/>
        <result property="auditorId" column="auditor_id" jdbcType="BIGINT"/>
        <result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
        <result property="auditRemark" column="audit_remark" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="inboundDescription" column="inbound_description" jdbcType="LONGVARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        inbound_id, business_date, supplier_name, inbound_type, status,
        creator_id, create_time, handler_id, handle_time, handle_remark,
        auditor_id, audit_time, audit_remark, update_time, inbound_description, del_flag
    </sql>

    <!-- 查询入库单列表带管理人员名称 -->
    <select id="selectInboundListWithManager" resultType="com.jingfang.wh_item.module.vo.ItemInboundVo">
        SELECT
        i.inbound_id,
        i.business_date,
        i.supplier_name,
        i.inbound_type,
        i.status,
        i.creator_id,
        i.create_time,
        i.handler_id,
        i.handle_time,
        i.handle_remark,
        i.auditor_id,
        i.audit_time,
        i.audit_remark,
        i.update_time,
        i.inbound_description,
        creator.nick_name as creator_name,
        handler.nick_name as handler_name,
        auditor.nick_name as auditor_name
        FROM item_inbound i
        LEFT JOIN sys_user creator ON i.creator_id = creator.user_id
        LEFT JOIN sys_user handler ON i.handler_id = handler.user_id
        LEFT JOIN sys_user auditor ON i.auditor_id = auditor.user_id
        WHERE i.del_flag = '0'
        <if test="request.inboundId != null and request.inboundId != ''">
            AND i.inbound_id LIKE CONCAT('%', #{request.inboundId}, '%')
        </if>
        <if test="request.supplierName != null and request.supplierName != ''">
            AND i.supplier_name LIKE CONCAT('%', #{request.supplierName}, '%')
        </if>
        <if test="request.inboundType != null">
            AND i.inbound_type = #{request.inboundType}
        </if>
        <if test="request.status != null">
            AND i.status = #{request.status}
        </if>
        <if test="request.creatorId != null">
            AND i.creator_id = #{request.creatorId}
        </if>
        <if test="request.businessDateStart != null">
            AND i.business_date >= #{request.businessDateStart}
        </if>
        <if test="request.businessDateEnd != null">
            AND i.business_date &lt;= #{request.businessDateEnd}
        </if>
        <if test="request.createTimeStart != null">
            AND DATE(i.create_time) >= #{request.createTimeStart}
        </if>
        <if test="request.createTimeEnd != null">
            AND DATE(i.create_time) &lt;= #{request.createTimeEnd}
        </if>
        <if test="request.itemName != null and request.itemName != ''">
            AND EXISTS (
            SELECT 1 FROM item_inbound_detail d
            LEFT JOIN item_base_info i ON d.item_id = i.item_id AND i.deleted = 0
            WHERE d.inbound_id = i.inbound_id
            AND i.item_name LIKE CONCAT('%', #{request.itemName}, '%')
            )
        </if>
        <if test="request.itemCode != null and request.itemCode != ''">
            AND EXISTS (
            SELECT 1 FROM item_inbound_detail d
            LEFT JOIN item_base_info i ON d.item_id = i.item_id AND i.deleted = 0
            WHERE d.inbound_id = i.inbound_id
            AND i.item_code LIKE CONCAT('%', #{request.itemCode}, '%')
            )
        </if>
        <if test="request.itemType != null">
            AND EXISTS (
            SELECT 1 FROM item_inbound_detail d
            LEFT JOIN item_base_info i ON d.item_id = i.item_id AND i.deleted = 0
            WHERE d.inbound_id = i.inbound_id
            AND i.item_type = #{request.itemType}
            )
        </if>
        ORDER BY i.create_time DESC
    </select>

    <!-- 通过ID查询入库单详情带管理人员名称 -->
    <select id="selectInboundDetailById" resultType="com.jingfang.wh_item.module.vo.ItemInboundVo">
        SELECT
            i.inbound_id,
            i.business_date,
            i.supplier_name,
            i.inbound_type,
            i.status,
            i.creator_id,
            i.create_time,
            i.handler_id,
            i.handle_time,
            i.handle_remark,
            i.auditor_id,
            i.audit_time,
            i.audit_remark,
            i.update_time,
            i.inbound_description,
            creator.nick_name as creator_name,
            handler.nick_name as handler_name,
            auditor.nick_name as auditor_name
        FROM item_inbound i
                 LEFT JOIN sys_user creator ON i.creator_id = creator.user_id
                 LEFT JOIN sys_user handler ON i.handler_id = handler.user_id
                 LEFT JOIN sys_user auditor ON i.auditor_id = auditor.user_id
        WHERE i.inbound_id = #{inboundId} AND i.del_flag = '0'
    </select>

</mapper>