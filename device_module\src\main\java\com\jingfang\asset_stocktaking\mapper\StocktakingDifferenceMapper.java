package com.jingfang.asset_stocktaking.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.asset_stocktaking.module.entity.AssetStocktakingDifference;
import com.jingfang.asset_stocktaking.module.vo.DeptStatistics;
import com.jingfang.asset_stocktaking.module.vo.DifferenceStatistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 盘点差异数据访问层
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Mapper
public interface StocktakingDifferenceMapper extends BaseMapper<AssetStocktakingDifference> {

    /**
     * 分页查询盘点差异列表
     * 
     * @param page 分页对象
     * @param planId 计划ID
     * @param diffType 差异类型
     * @param handleStatus 处理状态
     * @return 差异列表
     */
    IPage<AssetStocktakingDifference> selectDifferenceList(IPage<AssetStocktakingDifference> page, 
                                                          @Param("planId") String planId,
                                                          @Param("diffType") Integer diffType,
                                                          @Param("handleStatus") Integer handleStatus);

    /**
     * 根据计划ID查询差异列表
     * 
     * @param planId 计划ID
     * @return 差异列表
     */
    List<AssetStocktakingDifference> selectDifferenceByPlanId(@Param("planId") String planId);

    /**
     * 根据差异类型查询差异列表
     * 
     * @param planId 计划ID
     * @param diffType 差异类型
     * @return 差异列表
     */
    List<AssetStocktakingDifference> selectDifferenceByType(@Param("planId") String planId, @Param("diffType") Integer diffType);

    /**
     * 根据处理状态查询差异列表
     * 
     * @param planId 计划ID
     * @param handleStatus 处理状态
     * @return 差异列表
     */
    List<AssetStocktakingDifference> selectDifferenceByHandleStatus(@Param("planId") String planId, @Param("handleStatus") Integer handleStatus);

    /**
     * 统计计划的差异情况
     * 
     * @param planId 计划ID
     * @return 差异统计
     */
    DifferenceStatistics selectDifferenceStatistics(@Param("planId") String planId);

    /**
     * 统计各类型差异数量
     * 
     * @param planId 计划ID
     * @return 类型统计
     */
    List<java.util.Map<String, Object>> countDifferenceByType(@Param("planId") String planId);

    /**
     * 统计各处理状态差异数量
     * 
     * @param planId 计划ID
     * @return 状态统计
     */
    List<java.util.Map<String, Object>> countDifferenceByHandleStatus(@Param("planId") String planId);

    /**
     * 查询盘盈差异列表
     * 
     * @param planId 计划ID
     * @return 盘盈差异列表
     */
    List<AssetStocktakingDifference> selectSurplusDifferences(@Param("planId") String planId);

    /**
     * 查询盘亏差异列表
     * 
     * @param planId 计划ID
     * @return 盘亏差异列表
     */
    List<AssetStocktakingDifference> selectDeficitDifferences(@Param("planId") String planId);

    /**
     * 查询状态差异列表
     * 
     * @param planId 计划ID
     * @return 状态差异列表
     */
    List<AssetStocktakingDifference> selectStatusDifferences(@Param("planId") String planId);

    /**
     * 查询位置差异列表
     * 
     * @param planId 计划ID
     * @return 位置差异列表
     */
    List<AssetStocktakingDifference> selectLocationDifferences(@Param("planId") String planId);

    /**
     * 查询待处理差异列表
     * 
     * @param planId 计划ID
     * @return 待处理差异列表
     */
    List<AssetStocktakingDifference> selectPendingDifferences(@Param("planId") String planId);

    /**
     * 批量插入差异记录
     * 
     * @param differences 差异列表
     * @return 影响行数
     */
    int batchInsertDifferences(@Param("differences") List<AssetStocktakingDifference> differences);

    /**
     * 批量更新差异处理状态
     * 
     * @param diffIds 差异ID列表
     * @param handleStatus 处理状态
     * @return 影响行数
     */
    int batchUpdateHandleStatus(@Param("diffIds") List<String> diffIds, @Param("handleStatus") Integer handleStatus);

    /**
     * 更新差异处理信息
     * 
     * @param diffId 差异ID
     * @param handleStatus 处理状态
     * @param handleSuggestion 处理建议
     * @return 影响行数
     */
    int updateDifferenceHandle(@Param("diffId") String diffId, 
                              @Param("handleStatus") Integer handleStatus,
                              @Param("handleSuggestion") String handleSuggestion);

    /**
     * 根据资产ID查询差异记录
     * 
     * @param assetId 资产ID
     * @param planId 计划ID
     * @return 差异记录
     */
    AssetStocktakingDifference selectDifferenceByAssetId(@Param("assetId") String assetId, @Param("planId") String planId);

    /**
     * 检查资产是否存在差异
     * 
     * @param assetId 资产ID
     * @param planId 计划ID
     * @return 是否存在差异
     */
    boolean checkAssetHasDifference(@Param("assetId") String assetId, @Param("planId") String planId);

    /**
     * 删除计划的所有差异记录
     * 
     * @param planId 计划ID
     * @return 影响行数
     */
    int deleteDifferencesByPlanId(@Param("planId") String planId);

    /**
     * 查询部门差异统计
     * 
     * @param planId 计划ID
     * @return 部门差异统计列表
     */
    List<DeptStatistics> selectDeptDifferenceStatistics(@Param("planId") String planId);
}
