package com.jingfang.asset_part_relation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jingfang.asset_part_relation.module.dto.AssetPartRelationDto;
import com.jingfang.asset_part_relation.module.entity.AssetPartRelation;
import com.jingfang.asset_part_relation.module.request.AssetPartRelationSearchRequest;
import com.jingfang.asset_part_relation.module.vo.AssetPartRelationVo;

import java.util.List;

/**
 * 资产备品备件关联Service接口
 */
public interface AssetPartRelationService extends IService<AssetPartRelation> {
    
    /**
     * 新增资产备品备件关联
     * 
     * @param relationDto 关联信息
     * @param username 操作人
     * @return 操作结果
     */
    boolean addRelation(AssetPartRelationDto relationDto, String username);
    
    /**
     * 批量新增资产备品备件关联
     * 
     * @param assetId 资产ID
     * @param relationDtos 关联信息列表
     * @param username 操作人
     * @return 操作结果
     */
    boolean batchAddRelations(String assetId, List<AssetPartRelationDto> relationDtos, String username);
    
    /**
     * 更新资产备品备件关联
     * 
     * @param relationDto 关联信息
     * @param username 操作人
     * @return 操作结果
     */
    boolean updateRelation(AssetPartRelationDto relationDto, String username);
    
    /**
     * 删除资产备品备件关联
     * 
     * @param relationId 关联ID
     * @param username 操作人
     * @return 操作结果
     */
    boolean deleteRelation(String relationId, String username);
    
    /**
     * 批量删除资产备品备件关联
     * 
     * @param relationIds 关联ID数组
     * @param username 操作人
     * @return 操作结果
     */
    boolean deleteRelations(String[] relationIds, String username);
    
    /**
     * 查询资产备品备件关联列表
     * 
     * @param request 查询条件
     * @return 分页列表
     */
    IPage<AssetPartRelationVo> selectRelationList(AssetPartRelationSearchRequest request);
    
    /**
     * 根据资产ID查询关联的备品备件列表
     * 
     * @param assetId 资产ID
     * @return 备品备件列表
     */
    List<AssetPartRelationVo> selectPartsByAssetId(String assetId);
    
    /**
     * 根据备品备件ID查询关联的资产列表
     * 
     * @param partId 备品备件ID
     * @return 资产列表
     */
    List<AssetPartRelationVo> selectAssetsByPartId(String partId);
    
    /**
     * 检查资产和备品备件是否已关联
     * 
     * @param assetId 资产ID
     * @param partId 备品备件ID
     * @return 是否已关联
     */
    boolean checkRelationExists(String assetId, String partId);
    
    /**
     * 删除资产的所有备品备件关联（资产删除时调用）
     * 
     * @param assetId 资产ID
     * @param username 操作人
     * @return 操作结果
     */
    boolean deleteByAssetId(String assetId, String username);
    
    /**
     * 删除备品备件的所有资产关联（备品备件删除时调用）
     * 
     * @param partId 备品备件ID
     * @param username 操作人
     * @return 操作结果
     */
    boolean deleteByPartId(String partId, String username);
} 