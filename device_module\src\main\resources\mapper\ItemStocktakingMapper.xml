<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.wh_item.mapper.ItemStocktakingMapper">

    <resultMap type="com.jingfang.wh_item.module.vo.ItemStocktakingVo" id="ItemStocktakingResult">
        <result property="stocktakingId" column="stocktaking_id"/>
        <result property="stocktakingCode" column="stocktaking_code"/>
        <result property="stocktakingName" column="stocktaking_name"/>
        <result property="stocktakingType" column="stocktaking_type"/>
        <result property="stocktakingTypeName" column="stocktaking_type_name"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="warehouseName" column="warehouse_name"/>
        <result property="status" column="status"/>
        <result property="statusName" column="status_name"/>
        <result property="planStartTime" column="plan_start_time"/>
        <result property="planEndTime" column="plan_end_time"/>
        <result property="actualStartTime" column="actual_start_time"/>
        <result property="actualEndTime" column="actual_end_time"/>
        <result property="creatorName" column="creator_name"/>
        <result property="auditorName" column="auditor_name"/>
        <result property="auditTime" column="audit_time"/>
        <result property="totalItems" column="total_items"/>
        <result property="checkedItems" column="checked_items"/>
        <result property="differenceItems" column="difference_items"/>
        <result property="progress" column="progress"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectStocktakingVo">
        select s.stocktaking_id,
               s.stocktaking_code,
               s.stocktaking_name,
               s.stocktaking_type,
               case s.stocktaking_type
                   when 1 then '全盘'
                   when 2 then '抽盘'
                   when 3 then '循环盘点'
                   else '未知'
               end as stocktaking_type_name,
               s.warehouse_id,
               null as warehouse_name,
               s.status,
               case s.status
                   when 0 then '草稿'
                   when 1 then '进行中'
                   when 2 then '已完成'
                   when 3 then '已审核'
                   else '未知'
               end as status_name,
               s.plan_start_time,
               s.plan_end_time,
               s.actual_start_time,
               s.actual_end_time,
               s.creator_name,
               s.auditor_name,
               s.audit_time,
               s.remark,
               s.create_time
        from item_stocktaking s
    </sql>

    <select id="selectStocktakingList" parameterType="com.jingfang.wh_item.module.request.ItemStocktakingSearchRequest" resultMap="ItemStocktakingResult">
        <include refid="selectStocktakingVo"/>
        <where>
            <if test="request.stocktakingCode != null and request.stocktakingCode != ''">
                and s.stocktaking_code like concat('%', #{request.stocktakingCode}, '%')
            </if>
            <if test="request.stocktakingName != null and request.stocktakingName != ''">
                and s.stocktaking_name like concat('%', #{request.stocktakingName}, '%')
            </if>
            <if test="request.stocktakingType != null">
                and s.stocktaking_type = #{request.stocktakingType}
            </if>
            <if test="request.warehouseId != null">
                and s.warehouse_id = #{request.warehouseId}
            </if>
            <if test="request.status != null">
                and s.status = #{request.status}
            </if>
            <if test="request.creatorName != null and request.creatorName != ''">
                and s.creator_name like concat('%', #{request.creatorName}, '%')
            </if>
            <if test="request.planStartTimeBegin != null">
                and s.plan_start_time >= #{request.planStartTimeBegin}
            </if>
            <if test="request.planStartTimeEnd != null">
                and s.plan_start_time &lt;= #{request.planStartTimeEnd}
            </if>
            <if test="request.createTimeBegin != null">
                and s.create_time >= #{request.createTimeBegin}
            </if>
            <if test="request.createTimeEnd != null">
                and s.create_time &lt;= #{request.createTimeEnd}
            </if>
        </where>
        order by s.create_time desc
    </select>

    <select id="selectStocktakingDetail" parameterType="String" resultMap="ItemStocktakingResult">
        <include refid="selectStocktakingVo"/>
        where s.stocktaking_id = #{stocktakingId}
    </select>

    <select id="getNextStocktakingCode" resultType="String">
        select stocktaking_code
        from item_stocktaking
        where stocktaking_code like concat('PD', date_format(now(), '%Y%m%d'), '%')
        order by stocktaking_code desc
        limit 1
    </select>

</mapper> 