# 资产工作台接口文档

## 概述

资产工作台模块提供了资产管理相关的统计数据和图表数据接口，用于支持工作台页面的数据展示。

## 接口列表

### 1. 获取资产工作台完整数据

**接口地址**: `GET /asset/workbench/data`

**权限要求**: `asset:workbench:view`

**描述**: 获取资产工作台的完整数据，包括概览、趋势、入库出库统计、处置统计等所有数据。

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "overview": {
      "totalCount": 1500,
      "totalValue": 5000000.00,
      "monthlyNewCount": 50,
      "monthlyNewValue": 200000.00,
      "pendingDisposalCount": 5,
      "normalStatusCount": 1400,
      "repairStatusCount": 80,
      "scrapStatusCount": 20,
      "utilizationRate": 93.33
    },
    "inOutStatistics": {
      "monthlyInboundCount": 10,
      "monthlyInboundAssetCount": 50,
      "monthlyInboundValue": 200000.00,
      "pendingConfirmCount": 3,
      "pendingAuditCount": 2,
      "inboundTrends": [...]
    },
    "disposalStatistics": {
      "pendingApprovalCount": 5,
      "processingCount": 2,
      "monthlyCompletedCount": 8,
      "monthlyDisposalValue": 50000.00,
      "typeStatistics": [...],
      "disposalTrends": [...]
    },
    "trends": [...]
  }
}
```

### 2. 获取资产概览数据

**接口地址**: `GET /asset/workbench/overview`

**权限要求**: `asset:workbench:overview`

**描述**: 获取资产概览统计数据。

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalCount": 1500,
    "totalValue": 5000000.00,
    "monthlyNewCount": 50,
    "monthlyNewValue": 200000.00,
    "pendingDisposalCount": 5,
    "normalStatusCount": 1400,
    "repairStatusCount": 80,
    "scrapStatusCount": 20,
    "utilizationRate": 93.33
  }
}
```

### 3. 获取资产趋势数据

**接口地址**: `GET /asset/workbench/trends`

**权限要求**: `asset:workbench:trends`

**参数**:
- `days` (可选): 天数，默认30天，范围1-365

**描述**: 获取指定天数内的资产趋势数据。

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "date": "2025-06-27",
    "totalValue": 5000000.00,
    "newCount": 2,
    "newValue": 10000.00,
    "disposalCount": 1,
    "disposalValue": 5000.00
  }
}
```

### 4. 获取入库出库统计数据

**接口地址**: `GET /asset/workbench/inout-statistics`

**权限要求**: `asset:workbench:inout`

**描述**: 获取入库出库相关的统计数据。

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "monthlyInboundCount": 10,
    "monthlyInboundAssetCount": 50,
    "monthlyInboundValue": 200000.00,
    "pendingConfirmCount": 3,
    "pendingAuditCount": 2,
    "inboundTrends": [
      {
        "date": "2025-06-27",
        "inboundCount": 2,
        "assetCount": 10,
        "assetValue": 50000.00
      }
    ]
  }
}
```

### 5. 获取处置统计数据

**接口地址**: `GET /asset/workbench/disposal-statistics`

**权限要求**: `asset:workbench:disposal`

**描述**: 获取资产处置相关的统计数据。

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "pendingApprovalCount": 5,
    "processingCount": 2,
    "monthlyCompletedCount": 8,
    "monthlyDisposalValue": 50000.00,
    "typeStatistics": [
      {
        "disposalType": 1,
        "typeName": "报废",
        "count": 10,
        "totalValue": 30000.00
      }
    ],
    "disposalTrends": [
      {
        "date": "2025-06-27",
        "applicationCount": 2,
        "completedCount": 1,
        "disposalValue": 5000.00
      }
    ]
  }
}
```

### 6. 获取资产状态分布数据

**接口地址**: `GET /asset/workbench/status-distribution`

**权限要求**: `asset:workbench:status`

**描述**: 获取资产状态分布统计数据。

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "normal": 1400,
    "repair": 80,
    "scrap": 20,
    "utilizationRate": 93.33
  }
}
```

### 7. 获取资产价值统计

**接口地址**: `GET /asset/workbench/value-statistics`

**权限要求**: `asset:workbench:value`

**描述**: 获取资产价值相关的统计数据。

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalValue": 5000000.00,
    "monthlyNewValue": 200000.00,
    "totalCount": 1500,
    "monthlyNewCount": 50
  }
}
```

### 8. 获取待办事项统计

**接口地址**: `GET /asset/workbench/todo-statistics`

**权限要求**: `asset:workbench:todo`

**描述**: 获取资产管理相关的待办事项统计。

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "pendingDisposal": 5,
    "pendingConfirm": 3,
    "pendingAudit": 2,
    "pendingApproval": 5,
    "processing": 2
  }
}
```

### 9. 刷新工作台缓存数据

**接口地址**: `POST /asset/workbench/refresh`

**权限要求**: `asset:workbench:refresh`

**描述**: 刷新工作台缓存数据。

**响应示例**:
```json
{
  "code": 200,
  "msg": "刷新成功"
}
```

## 数据字典

### 资产状态
- 1: 正常
- 2: 维修
- 3: 报废

### 入库单状态
- 1: 草稿
- 2: 待确认
- 3: 待审核
- 4: 已通过
- 5: 已退回

### 处置类型
- 1: 报废
- 2: 调拨
- 3: 出售
- 4: 其他

### 处置状态
- 1: 草稿
- 2: 待审核
- 3: 审核中
- 4: 审核通过
- 5: 审核拒绝
- 6: 处置中
- 7: 已完成
- 8: 已取消

## 权限配置

在使用这些接口前，需要确保用户具有相应的权限：

```sql
-- 资产工作台权限菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('资产工作台', 2000, 1, 'workbench', 'asset/workbench/index', 1, 0, 'C', '0', '0', 'asset:workbench:view', 'dashboard', 'admin', sysdate(), '', null, '资产工作台菜单');

-- 资产工作台权限按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('概览查看', 上级菜单ID, 1, '', '', 1, 0, 'F', '0', '0', 'asset:workbench:overview', '#', 'admin', sysdate(), '', null, ''),
('趋势查看', 上级菜单ID, 2, '', '', 1, 0, 'F', '0', '0', 'asset:workbench:trends', '#', 'admin', sysdate(), '', null, ''),
('统计查看', 上级菜单ID, 3, '', '', 1, 0, 'F', '0', '0', 'asset:workbench:inout', '#', 'admin', sysdate(), '', null, ''),
('处置统计', 上级菜单ID, 4, '', '', 1, 0, 'F', '0', '0', 'asset:workbench:disposal', '#', 'admin', sysdate(), '', null, ''),
('数据刷新', 上级菜单ID, 5, '', '', 1, 0, 'F', '0', '0', 'asset:workbench:refresh', '#', 'admin', sysdate(), '', null, '');
```

## 注意事项

1. 所有接口都需要相应的权限才能访问
2. 统计数据基于当前月份计算
3. 趋势数据支持自定义天数范围
4. 建议在生产环境中添加缓存机制以提高性能
5. 大数据量情况下建议使用分页或限制查询范围
