package com.jingfang.wh_item.module.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 盘点明细更新DTO
 * 用于微信小程序端更新盘点明细信息
 */
@Data
public class ItemStocktakingDetailUpdateDto implements Serializable {

    /**
     * 实盘数量
     */
    private BigDecimal actualQuantity;

    /**
     * 差异原因
     */
    private String differenceReason;

    /**
     * 照片URL列表
     */
    private List<String> photos;

    private static final long serialVersionUID = 1L;
}
