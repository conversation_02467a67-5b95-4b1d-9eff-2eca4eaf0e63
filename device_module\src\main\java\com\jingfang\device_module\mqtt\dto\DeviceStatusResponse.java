package com.jingfang.device_module.mqtt.dto;

import lombok.Data;

import java.util.List;

/**
 * MQTT设备状态查询响应DTO
 */
@Data
public class DeviceStatusResponse {

    /**
     * 响应ID，对应请求ID
     */
    private String id;

    /**
     * 版本号
     */
    private String version;

    /**
     * 响应代码，0表示成功
     */
    private Integer code;

    /**
     * 设备状态参数列表
     */
    private List<DeviceStatusParam> params;

    /**
     * 设备状态参数
     */
    @Data
    public static class DeviceStatusParam {
        /**
         * 客户端ID（设备IP地址）
         */
        private String clientID;

        /**
         * 通信状态：onLine/offLine
         */
        private String commStatus;
    }
}
