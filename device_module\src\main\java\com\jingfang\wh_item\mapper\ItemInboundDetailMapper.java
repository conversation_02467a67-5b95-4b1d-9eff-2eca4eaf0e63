package com.jingfang.wh_item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jingfang.wh_item.module.entity.ItemInboundDetail;
import com.jingfang.wh_item.module.vo.ItemInboundDetailVo;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * 物品入库明细Mapper
 */
public interface ItemInboundDetailMapper extends BaseMapper<ItemInboundDetail> {

    /**
     * 根据入库单ID查询明细列表
     */
    List<ItemInboundDetailVo> selectDetailsByInboundId(@Param("inboundId") String inboundId);

    /**
     * 根据入库单ID删除明细
     */
    int deleteByInboundId(@Param("inboundId") String inboundId);
}