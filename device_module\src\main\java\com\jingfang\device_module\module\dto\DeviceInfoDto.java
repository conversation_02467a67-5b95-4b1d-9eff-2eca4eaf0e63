package com.jingfang.device_module.module.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jingfang.common.enums.DeviceStatus;
import com.jingfang.common.enums.DeviceType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class DeviceInfoDto implements Serializable {

    private Long id;

    private String deviceName;

    private String icon;

    private int deviceType;

    private String deviceModel;

    private String ipAddress;

    private String devicePort;

    private String location;

    private String manufacturer;

    private String serialNumber;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private String installationDate;

    private Date createdAt;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;



}
