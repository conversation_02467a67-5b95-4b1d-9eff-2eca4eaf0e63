package com.jingfang.wh_item.module.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jingfang.wh_item.module.entity.ItemOutboundOperationLog;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ItemOutboundVo {
    private String outboundId;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date businessDate;

    private String recipientName;

    private String recipientDept;

    private Integer outboundType;
    private String outboundTypeName;

    private Integer status;
    private String statusName;

    private Long creatorId;
    private String creatorName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private Long handlerId;
    private String handlerName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date handleTime;

    private String handleRemark;

    private Long auditorId;
    private String auditorName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditTime;

    private String auditRemark;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    private String outboundDescription;

    /**
     * 明细列表
     */
    private List<ItemOutboundDetailVo> details;

    /**
     * 操作日志列表
     */
    private List<ItemOutboundOperationLog> logs;
} 