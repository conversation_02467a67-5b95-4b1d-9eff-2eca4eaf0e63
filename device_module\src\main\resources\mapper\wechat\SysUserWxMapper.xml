<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.wechat.mapper.SysUserWxMapper">

    <resultMap type="com.jingfang.wechat.module.entity.SysUserWx" id="SysUserWxResult">
        <id     property="id"           column="id"             />
        <result property="userId"       column="user_id"        />
        <result property="wxUserId"     column="wx_user_id"     />
        <result property="bindTime"     column="bind_time"      />
        <result property="bindType"     column="bind_type"      />
        <result property="status"       column="status"         />
    </resultMap>

    <sql id="selectUserWxVo">
        select id, user_id, wx_user_id, bind_time, bind_type, status
        from sys_user_wx
    </sql>

    <select id="selectUserWxByWxUserId" parameterType="Long" resultMap="SysUserWxResult">
        <include refid="selectUserWxVo"/>
        where wx_user_id = #{wxUserId} and status = '0'
    </select>

    <select id="selectUserWxByUserIdAndWxUserId" resultMap="SysUserWxResult">
        <include refid="selectUserWxVo"/>
        where user_id = #{param1} and wx_user_id = #{param2}
    </select>

    <insert id="insertUserWx" parameterType="com.jingfang.wechat.module.entity.SysUserWx" useGeneratedKeys="true" keyProperty="id">
        insert into sys_user_wx (
        <if test="userId != null">user_id,</if>
        <if test="wxUserId != null">wx_user_id,</if>
        <if test="bindTime != null">bind_time,</if>
        <if test="bindType != null">bind_type,</if>
        <if test="status != null">status</if>
        ) values (
        <if test="userId != null">#{userId},</if>
        <if test="wxUserId != null">#{wxUserId},</if>
        <if test="bindTime != null">#{bindTime},</if>
        <if test="bindType != null">#{bindType},</if>
        <if test="status != null">#{status}</if>
        )
    </insert>

    <update id="updateUserWx" parameterType="com.jingfang.wechat.module.entity.SysUserWx">
        update sys_user_wx
        <set>
            <if test="bindTime != null">bind_time = #{bindTime},</if>
            <if test="bindType != null">bind_type = #{bindType},</if>
            <if test="status != null">status = #{status},</if>
        </set>
        where id = #{id}
    </update>

</mapper>