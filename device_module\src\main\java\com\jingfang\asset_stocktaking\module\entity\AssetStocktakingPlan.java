package com.jingfang.asset_stocktaking.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 资产盘点计划实体类
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@TableName(value = "asset_stocktaking_plan")
public class AssetStocktakingPlan implements Serializable {

    /**
     * 盘点计划ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String planId;

    /**
     * 盘点计划名称
     */
    private String planName;

    /**
     * 盘点类型：1-全盘，2-部分盘点
     */
    private Integer planType;

    /**
     * 盘点范围（JSON格式）
     */
    private String planScope;

    /**
     * 计划开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    /**
     * 计划结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    /**
     * 负责人ID
     */
    private Long responsibleUserId;

    /**
     * 状态：1-草稿，2-待审批，3-执行中，4-已完成，5-已取消
     */
    private Integer status;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 盘点类型常量
     */
    public static final class PlanType {
        /** 全盘 */
        public static final int FULL = 1;
        /** 部分盘点 */
        public static final int PARTIAL = 2;
    }

    /**
     * 计划状态常量
     */
    public static final class Status {
        /** 草稿 */
        public static final int DRAFT = 1;
        /** 待审批 */
        public static final int PENDING_APPROVAL = 2;
        /** 执行中 */
        public static final int IN_PROGRESS = 3;
        /** 已完成 */
        public static final int COMPLETED = 4;
        /** 已取消 */
        public static final int CANCELLED = 5;
    }
}
