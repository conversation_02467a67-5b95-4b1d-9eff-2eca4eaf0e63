# 登录并获取Token，然后执行测试
param(
    [string]$BaseUrl = "http://localhost:8080",
    [string]$Username = "admin",
    [string]$Password = "admin123"
)

Write-Host "开始登录获取Token..." -ForegroundColor Green

# 登录获取Token
$loginData = @{
    username = $Username
    password = $Password
} | ConvertTo-Json

try {
    Write-Host "尝试登录: $Username" -ForegroundColor Yellow
    $loginResponse = Invoke-RestMethod -Uri "$BaseUrl/login" -Method POST -Body $loginData -ContentType 'application/json'
    
    if ($loginResponse.token) {
        $token = $loginResponse.token
        Write-Host "登录成功，获取到Token: $($token.Substring(0, 20))..." -ForegroundColor Green
        
        # 使用获取到的Token执行测试
        Write-Host "`n开始执行接口测试..." -ForegroundColor Cyan
        
        # 测试函数
        function Test-ApiWithToken {
            param(
                [string]$TestName,
                [string]$Method,
                [string]$Url,
                [string]$Body = $null
            )
            
            Write-Host "`n测试: $TestName" -ForegroundColor Yellow
            Write-Host "URL: $Method $Url" -ForegroundColor Gray
            
            try {
                $headers = @{
                    'Authorization' = "Bearer $token"
                    'Content-Type' = 'application/json'
                }
                
                if ($Method -eq "GET") {
                    $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $headers
                } else {
                    $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $headers -Body $Body
                }
                
                Write-Host "✓ 成功" -ForegroundColor Green
                Write-Host "响应: $($response | ConvertTo-Json -Depth 2)" -ForegroundColor Cyan
                return $response
            }
            catch {
                Write-Host "✗ 失败: $($_.Exception.Message)" -ForegroundColor Red
                if ($_.Exception.Response) {
                    Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
                }
                return $null
            }
        }
        
        # 执行测试
        Write-Host "`n=== P0级别接口测试 ===" -ForegroundColor Blue
        
        # 1. 获取我的盘点任务列表
        Test-ApiWithToken -TestName "获取我的盘点任务列表" -Method "GET" -Url "$BaseUrl/item/stocktaking/my-tasks"
        
        # 2. 根据物品条码查询物品信息
        Test-ApiWithToken -TestName "根据物品条码查询物品信息" -Method "GET" -Url "$BaseUrl/item/by-code/TEST001"
        
        # 3. 根据物品信息查找盘点明细
        $detailUrl = "$BaseUrl/item/stocktaking/detail/by-item?stocktakingId=test_id&itemId=test_item&warehouseId=1"
        Test-ApiWithToken -TestName "根据物品信息查找盘点明细" -Method "GET" -Url $detailUrl
        
        # 4. 更新盘点明细
        $updateJson = '{"actualQuantity": 95.00, "differenceReason": "测试差异", "photos": ["http://example.com/photo1.jpg"]}'
        Test-ApiWithToken -TestName "更新盘点明细" -Method "PUT" -Url "$BaseUrl/item/stocktaking/detail/test_detail_id" -Body $updateJson
        
        Write-Host "`n=== P1级别接口测试 ===" -ForegroundColor Blue
        
        # 5. 获取盘点进度
        Test-ApiWithToken -TestName "获取盘点进度" -Method "GET" -Url "$BaseUrl/item/stocktaking/test_stocktaking_id/progress"
        
        # 6. 获取个人盘点进度
        Test-ApiWithToken -TestName "获取个人盘点进度" -Method "GET" -Url "$BaseUrl/item/stocktaking/my-progress"
        
        Write-Host "`n=== P2级别接口测试 ===" -ForegroundColor Blue
        
        # 7. 获取个人盘点记录
        Test-ApiWithToken -TestName "获取个人盘点记录(今日)" -Method "GET" -Url "$BaseUrl/item/stocktaking/my-records?timeRange=today"
        
        # 8. 获取物品盘点历史
        Test-ApiWithToken -TestName "获取物品盘点历史" -Method "GET" -Url "$BaseUrl/item/stocktaking/item-history/test_item_id"
        
        Write-Host "`n=== 错误场景测试 ===" -ForegroundColor Red
        
        # 测试不存在的物品条码
        Test-ApiWithToken -TestName "查询不存在的物品条码" -Method "GET" -Url "$BaseUrl/item/by-code/NOTEXIST"
        
        Write-Host "`n测试完成!" -ForegroundColor Green
        
    } else {
        Write-Host "登录失败：未获取到Token" -ForegroundColor Red
        Write-Host "响应: $($loginResponse | ConvertTo-Json)" -ForegroundColor Red
    }
}
catch {
    Write-Host "登录失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}
