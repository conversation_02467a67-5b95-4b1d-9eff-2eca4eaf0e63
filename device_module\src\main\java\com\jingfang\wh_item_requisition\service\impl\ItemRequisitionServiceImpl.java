package com.jingfang.wh_item_requisition.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingfang.common.utils.SecurityUtils;
import com.jingfang.common.utils.uuid.UUID;
import com.jingfang.wh_item.service.ItemService;
import com.jingfang.wh_item_requisition.mapper.ItemRequisitionDetailMapper;
import com.jingfang.wh_item_requisition.mapper.ItemRequisitionMapper;
import com.jingfang.wh_item_requisition.mapper.ItemRequisitionOperationLogMapper;
import com.jingfang.wh_item_requisition.module.dto.ItemRequisitionDto;
import com.jingfang.wh_item_requisition.module.entity.ItemRequisition;
import com.jingfang.wh_item_requisition.module.entity.ItemRequisitionDetail;
import com.jingfang.wh_item_requisition.module.entity.ItemRequisitionOperationLog;
import com.jingfang.wh_item_requisition.module.request.ItemRequisitionSearchRequest;
import com.jingfang.wh_item_requisition.module.vo.ItemRequisitionDetailVo;
import com.jingfang.wh_item_requisition.module.vo.ItemRequisitionVo;
import com.jingfang.wh_item_requisition.service.ItemRequisitionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 物品领用单Service实现类
 */
@Slf4j
@Service
public class ItemRequisitionServiceImpl extends ServiceImpl<ItemRequisitionMapper, ItemRequisition> implements ItemRequisitionService {

    @Resource
    private ItemRequisitionMapper requisitionMapper;

    @Resource
    private ItemRequisitionDetailMapper detailMapper;

    @Resource
    private ItemRequisitionOperationLogMapper logMapper;

    @Resource
    private ItemService itemService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addItemRequisition(ItemRequisitionDto requisitionDto, String username) {
        // 1. 构建并保存领用单主表信息
        ItemRequisition requisition = new ItemRequisition();
        BeanUtils.copyProperties(requisitionDto.getMain(), requisition);

        // 生成领用单ID
        String requisitionId = "REQ" + UUID.fastUUID().toString(true).toUpperCase();
        requisition.setRequisitionId(requisitionId);

        // 设置初始状态为草稿状态(1)
        requisition.setStatus(1);
        requisition.setCreatorId(getUserId(username));
        requisition.setCreateTime(new Date());
        requisition.setUpdateTime(new Date());
        requisition.setDelFlag("0");

        // 保存领用单主表
        requisitionMapper.insert(requisition);

        // 2. 保存领用单明细
        if (requisitionDto.getDetails() != null && !requisitionDto.getDetails().isEmpty()) {
            for (ItemRequisitionDetail detail : requisitionDto.getDetails()) {
                // 校验物品是否存在
                validateItemExists(detail.getItemId());
                
                // 设置领用单ID
                detail.setRequisitionId(requisitionId);
                detail.setCreateTime(new Date());
                detail.setUpdateTime(new Date());

                // 保存明细
                detailMapper.insert(detail);
            }
        }

        // 3. 记录操作日志
        recordOperationLog(requisitionId, 1, "创建领用单", username);

        return requisitionId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateItemRequisition(String requisitionId, ItemRequisitionDto requisitionDto, String username) {
        // 1. 查询领用单
        ItemRequisition requisition = requisitionMapper.selectById(requisitionId);
        if (requisition == null) {
            throw new RuntimeException("领用单不存在");
        }

        // 2. 校验当前状态（只允许编辑草稿(1)和退回(5)状态的领用单）
        if (requisition.getStatus() != 1 && requisition.getStatus() != 5) {
            throw new RuntimeException("当前状态不允许编辑");
        }

        // 3. 更新领用单主表信息
        ItemRequisition newRequisition = requisitionDto.getMain();
        newRequisition.setRequisitionId(requisitionId);
        newRequisition.setStatus(requisition.getStatus()); // 保持状态不变
        newRequisition.setCreatorId(requisition.getCreatorId()); // 保持制单人不变
        newRequisition.setCreateTime(requisition.getCreateTime()); // 保留创建时间
        newRequisition.setHandlerId(requisition.getHandlerId());
        newRequisition.setHandleTime(requisition.getHandleTime());
        newRequisition.setHandleRemark(requisition.getHandleRemark());
        newRequisition.setAuditorId(requisition.getAuditorId());
        newRequisition.setAuditTime(requisition.getAuditTime());
        newRequisition.setAuditRemark(requisition.getAuditRemark());
        newRequisition.setUpdateTime(new Date());
        newRequisition.setDelFlag("0");

        requisitionMapper.updateById(newRequisition);

        // 4. 删除原领用单明细
        detailMapper.deleteByRequisitionId(requisitionId);

        // 5. 保存新的领用单明细
        if (requisitionDto.getDetails() != null && !requisitionDto.getDetails().isEmpty()) {
            for (ItemRequisitionDetail detail : requisitionDto.getDetails()) {
                // 校验物品是否存在
                validateItemExists(detail.getItemId());
                
                // 设置领用单ID
                detail.setRequisitionId(requisitionId);
                detail.setCreateTime(new Date());
                detail.setUpdateTime(new Date());

                // 保存明细
                detailMapper.insert(detail);
            }
        }

        // 6. 记录操作日志
        recordOperationLog(requisitionId, 1, "修改领用单", username);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitRequisition(String requisitionId, String username) {
        // 1. 查询领用单
        ItemRequisition requisition = requisitionMapper.selectById(requisitionId);
        if (requisition == null) {
            throw new RuntimeException("领用单不存在");
        }

        // 2. 校验当前状态，只有草稿(1)和退回(5)状态可以提交
        if (requisition.getStatus() != 1 && requisition.getStatus() != 5) {
            throw new RuntimeException("当前状态不允许提交");
        }

        // 3. 更新状态为待确认(2)
        requisition.setStatus(2);
        requisition.setUpdateTime(new Date());
        requisitionMapper.updateById(requisition);

        // 4. 记录操作日志
        recordOperationLog(requisitionId, 2, "提交领用单", username);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmRequisition(String requisitionId, String remark, String username) {
        // 1. 查询领用单
        ItemRequisition requisition = requisitionMapper.selectById(requisitionId);
        if (requisition == null) {
            throw new RuntimeException("领用单不存在");
        }

        // 2. 校验当前状态，只有待确认(2)状态可以确认
        if (requisition.getStatus() != 2) {
            throw new RuntimeException("当前状态不允许确认");
        }

        // 3. 获取当前用户ID
        Long userId = getUserId(username);

        // 4. 更新状态为待审核(3)
        requisition.setStatus(3);
        requisition.setHandlerId(userId);
        requisition.setHandleTime(new Date());
        requisition.setHandleRemark(remark);
        requisition.setUpdateTime(new Date());
        requisitionMapper.updateById(requisition);

        // 5. 记录操作日志
        recordOperationLog(requisitionId, 3, "确认领用单：" + remark, username);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditRequisition(String requisitionId, Integer status, String remark, String username) {
        // 1. 查询领用单
        ItemRequisition requisition = requisitionMapper.selectById(requisitionId);
        if (requisition == null) {
            throw new RuntimeException("领用单不存在");
        }

        // 2. 校验当前状态，只有待审核(3)状态可以审核
        if (requisition.getStatus() != 3) {
            throw new RuntimeException("当前状态不允许审核");
        }

        // 3. 校验审核结果参数
        if (status != 4 && status != 5) {
            throw new RuntimeException("无效的审核结果");
        }

        // 4. 获取当前用户ID
        Long userId = getUserId(username);

        // 5. 更新状态和审核信息
        requisition.setStatus(status);
        requisition.setAuditorId(userId);
        requisition.setAuditTime(new Date());
        requisition.setAuditRemark(remark);
        requisition.setUpdateTime(new Date());
        requisitionMapper.updateById(requisition);

        // 6. 记录操作日志
        recordOperationLog(requisitionId, status == 4 ? 4 : 5,
                status == 4 ? "审核通过：" + remark : "审核退回：" + remark, username);

        // 7. 如果审核通过，则自动扣减库存
        if (status == 4) {
            try {
                updateItemStock(requisitionId);

                // 扣减库存成功后，直接更新状态为已完成(6)
                requisition.setStatus(6);
                requisition.setUpdateTime(new Date());
                requisitionMapper.updateById(requisition);

                // 更新库存后，记录操作日志
                recordOperationLog(requisitionId, 6, "审核通过，已自动扣减库存，领用完成", username);

                log.info("领用单[{}]审核通过，已自动完成库存扣减，状态更新为已完成", requisitionId);
            } catch (Exception e) {
                log.error("领用单[{}]自动扣减库存失败: {}", requisitionId, e.getMessage(), e);
                throw new RuntimeException("审核通过，但自动扣减库存失败: " + e.getMessage());
            }
        }

        log.info("领用单[{}]审核{}，审核人：{}", requisitionId, status == 4 ? "通过" : "退回", username);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void completeRequisition(String requisitionId, String username) {
        // 1. 查询领用单
        ItemRequisition requisition = requisitionMapper.selectById(requisitionId);
        if (requisition == null) {
            throw new RuntimeException("领用单不存在");
        }

        // 2. 校验当前状态，只有已通过(4)状态可以完成
        if (requisition.getStatus() != 4) {
            throw new RuntimeException("当前状态不允许完成，领用单状态应为已通过(4)");
        }

        // 3. 更新库存
        try {
            updateItemStock(requisitionId);

            // 4. 更新状态为已完成(6)
            requisition.setStatus(6);
            requisition.setUpdateTime(new Date());
            requisitionMapper.updateById(requisition);

            // 5. 记录操作日志
            recordOperationLog(requisitionId, 6, "手动完成领用，已扣减库存", username);

            log.info("领用单[{}]手动完成领用，已自动扣减库存", requisitionId);
        } catch (Exception e) {
            log.error("领用单[{}]完成领用失败: {}", requisitionId, e.getMessage(), e);
            throw new RuntimeException("完成领用失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRequisition(String[] requisitionIds, String username) {
        Long userId = getUserId(username);

        for (String requisitionId : requisitionIds) {
            // 1. 查询领用单
            ItemRequisition requisition = requisitionMapper.selectById(requisitionId);
            if (requisition == null) {
                continue;
            }

            // 2. 校验状态（只允许删除草稿(1)和退回(5)状态的领用单）
            if (requisition.getStatus() != 1 && requisition.getStatus() != 5) {
                throw new RuntimeException("领用单[" + requisitionId + "]当前状态不允许删除");
            }

            // 3. 逻辑删除领用单
            requisition.setDelFlag("1");
            requisition.setUpdateTime(new Date());
            requisitionMapper.updateById(requisition);

            // 4. 记录操作日志
            recordOperationLog(requisitionId, 7, "删除领用单", username);
        }
    }

    @Override
    public IPage<ItemRequisitionVo> selectRequisitionList(ItemRequisitionSearchRequest request) {
        Page<ItemRequisitionVo> page = new Page<>(request.getPageNum(), request.getPageSize());
        return requisitionMapper.selectRequisitionList(page, request);
    }

    @Override
    public ItemRequisitionDetailVo getRequisitionDetail(String requisitionId) {
        // 1. 查询领用单基本信息
        ItemRequisitionDetailVo detail = requisitionMapper.selectRequisitionDetail(requisitionId);
        if (detail == null) {
            return null;
        }

        // 2. 查询明细列表（包含物品详细信息）
        List<ItemRequisitionDetailVo.ItemRequisitionDetailItemVo> details = detailMapper.selectDetailWithItemInfo(requisitionId);
        detail.setDetails(details);

        // 3. 查询操作日志
        List<ItemRequisitionOperationLog> logs = logMapper.selectByRequisitionId(requisitionId);
        detail.setOperationLogs(logs);

        return detail;
    }

    /**
     * 校验物品是否存在
     */
    private void validateItemExists(String itemId) {
        // 调用物品服务校验物品是否存在
        boolean exists = itemService.existsById(itemId);
        if (!exists) {
            throw new RuntimeException("物品[" + itemId + "]不存在");
        }
    }

    /**
     * 更新物品库存
     */
    private void updateItemStock(String requisitionId) {
        // 1. 查询领用单明细
        List<ItemRequisitionDetail> details = detailMapper.selectByRequisitionId(requisitionId);

        if (details == null || details.isEmpty()) {
            log.warn("领用单[{}]没有明细数据，无法更新库存", requisitionId);
            return;
        }

        // 2. 遍历明细，扣减库存
        for (ItemRequisitionDetail detail : details) {
            // 获取仓库ID，明细中必须指定仓库ID
            Integer warehouseId = detail.getWarehouseId();
            
            if (warehouseId == null) {
                log.error("领用单[{}]明细[物品ID:{}]未指定仓库ID，无法更新库存", requisitionId, detail.getItemId());
                throw new RuntimeException("物品[" + detail.getItemId() + "]未指定仓库，无法更新库存");
            }
            
            // 使用实际领用数量，如果没有则使用批准数量，再没有则使用申请数量
            int quantity = 0;
            if (detail.getActualQuantity() != null) {
                quantity = detail.getActualQuantity().intValue();
            } else if (detail.getApprovedQuantity() != null) {
                quantity = detail.getApprovedQuantity().intValue();
            } else {
                quantity = detail.getRequisitionQuantity().intValue();
            }
            
            // 调用物品服务更新库存（负数表示出库）
            boolean success = itemService.updateItemStock(
                    detail.getItemId(),
                    warehouseId,
                    -quantity, // 负数表示出库
                    2, // 出库操作类型
                    requisitionId,
                    "领用单出库",
                    detail.getShelfLocation()
            );

            if (!success) {
                throw new RuntimeException("物品[" + detail.getItemId() + "]在仓库[" + warehouseId + "]库存扣减失败");
            }

            log.info("根据领用单明细扣减库存成功: 物品ID[{}]，仓库[{}]，数量[{}]", 
                    detail.getItemId(), warehouseId, quantity);
        }
    }

    /**
     * 记录操作日志
     */
    private void recordOperationLog(String requisitionId, Integer operationType, String operationContent, String username) {
        try {
            ItemRequisitionOperationLog log = new ItemRequisitionOperationLog();
            log.setRequisitionId(requisitionId);
            log.setOperationType(operationType);
            log.setOperationContent(operationContent);
            log.setOperationTime(new Date());
            log.setOperatorId(getUserId(username));
            log.setOperatorName(username);

            logMapper.insert(log);
        } catch (Exception e) {
            // 记录日志失败不影响主流程
            log.error("记录领用单操作日志失败", e);
        }
    }

    /**
     * 获取用户ID
     */
    private Long getUserId(String username) {
        try {
            return SecurityUtils.getUserId();
        } catch (Exception e) {
            log.warn("获取用户ID失败，使用默认值1，用户名：{}", username);
            return 1L;
        }
    }
} 