<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.maintenance_plan.mapper.MaintenancePlanMapper">

    <resultMap id="MaintenancePlanVoMap" type="com.jingfang.maintenance_plan.module.vo.MaintenancePlanVo">
        <id property="planId" column="plan_id"/>
        <result property="planName" column="plan_name"/>
        <result property="assetId" column="asset_id"/>
        <result property="assetName" column="asset_name"/>
        <result property="assetCode" column="asset_code"/>
        <result property="maintenanceItems" column="maintenance_items"/>
        <result property="cycleType" column="cycle_type"/>
        <result property="cycleTypeName" column="cycle_type_name"/>
        <result property="cycleValue" column="cycle_value"/>
        <result property="cycleDescription" column="cycle_description"/>
        <result property="responsibleType" column="responsible_type"/>
        <result property="responsibleTypeName" column="responsible_type_name"/>
        <result property="responsibleId" column="responsible_id"/>
        <result property="responsibleName" column="responsible_name"/>
        <result property="status" column="status"/>
        <result property="statusName" column="status_name"/>
        <result property="nextMaintenanceTime" column="next_maintenance_time"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
    </resultMap>

    <!-- 分页查询维护计划列表 -->
    <select id="selectMaintenancePlanList" resultMap="MaintenancePlanVoMap">
        SELECT 
            mp.plan_id,
            mp.plan_name,
            mp.asset_id,
            al.asset_name,
            al.asset_id as asset_code,
            mp.maintenance_items,
            mp.cycle_type,
            CASE mp.cycle_type 
                WHEN 1 THEN '按天'
                WHEN 2 THEN '按周'
                WHEN 3 THEN '按月'
                WHEN 4 THEN '按年'
                ELSE '未知'
            END as cycle_type_name,
            mp.cycle_value,
            CONCAT(mp.cycle_value, 
                CASE mp.cycle_type 
                    WHEN 1 THEN '天'
                    WHEN 2 THEN '周'
                    WHEN 3 THEN '月'
                    WHEN 4 THEN '年'
                    ELSE ''
                END
            ) as cycle_description,
            mp.responsible_type,
            CASE mp.responsible_type 
                WHEN 1 THEN '个人'
                WHEN 2 THEN '部门'
                ELSE '未知'
            END as responsible_type_name,
            mp.responsible_id,
            CASE mp.responsible_type 
                WHEN 1 THEN su.nick_name
                WHEN 2 THEN sd.dept_name
                ELSE '未知'
            END as responsible_name,
            mp.status,
            CASE mp.status 
                WHEN 1 THEN '启用'
                WHEN 0 THEN '停用'
                ELSE '未知'
            END as status_name,
            mp.next_maintenance_time,
            mp.remark,
            mp.create_time,
            mp.create_by
        FROM maintenance_plan mp
        LEFT JOIN asset_ledger al ON mp.asset_id = al.asset_id
        LEFT JOIN sys_user su ON mp.responsible_type = 1 AND mp.responsible_id = su.user_id
        LEFT JOIN sys_dept sd ON mp.responsible_type = 2 AND mp.responsible_id = sd.dept_id
        WHERE mp.deleted = 0
        <if test="request.planName != null and request.planName != ''">
            AND mp.plan_name LIKE CONCAT('%', #{request.planName}, '%')
        </if>
        <if test="request.assetId != null and request.assetId != ''">
            AND mp.asset_id = #{request.assetId}
        </if>
        <if test="request.assetName != null and request.assetName != ''">
            AND al.asset_name LIKE CONCAT('%', #{request.assetName}, '%')
        </if>
        <if test="request.cycleType != null">
            AND mp.cycle_type = #{request.cycleType}
        </if>
        <if test="request.responsibleType != null">
            AND mp.responsible_type = #{request.responsibleType}
        </if>
        <if test="request.responsibleId != null">
            AND mp.responsible_id = #{request.responsibleId}
        </if>
        <if test="request.status != null">
            AND mp.status = #{request.status}
        </if>
        <if test="request.nextMaintenanceTimeStart != null">
            AND mp.next_maintenance_time >= #{request.nextMaintenanceTimeStart}
        </if>
        <if test="request.nextMaintenanceTimeEnd != null">
            AND mp.next_maintenance_time &lt;= #{request.nextMaintenanceTimeEnd}
        </if>
        <if test="request.createTimeStart != null">
            AND mp.create_time >= #{request.createTimeStart}
        </if>
        <if test="request.createTimeEnd != null">
            AND mp.create_time &lt;= #{request.createTimeEnd}
        </if>
        ORDER BY mp.create_time DESC
    </select>

    <!-- 根据ID查询维护计划详情 -->
    <select id="selectMaintenancePlanById" resultMap="MaintenancePlanVoMap">
        SELECT 
            mp.plan_id,
            mp.plan_name,
            mp.asset_id,
            al.asset_name,
            al.asset_id as asset_code,
            mp.maintenance_items,
            mp.cycle_type,
            CASE mp.cycle_type 
                WHEN 1 THEN '按天'
                WHEN 2 THEN '按周'
                WHEN 3 THEN '按月'
                WHEN 4 THEN '按年'
                ELSE '未知'
            END as cycle_type_name,
            mp.cycle_value,
            CONCAT(mp.cycle_value, 
                CASE mp.cycle_type 
                    WHEN 1 THEN '天'
                    WHEN 2 THEN '周'
                    WHEN 3 THEN '月'
                    WHEN 4 THEN '年'
                    ELSE ''
                END
            ) as cycle_description,
            mp.responsible_type,
            CASE mp.responsible_type 
                WHEN 1 THEN '个人'
                WHEN 2 THEN '部门'
                ELSE '未知'
            END as responsible_type_name,
            mp.responsible_id,
            CASE mp.responsible_type 
                WHEN 1 THEN su.nick_name
                WHEN 2 THEN sd.dept_name
                ELSE '未知'
            END as responsible_name,
            mp.status,
            CASE mp.status 
                WHEN 1 THEN '启用'
                WHEN 0 THEN '停用'
                ELSE '未知'
            END as status_name,
            mp.next_maintenance_time,
            mp.remark,
            mp.create_time,
            mp.create_by
        FROM maintenance_plan mp
        LEFT JOIN asset_ledger al ON mp.asset_id = al.asset_id
        LEFT JOIN sys_user su ON mp.responsible_type = 1 AND mp.responsible_id = su.user_id
        LEFT JOIN sys_dept sd ON mp.responsible_type = 2 AND mp.responsible_id = sd.dept_id
        WHERE mp.deleted = 0 AND mp.plan_id = #{planId}
    </select>

    <!-- 查询即将到期的维护计划 -->
    <select id="selectUpcomingMaintenancePlans" resultMap="MaintenancePlanVoMap">
        SELECT 
            mp.plan_id,
            mp.plan_name,
            mp.asset_id,
            al.asset_name,
            al.asset_id as asset_code,
            mp.maintenance_items,
            mp.cycle_type,
            CASE mp.cycle_type 
                WHEN 1 THEN '按天'
                WHEN 2 THEN '按周'
                WHEN 3 THEN '按月'
                WHEN 4 THEN '按年'
                ELSE '未知'
            END as cycle_type_name,
            mp.cycle_value,
            CONCAT(mp.cycle_value, 
                CASE mp.cycle_type 
                    WHEN 1 THEN '天'
                    WHEN 2 THEN '周'
                    WHEN 3 THEN '月'
                    WHEN 4 THEN '年'
                    ELSE ''
                END
            ) as cycle_description,
            mp.responsible_type,
            CASE mp.responsible_type 
                WHEN 1 THEN '个人'
                WHEN 2 THEN '部门'
                ELSE '未知'
            END as responsible_type_name,
            mp.responsible_id,
            CASE mp.responsible_type 
                WHEN 1 THEN su.nick_name
                WHEN 2 THEN sd.dept_name
                ELSE '未知'
            END as responsible_name,
            mp.status,
            CASE mp.status 
                WHEN 1 THEN '启用'
                WHEN 0 THEN '停用'
                ELSE '未知'
            END as status_name,
            mp.next_maintenance_time,
            mp.remark,
            mp.create_time,
            mp.create_by
        FROM maintenance_plan mp
        LEFT JOIN asset_ledger al ON mp.asset_id = al.asset_id
        LEFT JOIN sys_user su ON mp.responsible_type = 1 AND mp.responsible_id = su.user_id
        LEFT JOIN sys_dept sd ON mp.responsible_type = 2 AND mp.responsible_id = sd.dept_id
        WHERE mp.deleted = 0 
        AND mp.status = 1
        AND mp.next_maintenance_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL #{days} DAY)
        ORDER BY mp.next_maintenance_time ASC
    </select>

    <!-- 查询已过期的维护计划 -->
    <select id="selectOverdueMaintenancePlans" resultMap="MaintenancePlanVoMap">
        SELECT 
            mp.plan_id,
            mp.plan_name,
            mp.asset_id,
            al.asset_name,
            al.asset_id as asset_code,
            mp.maintenance_items,
            mp.cycle_type,
            CASE mp.cycle_type 
                WHEN 1 THEN '按天'
                WHEN 2 THEN '按周'
                WHEN 3 THEN '按月'
                WHEN 4 THEN '按年'
                ELSE '未知'
            END as cycle_type_name,
            mp.cycle_value,
            CONCAT(mp.cycle_value, 
                CASE mp.cycle_type 
                    WHEN 1 THEN '天'
                    WHEN 2 THEN '周'
                    WHEN 3 THEN '月'
                    WHEN 4 THEN '年'
                    ELSE ''
                END
            ) as cycle_description,
            mp.responsible_type,
            CASE mp.responsible_type 
                WHEN 1 THEN '个人'
                WHEN 2 THEN '部门'
                ELSE '未知'
            END as responsible_type_name,
            mp.responsible_id,
            CASE mp.responsible_type 
                WHEN 1 THEN su.nick_name
                WHEN 2 THEN sd.dept_name
                ELSE '未知'
            END as responsible_name,
            mp.status,
            CASE mp.status 
                WHEN 1 THEN '启用'
                WHEN 0 THEN '停用'
                ELSE '未知'
            END as status_name,
            mp.next_maintenance_time,
            mp.remark,
            mp.create_time,
            mp.create_by
        FROM maintenance_plan mp
        LEFT JOIN asset_ledger al ON mp.asset_id = al.asset_id
        LEFT JOIN sys_user su ON mp.responsible_type = 1 AND mp.responsible_id = su.user_id
        LEFT JOIN sys_dept sd ON mp.responsible_type = 2 AND mp.responsible_id = sd.dept_id
        WHERE mp.deleted = 0 
        AND mp.status = 1
        AND mp.next_maintenance_time &lt;NOW()
        ORDER BY mp.next_maintenance_time ASC
    </select>

    <!-- 根据资产ID查询维护计划 -->
    <select id="selectMaintenancePlansByAssetId" resultMap="MaintenancePlanVoMap">
        SELECT 
            mp.plan_id,
            mp.plan_name,
            mp.asset_id,
            al.asset_name,
            al.asset_id as asset_code,
            mp.maintenance_items,
            mp.cycle_type,
            CASE mp.cycle_type 
                WHEN 1 THEN '按天'
                WHEN 2 THEN '按周'
                WHEN 3 THEN '按月'
                WHEN 4 THEN '按年'
                ELSE '未知'
            END as cycle_type_name,
            mp.cycle_value,
            CONCAT(mp.cycle_value, 
                CASE mp.cycle_type 
                    WHEN 1 THEN '天'
                    WHEN 2 THEN '周'
                    WHEN 3 THEN '月'
                    WHEN 4 THEN '年'
                    ELSE ''
                END
            ) as cycle_description,
            mp.responsible_type,
            CASE mp.responsible_type 
                WHEN 1 THEN '个人'
                WHEN 2 THEN '部门'
                ELSE '未知'
            END as responsible_type_name,
            mp.responsible_id,
            CASE mp.responsible_type 
                WHEN 1 THEN su.nick_name
                WHEN 2 THEN sd.dept_name
                ELSE '未知'
            END as responsible_name,
            mp.status,
            CASE mp.status 
                WHEN 1 THEN '启用'
                WHEN 0 THEN '停用'
                ELSE '未知'
            END as status_name,
            mp.next_maintenance_time,
            mp.remark,
            mp.create_time,
            mp.create_by
        FROM maintenance_plan mp
        LEFT JOIN asset_ledger al ON mp.asset_id = al.asset_id
        LEFT JOIN sys_user su ON mp.responsible_type = 1 AND mp.responsible_id = su.user_id
        LEFT JOIN sys_dept sd ON mp.responsible_type = 2 AND mp.responsible_id = sd.dept_id
        WHERE mp.deleted = 0 AND mp.asset_id = #{assetId}
        ORDER BY mp.create_time DESC
    </select>

    <!-- 根据负责人查询维护计划 -->
    <select id="selectMaintenancePlansByResponsible" resultMap="MaintenancePlanVoMap">
        SELECT 
            mp.plan_id,
            mp.plan_name,
            mp.asset_id,
            al.asset_name,
            al.asset_id as asset_code,
            mp.maintenance_items,
            mp.cycle_type,
            CASE mp.cycle_type 
                WHEN 1 THEN '按天'
                WHEN 2 THEN '按周'
                WHEN 3 THEN '按月'
                WHEN 4 THEN '按年'
                ELSE '未知'
            END as cycle_type_name,
            mp.cycle_value,
            CONCAT(mp.cycle_value, 
                CASE mp.cycle_type 
                    WHEN 1 THEN '天'
                    WHEN 2 THEN '周'
                    WHEN 3 THEN '月'
                    WHEN 4 THEN '年'
                    ELSE ''
                END
            ) as cycle_description,
            mp.responsible_type,
            CASE mp.responsible_type 
                WHEN 1 THEN '个人'
                WHEN 2 THEN '部门'
                ELSE '未知'
            END as responsible_type_name,
            mp.responsible_id,
            CASE mp.responsible_type 
                WHEN 1 THEN su.nick_name
                WHEN 2 THEN sd.dept_name
                ELSE '未知'
            END as responsible_name,
            mp.status,
            CASE mp.status 
                WHEN 1 THEN '启用'
                WHEN 0 THEN '停用'
                ELSE '未知'
            END as status_name,
            mp.next_maintenance_time,
            mp.remark,
            mp.create_time,
            mp.create_by
        FROM maintenance_plan mp
        LEFT JOIN asset_ledger al ON mp.asset_id = al.asset_id
        LEFT JOIN sys_user su ON mp.responsible_type = 1 AND mp.responsible_id = su.user_id
        LEFT JOIN sys_dept sd ON mp.responsible_type = 2 AND mp.responsible_id = sd.dept_id
        WHERE mp.deleted = 0 
        AND mp.responsible_type = #{responsibleType}
        AND mp.responsible_id = #{responsibleId}
        ORDER BY mp.create_time DESC
    </select>

</mapper> 