package com.jingfang.asset_ledger.module.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 资产工作台数据VO
 */
@Data
public class AssetWorkbenchVo {
    
    /**
     * 资产概览数据
     */
    private AssetOverviewVo overview;
    
    /**
     * 资产趋势数据
     */
    private List<AssetTrendVo> trends;
    
    /**
     * 入库出库统计
     */
    private AssetInOutStatisticsVo inOutStatistics;
    
    /**
     * 处置统计
     */
    private AssetDisposalStatisticsVo disposalStatistics;
    
    /**
     * 资产概览数据
     */
    @Data
    public static class AssetOverviewVo {
        /**
         * 资产总数
         */
        private Long totalCount;
        
        /**
         * 资产总值
         */
        private BigDecimal totalValue;
        
        /**
         * 本月新增资产数
         */
        private Long monthlyNewCount;
        
        /**
         * 本月新增资产值
         */
        private BigDecimal monthlyNewValue;
        
        /**
         * 待处置资产数
         */
        private Long pendingDisposalCount;
        
        /**
         * 正常状态资产数
         */
        private Long normalStatusCount;
        
        /**
         * 维修状态资产数
         */
        private Long repairStatusCount;
        
        /**
         * 报废状态资产数
         */
        private Long scrapStatusCount;
        
        /**
         * 资产利用率
         */
        private BigDecimal utilizationRate;
    }
    
    /**
     * 资产趋势数据
     */
    @Data
    public static class AssetTrendVo {
        /**
         * 日期
         */
        private String date;
        
        /**
         * 资产总值
         */
        private BigDecimal totalValue;
        
        /**
         * 新增资产数
         */
        private Long newCount;
        
        /**
         * 新增资产值
         */
        private BigDecimal newValue;
        
        /**
         * 处置资产数
         */
        private Long disposalCount;
        
        /**
         * 处置资产值
         */
        private BigDecimal disposalValue;
    }
    
    /**
     * 入库出库统计
     */
    @Data
    public static class AssetInOutStatisticsVo {
        /**
         * 本月入库单数
         */
        private Long monthlyInboundCount;
        
        /**
         * 本月入库资产数
         */
        private Long monthlyInboundAssetCount;
        
        /**
         * 本月入库资产值
         */
        private BigDecimal monthlyInboundValue;
        
        /**
         * 待确认入库单数
         */
        private Long pendingConfirmCount;
        
        /**
         * 待审核入库单数
         */
        private Long pendingAuditCount;
        
        /**
         * 入库趋势数据
         */
        private List<InboundTrendVo> inboundTrends;
    }
    
    /**
     * 入库趋势数据
     */
    @Data
    public static class InboundTrendVo {
        /**
         * 日期
         */
        private String date;
        
        /**
         * 入库单数
         */
        private Long inboundCount;
        
        /**
         * 入库资产数
         */
        private Long assetCount;
        
        /**
         * 入库资产值
         */
        private BigDecimal assetValue;
    }
    
    /**
     * 处置统计
     */
    @Data
    public static class AssetDisposalStatisticsVo {
        /**
         * 待审核处置申请数
         */
        private Long pendingApprovalCount;
        
        /**
         * 处置中申请数
         */
        private Long processingCount;
        
        /**
         * 本月完成处置数
         */
        private Long monthlyCompletedCount;
        
        /**
         * 本月处置资产值
         */
        private BigDecimal monthlyDisposalValue;
        
        /**
         * 各类型处置统计
         */
        private List<DisposalTypeStatisticsVo> typeStatistics;
        
        /**
         * 处置趋势数据
         */
        private List<DisposalTrendVo> disposalTrends;
    }
    
    /**
     * 处置类型统计
     */
    @Data
    public static class DisposalTypeStatisticsVo {
        /**
         * 处置类型(1-报废, 2-调拨, 3-出售, 4-其他)
         */
        private Integer disposalType;
        
        /**
         * 类型名称
         */
        private String typeName;
        
        /**
         * 数量
         */
        private Long count;
        
        /**
         * 总值
         */
        private BigDecimal totalValue;
    }
    
    /**
     * 处置趋势数据
     */
    @Data
    public static class DisposalTrendVo {
        /**
         * 日期
         */
        private String date;
        
        /**
         * 处置申请数
         */
        private Long applicationCount;
        
        /**
         * 完成处置数
         */
        private Long completedCount;
        
        /**
         * 处置资产值
         */
        private BigDecimal disposalValue;
    }
}
