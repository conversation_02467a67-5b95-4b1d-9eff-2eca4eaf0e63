-- ========================================
-- 设备参数MQTT配置示例
-- 提供常见设备类型的参数配置模板
-- ========================================

-- 清理示例数据（如果存在）
DELETE FROM device_param WHERE param_key LIKE 'EXAMPLE_%';

-- ========================================
-- 示例1：温湿度传感器参数配置
-- ========================================
INSERT INTO device_param (
    device_id, param_key, param_name, mqtt_property_name, param_description,
    param_unit, data_type, category_id, is_alert_param,
    range_start, range_end, alert_threshold_min, alert_threshold_max,
    del_flag, created_time
) VALUES 
-- 温度参数（运行参数）
(1, 'EXAMPLE_TEMP_01', '环境温度', 'temperature', '设备周围环境温度', 
 '°C', 1, 1, 0, -10.0, 50.0, 5.0, 35.0, 0, NOW()),

-- 湿度参数（运行参数） 
(1, 'EXAMPLE_HUMI_01', '环境湿度', 'humidity', '设备周围环境湿度',
 '%', 1, 2, 0, 0.0, 100.0, 20.0, 80.0, 0, NOW()),

-- 温度告警参数
(1, 'EXAMPLE_TEMP_ALERT', '温度告警', 'temp_alarm', '温度超限告警状态',
 '', 2, 1, 1, NULL, NULL, NULL, NULL, 0, NOW()),

-- 湿度告警参数
(1, 'EXAMPLE_HUMI_ALERT', '湿度告警', 'humi_alarm', '湿度超限告警状态',
 '', 2, 2, 1, NULL, NULL, NULL, NULL, 0, NOW());

-- ========================================
-- 示例2：电机控制器参数配置
-- ========================================
INSERT INTO device_param (
    device_id, param_key, param_name, mqtt_property_name, param_description,
    param_unit, data_type, category_id, is_alert_param,
    range_start, range_end, alert_threshold_min, alert_threshold_max,
    del_flag, created_time
) VALUES 
-- 电机转速
(2, 'EXAMPLE_MOTOR_SPEED', '电机转速', 'motor_speed', '电机当前转速',
 'rpm', 0, 8, 0, 0.0, 3000.0, 100.0, 2800.0, 0, NOW()),

-- 输入电压
(2, 'EXAMPLE_INPUT_VOLTAGE', '输入电压', 'input_voltage', '电机输入电压',
 'V', 1, 4, 0, 200.0, 240.0, 210.0, 230.0, 0, NOW()),

-- 输入电流
(2, 'EXAMPLE_INPUT_CURRENT', '输入电流', 'input_current', '电机输入电流',
 'A', 1, 5, 0, 0.0, 50.0, 5.0, 45.0, 0, NOW()),

-- 电机温度
(2, 'EXAMPLE_MOTOR_TEMP', '电机温度', 'motor_temperature', '电机绕组温度',
 '°C', 1, 1, 0, 20.0, 80.0, 30.0, 70.0, 0, NOW()),

-- 运行状态
(2, 'EXAMPLE_MOTOR_STATUS', '运行状态', 'motor_status', '电机运行状态',
 '', 2, 6, 0, NULL, NULL, NULL, NULL, 0, NOW()),

-- 故障告警
(2, 'EXAMPLE_MOTOR_FAULT', '故障告警', 'motor_fault', '电机故障告警',
 '', 2, 6, 1, NULL, NULL, NULL, NULL, 0, NOW()),

-- 过载告警
(2, 'EXAMPLE_OVERLOAD_ALERT', '过载告警', 'overload_alarm', '电机过载告警',
 '', 2, 5, 1, NULL, NULL, NULL, NULL, 0, NOW());

-- ========================================
-- 示例3：压力传感器参数配置
-- ========================================
INSERT INTO device_param (
    device_id, param_key, param_name, mqtt_property_name, param_description,
    param_unit, data_type, category_id, is_alert_param,
    range_start, range_end, alert_threshold_min, alert_threshold_max,
    del_flag, created_time
) VALUES 
-- 系统压力
(3, 'EXAMPLE_SYS_PRESSURE', '系统压力', 'system_pressure', '系统当前压力值',
 'MPa', 1, 3, 0, 0.0, 10.0, 1.0, 8.0, 0, NOW()),

-- 压力变化率
(3, 'EXAMPLE_PRESSURE_RATE', '压力变化率', 'pressure_rate', '压力变化速率',
 'MPa/min', 1, 3, 0, -1.0, 1.0, -0.5, 0.5, 0, NOW()),

-- 压力告警
(3, 'EXAMPLE_PRESSURE_ALERT', '压力告警', 'pressure_alarm', '压力超限告警',
 '', 2, 3, 1, NULL, NULL, NULL, NULL, 0, NOW());

-- ========================================
-- 示例4：流量计参数配置
-- ========================================
INSERT INTO device_param (
    device_id, param_key, param_name, mqtt_property_name, param_description,
    param_unit, data_type, category_id, is_alert_param,
    range_start, range_end, alert_threshold_min, alert_threshold_max,
    del_flag, created_time
) VALUES 
-- 瞬时流量
(4, 'EXAMPLE_INSTANT_FLOW', '瞬时流量', 'instant_flow', '当前瞬时流量',
 'L/min', 1, 7, 0, 0.0, 1000.0, 50.0, 800.0, 0, NOW()),

-- 累计流量
(4, 'EXAMPLE_TOTAL_FLOW', '累计流量', 'total_flow', '累计流量值',
 'L', 1, 7, 0, 0.0, 999999.0, NULL, NULL, 0, NOW()),

-- 流量温度
(4, 'EXAMPLE_FLOW_TEMP', '流体温度', 'flow_temperature', '流体温度',
 '°C', 1, 1, 0, 0.0, 100.0, 10.0, 80.0, 0, NOW()),

-- 低流量告警
(4, 'EXAMPLE_LOW_FLOW_ALERT', '低流量告警', 'low_flow_alarm', '流量过低告警',
 '', 2, 7, 1, NULL, NULL, NULL, NULL, 0, NOW());

-- ========================================
-- 示例5：UPS电源参数配置
-- ========================================
INSERT INTO device_param (
    device_id, param_key, param_name, mqtt_property_name, param_description,
    param_unit, data_type, category_id, is_alert_param,
    range_start, range_end, alert_threshold_min, alert_threshold_max,
    del_flag, created_time
) VALUES 
-- 输入电压
(5, 'EXAMPLE_UPS_INPUT_V', 'UPS输入电压', 'ups_input_voltage', 'UPS输入电压',
 'V', 1, 4, 0, 180.0, 250.0, 190.0, 240.0, 0, NOW()),

-- 输出电压
(5, 'EXAMPLE_UPS_OUTPUT_V', 'UPS输出电压', 'ups_output_voltage', 'UPS输出电压',
 'V', 1, 4, 0, 200.0, 230.0, 210.0, 225.0, 0, NOW()),

-- 电池电量
(5, 'EXAMPLE_UPS_BATTERY', 'UPS电池电量', 'ups_battery_level', 'UPS电池剩余电量',
 '%', 0, 6, 0, 0.0, 100.0, 20.0, NULL, 0, NOW()),

-- 负载率
(5, 'EXAMPLE_UPS_LOAD', 'UPS负载率', 'ups_load_rate', 'UPS当前负载率',
 '%', 0, 6, 0, 0.0, 100.0, NULL, 80.0, 0, NOW()),

-- 市电状态
(5, 'EXAMPLE_UPS_MAINS', '市电状态', 'mains_status', '市电供电状态',
 '', 2, 6, 0, NULL, NULL, NULL, NULL, 0, NOW()),

-- 电池告警
(5, 'EXAMPLE_UPS_BATT_ALERT', '电池告警', 'battery_alarm', '电池电量低告警',
 '', 2, 6, 1, NULL, NULL, NULL, NULL, 0, NOW()),

-- 过载告警
(5, 'EXAMPLE_UPS_LOAD_ALERT', '过载告警', 'overload_alarm', 'UPS过载告警',
 '', 2, 6, 1, NULL, NULL, NULL, NULL, 0, NOW());

-- ========================================
-- 查看配置结果
-- ========================================
SELECT 
    d.device_name,
    p.param_name,
    p.mqtt_property_name,
    p.param_unit,
    CASE p.data_type 
        WHEN 0 THEN '整型'
        WHEN 1 THEN '浮点型' 
        WHEN 2 THEN '布尔型'
        WHEN 3 THEN '字符串型'
        ELSE '未知'
    END as data_type_name,
    c.category_name,
    CASE p.is_alert_param 
        WHEN 0 THEN '运行参数'
        WHEN 1 THEN '告警参数'
        ELSE '未知'
    END as param_type,
    CONCAT(IFNULL(p.range_start, ''), ' - ', IFNULL(p.range_end, '')) as normal_range,
    CONCAT(IFNULL(p.alert_threshold_min, ''), ' - ', IFNULL(p.alert_threshold_max, '')) as alert_range
FROM device_param p
LEFT JOIN device_info d ON p.device_id = d.id
LEFT JOIN device_param_category c ON p.category_id = c.category_id
WHERE p.param_key LIKE 'EXAMPLE_%'
ORDER BY p.device_id, p.category_id, p.param_name;

-- ========================================
-- 使用说明：
-- 1. 以上示例展示了不同类型设备的参数配置方法
-- 2. mqtt_property_name 字段是关键，需要与实际MQTT网关配置一致
-- 3. is_alert_param 区分运行参数和告警参数
-- 4. 运行参数有正常范围和告警阈值
-- 5. 告警参数通常是布尔型，表示告警状态
-- 6. 根据实际设备调整参数配置
-- ========================================
