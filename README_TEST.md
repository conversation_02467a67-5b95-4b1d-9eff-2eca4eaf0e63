# 资产盘点功能测试指南

本文档提供了资产盘点功能的完整测试指南，包括单元测试、集成测试、API测试等。

## 📋 测试概览

### 测试类型
- **单元测试**: 测试各个Service层的业务逻辑
- **集成测试**: 测试完整的业务流程
- **控制器测试**: 测试REST API接口
- **API测试**: 使用Python脚本测试实际HTTP接口

### 测试覆盖范围
- ✅ 盘点计划管理（CRUD、工作流）
- ✅ 盘点任务管理（分发、执行、状态管理）
- ✅ 盘点记录管理（扫码、批量操作）
- ✅ 差异分析处理（自动分析、处理建议）
- ✅ 报告生成（汇总、明细、图表）

## 🚀 快速开始

### 1. 环境准备

#### 必需环境
- Java 8+
- Maven 3.6+
- MySQL 5.7+
- Redis (可选)

#### 可选环境
- Python 3.6+ (用于API测试)
- requests库: `pip install requests`

### 2. 数据库准备

```bash
# 1. 创建测试数据库
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS device_monitor_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 2. 导入基础表结构（使用您现有的数据库脚本）
mysql -u root -p device_monitor_test < your_database_schema.sql

# 3. 导入测试数据
mysql -u root -p device_monitor_test < scripts/test_data.sql
```

### 3. 配置文件

测试配置文件位于 `device_module/src/test/resources/application-test.yml`，请根据您的环境调整数据库连接信息：

```yaml
spring:
  datasource:
    url: ************************************************************************************************************************************************************
    username: root
    password: your_password
```

## 🧪 运行测试

### 使用测试脚本（推荐）

```bash
# 给脚本执行权限
chmod +x scripts/run_tests.sh

# 运行所有测试
./scripts/run_tests.sh

# 只运行单元测试
./scripts/run_tests.sh --unit

# 只运行集成测试
./scripts/run_tests.sh --integration

# 只运行API测试
./scripts/run_tests.sh --api

# 生成测试报告
./scripts/run_tests.sh --report

# 查看帮助
./scripts/run_tests.sh --help
```

### 使用Maven命令

```bash
# 运行所有测试
mvn test -Dspring.profiles.active=test

# 运行特定测试类
mvn test -Dtest=StocktakingPlanServiceTest -Dspring.profiles.active=test

# 运行集成测试
mvn test -Dtest=StocktakingIntegrationTest -Dspring.profiles.active=test

# 生成测试报告
mvn surefire-report:report
```

### 手动运行API测试

```bash
# 1. 启动应用
mvn spring-boot:run -Dspring.profiles.active=test

# 2. 运行API测试脚本
python3 scripts/test_api.py
```

## 📊 测试详情

### 单元测试

#### StocktakingPlanServiceTest
测试盘点计划服务的各项功能：

- ✅ `testCreatePlan()` - 创建盘点计划
- ✅ `testCreatePlanWithInvalidData()` - 无效数据验证
- ✅ `testCreatePlanWithDuplicateName()` - 重复名称检查
- ✅ `testEditPlan()` - 修改盘点计划
- ✅ `testDeletePlan()` - 删除盘点计划
- ✅ `testSelectPlanList()` - 查询计划列表
- ✅ `testSubmitForApproval()` - 提交审批
- ✅ `testApprovePlan()` - 审批通过
- ✅ `testRejectPlan()` - 审批拒绝
- ✅ `testCheckPlanNameExists()` - 名称重复检查
- ✅ `testCopyPlan()` - 复制计划
- ✅ `testValidatePlanData()` - 数据验证

#### StocktakingTaskServiceTest
测试盘点任务服务的各项功能：

- ✅ `testCreateTask()` - 创建任务
- ✅ `testEditTask()` - 修改任务
- ✅ `testDeleteTask()` - 删除任务
- ✅ `testClaimTask()` - 领取任务
- ✅ `testStartTask()` - 开始任务
- ✅ `testCompleteTask()` - 完成任务
- ✅ `testReassignTask()` - 重新分配任务
- ✅ `testCalculateProgress()` - 计算进度
- ✅ `testUpdateTaskProgress()` - 更新进度
- ✅ `testBatchUpdateTaskStatus()` - 批量更新状态

### 集成测试

#### StocktakingIntegrationTest
测试完整的业务流程：

- ✅ `testCompleteStocktakingWorkflow()` - 完整盘点流程
- ✅ `testPlanRejectionWorkflow()` - 计划拒绝流程
- ✅ `testTaskReassignmentWorkflow()` - 任务重分配流程
- ✅ `testBatchOperationsWorkflow()` - 批量操作流程
- ✅ `testErrorHandlingWorkflow()` - 错误处理流程
- ✅ `testDataConsistencyWorkflow()` - 数据一致性验证

### 控制器测试

#### StocktakingPlanControllerTest
测试REST API接口：

- ✅ HTTP请求/响应测试
- ✅ 权限验证测试
- ✅ 参数验证测试
- ✅ 错误处理测试

### API测试

#### test_api.py
使用Python脚本测试实际HTTP接口：

- ✅ 用户登录认证
- ✅ 盘点计划CRUD操作
- ✅ 盘点工作流测试
- ✅ 任务管理测试
- ✅ 记录管理测试
- ✅ 差异分析测试
- ✅ 报告生成测试

## 📈 测试报告

### 查看测试报告

运行测试后，可以查看以下报告：

```bash
# 测试结果报告
open target/site/surefire-report.html

# 代码覆盖率报告
open target/site/jacoco/index.html
```

### 测试覆盖率目标

- **行覆盖率**: ≥ 80%
- **分支覆盖率**: ≥ 70%
- **方法覆盖率**: ≥ 90%

## 🐛 常见问题

### 1. 数据库连接失败

**问题**: `java.sql.SQLException: Access denied for user`

**解决方案**:
- 检查数据库用户名密码
- 确保测试数据库存在
- 检查数据库权限

### 2. 端口被占用

**问题**: `Port 8080 was already in use`

**解决方案**:
```bash
# 查找占用端口的进程
lsof -i :8080

# 杀死进程
kill -9 <PID>

# 或者修改测试配置中的端口
```

### 3. 测试数据冲突

**问题**: 测试数据重复或冲突

**解决方案**:
```bash
# 清理测试数据
mysql -u root -p device_monitor_test -e "
DELETE FROM asset_stocktaking_difference WHERE plan_id LIKE 'test-%';
DELETE FROM asset_stocktaking_record WHERE task_id IN (SELECT task_id FROM asset_stocktaking_task WHERE plan_id LIKE 'test-%');
DELETE FROM asset_stocktaking_task WHERE plan_id LIKE 'test-%';
DELETE FROM asset_stocktaking_plan WHERE plan_id LIKE 'test-%';
"

# 重新导入测试数据
mysql -u root -p device_monitor_test < scripts/test_data.sql
```

### 4. Maven依赖问题

**问题**: 依赖下载失败或版本冲突

**解决方案**:
```bash
# 清理Maven缓存
mvn clean

# 强制更新依赖
mvn clean install -U

# 跳过测试重新编译
mvn clean compile -DskipTests
```

## 🔧 自定义测试

### 添加新的测试用例

1. **单元测试**: 在对应的Service测试类中添加新方法
2. **集成测试**: 在`StocktakingIntegrationTest`中添加新的工作流测试
3. **API测试**: 在`test_api.py`中添加新的API测试方法

### 测试数据管理

- 测试数据统一使用`test-`前缀
- 在`scripts/test_data.sql`中维护测试数据
- 测试完成后自动清理临时数据

### 性能测试

```bash
# 使用JMeter进行性能测试
jmeter -n -t stocktaking_performance_test.jmx -l results.jtl

# 使用ab进行简单压力测试
ab -n 1000 -c 10 http://localhost:8080/asset/stocktaking/plan/list
```

## 📝 测试最佳实践

1. **测试隔离**: 每个测试方法独立，不依赖其他测试
2. **数据清理**: 使用`@Transactional`注解自动回滚
3. **异常测试**: 测试各种异常情况和边界条件
4. **性能考虑**: 避免在测试中使用真实的大量数据
5. **文档更新**: 添加新功能时同步更新测试用例

## 🎯 持续集成

### GitHub Actions配置示例

```yaml
name: 资产盘点功能测试

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:5.7
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: device_monitor_test
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up JDK 8
      uses: actions/setup-java@v2
      with:
        java-version: '8'
        distribution: 'adopt'
    
    - name: Cache Maven packages
      uses: actions/cache@v2
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
    
    - name: Run tests
      run: ./scripts/run_tests.sh --all
    
    - name: Upload test reports
      uses: actions/upload-artifact@v2
      with:
        name: test-reports
        path: target/site/
```

---

## 📞 支持

如果在测试过程中遇到问题，请：

1. 查看本文档的常见问题部分
2. 检查测试日志输出
3. 确认环境配置正确
4. 联系开发团队获取支持

**祝您测试顺利！** 🎉
