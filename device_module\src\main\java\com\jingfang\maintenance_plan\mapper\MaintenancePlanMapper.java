package com.jingfang.maintenance_plan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jingfang.maintenance_plan.module.entity.MaintenancePlan;
import com.jingfang.maintenance_plan.module.request.MaintenancePlanSearchRequest;
import com.jingfang.maintenance_plan.module.vo.MaintenancePlanVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 维护计划Mapper接口
 */
@Mapper
public interface MaintenancePlanMapper extends BaseMapper<MaintenancePlan> {
    
    /**
     * 分页查询维护计划列表
     * 
     * @param page 分页参数
     * @param request 查询条件
     * @return 维护计划列表
     */
    IPage<MaintenancePlanVo> selectMaintenancePlanList(Page<MaintenancePlanVo> page, @Param("request") MaintenancePlanSearchRequest request);
    
    /**
     * 根据ID查询维护计划详情
     * 
     * @param planId 计划ID
     * @return 维护计划详情
     */
    MaintenancePlanVo selectMaintenancePlanById(@Param("planId") String planId);
    
    /**
     * 查询即将到期的维护计划
     * 
     * @param days 提前天数
     * @return 即将到期的维护计划列表
     */
    List<MaintenancePlanVo> selectUpcomingMaintenancePlans(@Param("days") Integer days);
    
    /**
     * 查询已过期的维护计划
     * 
     * @return 已过期的维护计划列表
     */
    List<MaintenancePlanVo> selectOverdueMaintenancePlans();
    
    /**
     * 根据资产ID查询维护计划
     * 
     * @param assetId 资产ID
     * @return 维护计划列表
     */
    List<MaintenancePlanVo> selectMaintenancePlansByAssetId(@Param("assetId") String assetId);
    
    /**
     * 根据负责人查询维护计划
     * 
     * @param responsibleType 负责人类型
     * @param responsibleId 负责人ID
     * @return 维护计划列表
     */
    List<MaintenancePlanVo> selectMaintenancePlansByResponsible(@Param("responsibleType") Integer responsibleType, 
                                                               @Param("responsibleId") Long responsibleId);
} 