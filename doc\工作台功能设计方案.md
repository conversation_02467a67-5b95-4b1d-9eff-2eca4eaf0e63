# 设备监控系统工作台功能设计方案

## 1. 概述

基于对系统现有功能模块的分析，设计一个集成化的工作台主页，为用户提供统一的工作入口和数据概览。工作台将整合设备管理、资产管理、物品管理、维护管理和协作功能等核心业务模块。

## 2. 系统功能模块分析

### 2.1 设备管理模块 (Device)
- **功能范围**: 设备信息管理、设备参数配置、设备图片管理
- **核心接口**: 设备新增、查询、更新、删除、参数管理
- **工作台需求**: 设备总数统计、设备状态分布、最新设备动态

### 2.2 资产管理模块 (Asset)
- **功能范围**: 
  - 资产台账管理 (AssetLedger)
  - 资产入库管理 (AssetInbound) 
  - 资产处置管理 (AssetDisposal)
  - 资产零件关系管理 (AssetPartRelation)
- **核心接口**: 资产CRUD、入库出库、处置流程、关系维护
- **工作台需求**: 资产总值统计、入库出库趋势、待处置资产提醒

### 2.3 物品管理模块 (Item)
- **功能范围**:
  - 物品基础信息管理 (Item)
  - 物品入库管理 (ItemInbound)
  - 物品出库管理 (ItemOutbound)
  - 物品申请管理 (ItemRequisition)
  - 库存管理 (ItemStock)
  - 盘点管理 (ItemStocktaking)
- **核心接口**: 物品CRUD、库存操作、申请审批、盘点流程
- **工作台需求**: 库存预警、申请待审批、盘点计划提醒

### 2.4 维护管理模块 (Maintenance)
- **功能范围**:
  - 维护计划管理 (MaintenancePlan)
  - 维护任务管理 (MaintenanceTask)
  - 维护辅助功能 (MaintenanceHelper)
- **核心接口**: 计划制定、任务分配、执行跟踪、结果记录
- **工作台需求**: 待执行任务、逾期任务提醒、维护完成率统计

### 2.5 协作管理模块 (Collaboration)
- **功能范围**:
  - 在线表格协作 (Spreadsheet)
  - 协作邀请管理
  - 实时编辑功能
- **核心接口**: 表格CRUD、协作邀请、权限管理、实时同步
- **工作台需求**: 活跃协作项目、待处理邀请、在线用户统计

## 3. 工作台功能设计

### 3.1 数据概览区域

#### 3.1.1 核心指标卡片
- **设备管理指标**
  - 设备总数
  - 在线设备数
  - 离线设备数
  - 故障设备数

- **资产管理指标**
  - 资产总值
  - 本月新增资产
  - 待处置资产数
  - 资产利用率

- **物品库存指标**
  - 库存总价值
  - 低库存预警数
  - 待审批申请数
  - 本月出入库次数

- **维护管理指标**
  - 待执行任务数
  - 逾期任务数
  - 本月完成任务数
  - 维护完成率

- **协作活动指标**
  - 活跃协作项目数
  - 在线协作用户数
  - 待处理邀请数
  - 今日协作活动数

#### 3.1.2 趋势图表
- **设备状态趋势图**: 显示设备在线率、故障率的时间趋势
- **资产价值趋势图**: 显示资产总值变化和月度增减
- **库存流转图**: 显示入库、出库、库存变化趋势
- **维护执行率图**: 显示维护任务完成情况和效率趋势

### 3.2 快速操作区域

#### 3.2.1 常用功能快捷入口
- **设备管理**
  - 新增设备
  - 设备查询
  - 设备监控

- **资产管理**
  - 资产登记
  - 资产入库
  - 资产查询

- **物品管理**
  - 物品申请
  - 库存查询
  - 盘点计划

- **维护管理**
  - 创建维护计划
  - 维护任务分配
  - 维护记录查询

- **协作功能**
  - 创建协作表格
  - 加入协作
  - 协作历史

#### 3.2.2 智能推荐操作
基于用户角色和历史操作，推荐最可能需要的操作：
- 管理员：系统配置、用户管理、数据统计
- 设备管理员：设备维护、故障处理、状态监控
- 仓库管理员：库存管理、出入库操作、盘点计划
- 普通用户：物品申请、协作参与、信息查询

### 3.3 任务提醒区域

#### 3.3.1 待办事项列表
- **紧急任务**
  - 设备故障处理
  - 逾期维护任务
  - 库存紧急补充

- **日常任务**
  - 待审批申请
  - 计划内维护
  - 协作邀请处理

- **计划任务**
  - 定期盘点
  - 设备巡检
  - 资产评估

#### 3.3.2 消息通知中心
- **系统通知**: 系统更新、维护公告
- **业务通知**: 审批结果、任务分配、协作邀请
- **预警通知**: 库存预警、设备异常、任务逾期

### 3.4 个人工作区域

#### 3.4.1 我的工作台
- **我的任务**: 分配给当前用户的所有任务
- **我的申请**: 用户提交的各类申请及状态
- **我的协作**: 参与的协作项目和权限
- **我的收藏**: 常用功能和页面收藏

#### 3.4.2 个人效率统计
- **工作量统计**: 本周/本月完成的任务数量
- **效率分析**: 任务完成时间分析
- **协作贡献**: 参与协作的活跃度统计

### 3.5 系统状态区域

#### 3.5.1 系统健康状态
- **服务状态**: 各个微服务模块运行状态
- **数据库状态**: 连接状态和性能指标
- **缓存状态**: Redis等缓存服务状态

#### 3.5.2 用户活动统计
- **在线用户数**: 当前在线用户统计
- **活跃度分析**: 用户操作频率分析
- **功能使用率**: 各功能模块使用情况

## 4. 技术实现建议

### 4.1 前端实现
- **框架**: 基于现有Vue.js框架
- **组件库**: 使用Element Plus等UI组件库
- **图表库**: 使用ECharts实现数据可视化
- **实时更新**: WebSocket实现实时数据推送

### 4.2 后端实现
- **数据聚合**: 创建专门的工作台数据聚合服务
- **缓存策略**: 使用Redis缓存统计数据，提高响应速度
- **定时任务**: 使用Quartz定时更新统计数据
- **权限控制**: 基于RuoYi权限系统控制数据访问

### 4.3 数据接口设计
```java
// 工作台数据聚合接口
@RestController
@RequestMapping("/workbench")
public class WorkbenchController {
    
    // 获取概览数据
    @GetMapping("/overview")
    public AjaxResult getOverviewData();
    
    // 获取趋势数据
    @GetMapping("/trends")
    public AjaxResult getTrendData(@RequestParam String type);
    
    // 获取待办任务
    @GetMapping("/todos")
    public AjaxResult getTodoList();
    
    // 获取个人统计
    @GetMapping("/personal")
    public AjaxResult getPersonalStats();
}
```

## 5. 实施计划

### 5.1 第一阶段：基础框架搭建
- 创建工作台页面结构
- 实现基础数据展示组件
- 建立数据聚合服务

### 5.2 第二阶段：核心功能实现
- 实现各模块数据统计
- 添加图表展示功能
- 完成快捷操作入口

### 5.3 第三阶段：高级功能完善
- 实现实时数据更新
- 添加个性化配置
- 完善权限控制

### 5.4 第四阶段：优化和扩展
- 性能优化
- 用户体验改进
- 功能扩展和定制

## 6. 预期效果

### 6.1 用户体验提升
- 一站式工作入口，减少页面跳转
- 直观的数据展示，快速了解系统状态
- 智能化的任务提醒，提高工作效率

### 6.2 管理效率提升
- 实时监控各模块运行状态
- 快速识别问题和瓶颈
- 数据驱动的决策支持

### 6.3 系统价值提升
- 提高系统整体使用率
- 增强用户粘性
- 为后续功能扩展奠定基础

---

## 7. 资产管理模块工作台实现状态

### 7.1 已完成功能

#### ✅ 后端实现
1. **数据模型层**
   - 创建了 `AssetWorkbenchVo` 及其内部类，定义了完整的工作台数据结构
   - 包含资产概览、趋势数据、入库出库统计、处置统计等数据模型

2. **数据访问层**
   - 在 `AssetBaseInfoMapper` 中添加了资产统计查询方法
   - 在 `AssetInboundMapper` 中添加了入库统计查询方法
   - 在 `AssetDisposalMapper` 中添加了处置统计查询方法
   - 完善了对应的 XML 映射文件，实现了所有统计 SQL 查询

3. **业务服务层**
   - 创建了 `AssetWorkbenchService` 接口
   - 实现了 `AssetWorkbenchServiceImpl` 服务类
   - 提供了资产概览、趋势分析、入库出库统计、处置统计等业务方法

4. **控制器层**
   - 创建了 `AssetWorkbenchController` 控制器
   - 提供了9个RESTful API接口，支持不同粒度的数据查询
   - 包含完整的权限控制和异常处理

5. **测试和文档**
   - 创建了基础的单元测试框架
   - 编写了详细的API接口文档
   - 包含权限配置说明和数据字典

#### 📊 提供的统计数据
- **资产概览**: 总数、总值、月度新增、状态分布、利用率等
- **入库统计**: 月度入库数量、金额、待处理单据等
- **处置统计**: 待审核、处置中、完成情况、类型分布等
- **趋势分析**: 支持自定义时间范围的趋势数据

#### 🔌 API接口清单
1. `GET /asset/workbench/data` - 获取完整工作台数据
2. `GET /asset/workbench/overview` - 获取资产概览
3. `GET /asset/workbench/trends` - 获取趋势数据
4. `GET /asset/workbench/inout-statistics` - 获取入库出库统计
5. `GET /asset/workbench/disposal-statistics` - 获取处置统计
6. `GET /asset/workbench/status-distribution` - 获取状态分布
7. `GET /asset/workbench/value-statistics` - 获取价值统计
8. `GET /asset/workbench/todo-statistics` - 获取待办事项
9. `POST /asset/workbench/refresh` - 刷新缓存数据

### 7.2 技术特点

- **模块化设计**: 按功能模块分离，便于维护和扩展
- **权限控制**: 每个接口都有对应的权限验证
- **异常处理**: 完善的异常捕获和错误信息返回
- **性能考虑**: 支持缓存刷新，为后续性能优化预留接口
- **扩展性**: 支持自定义时间范围查询，便于灵活使用

### 7.3 下一步工作建议

1. **前端集成**: 创建对应的Vue组件来展示这些数据
2. **缓存优化**: 添加Redis缓存来提高查询性能
3. **定时任务**: 创建定时任务来预计算统计数据
4. **权限配置**: 在数据库中添加相应的菜单和权限配置
5. **性能测试**: 在大数据量环境下测试查询性能

---

*本文档基于当前系统功能模块分析制定，资产管理模块的工作台功能已完成后端实现，可直接用于前端集成。*
