package com.jingfang.asset_disposal.module.request;

import com.jingfang.common.request.PageRequest;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 资产处置查询请求
 */
@Data
public class AssetDisposalSearchRequest extends PageRequest {
    
    /**
     * 处置单ID
     */
    private String disposalId;
    
    /**
     * 处置标题（模糊查询）
     */
    private String disposalTitle;
    
    /**
     * 资产ID
     */
    private String assetId;
    
    /**
     * 资产名称（模糊查询）
     */
    private String assetName;
    
    /**
     * 处置类型(1-报废, 2-调拨, 3-出售, 4-其他)
     */
    private Integer disposalType;
    
    /**
     * 申请人ID
     */
    private Long applicantId;
    
    /**
     * 状态列表
     */
    private List<Integer> statusList;
    
    /**
     * 申请时间开始
     */
    private Date applyTimeStart;
    
    /**
     * 申请时间结束
     */
    private Date applyTimeEnd;
    
    /**
     * 预期处置时间开始
     */
    private Date expectedDisposalTimeStart;
    
    /**
     * 预期处置时间结束
     */
    private Date expectedDisposalTimeEnd;
    
    /**
     * 调拨目标部门ID
     */
    private Long transferDeptId;
    
    /**
     * 创建人
     */
    private String createBy;
} 