# 库存盘点功能实现文档

本文档详细描述了库存盘点功能的实现方式，以便于在微信小程序上复现该功能。

## 一、功能概述

库存盘点功能是物资管理系统中的重要功能之一，用于核对实际库存与系统记录的库存数量是否一致，并处理差异。该功能包含完整的业务流程：从创建盘点计划、生成盘点明细、录入盘点结果到最终应用差异调整库存。

### 1.1 业务流程

库存盘点的完整业务流程如下：

1. **创建盘点计划**：用户创建盘点计划，设置盘点名称、盘点类型、仓库等信息
2. **生成盘点明细**：系统根据盘点计划自动生成需要盘点的物品明细列表
3. **开始盘点**：用户开始执行盘点工作，状态从"草稿"变为"进行中"
4. **录入盘点结果**：用户录入每个物品的实际盘点数量和差异原因
5. **完成盘点**：用户完成所有物品的盘点工作，状态从"进行中"变为"已完成"
6. **审核盘点结果**：审核人审核盘点结果，可以通过或退回
7. **应用差异**：审核通过后，系统根据盘点差异自动调整库存数量

### 1.2 状态流转

盘点计划状态流转图：

```
[草稿(0)] ----开始盘点----> [进行中(1)] ----完成盘点----> [已完成(2)]
                                ^                           |
                                |                           v
                                +-------------退回---------- [已审核(3)]
```

**状态说明**：
- 0：草稿 - 新创建的盘点计划，可以编辑和删除
- 1：进行中 - 已开始盘点，可以录入盘点结果
- 2：已完成 - 盘点工作已完成，等待审核
- 3：已审核 - 审核通过，可以应用差异调整库存

## 二、数据结构

### 2.1 数据库表设计

库存盘点功能涉及以下数据库表：

1. **item_stocktaking**：盘点计划主表
2. **item_stocktaking_detail**：盘点明细表

#### 2.1.1 盘点计划主表 (item_stocktaking)

| 字段名 | 类型 | 说明 |
|-------|------|------|
| stocktaking_id | varchar | 盘点单ID（主键） |
| stocktaking_code | varchar | 盘点单号 |
| stocktaking_name | varchar | 盘点名称 |
| stocktaking_type | int | 盘点类型(1-全盘, 2-抽盘, 3-循环盘点) |
| warehouse_id | int | 仓库ID（全盘时可为空） |
| status | int | 状态(0-草稿, 1-进行中, 2-已完成, 3-已审核) |
| plan_start_time | datetime | 计划开始时间 |
| plan_end_time | datetime | 计划结束时间 |
| actual_start_time | datetime | 实际开始时间 |
| actual_end_time | datetime | 实际结束时间 |
| creator_id | bigint | 创建人ID |
| creator_name | varchar | 创建人姓名 |
| auditor_id | bigint | 审核人ID |
| auditor_name | varchar | 审核人姓名 |
| audit_time | datetime | 审核时间 |
| audit_remark | varchar | 审核备注 |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |
| create_by | varchar | 创建人 |
| update_by | varchar | 更新人 |
| remark | varchar | 备注 |

#### 2.1.2 盘点明细表 (item_stocktaking_detail)

| 字段名 | 类型 | 说明 |
|-------|------|------|
| detail_id | varchar | 明细ID（主键） |
| stocktaking_id | varchar | 盘点单ID（外键） |
| item_id | varchar | 物品ID |
| warehouse_id | int | 仓库ID |
| shelf_location | varchar | 货架位置 |
| book_quantity | decimal | 账面数量 |
| actual_quantity | decimal | 实盘数量 |
| difference_quantity | decimal | 差异数量 |
| difference_reason | varchar | 差异原因 |
| checker_id | bigint | 盘点人ID |
| checker_name | varchar | 盘点人姓名 |
| check_time | datetime | 盘点时间 |
| status | int | 状态(0-待盘点, 1-已盘点无差异, 2-已盘点有差异) |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |

### 2.2 数据传输对象

#### 2.2.1 盘点计划DTO (ItemStocktakingDto)

用于前后端交互的数据传输对象：

```json
{
  "stocktakingId": "string",      // 盘点单ID（新增时可为空，系统自动生成）
  "stocktakingName": "string",    // 盘点名称（必填）
  "stocktakingType": 1,           // 盘点类型：1-全盘，2-抽盘，3-循环盘点（必填）
  "warehouseId": 1,               // 仓库ID（全盘时为空）
  "planStartTime": "2024-01-01 09:00:00", // 计划开始时间
  "planEndTime": "2024-01-01 18:00:00",   // 计划结束时间
  "remark": "string"              // 备注
}
```

## 三、接口设计

### 3.1 后端接口

库存盘点功能的后端接口路径为 `/item/stocktaking`，主要包括以下接口：

#### 3.1.1 新增盘点计划

- **接口**: `POST /item/stocktaking`
- **功能**: 创建新的盘点计划
- **权限**: `item:stocktaking:add`
- **参数**: ItemStocktakingDto（盘点计划信息）
- **返回**: 操作结果和盘点单ID

#### 3.1.2 生成盘点明细

- **接口**: `POST /item/stocktaking/{stocktakingId}/generate`
- **功能**: 根据盘点计划生成盘点明细
- **权限**: `item:stocktaking:generate`
- **参数**: stocktakingId（盘点单ID）
- **返回**: 操作结果

#### 3.1.3 开始盘点

- **接口**: `PUT /item/stocktaking/{stocktakingId}/start`
- **功能**: 开始执行盘点工作
- **权限**: `item:stocktaking:start`
- **参数**: stocktakingId（盘点单ID）
- **返回**: 操作结果

#### 3.1.4 录入盘点结果

- **接口**: `POST /item/stocktaking/record`
- **功能**: 录入物品的实际盘点数量
- **权限**: `item:stocktaking:record`
- **参数**: 
  - detailId: 明细ID
  - actualQuantity: 实盘数量
  - differenceReason: 差异原因（可选）
- **返回**: 操作结果

#### 3.1.5 完成盘点

- **接口**: `PUT /item/stocktaking/{stocktakingId}/complete`
- **功能**: 完成盘点工作
- **权限**: `item:stocktaking:complete`
- **参数**: stocktakingId（盘点单ID）
- **返回**: 操作结果

#### 3.1.6 审核盘点结果

- **接口**: `PUT /item/stocktaking/{stocktakingId}/audit`
- **功能**: 审核盘点结果
- **权限**: `item:stocktaking:audit`
- **参数**: 
  - stocktakingId: 盘点单ID
  - approved: 是否通过（true/false）
  - auditRemark: 审核备注（可选）
- **返回**: 操作结果

#### 3.1.7 应用盘点差异

- **接口**: `PUT /item/stocktaking/{stocktakingId}/apply`
- **功能**: 应用盘点差异调整库存
- **权限**: `item:stocktaking:apply`
- **参数**: stocktakingId（盘点单ID）
- **返回**: 操作结果

#### 3.1.8 查询盘点计划列表

- **接口**: `GET /item/stocktaking/list`
- **功能**: 分页查询盘点计划列表
- **权限**: `item:stocktaking:list`
- **参数**: ItemStocktakingSearchRequest（查询条件）
- **返回**: 分页数据

#### 3.1.9 查询盘点计划详情

- **接口**: `GET /item/stocktaking/{stocktakingId}`
- **功能**: 查询盘点计划详情
- **权限**: `item:stocktaking:query`
- **参数**: stocktakingId（盘点单ID）
- **返回**: 盘点计划详情

#### 3.1.10 查询盘点明细列表

- **接口**: `GET /item/stocktaking/{stocktakingId}/details`
- **功能**: 查询盘点明细列表
- **权限**: `item:stocktaking:query`
- **参数**: stocktakingId（盘点单ID）
- **返回**: 盘点明细列表

### 3.2 接口返回格式

所有接口都遵循统一的返回格式：

```json
{
  "code": 200,           // 状态码：200-成功，其他-失败
  "msg": "success",      // 消息
  "data": {}            // 数据（可选）
}
```

## 四、业务逻辑实现

### 4.1 核心业务流程

#### 4.1.1 创建盘点计划

1. **数据校验**：
   - 验证盘点名称是否为空
   - 验证盘点类型是否有效（1-全盘，2-抽盘，3-循环盘点）
   - 验证仓库ID是否有效（抽盘和循环盘点时必填）

2. **生成盘点单ID和单号**：
   - 盘点单ID：使用UUID生成
   - 盘点单号：使用业务代码生成器生成

3. **保存数据**：
   - 保存盘点计划主表信息，状态设为草稿(0)
   - 记录创建人信息和创建时间

#### 4.1.2 生成盘点明细

1. **查询库存数据**：
   - 根据盘点类型查询需要盘点的库存记录
   - 全盘：查询所有仓库的有库存物品
   - 抽盘/循环盘点：查询指定仓库的有库存物品

2. **生成明细记录**：
   - 为每个库存记录创建盘点明细
   - 设置账面数量为当前库存数量
   - 初始状态设为待盘点(0)

#### 4.1.3 状态流转逻辑

1. **开始盘点**：
   - 只有草稿(0)状态可以开始盘点
   - 状态更新为进行中(1)
   - 记录实际开始时间

2. **录入盘点结果**：
   - 只有进行中(1)状态可以录入结果
   - 计算差异数量：实盘数量 - 账面数量
   - 根据差异设置明细状态：无差异(1)或有差异(2)
   - 记录盘点人和盘点时间

3. **完成盘点**：
   - 只有进行中(1)状态可以完成
   - 检查是否所有明细都已盘点
   - 状态更新为已完成(2)
   - 记录实际结束时间

4. **审核盘点结果**：
   - 只有已完成(2)状态可以审核
   - 审核通过：状态更新为已审核(3)
   - 审核不通过：状态退回到进行中(1)
   - 记录审核人信息和审核时间

#### 4.1.4 库存差异应用逻辑

当盘点审核通过后，可以应用差异调整库存：

1. **遍历盘点明细**：
   - 获取每个明细的差异数量
   - 只处理有差异的明细记录

2. **调整库存**：
   - 调用物品服务的库存更新接口
   - 操作类型为盘点(3)
   - 关联单据ID为盘点单ID
   - 变动数量为差异数量

3. **库存状态更新**：
   - 根据安全库存设置更新库存状态
   - 同步更新物品总库存

### 4.2 权限控制

库存盘点功能基于RuoYi框架的权限体系，主要权限包括：

- `item:stocktaking:add`：新增盘点计划
- `item:stocktaking:edit`：修改盘点计划
- `item:stocktaking:generate`：生成盘点明细
- `item:stocktaking:start`：开始盘点
- `item:stocktaking:record`：录入盘点结果
- `item:stocktaking:complete`：完成盘点
- `item:stocktaking:audit`：审核盘点结果
- `item:stocktaking:apply`：应用盘点差异
- `item:stocktaking:list`：查询盘点列表
- `item:stocktaking:query`：查询盘点详情
- `item:stocktaking:remove`：删除盘点计划

### 4.3 数据校验规则

1. **必填字段校验**：
   - 盘点名称、盘点类型为必填
   - 抽盘和循环盘点时仓库ID为必填
   - 录入盘点结果时实盘数量为必填

2. **业务规则校验**：
   - 实盘数量必须为非负数
   - 物品必须存在且未删除
   - 仓库必须存在且有效

3. **状态校验**：
   - 只有特定状态的盘点计划才能进行相应操作
   - 已审核的盘点计划不能修改

### 4.4 盘点类型说明

1. **全盘（1）**：
   - 对所有仓库的所有物品进行盘点
   - 仓库ID可以为空
   - 适用于定期全面盘点

2. **抽盘（2）**：
   - 对指定仓库的物品进行抽样盘点
   - 必须指定仓库ID
   - 适用于重点物品或问题仓库的盘点

3. **循环盘点（3）**：
   - 按计划对不同仓库轮流进行盘点
   - 必须指定仓库ID
   - 适用于日常持续的库存管理

## 五、前端实现

### 5.1 页面结构

前端页面主要包括以下部分：

1. **查询条件区域**：
   - 盘点名称、盘点单号、盘点类型、状态等查询条件

2. **操作按钮区域**：
   - 新增盘点计划等操作按钮

3. **数据表格区域**：
   - 显示盘点计划列表
   - 支持分页、排序、筛选

4. **盘点详情页面**：
   - 显示盘点计划详细信息
   - 盘点明细列表和操作功能

### 5.2 关键功能实现

#### 5.2.1 状态显示

根据盘点计划状态显示不同的标签颜色：

```javascript
const statusMap = {
  0: { label: '草稿', type: 'info' },
  1: { label: '进行中', type: 'warning' },
  2: { label: '已完成', type: 'primary' },
  3: { label: '已审核', type: 'success' }
}
```

#### 5.2.2 操作按钮控制

根据盘点计划状态和用户权限控制操作按钮的显示：

```javascript
// 可编辑状态：草稿
const canEdit = status === 0

// 可生成明细状态：草稿
const canGenerate = status === 0

// 可开始盘点状态：草稿
const canStart = status === 0

// 可录入结果状态：进行中
const canRecord = status === 1

// 可完成盘点状态：进行中
const canComplete = status === 1

// 可审核状态：已完成
const canAudit = status === 2

// 可应用差异状态：已审核
const canApply = status === 3

// 可删除状态：草稿
const canDelete = status === 0
```

#### 5.2.3 盘点类型管理

盘点类型的定义和显示：

```javascript
const stocktakingTypeMap = {
  1: '全盘',
  2: '抽盘',
  3: '循环盘点'
}
```

#### 5.2.4 明细状态管理

盘点明细状态的定义和显示：

```javascript
const detailStatusMap = {
  0: { label: '待盘点', type: 'info' },
  1: { label: '已盘点', type: 'success' },
  2: { label: '有差异', type: 'warning' }
}
```

## 六、微信小程序适配建议

### 6.1 界面设计建议

1. **简化操作流程**：
   - 合并相似功能，减少页面跳转
   - 使用底部弹窗代替新页面
   - 提供快速盘点功能

2. **优化交互体验**：
   - 使用下拉刷新和上拉加载
   - 支持扫码录入盘点结果
   - 提供语音录入功能

3. **适配小屏幕**：
   - 精简表格列，只显示关键信息
   - 使用卡片式布局代替表格
   - 优化表单布局

### 6.2 功能简化建议

1. **状态流转简化**：
   - 可以考虑简化审核流程
   - 提供快速审核功能
   - 支持批量操作

2. **权限简化**：
   - 基于用户角色自动判断权限
   - 减少复杂的权限配置
   - 提供角色模板

3. **数据录入优化**：
   - 支持扫码录入物品信息
   - 提供常用差异原因快速选择
   - 支持拍照记录盘点现场

### 6.3 技术实现建议

1. **接口调用**：
   - 使用wx.request进行接口调用
   - 统一封装请求和响应处理
   - 实现请求重试机制

2. **数据管理**：
   - 使用全局状态管理
   - 实现数据缓存和同步机制
   - 支持离线操作

3. **组件化开发**：
   - 封装通用组件（状态标签、操作按钮等）
   - 提高代码复用性
   - 统一UI风格

### 6.4 特殊功能建议

1. **扫码功能**：
   - 支持扫描物品条码自动填充信息
   - 支持扫描盘点单二维码查看详情

2. **拍照功能**：
   - 支持拍照记录盘点现场
   - 上传图片作为盘点凭证

3. **语音功能**：
   - 支持语音录入盘点数量
   - 语音播报盘点结果

4. **定位功能**：
   - 记录盘点操作的地理位置
   - 用于审计和追溯

## 七、总结

库存盘点功能是一个完整的库存管理系统，包含了状态流转、权限控制、差异处理等多个方面。在微信小程序上实现时，需要根据移动端的特点进行适当的简化和优化，确保用户体验的同时保持业务逻辑的完整性。

关键实现要点：
1. 完整的状态流转机制
2. 严格的权限控制
3. 自动的明细生成
4. 灵活的差异处理
5. 详细的操作记录
6. 支持多种盘点类型
7. 实时库存调整
8. 完善的审核机制
