package com.jingfang.asset_disposal.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 资产处置申请表
 * @TableName asset_disposal
 */
@TableName(value = "asset_disposal")
@Data
public class AssetDisposal implements Serializable {
    
    /**
     * 处置单ID
     */
    @TableId(type = IdType.INPUT)
    private String disposalId;
    
    /**
     * 处置申请标题
     */
    private String disposalTitle;
    
    /**
     * 资产ID
     */
    private String assetId;
    
    /**
     * 处置类型(1-报废, 2-调拨, 3-出售, 4-其他)
     */
    private Integer disposalType;
    
    /**
     * 处置原因
     */
    private String disposalReason;
    
    /**
     * 申请人ID
     */
    private Long applicantId;
    
    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyTime;
    
    /**
     * 预期处置时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expectedDisposalTime;
    
    /**
     * 当前资产价值
     */
    private BigDecimal currentValue;
    
    /**
     * 处置价值
     */
    private BigDecimal disposalValue;
    
    /**
     * 买方信息（出售时使用）
     */
    private String buyerInfo;
    
    /**
     * 调拨目标部门ID（调拨时使用）
     */
    private Long transferDeptId;
    
    /**
     * 状态(1-草稿, 2-待审核, 3-审核中, 4-审核通过, 5-审核拒绝, 6-处置中, 7-已完成, 8-已取消)
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 是否删除(0-否, 1-是)
     */
    private Integer deleted;
    
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
} 