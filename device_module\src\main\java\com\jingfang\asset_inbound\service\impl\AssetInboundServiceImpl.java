package com.jingfang.asset_inbound.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingfang.asset_inbound.mapper.AssetInboundAttachmentMapper;
import com.jingfang.asset_inbound.mapper.AssetInboundDetailMapper;
import com.jingfang.asset_inbound.mapper.AssetInboundMapper;
import com.jingfang.asset_inbound.mapper.AssetInboundOperationLogMapper;
import com.jingfang.asset_inbound.module.dto.AssetInboundDto;
import com.jingfang.asset_inbound.module.entity.AssetInbound;
import com.jingfang.asset_inbound.module.entity.AssetInboundDetail;
import com.jingfang.asset_inbound.module.entity.AssetInboundOperationLog;
import com.jingfang.asset_inbound.module.request.AssetInboundSearchRequest;
import com.jingfang.asset_inbound.module.vo.AssetInboundDetailVo;
import com.jingfang.asset_inbound.module.vo.AssetInboundVo;
import com.jingfang.asset_inbound.service.AssetInboundService;
import com.jingfang.asset_ledger.module.dto.AssetDto;
import com.jingfang.asset_ledger.module.entity.AssetBaseInfo;
import com.jingfang.asset_ledger.module.entity.AssetMaintainInfo;
import com.jingfang.asset_ledger.service.impl.AssetService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 资产入库业务实现类
*/
@Slf4j
@Service
public class AssetInboundServiceImpl extends ServiceImpl<AssetInboundMapper, AssetInbound> implements AssetInboundService {

    @Resource
    private AssetInboundMapper inboundMapper;
    
    @Resource
    private AssetInboundDetailMapper detailMapper;
    
    @Resource
    private AssetInboundAttachmentMapper attachmentMapper;
    
    @Resource
    private AssetInboundOperationLogMapper logMapper;
    
    @Resource
    private AssetService assetService;

    /**
     * 新增资产入库单
     *
     * @param inboundDto 入库单信息
     * @param username 操作用户
     * @return 入库单ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addAssetInbound(AssetInboundDto inboundDto, String username) {
        // 1. 构建并保存入库单主表信息
        AssetInbound inbound = new AssetInbound();
        BeanUtils.copyProperties(inboundDto.getMain(), inbound);
        
        // 设置初始状态为草稿状态(1)
        inbound.setStatus(1);
        inbound.setCreateTime(new Date());
        inbound.setUpdateTime(new Date());
        inbound.setDelFlag("0");

        
        // 保存入库单主表
        inboundMapper.insert(inbound);
        String inboundId = inbound.getInboundId();
        
        // 2. 保存入库单明细
        if (inboundDto.getDetails() != null && !inboundDto.getDetails().isEmpty()) {
            for (AssetInboundDetail detail : inboundDto.getDetails()) {
                // 设置入库单ID
                detail.setInboundId(inboundId);
                detail.setCreateTime(new Date());
                detail.setUpdateTime(new Date());
                
                // 保存明细
                detailMapper.insert(detail);
            }
        }
        
        // 3. 记录操作日志
        AssetInboundOperationLog log = new AssetInboundOperationLog();
        log.setInboundId(inboundId);
        log.setOperationType(1); // 创建操作
        log.setOperationContent("创建入库单");
        log.setOperationTime(new Date());
        log.setOperatorId(inbound.getCreatorId());
        log.setOperatorName(username);
        logMapper.insert(log);
        
        return inboundId;
    }

    /**
     * 获取入库单详情
     *
     * @param inboundId 入库单ID
     * @return 入库单详情
     */
    @Override
    public AssetInboundVo getInboundDetail(String inboundId) {
        // 使用关联查询获取管理人员名称
        AssetInboundVo vo = inboundMapper.selectInboundDetailById(inboundId);
        if (vo == null) {
            throw new RuntimeException("入库单不存在");
        }
        
        // 设置状态名称
        if (vo.getStatus() != null) {
            switch(vo.getStatus()) {
                case 1:
                    vo.setStatusName("草稿");
                    break;
                case 2:
                    vo.setStatusName("待确认");
                    break;
                case 3:
                    vo.setStatusName("待审核");
                    break;
                case 4:
                    vo.setStatusName("已通过");
                    break;
                case 5:
                    vo.setStatusName("已退回");
                    break;
                default:
                    vo.setStatusName("未知");
            }
        }
        
        // 设置存放位置名称
        if (vo.getStorageLocation() != null) {
            // 示例逻辑，实际应该查询字典表
            vo.setStorageLocationName("位置" + vo.getStorageLocation());
        }
        
        // 查询并设置入库明细
        LambdaQueryWrapper<AssetInboundDetail> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(AssetInboundDetail::getInboundId, inboundId);
        List<AssetInboundDetail> details = detailMapper.selectList(detailWrapper);
        
        // 转换明细到DetailVo
        List<AssetInboundDetailVo> detailVos = details.stream().map(detail -> {
            AssetInboundDetailVo detailVo = new AssetInboundDetailVo();
            BeanUtils.copyProperties(detail, detailVo);
            
            // 设置资产状态名称
            if (detail.getAssetStatus() != null) {
                switch(detail.getAssetStatus()) {
                    case 0:
                        detailVo.setAssetStatusName("闲置");
                        break;
                    case 1:
                        detailVo.setAssetStatusName("在用");
                        break;
                    case 2:
                        detailVo.setAssetStatusName("维修");
                        break;
                    case 3:
                        detailVo.setAssetStatusName("报废");
                        break;
                    default:
                        detailVo.setAssetStatusName("未知");
                }
            }
            
            // 设置存放位置名称
            // 实际项目中应该有一个位置字典表来获取名称
            if (detail.getStorageLocation() != null) {
                // 示例逻辑，实际应该查询字典表
                detailVo.setStorageLocationName("位置" + detail.getStorageLocation());
            }
            
            // 设置维保状态名称
            if (detail.getMaintainStatus() != null) {
                switch(detail.getMaintainStatus()) {
                    case 0:
                        detailVo.setMaintainStatusName("未维保");
                        break;
                    case 1:
                        detailVo.setMaintainStatusName("维保中");
                        break;
                    case 2:
                        detailVo.setMaintainStatusName("已过期");
                        break;
                    default:
                        detailVo.setMaintainStatusName("未知");
                }
            }
            
            // 设置维保方式名称
            if (detail.getMaintainMethod() != null) {
                switch(detail.getMaintainMethod()) {
                    case 0:
                        detailVo.setMaintainMethodName("无维保");
                        break;
                    case 1:
                        detailVo.setMaintainMethodName("厂商维保");
                        break;
                    case 2:
                        detailVo.setMaintainMethodName("自维保");
                        break;
                    case 3:
                        detailVo.setMaintainMethodName("第三方维保");
                        break;
                    default:
                        detailVo.setMaintainMethodName("未知");
                }
            }
            
            // 查询分类名称
            // 实际项目中应该查询资产分类表获取名称
            if (detail.getCategoryId() != null) {
                // 示例逻辑，实际应该查询资产分类表
                detailVo.setCategoryName("分类" + detail.getCategoryId());
            }
            
            return detailVo;
        }).collect(Collectors.toList());
        
        vo.setDetails(detailVos);
        
        // 查询并设置操作日志
        LambdaQueryWrapper<AssetInboundOperationLog> logWrapper = new LambdaQueryWrapper<>();
        logWrapper.eq(AssetInboundOperationLog::getInboundId, inboundId)
                 .orderByDesc(AssetInboundOperationLog::getOperationTime);
        List<AssetInboundOperationLog> logs = logMapper.selectList(logWrapper);
        vo.setLogs(logs);
        
        return vo;
    }

    /**
     * 查询入库单列表
     *
     * @param request 查询条件
     * @return 入库单列表
     */
    @Override
    public IPage<AssetInboundVo> selectInboundList(AssetInboundSearchRequest request) {
        // 使用关联查询获取管理人员名称
        Page<AssetInboundVo> page = new Page<>(request.getPageNum(), request.getPageSize());
        return inboundMapper.selectInboundListWithManager(page, request);
    }

    /**
     * 提交入库单待确认
     *
     * @param inboundId 入库单ID
     * @param username 操作用户
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitInbound(String inboundId, String username) {
        // 1. 查询入库单
        AssetInbound inbound = inboundMapper.selectById(inboundId);
        if (inbound == null) {
            throw new RuntimeException("入库单不存在");
        }
        
        // 2. 校验当前状态，只有草稿(1)或退回(5)状态可以提交
        if (inbound.getStatus() != 1 && inbound.getStatus() != 5) {
            throw new RuntimeException("当前状态不允许提交确认");
        }
        
        // 3. 更新状态为待确认(2)
        inbound.setStatus(2);
        inbound.setUpdateTime(new Date());
        inboundMapper.updateById(inbound);
        
        // 4. 记录操作日志
        Long userId = getUserId(username);
        AssetInboundOperationLog log = new AssetInboundOperationLog();
        log.setInboundId(inboundId);
        log.setOperationType(3); // 提交操作
        log.setOperationContent("提交入库单");
        log.setOperationTime(new Date());
        log.setOperatorId(userId);
        log.setOperatorName(username);
        logMapper.insert(log);
    }
    
    /**
     * 经手人确认入库单
     *
     * @param inboundId 入库单ID
     * @param remark 确认意见
     * @param username 操作用户
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleInbound(String inboundId, String remark, String username) {
        // 1. 查询入库单
        AssetInbound inbound = inboundMapper.selectById(inboundId);
        if (inbound == null) {
            throw new RuntimeException("入库单不存在");
        }
        
        // 2. 校验当前状态，只有待确认(2)状态可以确认
        if (inbound.getStatus() != 2) {
            throw new RuntimeException("当前状态不允许经手人确认");
        }
        
        // 3. 获取当前用户ID
        Long userId = getUserId(username);
        
        // 4. 更新状态为待审核(3)，并记录经手人信息
        inbound.setStatus(3);
        inbound.setHandlerId(userId);
        inbound.setHandleTime(new Date());
        inbound.setHandleRemark(remark);
        inbound.setUpdateTime(new Date());
        inboundMapper.updateById(inbound);
        
        // 5. 记录操作日志
        AssetInboundOperationLog log = new AssetInboundOperationLog();
        log.setInboundId(inboundId);
        log.setOperationType(4); // 经手确认操作
        log.setOperationContent("经手人确认：" + (remark != null ? remark : ""));
        log.setOperationTime(new Date());
        log.setOperatorId(userId);
        log.setOperatorName(username);
        logMapper.insert(log);
    }

    /**
     * 获取用户ID
     * 
     * @param username 用户名
     * @return 用户ID
     */
    private Long getUserId(String username) {
        // 这里应该调用系统的用户服务来获取用户ID
        // 为了示例，这里简单返回null，实际实现时应替换为真实的用户查询逻辑
        return null;
    }
    
    /**
     * 审核入库单
     *
     * @param inboundId 入库单ID
     * @param status 审核结果
     * @param remark 审核意见
     * @param username 操作用户
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditInbound(String inboundId, Integer status, String remark, String username) {
        // 1. 查询入库单
        AssetInbound inbound = inboundMapper.selectById(inboundId);
        if (inbound == null) {
            throw new RuntimeException("入库单不存在");
        }
        
        // 2. 校验当前状态，只有待审核(3)状态可以审核
        if (inbound.getStatus() != 3) {
            throw new RuntimeException("当前状态不允许审核");
        }
        
        // 3. 校验审核结果参数
        if (status != 4 && status != 5) {
            throw new RuntimeException("无效的审核结果");
        }
        
        // 4. 获取当前用户ID
        Long userId = getUserId(username);
        
        // 5. 更新状态和审核信息
        inbound.setStatus(status);
        inbound.setAuditorId(userId);
        inbound.setAuditTime(new Date());
        inbound.setAuditRemark(remark);
        inbound.setUpdateTime(new Date());
        inboundMapper.updateById(inbound);
        
        // 6. 记录操作日志
        AssetInboundOperationLog log = new AssetInboundOperationLog();
        log.setInboundId(inboundId);
        log.setOperationType(status == 4 ? 4 : 5); // 4-审核通过，5-审核退回
        log.setOperationContent(status == 4 ? "审核通过：" + remark : "审核退回：" + remark);
        log.setOperationTime(new Date());
        log.setOperatorId(userId);
        log.setOperatorName(username);
        logMapper.insert(log);
        
        // 7. 如果审核通过，则自动登记资产
        if (status == 4) {
            try {
                registerAssets(inboundId);
                
                // 登记资产后，记录操作日志
                AssetInboundOperationLog assetLog = new AssetInboundOperationLog();
                assetLog.setInboundId(inboundId);
                assetLog.setOperationType(6); // 6-资产登记
                assetLog.setOperationContent("根据入库单自动登记资产");
                assetLog.setOperationTime(new Date());
                assetLog.setOperatorId(userId);
                assetLog.setOperatorName(username);
                logMapper.insert(assetLog);
                
                this.log.info("入库单[{}]审核通过，已自动完成资产登记", inboundId);
            } catch (Exception e) {
                this.log.error("入库单[{}]自动登记资产失败: {}", inboundId, e.getMessage(), e);
                throw new RuntimeException("审核通过，但自动登记资产失败: " + e.getMessage());
            }
        }
    }

    /**
     * 根据入库单登记资产
     * 
     * @param inboundId 入库单ID
     */
    private void registerAssets(String inboundId) {
        // 1. 查询入库单明细
        LambdaQueryWrapper<AssetInboundDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AssetInboundDetail::getInboundId, inboundId);
        List<AssetInboundDetail> details = detailMapper.selectList(queryWrapper);
        
        if (details == null || details.isEmpty()) {
            log.warn("入库单[{}]没有明细数据，无法登记资产", inboundId);
            return;
        }
        
        // 2. 遍历明细，进行资产登记
        for (AssetInboundDetail detail : details) {
            // 对每个明细生成一条资产记录
            AssetDto assetDto = createAssetDtoFromDetail(detail);
            
            // 调用资产服务保存资产
            boolean success = assetService.add(assetDto);
            if (!success) {
                throw new RuntimeException("资产[" + detail.getAssetName() + "]登记失败");
            }
            
            this.log.info("根据入库单明细登记资产成功: {}", detail.getAssetName());
        }
    }
    
    /**
     * 将入库明细转换为资产DTO
     * 
     * @param detail 入库明细
     * @return 资产DTO
     */
    private AssetDto createAssetDtoFromDetail(AssetInboundDetail detail) {
        AssetDto assetDto = new AssetDto();
        
        // 创建资产基本信息
        AssetBaseInfo baseInfo = new AssetBaseInfo();
        baseInfo.setAssetName(detail.getAssetName());
        baseInfo.setCategoryId(detail.getCategoryId());
        baseInfo.setAssetStatus(detail.getAssetStatus());
        baseInfo.setSpecModel(detail.getSpecModel());
        baseInfo.setAssetBrand(detail.getAssetBrand());
        baseInfo.setAssetPurpose(detail.getAssetPurpose());
        baseInfo.setAssetUnit(detail.getAssetUnit());
        baseInfo.setStorageLocation(detail.getStorageLocation());
        baseInfo.setDetailLocation(detail.getDetailLocation());
        baseInfo.setRemark(detail.getRemark());
        baseInfo.setCreateTime(new Date());
        baseInfo.setUpdateTime(new Date());
        
        // 将负责人ID添加到managerIds列表中
        if (detail.getManagerId() != null) {
            List<Long> managerIds = new ArrayList<>();
            managerIds.add(detail.getManagerId());
            baseInfo.setManagerIds(managerIds);
        }
        
        // 创建资产维保信息
        AssetMaintainInfo maintainInfo = new AssetMaintainInfo();
        maintainInfo.setMaintainVendor(detail.getMaintainVendor());
        maintainInfo.setStartTime(detail.getStartTime());
        maintainInfo.setEndTime(detail.getEndTime());
        maintainInfo.setMaintainStatus(detail.getMaintainStatus());
        maintainInfo.setContactNumber(detail.getContactNumber());
        maintainInfo.setManagerId(detail.getManagerId());
        maintainInfo.setMaintainMethod(detail.getMaintainMethod());
        maintainInfo.setRemark(detail.getRemark());
        
        assetDto.setBaseInfo(baseInfo);
        assetDto.setMaintainInfo(maintainInfo);
        
        return assetDto;
    }

    /**
     * 删除入库单
     *
     * @param inboundIds 入库单ID数组
     * @param username 操作用户
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteInbound(String[] inboundIds, String username) {
        Long userId = getUserId(username);
        
        for (String inboundId : inboundIds) {
            // 1. 查询入库单
            AssetInbound inbound = inboundMapper.selectById(inboundId);
            if (inbound == null) {
                continue;
            }
            
            // 2. 校验状态（只允许删除草稿(0)和退回(9)状态的入库单）
            if (inbound.getStatus() != 0 && inbound.getStatus() != 9) {
                throw new RuntimeException("入库单[" + inboundId + "]当前状态不允许删除");
            }
            
            // 3. 逻辑删除入库单
            inbound.setDelFlag("1");
            inbound.setUpdateTime(new Date());
            inboundMapper.updateById(inbound);
            
            // 4. 记录操作日志
            AssetInboundOperationLog log = new AssetInboundOperationLog();
            log.setInboundId(inboundId);
            log.setOperationType(9); // 删除操作
            log.setOperationContent("删除入库单");
            log.setOperationTime(new Date());
            log.setOperatorId(userId);
            log.setOperatorName(username);
            logMapper.insert(log);
        }
    }

    /**
     * 编辑资产入库单
     *
     * @param inboundId 入库单ID
     * @param inboundDto 入库单信息
     * @param username 操作用户
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAssetInbound(String inboundId, AssetInboundDto inboundDto, String username) {
        // 1. 查询入库单
        AssetInbound inbound = inboundMapper.selectById(inboundId);
        if (inbound == null) {
            throw new RuntimeException("入库单不存在");
        }
        
        // 2. 校验当前状态（只允许编辑草稿(0)和退回(9)状态的入库单）
        if (inbound.getStatus() != 0 && inbound.getStatus() != 9) {
            throw new RuntimeException("当前状态不允许编辑");
        }
        
        // 3. 获取当前用户ID
        Long userId = getUserId(username);
        
        // 4. 更新入库单主表信息
        AssetInbound newInbound = inboundDto.getMain();
        newInbound.setInboundId(inboundId);
        newInbound.setStatus(inbound.getStatus()); // 保持状态不变
        newInbound.setCreatorId(inbound.getCreatorId()); // 保持制单人不变
        newInbound.setCreateTime(inbound.getCreateTime()); // 保留创建时间
        newInbound.setHandlerId(inbound.getHandlerId()); //
        newInbound.setHandleTime(inbound.getHandleTime());
        newInbound.setHandleRemark(inbound.getHandleRemark());
        newInbound.setAuditorId(inbound.getAuditorId());
        newInbound.setAuditTime(inbound.getAuditTime());
        newInbound.setAuditRemark(inbound.getAuditRemark());
        newInbound.setUpdateTime(new Date());
        newInbound.setDelFlag("0");
        
        inboundMapper.updateById(newInbound);
        
        // 5. 删除原入库单明细
        LambdaQueryWrapper<AssetInboundDetail> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(AssetInboundDetail::getInboundId, inboundId);
        detailMapper.delete(detailWrapper);
        
        // 6. 保存新的入库单明细
        if (inboundDto.getDetails() != null && !inboundDto.getDetails().isEmpty()) {
            for (AssetInboundDetail detail : inboundDto.getDetails()) {
                // 设置入库单ID
                detail.setInboundId(inboundId);
                detail.setCreateTime(new Date());
                detail.setUpdateTime(new Date());
                
                // 保存明细
                detailMapper.insert(detail);
            }
        }
        
        // 7. 记录操作日志
        AssetInboundOperationLog log = new AssetInboundOperationLog();
        log.setInboundId(inboundId);
        log.setOperationType(2); // 编辑操作
        log.setOperationContent("编辑入库单");
        log.setOperationTime(new Date());
        log.setOperatorId(userId);
        log.setOperatorName(username);
        logMapper.insert(log);
    }
}




