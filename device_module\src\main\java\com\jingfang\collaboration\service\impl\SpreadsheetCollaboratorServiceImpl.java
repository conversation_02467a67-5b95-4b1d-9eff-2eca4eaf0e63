package com.jingfang.collaboration.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingfang.collaboration.domain.SpreadsheetCollaborator;
import com.jingfang.collaboration.mapper.SpreadsheetCollaboratorMapper;
import com.jingfang.collaboration.service.SpreadsheetCollaboratorService;
import com.jingfang.collaboration.vo.CollaboratorVo;
import com.jingfang.collaboration.vo.OnlineUserVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 表格协作者服务实现类
 */
@Slf4j
@Service
public class SpreadsheetCollaboratorServiceImpl extends ServiceImpl<SpreadsheetCollaboratorMapper, SpreadsheetCollaborator> 
        implements SpreadsheetCollaboratorService {
    
    @Autowired
    private SpreadsheetCollaboratorMapper collaboratorMapper;
    
    @Override
    public List<CollaboratorVo> getCollaboratorsBySpreadsheetId(String spreadsheetId) {
        return collaboratorMapper.selectCollaboratorsBySpreadsheetId(spreadsheetId);
    }
    
    @Override
    public String getUserPermission(String spreadsheetId, Long userId) {
        return collaboratorMapper.selectUserPermission(spreadsheetId, userId);
    }
    
    @Override
    public List<OnlineUserVo> getOnlineUsers(String spreadsheetId) {
        return collaboratorMapper.selectOnlineUsers(spreadsheetId);
    }
    
    @Override
    public boolean updateUserOnlineStatus(String spreadsheetId, Long userId, String isOnline) {
        LambdaUpdateWrapper<SpreadsheetCollaborator> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(SpreadsheetCollaborator::getSpreadsheetId, spreadsheetId)
               .eq(SpreadsheetCollaborator::getUserId, userId)
               .eq(SpreadsheetCollaborator::getStatus, "1") // 已接受
               .set(SpreadsheetCollaborator::getIsOnline, isOnline)
               .set(SpreadsheetCollaborator::getLastAccessTime, new Date())
               .set(SpreadsheetCollaborator::getUpdateTime, new Date());
        
        return this.update(wrapper);
    }
    
    @Override
    public boolean batchUpdateUserOnlineStatus(List<Long> userIds, String spreadsheetId, String isOnline) {
        return collaboratorMapper.updateUserOnlineStatus(userIds, spreadsheetId, isOnline) > 0;
    }
}
