<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.wh_item.mapper.ItemInboundDetailMapper">

    <resultMap id="BaseResultMap" type="com.jingfang.wh_item.module.entity.ItemInboundDetail">
        <id property="detailId" column="detail_id" jdbcType="BIGINT"/>
        <result property="inboundId" column="inbound_id" jdbcType="VARCHAR"/>
        <result property="itemId" column="item_id" jdbcType="VARCHAR"/>
        <result property="batchNo" column="batch_no" jdbcType="VARCHAR"/>
        <result property="productionDate" column="production_date" jdbcType="DATE"/>
        <result property="expiryDate" column="expiry_date" jdbcType="DATE"/>
        <result property="quantity" column="quantity" jdbcType="DECIMAL"/>
        <result property="unitPrice" column="unit_price" jdbcType="DECIMAL"/>
        <result property="amount" column="amount" jdbcType="DECIMAL"/>
        <result property="warehouseId" column="warehouse_id" jdbcType="INTEGER"/>
        <result property="shelfLocation" column="shelf_location" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        detail_id, inbound_id, item_id,
        batch_no, production_date, expiry_date, quantity, unit_price, amount,
        warehouse_id, shelf_location, remark, create_time, update_time
    </sql>

    <!-- 根据入库单ID查询明细列表，关联物品基础信息 -->
    <select id="selectDetailsByInboundId" resultType="com.jingfang.wh_item.module.vo.ItemInboundDetailVo">
        SELECT
            d.detail_id,
            d.inbound_id,
            d.item_id,
            i.item_name,
            i.item_code,
            i.item_type,
            CASE i.item_type 
                WHEN 1 THEN '消耗品'
                WHEN 2 THEN '备品备件'
                ELSE '未知'
            END as item_type_name,
            i.spec_model,
            i.unit,
            i.image_url,
            d.batch_no,
            d.production_date,
            d.expiry_date,
            d.quantity,
            d.unit_price,
            d.amount,
            d.warehouse_id,
            d.shelf_location,
            d.remark
        FROM item_inbound_detail d
        LEFT JOIN item_base_info i ON d.item_id = i.item_id AND i.deleted = 0
        WHERE d.inbound_id = #{inboundId}
        ORDER BY d.detail_id
    </select>

    <!-- 根据入库单ID删除明细 -->
    <delete id="deleteByInboundId">
        DELETE FROM item_inbound_detail WHERE inbound_id = #{inboundId}
    </delete>

</mapper>