<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.asset_disposal.mapper.AssetDisposalApprovalMapper">

    <resultMap id="BaseResultMap" type="com.jingfang.asset_disposal.module.entity.AssetDisposalApproval">
        <id property="approvalId" column="approval_id" jdbcType="VARCHAR"/>
        <result property="disposalId" column="disposal_id" jdbcType="VARCHAR"/>
        <result property="approvalLevel" column="approval_level" jdbcType="INTEGER"/>
        <result property="approverId" column="approver_id" jdbcType="BIGINT"/>
        <result property="approvalStatus" column="approval_status" jdbcType="INTEGER"/>
        <result property="approvalTime" column="approval_time" jdbcType="TIMESTAMP"/>
        <result property="approvalComment" column="approval_comment" jdbcType="LONGVARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 根据处置单ID查询审批记录 -->
    <select id="selectApprovalsByDisposalId" resultType="com.jingfang.asset_disposal.module.vo.AssetDisposalApprovalVo">
        SELECT 
            a.approval_id,
            a.disposal_id,
            a.approval_level,
            a.approver_id,
            su.nick_name as approver_name,
            a.approval_status,
            CASE a.approval_status
                WHEN 1 THEN '待审批'
                WHEN 2 THEN '通过'
                WHEN 3 THEN '拒绝'
                ELSE '未知'
            END as approval_status_name,
            a.approval_time,
            a.approval_comment,
            a.create_time
        FROM asset_disposal_approval a
        LEFT JOIN sys_user su ON a.approver_id = su.user_id
        WHERE a.disposal_id = #{disposalId}
        ORDER BY a.approval_level, a.create_time
    </select>

    <!-- 批量插入审批记录 -->
    <insert id="batchInsertApprovals">
        INSERT INTO asset_disposal_approval 
        (approval_id, disposal_id, approval_level, approver_id, approval_status, create_time)
        VALUES
        <foreach collection="approvalList" item="approval" separator=",">
            (#{approval.approvalId}, #{approval.disposalId}, #{approval.approvalLevel}, 
             #{approval.approverId}, #{approval.approvalStatus}, #{approval.createTime})
        </foreach>
    </insert>

    <!-- 更新审批状态 -->
    <update id="updateApprovalStatus">
        UPDATE asset_disposal_approval 
        SET approval_status = #{approval.approvalStatus},
            approval_time = #{approval.approvalTime},
            approval_comment = #{approval.approvalComment}
        WHERE disposal_id = #{approval.disposalId} 
            AND approver_id = #{approval.approverId}
            AND approval_status = 1
    </update>

    <!-- 检查是否所有审批都通过 -->
    <select id="countPendingApprovals" resultType="int">
        SELECT COUNT(*)
        FROM asset_disposal_approval
        WHERE disposal_id = #{disposalId} 
            AND approval_status = 1
    </select>

</mapper> 