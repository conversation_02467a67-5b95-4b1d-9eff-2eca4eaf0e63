# 微信小程序库存盘点接口测试脚本
# 使用PowerShell执行API测试

param(
    [string]$BaseUrl = "http://localhost:8080",
    [string]$Token = "your_jwt_token_here"
)

# 全局变量
$Global:TestResults = @()
$Global:Headers = @{
    "Content-Type" = "application/json"
    "Authorization" = "Bearer $Token"
}

# 测试结果记录函数
function Add-TestResult {
    param(
        [string]$TestName,
        [string]$Method,
        [string]$Url,
        [bool]$Success,
        [int]$StatusCode,
        [string]$ResponseBody,
        [string]$ErrorMessage = ""
    )
    
    $Global:TestResults += [PSCustomObject]@{
        TestName = $TestName
        Method = $Method
        Url = $Url
        Success = $Success
        StatusCode = $StatusCode
        ResponseBody = $ResponseBody
        ErrorMessage = $ErrorMessage
        Timestamp = Get-Date
    }
}

# API调用函数
function Invoke-ApiTest {
    param(
        [string]$TestName,
        [string]$Method,
        [string]$Url,
        [hashtable]$Body = $null,
        [int]$ExpectedStatusCode = 200
    )
    
    Write-Host "执行测试: $TestName" -ForegroundColor Yellow
    Write-Host "  方法: $Method" -ForegroundColor Gray
    Write-Host "  URL: $Url" -ForegroundColor Gray
    
    try {
        $params = @{
            Uri = $Url
            Method = $Method
            Headers = $Global:Headers
        }
        
        if ($Body -and $Method -in @("POST", "PUT", "PATCH")) {
            $params.Body = ($Body | ConvertTo-Json -Depth 10)
            Write-Host "  请求体: $($params.Body)" -ForegroundColor Gray
        }
        
        $response = Invoke-RestMethod @params
        $statusCode = 200  # Invoke-RestMethod默认成功状态码
        
        Write-Host "  ✓ 测试通过" -ForegroundColor Green
        Write-Host "  响应: $($response | ConvertTo-Json -Depth 3)" -ForegroundColor Cyan
        
        Add-TestResult -TestName $TestName -Method $Method -Url $Url -Success $true -StatusCode $statusCode -ResponseBody ($response | ConvertTo-Json -Depth 10)
        
        return $response
    }
    catch {
        $statusCode = if ($_.Exception.Response) { $_.Exception.Response.StatusCode.value__ } else { 0 }
        $errorMessage = $_.Exception.Message
        
        Write-Host "  ✗ 测试失败" -ForegroundColor Red
        Write-Host "  错误: $errorMessage" -ForegroundColor Red
        
        Add-TestResult -TestName $TestName -Method $Method -Url $Url -Success $false -StatusCode $statusCode -ResponseBody "" -ErrorMessage $errorMessage
        
        return $null
    }
}

# 开始测试
Write-Host "开始执行微信小程序库存盘点接口测试" -ForegroundColor Magenta
Write-Host "基础URL: $BaseUrl" -ForegroundColor Magenta
Write-Host "认证Token: $Token" -ForegroundColor Magenta
Write-Host "=" * 80

# P0级别接口测试
Write-Host "`n=== P0级别接口测试 ===" -ForegroundColor Blue

# 1. 获取我的盘点任务列表
$response1 = Invoke-ApiTest -TestName "获取我的盘点任务列表" -Method "GET" -Url "$BaseUrl/item/stocktaking/my-tasks"

# 2. 根据物品条码查询物品信息
$response2 = Invoke-ApiTest -TestName "根据物品条码查询物品信息" -Method "GET" -Url "$BaseUrl/item/by-code/TEST001"

# 3. 根据物品信息查找盘点明细
$queryParams = "stocktakingId=test_stocktaking_id&itemId=test_item_id&warehouseId=1"
$response3 = Invoke-ApiTest -TestName "根据物品信息查找盘点明细" -Method "GET" -Url "$BaseUrl/item/stocktaking/detail/by-item?$queryParams"

# 4. 更新盘点明细
$updateBody = @{
    actualQuantity = 95.00
    differenceReason = "部分物品损耗"
    photos = @(
        "http://example.com/upload/2025/01/11/photo1.jpg",
        "http://example.com/upload/2025/01/11/photo2.jpg"
    )
}
$response4 = Invoke-ApiTest -TestName "更新盘点明细" -Method "PUT" -Url "$BaseUrl/item/stocktaking/detail/test_detail_id" -Body $updateBody

# P1级别接口测试
Write-Host "`n=== P1级别接口测试 ===" -ForegroundColor Blue

# 5. 获取盘点进度
$response5 = Invoke-ApiTest -TestName "获取盘点进度" -Method "GET" -Url "$BaseUrl/item/stocktaking/test_stocktaking_id/progress"

# 6. 获取个人盘点进度
$response6 = Invoke-ApiTest -TestName "获取个人盘点进度" -Method "GET" -Url "$BaseUrl/item/stocktaking/my-progress"

# P2级别接口测试
Write-Host "`n=== P2级别接口测试 ===" -ForegroundColor Blue

# 7. 获取个人盘点记录 - 多种时间范围测试
$timeRangeTests = @(
    @{ name = "今日记录"; params = "timeRange=today" },
    @{ name = "本周记录"; params = "timeRange=week" },
    @{ name = "本月记录"; params = "timeRange=month" },
    @{ name = "自定义时间范围"; params = "timeRange=custom&startDate=2025-01-01&endDate=2025-01-11" }
)

foreach ($test in $timeRangeTests) {
    $testName = "获取个人盘点记录 - $($test.name)"
    $url = "$BaseUrl/item/stocktaking/my-records?$($test.params)"
    Invoke-ApiTest -TestName $testName -Method "GET" -Url $url
}

# 8. 获取物品盘点历史
$response8 = Invoke-ApiTest -TestName "获取物品盘点历史" -Method "GET" -Url "$BaseUrl/item/stocktaking/item-history/test_item_id"

# 错误场景测试
Write-Host "`n=== 错误场景测试 ===" -ForegroundColor Blue

# 物品条码不存在
Invoke-ApiTest -TestName "物品条码不存在" -Method "GET" -Url "$BaseUrl/item/by-code/NOTEXIST" -ExpectedStatusCode 500

# 盘点明细不存在
$errorParams = "stocktakingId=not_exist&itemId=not_exist&warehouseId=999"
Invoke-ApiTest -TestName "盘点明细不存在" -Method "GET" -Url "$BaseUrl/item/stocktaking/detail/by-item?$errorParams" -ExpectedStatusCode 500

# 更新不存在的明细
$errorUpdateBody = @{ actualQuantity = 100.00 }
Invoke-ApiTest -TestName "更新不存在的明细" -Method "PUT" -Url "$BaseUrl/item/stocktaking/detail/not_exist_detail" -Body $errorUpdateBody -ExpectedStatusCode 500

# 生成测试报告
Write-Host "`n=== 测试报告 ===" -ForegroundColor Magenta

$totalTests = $Global:TestResults.Count
$passedTests = ($Global:TestResults | Where-Object { $_.Success }).Count
$failedTests = $totalTests - $passedTests

Write-Host "总测试数: $totalTests" -ForegroundColor White
Write-Host "通过测试: $passedTests" -ForegroundColor Green
Write-Host "失败测试: $failedTests" -ForegroundColor Red
Write-Host "通过率: $([math]::Round(($passedTests / $totalTests) * 100, 2))%" -ForegroundColor Yellow

# 详细测试结果
Write-Host "`n=== 详细测试结果 ===" -ForegroundColor Magenta
foreach ($result in $Global:TestResults) {
    $status = if ($result.Success) { "✓" } else { "✗" }
    $color = if ($result.Success) { "Green" } else { "Red" }
    
    Write-Host "$status $($result.TestName)" -ForegroundColor $color
    Write-Host "  方法: $($result.Method) | 状态码: $($result.StatusCode)" -ForegroundColor Gray
    
    if (-not $result.Success) {
        Write-Host "  错误: $($result.ErrorMessage)" -ForegroundColor Red
    }
}

# 数据验证函数
function Test-ResponseData {
    param(
        [string]$TestName,
        [object]$Response,
        [hashtable]$ExpectedFields
    )

    Write-Host "验证响应数据: $TestName" -ForegroundColor Yellow

    if (-not $Response) {
        Write-Host "  ✗ 响应为空" -ForegroundColor Red
        return $false
    }

    $isValid = $true

    # 验证基本结构
    if ($null -eq $Response.code) {
        Write-Host "  ✗ 缺少code字段" -ForegroundColor Red
        $isValid = $false
    }

    if ($null -eq $Response.msg) {
        Write-Host "  ✗ 缺少msg字段" -ForegroundColor Red
        $isValid = $false
    }

    # 验证预期字段
    foreach ($field in $ExpectedFields.Keys) {
        $expectedValue = $ExpectedFields[$field]
        $actualValue = $Response.data.$field

        if ($null -eq $actualValue) {
            Write-Host "  ✗ 缺少字段: $field" -ForegroundColor Red
            $isValid = $false
        } elseif ($null -ne $expectedValue -and $actualValue -ne $expectedValue) {
            Write-Host "  ✗ 字段值不匹配: $field (期望: $expectedValue, 实际: $actualValue)" -ForegroundColor Red
            $isValid = $false
        } else {
            Write-Host "  ✓ 字段验证通过: $field" -ForegroundColor Green
        }
    }

    return $isValid
}

# 测试数据准备函数
function Initialize-TestData {
    Write-Host "`n=== 准备测试数据 ===" -ForegroundColor Blue

    # 创建测试物品
    $testItem = @{
        itemCode = "TEST001"
        itemName = "测试物品A"
        specModel = "规格A"
        unit = "个"
        itemType = 1
        safetyStock = 10.00
    }

    Write-Host "准备创建测试物品: $($testItem.itemCode)" -ForegroundColor Gray

    # 创建盘点计划
    $testStocktaking = @{
        stocktakingName = "测试盘点计划"
        stocktakingType = 1
        warehouseId = 1
        planStartTime = "2025-01-11 09:00:00"
        planEndTime = "2025-01-11 18:00:00"
        remark = "API测试用盘点计划"
    }

    Write-Host "准备创建测试盘点计划: $($testStocktaking.stocktakingName)" -ForegroundColor Gray

    return @{
        TestItem = $testItem
        TestStocktaking = $testStocktaking
    }
}

# 导出测试结果到JSON文件
$reportFile = "stocktaking-api-test-report-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
$Global:TestResults | ConvertTo-Json -Depth 10 | Out-File -FilePath $reportFile -Encoding UTF8
Write-Host "`n测试报告已保存到: $reportFile" -ForegroundColor Cyan

# 生成HTML测试报告
$htmlReport = @"
<!DOCTYPE html>
<html>
<head>
    <title>微信小程序库存盘点接口测试报告</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .summary { margin: 20px 0; }
        .test-result { margin: 10px 0; padding: 10px; border-left: 4px solid #ccc; }
        .success { border-left-color: #4CAF50; background-color: #f9fff9; }
        .failure { border-left-color: #f44336; background-color: #fff9f9; }
        .details { font-size: 12px; color: #666; margin-top: 5px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>微信小程序库存盘点接口测试报告</h1>
        <p>测试时间: $(Get-Date)</p>
        <p>基础URL: $BaseUrl</p>
    </div>

    <div class="summary">
        <h2>测试摘要</h2>
        <p>总测试数: $totalTests</p>
        <p>通过测试: $passedTests</p>
        <p>失败测试: $failedTests</p>
        <p>通过率: $([math]::Round(($passedTests / $totalTests) * 100, 2))%</p>
    </div>

    <h2>详细测试结果</h2>
"@

foreach ($result in $Global:TestResults) {
    $cssClass = if ($result.Success) { "success" } else { "failure" }
    $status = if ($result.Success) { "✓ 通过" } else { "✗ 失败" }

    $htmlReport += @"
    <div class="test-result $cssClass">
        <strong>$($result.TestName)</strong> - $status
        <div class="details">
            方法: $($result.Method) | URL: $($result.Url) | 状态码: $($result.StatusCode)
"@

    if (-not $result.Success) {
        $htmlReport += "<br>错误: $($result.ErrorMessage)"
    }

    $htmlReport += @"
        </div>
    </div>
"@
}

$htmlReport += @"
</body>
</html>
"@

$htmlReportFile = "stocktaking-api-test-report-$(Get-Date -Format 'yyyyMMdd-HHmmss').html"
$htmlReport | Out-File -FilePath $htmlReportFile -Encoding UTF8
Write-Host "HTML测试报告已保存到: $htmlReportFile" -ForegroundColor Cyan

Write-Host "`n测试完成!" -ForegroundColor Magenta
