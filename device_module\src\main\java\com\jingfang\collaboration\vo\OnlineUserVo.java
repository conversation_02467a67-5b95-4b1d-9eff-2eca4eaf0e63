package com.jingfang.collaboration.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 在线用户视图对象
 */
@Data
public class OnlineUserVo {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String userName;
    
    /**
     * 昵称
     */
    private String nickName;
    
    /**
     * 头像
     */
    private String avatar;
    
    /**
     * 部门名称
     */
    private String deptName;
    
    /**
     * 权限类型
     */
    private String permission;
    
    /**
     * 权限名称
     */
    private String permissionName;
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 当前编辑位置
     */
    private String currentPosition;
    
    /**
     * 光标颜色
     */
    private String cursorColor;
    
    /**
     * 上线时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date onlineTime;
    
    /**
     * 最后活动时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastActiveTime;
}
