<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.wh_item_requisition.mapper.ItemRequisitionDetailMapper">

    <!-- 根据领用单ID删除明细 -->
    <delete id="deleteByRequisitionId">
        DELETE FROM item_requisition_detail 
        WHERE requisition_id = #{requisitionId}
    </delete>

    <!-- 根据领用单ID查询明细列表 -->
    <select id="selectByRequisitionId" resultType="com.jingfang.wh_item_requisition.module.entity.ItemRequisitionDetail">
        SELECT 
            detail_id,
            requisition_id,
            item_id,
            warehouse_id,
            shelf_location,
            requisition_quantity,
            approved_quantity,
            actual_quantity,
            remark,
            create_time,
            update_time
        FROM item_requisition_detail 
        WHERE requisition_id = #{requisitionId}
        ORDER BY detail_id
    </select>

    <!-- 根据领用单ID查询明细列表（包含物品详细信息） -->
    <select id="selectDetailWithItemInfo" resultType="com.jingfang.wh_item_requisition.module.vo.ItemRequisitionDetailVo$ItemRequisitionDetailItemVo">
        SELECT 
            d.detail_id,
            d.requisition_id,
            d.item_id,
            i.item_name,
            i.item_code,
            i.spec_model,
            i.unit,
            d.warehouse_id,
            d.shelf_location,
            d.requisition_quantity,
            d.approved_quantity,
            d.actual_quantity,
            d.remark,
            d.create_time,
            d.update_time
        FROM item_requisition_detail d
        LEFT JOIN item_base_info i ON d.item_id = i.item_id
        WHERE d.requisition_id = #{requisitionId}
        ORDER BY d.detail_id
    </select>

</mapper> 