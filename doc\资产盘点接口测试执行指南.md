# 资产盘点接口测试执行指南

## 概述

本指南提供了完整的资产盘点接口测试方案，包括功能测试、性能测试和异常测试。测试脚本支持Windows PowerShell和Linux Bash两种环境。

## 测试文件说明

### 1. 测试脚本文件
- `资产盘点接口测试脚本.md` - 详细的测试用例和curl命令
- `资产盘点接口测试脚本.ps1` - PowerShell自动化测试脚本
- `测试配置.json` - 测试配置和数据文件
- `资产盘点接口测试执行指南.md` - 本执行指南

### 2. 测试覆盖范围
- **盘点计划管理**: 创建、查询、更新、启动、完成计划
- **盘点任务管理**: 创建、分配、执行、完成任务
- **盘点记录管理**: 单个记录、批量记录、扫码盘点
- **盘点差异管理**: 差异识别、处理、统计分析
- **盘点报告生成**: 汇总报告、详细报告、图表数据

## 环境准备

### 1. 系统要求
- Windows 10/11 或 Linux
- PowerShell 5.1+ (Windows) 或 Bash (Linux)
- curl 工具
- jq 工具 (用于JSON处理)

### 2. 服务环境
- 后端服务已启动并可访问
- 数据库连接正常
- 相关权限配置完成

### 3. 测试数据准备
```sql
-- 创建测试用户
INSERT INTO sys_user (user_id, user_name, nick_name, email, phonenumber, sex, avatar, password, status, del_flag, login_ip, login_date, create_by, create_time, update_by, update_time, remark) 
VALUES (999, 'testuser', '测试用户', '<EMAIL>', '13800138000', '0', '', '$2a$10$7JB720yubVSOfvVWbfXCL.VqGOZTH4QLSuDqBOBdHzWLHkQNfcHvS', '0', '0', '', sysdate(), 'admin', sysdate(), '', null, '测试用户');

-- 创建测试部门
INSERT INTO sys_dept (dept_id, parent_id, ancestors, dept_name, order_num, leader, phone, email, status, del_flag, create_by, create_time, update_by, update_time) 
VALUES (999, 100, '0,100', '测试部门', 1, '测试负责人', '13800138000', '<EMAIL>', '0', '0', 'admin', sysdate(), '', null);

-- 创建测试资产
INSERT INTO asset_info (asset_id, asset_code, asset_name, category_id, dept_id, location_id, status, purchase_price, current_value, purchase_date, create_by, create_time) 
VALUES ('TEST001', 'T001', '测试资产1', 1, 999, 1, 1, 5000.00, 4500.00, '2024-01-01', 'admin', sysdate());
```

## 执行步骤

### 1. 配置测试环境

#### 修改配置文件
编辑 `doc/测试配置.json` 文件：
```json
{
  "testConfig": {
    "baseUrl": "http://your-server:8080",  // 修改为实际服务地址
    "token": "your_actual_jwt_token",      // 修改为实际Token
    "timeout": 30000,
    "retryCount": 3
  }
}
```

#### 获取JWT Token
```bash
# 登录获取Token
curl -X POST "http://localhost:8080/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

### 2. 执行PowerShell测试脚本

```powershell
# 进入项目目录
cd "d:\devCodeRepo\20250320134733\device_monitor"

# 修改脚本中的配置
$baseUrl = "http://localhost:8080"
$token = "your_actual_jwt_token"

# 执行测试脚本
.\doc\资产盘点接口测试脚本.ps1
```

### 3. 手动执行测试用例

#### 基础功能测试
```bash
# 1. 测试创建盘点计划
curl -X POST "http://localhost:8080/asset/stocktaking/plan" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "planName": "手动测试计划",
    "planType": 1,
    "startDate": "2025-01-15",
    "endDate": "2025-01-30",
    "responsibleUserId": 1
  }'

# 2. 测试查询计划列表
curl -X POST "http://localhost:8080/asset/stocktaking/plan/list" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "pageNum": 1,
    "pageSize": 10
  }'
```

#### 完整流程测试
按照以下顺序执行：
1. 创建盘点计划
2. 启动计划
3. 创建盘点任务
4. 开始执行任务
5. 创建盘点记录
6. 完成任务
7. 完成计划
8. 生成报告

### 4. 性能测试

#### 并发测试
```powershell
# PowerShell并发测试示例
$jobs = @()
for ($i = 1; $i -le 10; $i++) {
    $jobs += Start-Job -ScriptBlock {
        param($baseUrl, $token, $testId)
        
        $headers = @{
            "Authorization" = "Bearer $token"
            "Content-Type" = "application/json"
        }
        
        $body = @{
            planName = "并发测试计划$testId"
            planType = 1
            startDate = "2025-01-15"
            endDate = "2025-01-30"
            responsibleUserId = 1
        } | ConvertTo-Json
        
        Invoke-RestMethod -Uri "$baseUrl/asset/stocktaking/plan" -Method POST -Headers $headers -Body $body
    } -ArgumentList $baseUrl, $token, $i
}

# 等待所有任务完成
$jobs | Wait-Job | Receive-Job
$jobs | Remove-Job
```

#### 响应时间测试
```bash
# 测试接口响应时间
time curl -X POST "http://localhost:8080/asset/stocktaking/plan/list" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{"pageNum": 1, "pageSize": 100}'
```

## 测试结果验证

### 1. 功能验证检查点
- [ ] 接口返回状态码为200
- [ ] 响应数据结构正确
- [ ] 必填字段验证生效
- [ ] 数据库记录正确创建/更新
- [ ] 业务逻辑执行正确

### 2. 性能验证检查点
- [ ] 接口响应时间 < 2秒
- [ ] 并发处理无异常
- [ ] 内存使用正常
- [ ] 数据库连接池正常

### 3. 异常处理验证
- [ ] 无效参数返回400错误
- [ ] 未授权访问返回401错误
- [ ] 资源不存在返回404错误
- [ ] 服务器错误返回500错误

## 常见问题处理

### 1. 认证问题
**问题**: 401 Unauthorized
**解决**: 
- 检查Token是否有效
- 确认Token格式正确 (Bearer token)
- 重新登录获取新Token

### 2. 权限问题
**问题**: 403 Forbidden
**解决**:
- 检查用户权限配置
- 确认角色分配正确
- 联系管理员添加权限

### 3. 数据问题
**问题**: 404 Not Found
**解决**:
- 检查资源ID是否存在
- 确认数据库数据完整
- 验证请求路径正确

### 4. 性能问题
**问题**: 响应时间过长
**解决**:
- 检查数据库查询性能
- 优化查询条件
- 增加数据库索引
- 检查服务器资源使用

## 测试报告

### 1. 自动生成报告
PowerShell脚本会自动生成以下报告：
- CSV格式测试结果
- 控制台输出摘要
- 错误详情记录

### 2. 手动报告模板
```
测试报告
========
测试时间: 2025-01-15 10:00:00
测试环境: 开发环境
测试人员: [姓名]

测试概况:
- 总测试用例: 50
- 通过用例: 48
- 失败用例: 2
- 成功率: 96%

性能指标:
- 平均响应时间: 350ms
- 最大响应时间: 1200ms
- 并发处理能力: 100 req/s

问题记录:
1. 批量创建记录接口偶现超时
2. 差异统计查询性能待优化

建议:
1. 优化批量处理逻辑
2. 添加数据库索引
3. 增加接口缓存
```

## 持续集成

### 1. 集成到CI/CD流程
```yaml
# GitHub Actions示例
name: API Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: windows-latest
    steps:
    - uses: actions/checkout@v2
    - name: Run API Tests
      run: |
        .\doc\资产盘点接口测试脚本.ps1
```

### 2. 定期执行
建议设置定期执行测试：
- 每日构建后执行基础测试
- 每周执行完整测试套件
- 发版前执行全面测试

## 总结

通过本测试方案，可以全面验证资产盘点接口的功能正确性、性能表现和异常处理能力。建议在开发过程中持续执行测试，确保接口质量和系统稳定性。
