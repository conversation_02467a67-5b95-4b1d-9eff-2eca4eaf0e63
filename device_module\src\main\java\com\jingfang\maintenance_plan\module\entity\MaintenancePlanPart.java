package com.jingfang.maintenance_plan.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 维护计划备品备件关联表
 * @TableName maintenance_plan_part
 */
@TableName(value = "maintenance_plan_part")
@Data
public class MaintenancePlanPart implements Serializable {
    
    /**
     * 关联ID
     */
    @TableId(type = IdType.INPUT)
    private String relationId;
    
    /**
     * 维护计划ID
     */
    private String planId;
    
    /**
     * 备品备件ID
     */
    private String partId;
    
    /**
     * 每次维护所需数量
     */
    private BigDecimal requiredQuantity;
    
    /**
     * 备注说明
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 是否删除(0-否, 1-是)
     */
    private Integer deleted;
    
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
} 