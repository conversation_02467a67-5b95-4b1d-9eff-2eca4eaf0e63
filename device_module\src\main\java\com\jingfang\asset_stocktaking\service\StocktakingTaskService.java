package com.jingfang.asset_stocktaking.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jingfang.asset_stocktaking.module.dto.StocktakingTaskDto;
import com.jingfang.asset_stocktaking.module.entity.AssetStocktakingTask;
import com.jingfang.asset_stocktaking.module.request.TaskSearchRequest;
import com.jingfang.asset_stocktaking.module.vo.StocktakingTaskVo;

import java.util.List;

/**
 * 盘点任务服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface StocktakingTaskService extends IService<AssetStocktakingTask> {

    /**
     * 根据盘点计划自动分发任务
     * 
     * @param planId 计划ID
     * @return 是否成功
     */
    boolean distributeTasksByPlan(String planId);

    /**
     * 手动创建盘点任务
     * 
     * @param taskDto 任务数据
     * @return 是否成功
     */
    boolean createTask(StocktakingTaskDto taskDto);

    /**
     * 编辑盘点任务
     * 
     * @param taskDto 任务数据
     * @return 是否成功
     */
    boolean editTask(StocktakingTaskDto taskDto);

    /**
     * 删除盘点任务
     * 
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean deleteTask(String taskId);

    /**
     * 批量删除盘点任务
     * 
     * @param taskIds 任务ID列表
     * @return 是否成功
     */
    boolean batchDeleteTasks(List<String> taskIds);

    /**
     * 分页查询盘点任务列表
     * 
     * @param request 查询条件
     * @return 分页结果
     */
    IPage<StocktakingTaskVo> selectTaskList(TaskSearchRequest request);

    /**
     * 根据ID查询盘点任务详情
     * 
     * @param taskId 任务ID
     * @return 任务详情
     */
    StocktakingTaskVo selectTaskById(String taskId);

    /**
     * 领取盘点任务
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean claimTask(String taskId, Long userId);

    /**
     * 开始执行任务
     * 
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean startTask(String taskId);

    /**
     * 完成任务
     * 
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean completeTask(String taskId);

    /**
     * 重新分配任务
     * 
     * @param taskId 任务ID
     * @param newAssignedUserId 新分配用户ID
     * @return 是否成功
     */
    boolean reassignTask(String taskId, Long newAssignedUserId);

    /**
     * 计算任务进度
     * 
     * @param taskId 任务ID
     * @return 进度信息
     */
    StocktakingTaskVo.TaskProgress calculateProgress(String taskId);

    /**
     * 查询任务统计信息
     * 
     * @param taskId 任务ID
     * @return 统计信息
     */
    StocktakingTaskVo.TaskStatistics getTaskStatistics(String taskId);

    /**
     * 根据计划ID查询任务列表
     * 
     * @param planId 计划ID
     * @return 任务列表
     */
    List<AssetStocktakingTask> selectTaskByPlanId(String planId);

    /**
     * 查询用户的待执行任务
     * 
     * @param userId 用户ID
     * @return 待执行任务列表
     */
    List<AssetStocktakingTask> selectPendingTasksByUser(Long userId);

    /**
     * 查询用户的进行中任务
     * 
     * @param userId 用户ID
     * @return 进行中任务列表
     */
    List<AssetStocktakingTask> selectInProgressTasksByUser(Long userId);

    /**
     * 查询逾期任务
     * 
     * @return 逾期任务列表
     */
    List<AssetStocktakingTask> selectOverdueTasks();

    /**
     * 统计计划下各状态的任务数量
     * 
     * @param planId 计划ID
     * @return 状态统计
     */
    List<java.util.Map<String, Object>> countTaskByStatusInPlan(String planId);

    /**
     * 查询计划的任务完成情况
     * 
     * @param planId 计划ID
     * @return 完成情况统计
     */
    java.util.Map<String, Object> selectPlanTaskCompletion(String planId);

    /**
     * 批量更新任务状态
     * 
     * @param taskIds 任务ID列表
     * @param status 新状态
     * @return 是否成功
     */
    boolean batchUpdateTaskStatus(List<String> taskIds, Integer status);

    /**
     * 根据资产范围生成任务
     * 
     * @param planId 计划ID
     * @param assetIds 资产ID列表
     * @param distributionConfig 分发配置
     * @return 生成的任务列表
     */
    List<AssetStocktakingTask> generateTasksByAssets(String planId, List<String> assetIds, 
                                                    StocktakingTaskDto.TaskDistributionConfig distributionConfig);

    /**
     * 验证任务数据
     * 
     * @param taskDto 任务数据
     * @return 验证结果
     */
    boolean validateTaskData(StocktakingTaskDto taskDto);

    /**
     * 更新任务进度
     * 
     * @param taskId 任务ID
     * @param actualCount 实际盘点数量
     * @return 是否成功
     */
    boolean updateTaskProgress(String taskId, Integer actualCount);
}
