package com.jingfang.device_module.task;

import com.jingfang.device_module.service.impl.DeviceInfoServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component("DeviceInfoTask")
public class DeviceInfoTask {

    @Resource
    private DeviceInfoServiceImpl deviceInfoService;


    public void refreshDeviceInfo(){
      log.info(deviceInfoService.loadDeviceBaseInfo2Redis());
    }

    public void refreshOnlineDeviceList(){
        log.info(deviceInfoService.DeviceConnectStatusTest());
    }

    public void mqttRefreshOnlineDeviceList(){
        log.info(deviceInfoService.mqttDeviceStatusTest());
    }

    public void refreshCacheAndStatus(){
        log.info(deviceInfoService.refreshCatch());
    }


}
