package com.jingfang.asset_part_relation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.asset_part_relation.module.entity.AssetPartRelation;
import com.jingfang.asset_part_relation.module.request.AssetPartRelationSearchRequest;
import com.jingfang.asset_part_relation.module.vo.AssetPartRelationVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资产备品备件关联Mapper接口
 */
@Mapper
public interface AssetPartRelationMapper extends BaseMapper<AssetPartRelation> {
    
    /**
     * 查询资产关联的备品备件列表
     * 
     * @param page 分页参数
     * @param request 查询条件
     * @return 关联列表
     */
    IPage<AssetPartRelationVo> selectAssetPartRelationList(IPage<AssetPartRelationVo> page, @Param("request") AssetPartRelationSearchRequest request);
    
    /**
     * 根据资产ID查询关联的备品备件列表
     * 
     * @param assetId 资产ID
     * @return 备品备件列表
     */
    List<AssetPartRelationVo> selectPartsByAssetId(@Param("assetId") String assetId);
    
    /**
     * 根据备品备件ID查询关联的资产列表
     * 
     * @param partId 备品备件ID
     * @return 资产列表
     */
    List<AssetPartRelationVo> selectAssetsByPartId(@Param("partId") String partId);
    
    /**
     * 检查资产和备品备件是否已关联
     * 
     * @param assetId 资产ID
     * @param partId 备品备件ID
     * @return 关联记录数
     */
    int checkRelationExists(@Param("assetId") String assetId, @Param("partId") String partId);
    
    /**
     * 批量删除资产的所有备品备件关联
     * 
     * @param assetId 资产ID
     * @param updateBy 更新人
     * @return 影响行数
     */
    int deleteByAssetId(@Param("assetId") String assetId, @Param("updateBy") String updateBy);
    
    /**
     * 批量删除备品备件的所有资产关联
     * 
     * @param partId 备品备件ID
     * @param updateBy 更新人
     * @return 影响行数
     */
    int deleteByPartId(@Param("partId") String partId, @Param("updateBy") String updateBy);
} 