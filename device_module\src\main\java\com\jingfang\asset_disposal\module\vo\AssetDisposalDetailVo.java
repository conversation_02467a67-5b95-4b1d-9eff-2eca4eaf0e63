package com.jingfang.asset_disposal.module.vo;

import com.jingfang.asset_disposal.module.dto.AssetDisposalAttachmentDto;
import com.jingfang.asset_ledger.module.vo.AssetDetailBaseInfoVo;
import lombok.Data;

import java.util.List;

/**
 * 资产处置详情VO
 */
@Data
public class AssetDisposalDetailVo {
    
    /**
     * 处置申请信息
     */
    private AssetDisposalVo disposalInfo;
    
    /**
     * 关联的资产信息
     */
    private AssetDetailBaseInfoVo assetInfo;
    
    /**
     * 审批记录列表
     */
    private List<AssetDisposalApprovalVo> approvalRecords;
    
    /**
     * 执行记录
     */
    private AssetDisposalExecutionVo executionRecord;
    
    /**
     * 附件列表
     */
    private List<AssetDisposalAttachmentDto> attachmentList;
} 