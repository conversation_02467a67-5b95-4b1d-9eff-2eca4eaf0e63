package com.jingfang.device_module.Listener;

import com.jingfang.common.core.redis.RedisCache;
import com.jingfang.device_module.service.impl.DeviceInfoServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DeviceBaseInfoLoader {


    @Resource
    private DeviceInfoServiceImpl deviceInfoService;


    @EventListener(ApplicationReadyEvent.class)
    public void loadDeviceBaseInfo2Redis(){
        log.info(deviceInfoService.loadDeviceBaseInfo2Redis());
        log.info(deviceInfoService.DeviceConnectStatusTest());
    }


}
