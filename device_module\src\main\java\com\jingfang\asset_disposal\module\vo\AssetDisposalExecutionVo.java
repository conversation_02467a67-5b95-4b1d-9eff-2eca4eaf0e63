package com.jingfang.asset_disposal.module.vo;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 资产处置执行VO
 */
@Data
public class AssetDisposalExecutionVo {
    
    /**
     * 执行记录ID
     */
    private String executionId;
    
    /**
     * 处置单ID
     */
    private String disposalId;
    
    /**
     * 执行人ID
     */
    private Long executorId;
    
    /**
     * 执行人姓名
     */
    private String executorName;
    
    /**
     * 执行时间
     */
    private Date executionTime;
    
    /**
     * 实际处置价值
     */
    private BigDecimal actualDisposalValue;
    
    /**
     * 处置凭证
     */
    private String disposalCertificate;
    
    /**
     * 执行说明
     */
    private String executionDescription;
    
    /**
     * 执行状态(1-执行中, 2-已完成, 3-异常)
     */
    private Integer executionStatus;
    
    /**
     * 执行状态名称
     */
    private String executionStatusName;
    
    /**
     * 创建时间
     */
    private Date createTime;
} 