package com.jingfang.asset_disposal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jingfang.asset_disposal.module.entity.AssetDisposalApproval;
import com.jingfang.asset_disposal.module.vo.AssetDisposalApprovalVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资产处置审批Mapper接口
 */
@Mapper
public interface AssetDisposalApprovalMapper extends BaseMapper<AssetDisposalApproval> {
    
    /**
     * 根据处置单ID查询审批记录
     */
    List<AssetDisposalApprovalVo> selectApprovalsByDisposalId(@Param("disposalId") String disposalId);
    
    /**
     * 批量插入审批记录
     */
    int batchInsertApprovals(@Param("approvalList") List<AssetDisposalApproval> approvalList);
    
    /**
     * 更新审批状态
     */
    int updateApprovalStatus(@Param("approval") AssetDisposalApproval approval);
    
    /**
     * 检查是否所有审批都通过
     */
    int countPendingApprovals(@Param("disposalId") String disposalId);
} 