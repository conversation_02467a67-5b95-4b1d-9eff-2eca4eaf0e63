package com.jingfang.wh_item.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 物品基本信息表
 * @TableName item_base_info
 */
@TableName(value ="item_base_info")
@Data
public class ItemBaseInfo implements Serializable {
    /**
     * 物品ID
     */
    @TableId(type = IdType.INPUT)
    @TableField("item_id")
    private String itemId;

    /**
     * 物品名称
     */
    @TableField("item_name")
    private String itemName;

    /**
     * 物品编码(SKU)
     */
    @TableField("item_code")
    private String itemCode;

    /**
     * 物品类别(1-消耗品, 2-备品备件)
     */
    @TableField("item_type")
    private Integer itemType;

    /**
     * 规格型号
     */
    @TableField("spec_model")
    private String specModel;

    /**
     * 单位(个/盒/箱等)
     */
    private String unit;

    /**
     * 企业级安全库存
     */
    @TableField("safety_stock")
    private BigDecimal safetyStock;

    /**
     * 总库存数量（所有仓库库存之和）
     */
    @TableField("total_stock")
    private BigDecimal totalStock;

    /**
     * 图片URL
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否删除(0-否, 1-是)
     */
    private Integer deleted;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
} 