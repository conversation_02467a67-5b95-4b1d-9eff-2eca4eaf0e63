package com.jingfang.framework.manager.factory;

import com.ghgande.j2mod.modbus.facade.ModbusTCPMaster;
import com.jingfang.framework.manager.PooledModbusTCPMaster;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;

public class ModbusConnectionFactory extends BasePooledObjectFactory<ModbusTCPMaster> {

    private String host;

    private int port;

    public ModbusConnectionFactory(String host, int port) {
        this.host = host;
        this.port = port;
    }


    @Override
    public ModbusTCPMaster  create() throws Exception {
        // 创建一个新的 Modbus 连接
        ModbusTCPMaster master = new ModbusTCPMaster(host, port);
        master.connect();
        return master;

    }

    @Override
    public PooledObject<ModbusTCPMaster> wrap(ModbusTCPMaster master) {
        // 使用 PooledModbusTCPMaster 包装
        PooledModbusTCPMaster pooledMaster = new PooledModbusTCPMaster(master);
        return pooledMaster.wrap();  // 返回包装对象
    }

    @Override
    public void destroyObject(PooledObject<ModbusTCPMaster> pooledObject) throws Exception {
        // 连接销毁时关闭连接
        ModbusTCPMaster master = pooledObject.getObject();
        if (master != null) {
            master.disconnect();
        }
    }

    @Override
    public boolean validateObject(PooledObject<ModbusTCPMaster> pooledObject) {
        // 验证连接是否有效，可以通过简单的心跳或是否能够发送请求来检查
        ModbusTCPMaster master = pooledObject.getObject();
        try {
            return master != null && master.isConnected();
        } catch (Exception e) {
            return false;
        }
    }
}
