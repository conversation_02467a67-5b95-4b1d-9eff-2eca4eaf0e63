package com.jingfang.web.controller.item;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.common.annotation.Log;
import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.common.core.page.TableDataInfo;
import com.jingfang.common.enums.BusinessType;
import com.jingfang.wh_item.module.dto.ItemStockAdjustmentDto;
import com.jingfang.wh_item.module.request.ItemStockViewRequest;
import com.jingfang.wh_item.module.vo.ItemStockViewVo;
import com.jingfang.wh_item.service.ItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 物品库存管理Controller
 */
@RestController
@RequestMapping("/item/stock")
public class ItemStockController extends BaseController {
    
    @Autowired
    private ItemService itemService;
    
    /**
     * 库存查看（支持按仓库和按物品两种展示模式）
     */
    @PreAuthorize("@ss.hasPermi('item:stock:view')")
    @GetMapping("/view")
    public AjaxResult viewStock(ItemStockViewRequest request) {
        IPage<ItemStockViewVo> list = itemService.selectItemStockView(request);
        return AjaxResult.success(list);
    }
    
    /**
     * 调整物品库存数量
     */
    @PreAuthorize("@ss.hasPermi('item:stock:adjust')")
    @Log(title = "库存调整", businessType = BusinessType.UPDATE)
    @PutMapping("/adjust")
    public AjaxResult adjustStock(@Validated @RequestBody ItemStockAdjustmentDto adjustmentDto) {
        boolean result = itemService.adjustItemStock(adjustmentDto);
        return result ? AjaxResult.success("库存调整成功") : AjaxResult.error("库存调整失败");
    }
    
    /**
     * 获取物品在指定仓库的当前库存信息
     */
    @PreAuthorize("@ss.hasPermi('item:stock:view')")
    @GetMapping("/current")
    public AjaxResult getCurrentStock(@RequestParam String itemId, @RequestParam Integer warehouseId) {
        ItemStockViewRequest request = new ItemStockViewRequest();
        request.setViewMode(1); // 按仓库展示
        request.setItemId(itemId);
        request.setWarehouseId(warehouseId);
        request.setPageNum(1);
        request.setPageSize(1);
        
        IPage<ItemStockViewVo> result = itemService.selectItemStockView(request);
        if (result.getRecords() != null && !result.getRecords().isEmpty()) {
            return AjaxResult.success(result.getRecords().get(0));
        } else {
            return AjaxResult.error("未找到指定物品在该仓库的库存信息");
        }
    }
} 