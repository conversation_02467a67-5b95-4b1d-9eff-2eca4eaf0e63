# 设备参数检测模块前端开发文档

## 📋 模块概述

设备参数检测模块是一个实时监控工业设备运行状态和参数的系统，支持通过MQTT协议获取设备数据，提供设备管理、参数配置、实时监控等功能。

## 🏗️ 系统架构

```
前端页面 → API接口 → 后端服务 → MQTT网关 → 工业设备
```

## 📁 文件结构

```
device_monitor-ui/src/views/monitor/device/
├── index.vue          # 设备列表页面（卡片式布局）
├── detail.vue         # 设备详情页面（实时数据监控）
└── status.vue         # 设备状态页面（状态监控）

device_monitor-ui/src/api/monitor/
└── device.js          # 设备相关API接口
```

## 🔌 API接口文档

### 1. 设备管理接口

#### 1.1 获取设备列表
```javascript
// API: getDeviceList(query)
// 方法: POST /device/list
// 描述: 分页获取设备列表

// 请求参数
{
  pageNum: 1,        // 页码
  pageSize: 10,      // 每页数量
  deviceName: "",    // 设备名称（可选）
  deviceType: "",    // 设备类型（可选）
  deviceStatus: ""   // 设备状态（可选）
}

// 响应数据
{
  code: 200,
  msg: "查询成功",
  rows: [
    {
      id: 1,
      deviceName: "冷却水循环泵",
      deviceType: 1,
      deviceModel: "CWP-100",
      ipAddress: "*************",
      devicePort: 502,
      deviceStatus: 1,        // 0-离线 1-在线
      location: "车间A",
      manufacturer: "厂商A",
      serialNumber: "SN001",
      installationDate: "2024-01-01",
      createTime: "2024-01-01 10:00:00"
    }
  ],
  total: 50
}
```

#### 1.2 添加设备
```javascript
// API: addDevice(data)
// 方法: POST /device/add
// 描述: 添加新设备

// 请求参数
{
  deviceName: "设备名称",
  deviceType: 1,
  deviceModel: "设备型号",
  ipAddress: "*************",
  devicePort: 502,
  location: "安装位置",
  manufacturer: "生产厂商",
  serialNumber: "序列号",
  installationDate: "2024-01-01"
}

// 响应数据
{
  code: 200,
  msg: "add success"
}
```

#### 1.3 获取设备详情
```javascript
// API: getDeviceDetail(deviceId)
// 方法: GET /device/detail/{deviceId}
// 描述: 获取设备详细信息

// 响应数据
{
  code: 200,
  msg: "查询成功",
  data: {
    id: 1,
    deviceName: "冷却水循环泵",
    deviceType: 1,
    deviceModel: "CWP-100",
    ipAddress: "*************",
    devicePort: 502,
    deviceStatus: 1,
    location: "车间A",
    manufacturer: "厂商A",
    serialNumber: "SN001",
    installationDate: "2024-01-01",
    // ... 其他字段
  }
}
```

### 2. 设备参数接口

#### 2.1 获取设备运行参数
```javascript
// API: getDeviceNormalData(deviceId, query)
// 方法: GET /device/list/detail/normal
// 描述: 获取设备实时运行参数（通过MQTT）

// 请求参数
{
  deviceId: 1,       // 设备ID
  pageNum: 1,        // 页码
  pageSize: 20       // 每页数量
}

// 响应数据
{
  code: 200,
  msg: "查询成功",
  rows: [
    {
      paramId: 1,
      paramName: "冷却水进水温度",
      paramValue: "25.6°C",      // 实时值（已格式化）
      paramUnit: "°C",
      rangeStart: 5.0,           // 正常范围下限
      rangeEnd: 35.0,            // 正常范围上限
      deviceId: 1
    },
    {
      paramId: 2,
      paramName: "冷却水进水流量",
      paramValue: "45L/min",
      paramUnit: "L/min",
      rangeStart: 10.0,
      rangeEnd: 100.0,
      deviceId: 1
    }
  ],
  total: 10
}
```

#### 2.2 获取设备告警参数
```javascript
// API: getDeviceAlertData(deviceId, query)
// 方法: GET /device/list/detail/alert
// 描述: 获取设备告警状态参数

// 请求参数
{
  deviceId: 1,       // 设备ID
  pageNum: 1,        // 页码
  pageSize: 20       // 每页数量
}

// 响应数据
{
  code: 200,
  msg: "查询成功",
  rows: [
    {
      paramId: 10,
      paramName: "高温告警",
      alertValue: false,         // 告警状态 true-告警 false-正常
      paramUnit: "",
      deviceId: 1
    },
    {
      paramId: 11,
      paramName: "低压告警",
      alertValue: true,
      paramUnit: "",
      deviceId: 1
    }
  ],
  total: 5
}
```

### 3. 设备状态接口

#### 3.1 获取设备在线状态
```javascript
// API: getDeviceStatus(deviceId)
// 方法: GET /device/status/{deviceId}
// 描述: 获取设备在线状态

// 响应数据
{
  code: 200,
  msg: "查询成功",
  data: {
    deviceId: 1,
    status: 1,              // 0-离线 1-在线
    lastUpdateTime: "2024-01-01 10:30:00"
  }
}
```

### 4. 测试接口

#### 4.1 测试MQTT连接
```javascript
// API: 直接调用
// 方法: GET /device/property/test/mqtt-connection
// 描述: 测试MQTT连接状态

// 响应数据
{
  code: 200,
  msg: "MQTT属性查询连接状态正常"
}
```

#### 4.2 测试设备参数查询
```javascript
// API: 直接调用
// 方法: GET /device/property/test/normal-params?deviceId=1
// 描述: 测试设备运行参数获取

// 响应数据
{
  code: 200,
  msg: "设备运行参数获取成功",
  data: [/* 参数数组 */]
}
```

## 🎨 页面设计规范

### 1. 设备列表页面 (index.vue)

#### 布局要求
- 采用卡片式布局展示设备
- 每个卡片显示设备基本信息和状态
- 支持搜索、筛选、分页功能
- 提供添加设备按钮

#### 卡片内容
```html
<!-- 设备卡片示例 -->
<el-card class="device-card">
  <div class="device-header">
    <h3>{{ device.deviceName }}</h3>
    <el-tag :type="device.deviceStatus === 1 ? 'success' : 'danger'">
      {{ device.deviceStatus === 1 ? '在线' : '离线' }}
    </el-tag>
  </div>
  <div class="device-info">
    <p>设备型号: {{ device.deviceModel }}</p>
    <p>IP地址: {{ device.ipAddress }}</p>
    <p>安装位置: {{ device.location }}</p>
  </div>
  <div class="device-actions">
    <el-button @click="viewDetail(device.id)">查看详情</el-button>
    <el-button @click="editDevice(device)">编辑</el-button>
  </div>
</el-card>
```

#### 功能要求
- 实时显示设备在线状态
- 点击卡片或"查看详情"进入设备详情页
- 支持设备编辑和删除操作
- 自动刷新设备状态（建议30秒间隔）

### 2. 设备详情页面 (detail.vue)

#### 页面结构
```html
<div class="device-detail">
  <!-- 设备基本信息 -->
  <el-card class="device-info-card">
    <h2>{{ deviceInfo.deviceName }}</h2>
    <el-descriptions :column="3">
      <el-descriptions-item label="设备状态">
        <el-tag :type="deviceInfo.deviceStatus === 1 ? 'success' : 'danger'">
          {{ deviceInfo.deviceStatus === 1 ? '在线' : '离线' }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="IP地址">{{ deviceInfo.ipAddress }}</el-descriptions-item>
      <el-descriptions-item label="设备型号">{{ deviceInfo.deviceModel }}</el-descriptions-item>
      <!-- 更多信息... -->
    </el-descriptions>
  </el-card>

  <!-- 参数监控标签页 -->
  <el-tabs v-model="activeTab" @tab-change="handleTabChange">
    <el-tab-pane label="运行数据" name="operation">
      <device-params-table 
        :data="normalParams" 
        :loading="normalDataLoading"
        type="normal" />
    </el-tab-pane>
    <el-tab-pane label="告警数据" name="alert">
      <device-params-table 
        :data="alertParams" 
        :loading="alertDataLoading"
        type="alert" />
    </el-tab-pane>
  </el-tabs>
</div>
```

#### 功能要求
- **自动刷新**: 设备在线时，运行数据每5秒自动刷新
- **状态指示**: 实时显示设备在线/离线状态
- **参数展示**: 分别展示运行参数和告警参数
- **数值格式化**: 参数值已在后端格式化，直接显示即可
- **范围提示**: 运行参数显示正常范围，超出范围时高亮显示

#### 参数表格组件
```html
<!-- 运行参数表格 -->
<el-table :data="normalParams" v-loading="loading">
  <el-table-column prop="paramName" label="参数名称" />
  <el-table-column prop="paramValue" label="当前值">
    <template #default="{ row }">
      <span :class="getValueClass(row)">{{ row.paramValue }}</span>
    </template>
  </el-table-column>
  <el-table-column label="正常范围">
    <template #default="{ row }">
      {{ row.rangeStart }} - {{ row.rangeEnd }} {{ row.paramUnit }}
    </template>
  </el-table-column>
</el-table>

<!-- 告警参数表格 -->
<el-table :data="alertParams" v-loading="loading">
  <el-table-column prop="paramName" label="告警项目" />
  <el-table-column prop="alertValue" label="状态">
    <template #default="{ row }">
      <el-tag :type="row.alertValue ? 'danger' : 'success'">
        {{ row.alertValue ? '告警' : '正常' }}
      </el-tag>
    </template>
  </el-table-column>
</el-table>
```

### 3. 设备状态页面 (status.vue)

#### 功能要求
- 显示所有设备的在线状态统计
- 提供设备状态历史记录
- 支持批量状态检测

## 💻 前端开发要点

### 1. 数据刷新策略

```javascript
// 自动刷新逻辑
export default {
  data() {
    return {
      refreshTimer: null,
      refreshInterval: 5000, // 5秒刷新间隔
      activeTab: 'operation'
    }
  },
  methods: {
    startAutoRefresh() {
      // 只有在设备在线且当前在运行数据标签页时才启动定时器
      if (parseInt(this.deviceInfo.deviceStatus) === 1 && this.activeTab === 'operation') {
        this.refreshTimer = setInterval(() => {
          if (!this.normalDataLoading && !this.alertLoading) {
            this.getDeviceNormalData();
            this.getDeviceAlertData();
          }
        }, this.refreshInterval);
      }
    },
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer);
        this.refreshTimer = null;
      }
    }
  },
  beforeUnmount() {
    this.stopAutoRefresh();
  }
}
```

### 2. 错误处理

```javascript
// API调用错误处理
async getDeviceNormalData() {
  try {
    this.normalDataLoading = true;
    const response = await getDeviceNormalData(this.deviceId, this.queryParams);
    this.normalParams = response.rows || [];
  } catch (error) {
    console.error('获取设备运行参数失败:', error);
    this.$message.error('获取设备运行参数失败，请检查网络连接');
    // 设备可能离线，停止自动刷新
    this.stopAutoRefresh();
  } finally {
    this.normalDataLoading = false;
  }
}
```

### 3. 参数值状态判断

```javascript
// 判断参数值是否在正常范围内
getValueClass(row) {
  if (!row.rangeStart || !row.rangeEnd) return '';
  
  // 提取数值部分（去除单位）
  const numValue = parseFloat(row.paramValue);
  if (isNaN(numValue)) return '';
  
  if (numValue < row.rangeStart || numValue > row.rangeEnd) {
    return 'param-value-abnormal'; // 异常值样式
  }
  return 'param-value-normal'; // 正常值样式
}
```

### 4. 样式规范

```css
/* 设备卡片样式 */
.device-card {
  margin: 10px;
  cursor: pointer;
  transition: all 0.3s;
}

.device-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 参数值状态样式 */
.param-value-normal {
  color: #67C23A;
  font-weight: bold;
}

.param-value-abnormal {
  color: #F56C6C;
  font-weight: bold;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.5; }
}

/* 设备状态标签 */
.device-status-online {
  background-color: #67C23A;
}

.device-status-offline {
  background-color: #F56C6C;
}
```

## 🔧 开发注意事项

### 1. 数据格式
- 参数值已在后端格式化，包含单位，前端直接显示
- 布尔值显示为"是/否"
- 数值自动保留合适的小数位数

### 2. 性能优化
- 使用防抖处理频繁的API调用
- 页面不可见时停止自动刷新
- 合理使用缓存减少重复请求

### 3. 用户体验
- 加载状态提示
- 网络错误友好提示
- 参数异常值醒目显示
- 响应式布局适配不同屏幕

### 4. 测试建议
- 使用测试接口验证功能
- 模拟设备离线场景
- 测试长时间运行的稳定性

## 🚀 快速开始

### 1. 环境准备
```bash
# 确保已安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 2. 快速测试
```javascript
// 在浏览器控制台测试API
// 1. 测试MQTT连接
fetch('/api/device/property/test/mqtt-connection')
  .then(res => res.json())
  .then(data => console.log('MQTT连接状态:', data));

// 2. 测试设备参数
fetch('/api/device/property/test/normal-params?deviceId=1')
  .then(res => res.json())
  .then(data => console.log('设备参数:', data));
```

### 3. 开发流程
1. 先使用测试接口验证后端功能
2. 开发设备列表页面
3. 开发设备详情页面
4. 集成实时数据刷新
5. 添加错误处理和用户体验优化

## 🐛 常见问题

### Q1: 设备参数不刷新？
**A**: 检查以下几点：
- 设备是否在线（`deviceStatus === 1`）
- MQTT连接是否正常（调用测试接口）
- 是否在正确的标签页（运行数据标签页才自动刷新）
- 网络是否正常

### Q2: 参数值显示异常？
**A**: 参数值已在后端格式化，如果显示异常：
- 检查后端返回的数据格式
- 确认参数名称与MQTT网关配置一致
- 查看浏览器控制台错误信息

### Q3: 页面性能问题？
**A**: 优化建议：
- 减少自动刷新频率
- 页面不可见时停止刷新
- 使用虚拟滚动处理大量数据
- 合理使用缓存

### Q4: 如何添加新的参数类型？
**A**:
1. 在数据库中添加参数配置
2. 确保参数名称与MQTT网关一致
3. 前端会自动显示新参数，无需修改代码

## 📊 数据流程图

```
用户操作 → 前端页面 → API调用 → 后端服务 → MQTT查询 → 网关设备 → 工业设备
    ↓         ↓         ↓         ↓         ↓         ↓         ↓
页面展示 ← 数据渲染 ← 响应数据 ← 数据处理 ← MQTT响应 ← 设备数据 ← 传感器读数
```

## 📞 技术支持

### 联系方式
- 后端开发团队：查看API接口实现
- 系统管理员：MQTT网关配置问题
- 运维团队：网络连接问题

### 调试工具
- **API测试接口**: `/device/property/test/*`
- **系统日志**: 查看MQTT连接和参数查询日志
- **浏览器开发工具**: 网络请求和控制台错误
- **MQTT客户端工具**: 验证MQTT通信

### 文档更新
本文档会根据功能迭代持续更新，请关注版本变更。

---

**文档版本**: v1.0
**更新时间**: 2025-01-05
**适用版本**: 设备监控系统 v2.0+
**MQTT协议版本**: v3.1.1
