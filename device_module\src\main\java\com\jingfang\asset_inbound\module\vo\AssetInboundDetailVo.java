package com.jingfang.asset_inbound.module.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 资产入库明细视图对象
 * 用于返回入库单详情
 */
@Data
public class AssetInboundDetailVo {
    
    /**
     * 明细ID
     */
    private Long detailId;
    
    /**
     * 入库单ID
     */
    private String inboundId;
    
    /**
     * 资产编号
     */
    private String assetId;
    
    /**
     * 资产名称
     */
    private String assetName;
    
    /**
     * 资产分类ID
     */
    private Integer categoryId;
    
    /**
     * 资产分类名称
     */
    private String categoryName;
    
    /**
     * 规格型号
     */
    private String specModel;
    
    /**
     * 品牌信息
     */
    private String assetBrand;
    
    /**
     * 资产状态
     */
    private Integer assetStatus;
    
    /**
     * 资产状态名称
     */
    private String assetStatusName;
    
    /**
     * 资产用途
     */
    private String assetPurpose;
    
    /**
     * 计量单位
     */
    private String assetUnit;
    
    /**
     * 存放位置
     */
    private Integer storageLocation;
    
    /**
     * 存放位置名称
     */
    private String storageLocationName;
    
    /**
     * 详细地址
     */
    private String detailLocation;
    
    /**
     * 数量
     */
    private Integer quantity;
    
    /**
     * 单价
     */
    private BigDecimal unitPrice;
    
    /**
     * 金额
     */
    private BigDecimal amount;
    
    /**
     * 维保厂商
     */
    private String maintainVendor;
    
    /**
     * 维保开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;
    
    /**
     * 维保到期日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;
    
    /**
     * 维保状态
     */
    private Integer maintainStatus;
    
    /**
     * 维保状态名称
     */
    private String maintainStatusName;
    
    /**
     * 联系方式
     */
    private String contactNumber;
    
    /**
     * 负责人ID
     */
    private Long managerId;
    
    /**
     * 负责人姓名
     */
    private String managerName;
    
    /**
     * 维保方式
     */
    private Integer maintainMethod;
    
    /**
     * 维保方式名称
     */
    private String maintainMethodName;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
} 