package com.jingfang.device_module.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingfang.common.core.redis.RedisCache;
import com.jingfang.device_module.module.dto.DeviceInfoDto;
import com.jingfang.device_module.module.entity.DeviceInfo;
import com.jingfang.device_module.module.vo.DeviceBaseInfoVo;
import com.jingfang.device_module.module.vo.DeviceInfoVo;
import com.jingfang.device_module.request.DeviceInfoSearchRequest;
import com.jingfang.device_module.service.DeviceInfoService;
import com.jingfang.device_module.mapper.DeviceInfoMapper;
import com.jingfang.device_module.mqtt.service.MqttDeviceStatusService;
import com.jingfang.device_module.mqtt.dto.DeviceStatusResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @description 针对表【device_info】的数据库操作Service实现
 * @createDate 2025-03-20 15:41:39
 */
@Slf4j
@Service
public class DeviceInfoServiceImpl extends ServiceImpl<DeviceInfoMapper, DeviceInfo>
        implements DeviceInfoService {

    @Resource
    private RedisCache redisCache;

    @Resource
    private DeviceInfoMapper deviceMapper;

    @Resource
    private MqttDeviceStatusService mqttDeviceStatusService;

    @Override
    public String refreshCatch() {
        log.info(this.loadDeviceBaseInfo2Redis());
        log.info(this.DeviceConnectStatusTest());
        return "";
    }

    @Override
    public String redisTest() {
        Set<Long> deviceIdSet = new HashSet<>();
        deviceIdSet.add(1L);
        deviceIdSet.add(2L);
        deviceIdSet.add(3L);
        redisCache.setCacheSet("online device", deviceIdSet);
        Set<Long> ids = redisCache.getCacheSet("online device");
        return ids.toString();
    }

    @Async("threadPoolTaskExecutor")
    public boolean isDeviceOnline(String ipAddress, int port) {
        return isDeviceOnline(ipAddress, port, 3000);
    }


    @Override
    public String DeviceConnectStatusTest() {
        // 首先尝试使用MQTT检测
        try {
            return mqttDeviceStatusTest();
        } catch (Exception e) {
            log.warn("MQTT设备状态检测失败，回退到Socket检测: {}", e.getMessage());
            return socketDeviceStatusTest();
        }
    }

    @Override
    public String mqttDeviceStatusTest() {
        log.info("开始测试MQTT设备状态查询");
        try {
            CompletableFuture<DeviceStatusResponse> future = mqttDeviceStatusService.queryDeviceStatus();
            DeviceStatusResponse response = future.get(15, java.util.concurrent.TimeUnit.SECONDS);

            if (response != null && response.getCode() == 0 && response.getParams() != null) {
                // 清除旧的在线设备缓存
                redisCache.deleteObject("online device");

                // 获取设备信息映射
                Map<String, DeviceInfo> deviceMap = redisCache.getCacheMap("smart device");
                Set<Long> onlineDeviceIds = new HashSet<>();
                List<String> onlineDeviceIPs = new ArrayList<>();

                // 处理MQTT响应中的设备状态
                for (DeviceStatusResponse.DeviceStatusParam param : response.getParams()) {
                    if ("onLine".equals(param.getCommStatus())) {
                        String deviceIP = param.getClientID();
                        onlineDeviceIPs.add(deviceIP);

                        // 根据IP地址查找对应的设备ID
                        for (DeviceInfo device : deviceMap.values()) {
                            if (deviceIP.equals(device.getIpAddress())) {
                                onlineDeviceIds.add(device.getId());
                                break;
                            }
                        }
                    }
                }

                // 更新Redis缓存
                if (!onlineDeviceIds.isEmpty()) {
                    redisCache.setCacheSet("online device", onlineDeviceIds);
                }

                String result = "MQTT设备状态查询完成，在线设备IP: " + String.join(", ", onlineDeviceIPs);
                log.info(result);
                return result;
            } else {
                throw new RuntimeException("MQTT响应无效或返回错误代码: " + (response != null ? response.getCode() : "null"));
            }
        } catch (Exception e) {
            log.error("MQTT设备状态查询失败", e);
            throw new RuntimeException("MQTT设备状态查询失败: " + e.getMessage());
        }
    }

    /**
     * Socket方式检测设备状态（备用方案）
     */
    private String socketDeviceStatusTest() {
        StringBuffer buffer = new StringBuffer("当前在线设备(Socket检测):");
        //从redis中获取智能设备信息
        Map<String, DeviceInfo> maps = redisCache.getCacheMap("smart device");
        List<DeviceInfo> infos = new ArrayList<>();
        maps.values().stream().forEach(item -> {
            infos.add(item);
        });
        redisCache.deleteObject("online device");
        Set<Long> ids = new HashSet<>();
        infos.forEach(item -> {
            boolean isOnline = isDeviceOnline(item.getIpAddress(), item.getDevicePort());
            if (isOnline) {
                ids.add(item.getId());
                buffer.append(" ").append(item.getDeviceName()).append(" ID:").append(item.getId()).append(" IP:").append(item.getIpAddress()).append(" Port:").append(item.getDevicePort());
            }
        });
        if (ids.size() > 0) {
            redisCache.setCacheSet("online device", ids);
        }
        return buffer.toString();
    }

    public static boolean isDeviceOnline(String ip, int port, int timeout) {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(ip, port), timeout);
            return true; // 连接成功，设备在线
        } catch (Exception e) {
            return false; // 连接失败，设备离线或端口不可达
        }
    }


    @Override
    public boolean adeNewDevice(DeviceInfoDto dto) {
        DeviceInfo deviceInfo = new DeviceInfo();
        BeanUtils.copyProperties(dto, deviceInfo);
        deviceInfo.setCreatedAt(new Date());
        return this.save(deviceInfo);
    }

    @Override
    public int addPictureUrl(Long deviceId, String pictureUrl) {
        return deviceMapper.addPictureUrl(deviceId,pictureUrl);
    }

    @Override
    public int deletePictureUrl(Long deviceId, String pictureUrl) {
        return deviceMapper.deletePictureUrl(deviceId,pictureUrl);
    }

    @Override
    public Page<DeviceInfoVo> showDeviceInfoList(DeviceInfoSearchRequest request) {
        try {
            Page<DeviceInfo> rowPage = new Page<>(request.getPageNum(), request.getPageSize());
            LambdaQueryWrapper<DeviceInfo> queryWrapper = new LambdaQueryWrapper<>();
            Set<Long> ids = redisCache.getCacheSet("online device");
            if (request.getDeviceName() != null) {
                queryWrapper.like(DeviceInfo::getDeviceName, request.getDeviceName());
            }
            if (request.getDeviceModel() != null) {
                queryWrapper.eq(DeviceInfo::getDeviceModel, request.getDeviceModel());
            }
            if ((request.getDeviceStatus() != null)) {
                //查找在线设备
                if (request.getDeviceStatus() == 1) {
                    queryWrapper.in(DeviceInfo::getId, ids);
                }
                //查找离线设备
                if (request.getDeviceStatus() == 0) {
                    queryWrapper.notIn(DeviceInfo::getId, ids);
                }
            }
            if (request.getLocation() != null) {
                queryWrapper.like(DeviceInfo::getLocation, request.getLocation());
            }
            rowPage = this.page(rowPage, queryWrapper);
            Page<DeviceInfoVo> pageVo = new Page<>();
            pageVo.setCurrent(rowPage.getCurrent());
            pageVo.setSize(rowPage.getSize());
            pageVo.setTotal(rowPage.getTotal());
            List<DeviceInfo> infos = rowPage.getRecords();
            List<DeviceInfoVo> vos = new ArrayList<>();
            infos.forEach(item -> {
                DeviceInfoVo vo = new DeviceInfoVo();
                BeanUtils.copyProperties(item, vo);
                if (ids.contains(item.getId())) {
                    vo.setDeviceStatus(1);
                } else {
                    vo.setDeviceStatus(0);
                }
                //todo:运行状态runningState（运行异常）还没做，记得做完加上
                vo.setRunningStatus(1);
                vos.add(vo);
            });
            pageVo.setRecords(vos);
            return pageVo;
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    public String loadDeviceBaseInfo2Redis() {
        LambdaQueryWrapper<DeviceInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceInfo::getDeviceType, 1);
        List<DeviceInfo> infos = this.baseMapper.selectList(queryWrapper);
        StringBuffer buffer = new StringBuffer("智能设备缓存已更新 智能设备：");
        //先清除redis中保存的旧set
        redisCache.deleteObject("smart device");
        Map<String, DeviceInfo> infoMap = new HashMap<>();
        infos.forEach(item -> {
            infoMap.put(String.valueOf(item.getId()), item);
            buffer.append(" ").append(item.getDeviceName()).append(" ").append(" ip: ").append(item.getIpAddress()).append(" port: ").append(item.getDevicePort()).append(" ");
        });
        redisCache.setCacheMap("smart device", infoMap);
        return buffer.toString();
    }

    @Override
    public DeviceBaseInfoVo showDeviceBaseInfo(Long deviceId) {
        try {
            DeviceInfo deviceInfo = this.baseMapper.selectById(deviceId);
            DeviceBaseInfoVo vo = new DeviceBaseInfoVo();
            BeanUtils.copyProperties(deviceInfo, vo);
            List<String> urls = deviceMapper.getDevicePictureUrls(deviceId);
            vo.setPictureUrls(urls);
            Set<Long> ids = redisCache.getCacheSet("online device");
            if (ids.contains(deviceId)){
                vo.setDeviceStatus(1);
            }else {
                vo.setDeviceStatus(0);
            }
            return vo;
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }
}




