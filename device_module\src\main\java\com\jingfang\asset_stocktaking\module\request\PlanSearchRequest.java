package com.jingfang.asset_stocktaking.module.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 盘点计划查询请求对象
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
public class PlanSearchRequest implements Serializable {

    /**
     * 盘点计划名称（模糊查询）
     */
    private String planName;

    /**
     * 盘点类型：1-全盘，2-部分盘点
     */
    private Integer planType;

    /**
     * 负责人ID
     */
    private Long responsibleUserId;

    /**
     * 状态：1-草稿，2-待审批，3-执行中，4-已完成，5-已取消
     */
    private Integer status;

    /**
     * 状态列表（多选查询）
     */
    private List<Integer> statusList;

    /**
     * 开始日期范围-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDateBegin;

    /**
     * 开始日期范围-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDateEnd;

    /**
     * 结束日期范围-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDateBegin;

    /**
     * 结束日期范围-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDateEnd;

    /**
     * 创建时间范围-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimeBegin;

    /**
     * 创建时间范围-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimeEnd;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 部门ID（数据权限过滤）
     */
    private Long deptId;

    /**
     * 是否包含子部门
     */
    private Boolean includeSubDept;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序方向：asc-升序，desc-降序
     */
    private String orderDirection;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 是否查询统计信息
     */
    private Boolean includeStatistics;

    /**
     * 关键词搜索（计划名称、负责人、备注）
     */
    private String keyword;

    private static final long serialVersionUID = 1L;
}
