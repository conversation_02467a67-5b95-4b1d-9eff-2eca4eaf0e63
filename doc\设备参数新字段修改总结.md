# 设备参数新字段修改总结

## 修改概述

根据您的需求，在 `device_param` 表中新增了两个字段：
- `rw_type`：读写类型（0-只读，1-只写，2-读写）
- `param_type`：参数类型（0-一般参数，1-告警参数）

## 修改文件清单

### 1. 数据库相关

**新增文件**：
- `sql/device_param_add_fields.sql` - 添加新字段的SQL脚本
- `sql/device_param_examples_with_new_fields.sql` - 包含新字段的示例数据

**修改文件**：
- `device_module/src/main/resources/mapper/DeviceParamMapper.xml` - 更新字段映射

### 2. Java代码修改

**实体类**：
- `device_module/src/main/java/com/jingfang/device_module/module/entity/DeviceParam.java`
  - 新增 `rwType` 和 `paramType` 字段
  - 更新 `equals()`、`hashCode()` 和 `toString()` 方法

**DTO类**：
- `device_module/src/main/java/com/jingfang/device_module/module/dto/DeviceAddDto.java`
  - 新增 `rwType` 和 `paramType` 字段

**VO类**：
- `device_module/src/main/java/com/jingfang/device_module/module/vo/DeviceParamVo.java`
  - 新增 `rwType` 和 `paramType` 字段

**Service层**：
- `device_module/src/main/java/com/jingfang/device_module/service/impl/DeviceParamServiceImpl.java`
  - 优化运行参数查询：只查询 `param_type = 0` 的参数
  - 优化告警参数查询：只查询 `param_type = 1` 的参数

### 3. 文档更新

**新增文档**：
- `doc/设备参数新字段使用说明.md` - 详细的使用说明文档
- `doc/设备参数新字段修改总结.md` - 本文档

**更新文档**：
- `doc/MQTT设备参数采集使用指南.md` - 更新字段说明表格

## 主要改进

### 1. 精确的参数分类

**之前**：通过参数名称关键字（"告警"、"报警"等）来区分告警参数
**现在**：通过 `param_type` 字段精确区分一般参数和告警参数

### 2. 读写权限控制

**新增功能**：通过 `rw_type` 字段明确定义参数的读写权限
- 只读参数：传感器数据、状态监控
- 只写参数：控制指令、操作命令
- 读写参数：设定值、配置参数

### 3. 查询性能优化

- 为新字段添加了数据库索引
- 查询逻辑更加精确，减少不必要的数据处理

## 数据库变更

### 新增字段

```sql
-- 读写类型字段
ALTER TABLE device_param 
ADD COLUMN `rw_type` tinyint(1) DEFAULT 0 COMMENT '读写类型 0-只读 1-只写 2-读写';

-- 参数类型字段
ALTER TABLE device_param 
ADD COLUMN `param_type` tinyint(1) DEFAULT 0 COMMENT '参数类型 0-一般参数 1-告警参数';
```

### 新增索引

```sql
ALTER TABLE device_param ADD INDEX `idx_param_type` (`param_type`);
ALTER TABLE device_param ADD INDEX `idx_rw_type` (`rw_type`);
```

## API接口变化

### 1. 获取运行参数接口

**接口**：`GET /device/list/detail/normal`

**变化**：
- 查询条件增加：`param_type = 0`
- 响应数据增加：`rwType` 和 `paramType` 字段

### 2. 获取告警参数接口

**接口**：`GET /device/list/detail/alert`

**变化**：
- 查询条件从关键字匹配改为：`param_type = 1`
- 响应数据增加：`rwType` 和 `paramType` 字段

## 部署步骤

### 1. 数据库更新

```bash
# 执行字段添加脚本
mysql -u username -p database_name < sql/device_param_add_fields.sql

# 可选：导入示例数据
mysql -u username -p database_name < sql/device_param_examples_with_new_fields.sql
```

### 2. 应用部署

- 重新编译并部署应用
- 新代码向后兼容，不会影响现有功能

### 3. 数据迁移

现有数据会自动设置默认值：
- `rw_type = 0`（只读）
- `param_type = 0`（一般参数）

根据实际需求手动调整特定参数的类型。

## 使用示例

### 1. 配置一般参数（温度传感器）

```sql
INSERT INTO device_param (
    device_id, param_name, param_unit, 
    range_start, range_end, 
    rw_type, param_type, del_flag
) VALUES (
    1, '环境温度', '°C', 
    -10.0, 50.0, 
    0, 0, 0  -- 只读，一般参数
);
```

### 2. 配置告警参数（高温告警）

```sql
INSERT INTO device_param (
    device_id, param_name, param_unit, 
    range_start, range_end, 
    rw_type, param_type, del_flag
) VALUES (
    1, '高温告警', '', 
    NULL, NULL, 
    0, 1, 0  -- 只读，告警参数
);
```

### 3. 配置读写参数（温度设定值）

```sql
INSERT INTO device_param (
    device_id, param_name, param_unit, 
    range_start, range_end, 
    rw_type, param_type, del_flag
) VALUES (
    1, '温度设定值', '°C', 
    10.0, 40.0, 
    2, 0, 0  -- 读写，一般参数
);
```

## 注意事项

1. **向后兼容**：现有代码在添加字段后仍能正常工作
2. **数据一致性**：确保新字段的值与参数的实际用途一致
3. **权限控制**：可以根据 `rw_type` 字段实现更精细的权限控制
4. **性能优化**：新字段已添加索引，查询性能得到提升
5. **业务逻辑**：前端界面需要相应调整以支持新字段的配置

## 后续扩展

1. **权限控制**：基于 `rw_type` 实现参数读写权限控制
2. **参数分组**：基于 `param_type` 实现参数分组显示
3. **告警监控**：专门针对告警参数实现监控逻辑
4. **参数写入**：为可写参数实现MQTT写入功能

通过这些修改，设备参数管理将更加精确和灵活，为后续功能扩展奠定了良好的基础！
