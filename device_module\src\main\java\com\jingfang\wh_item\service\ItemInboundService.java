package com.jingfang.wh_item.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.wh_item.module.dto.ItemInboundDto;
import com.jingfang.wh_item.module.request.ItemInboundSearchRequest;
import com.jingfang.wh_item.module.vo.ItemInboundVo;

public interface ItemInboundService {

    /**
     * 新增物品入库单
     */
    String addItemInbound(ItemInboundDto inboundDto, String username);

    /**
     * 编辑物品入库单
     */
    void updateItemInbound(String inboundId, ItemInboundDto inboundDto, String username);

    /**
     * 获取入库单详情
     */
    ItemInboundVo getInboundDetail(String inboundId);

    /**
     * 查询入库单列表
     */
    IPage<ItemInboundVo> selectInboundList(ItemInboundSearchRequest request);

    /**
     * 提交入库单待确认
     */
    void submitInbound(String inboundId, String username);

    /**
     * 经手人确认入库单
     */
    void handleInbound(String inboundId, String remark, String username);

    /**
     * 审核入库单
     */
    void auditInbound(String inboundId, Integer status, String remark, String username);

    /**
     * 删除入库单
     */
    void deleteInbound(String[] inboundIds, String username);
}