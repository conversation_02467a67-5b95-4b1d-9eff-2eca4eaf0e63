package com.jingfang.wh_item.module.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 库存盘点VO
 */
@Data
public class ItemStocktakingVo implements Serializable {
    
    /**
     * 盘点单ID
     */
    private String stocktakingId;
    
    /**
     * 盘点单号
     */
    private String stocktakingCode;
    
    /**
     * 盘点名称
     */
    private String stocktakingName;
    
    /**
     * 盘点类型(1-全盘，2-抽盘，3-循环盘点)
     */
    private Integer stocktakingType;
    
    /**
     * 盘点类型名称
     */
    private String stocktakingTypeName;
    
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    
    /**
     * 仓库名称
     */
    private String warehouseName;
    
    /**
     * 状态(0-草稿，1-进行中，2-已完成，3-已审核)
     */
    private Integer status;
    
    /**
     * 状态名称
     */
    private String statusName;
    
    /**
     * 计划开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planStartTime;
    
    /**
     * 计划结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planEndTime;
    
    /**
     * 实际开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date actualStartTime;
    
    /**
     * 实际结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date actualEndTime;
    
    /**
     * 创建人姓名
     */
    private String creatorName;
    
    /**
     * 审核人姓名
     */
    private String auditorName;
    
    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;
    
    /**
     * 总物品数
     */
    private Integer totalItems;
    
    /**
     * 已盘点数
     */
    private Integer checkedItems;
    
    /**
     * 差异数
     */
    private Integer differenceItems;
    
    /**
     * 盘点进度
     */
    private BigDecimal progress;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 盘点明细列表
     */
    private List<ItemStocktakingDetailVo> details;
    
    private static final long serialVersionUID = 1L;
} 