{"info": {"_postman_id": "12345678-1234-1234-1234-123456789012", "name": "微信小程序库存盘点接口测试", "description": "微信小程序库存盘点后端接口完整测试集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "P0级别-核心功能", "item": [{"name": "1. 获取我的盘点任务列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/item/stocktaking/my-tasks", "host": ["{{baseUrl}}"], "path": ["item", "stocktaking", "my-tasks"]}, "description": "获取当前用户分配的盘点任务列表"}}, {"name": "2. 根据物品条码查询物品信息", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/item/by-code/{{itemCode}}", "host": ["{{baseUrl}}"], "path": ["item", "by-code", "{{itemCode}}"]}, "description": "扫码查询物品详情，支持扫码盘点功能"}}, {"name": "3. 根据物品信息查找盘点明细", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/item/stocktaking/detail/by-item?stocktakingId={{stocktakingId}}&itemId={{itemId}}&warehouseId={{warehouseId}}", "host": ["{{baseUrl}}"], "path": ["item", "stocktaking", "detail", "by-item"], "query": [{"key": "stocktakingId", "value": "{{stocktakingId}}"}, {"key": "itemId", "value": "{{itemId}}"}, {"key": "warehouseId", "value": "{{warehouseId}}"}]}, "description": "扫码后定位具体的盘点明细记录"}}, {"name": "4. 更新盘点明细", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"actualQuantity\": 95.00,\n  \"differenceReason\": \"部分物品损耗\",\n  \"photos\": [\n    \"http://example.com/upload/2025/01/11/photo1.jpg\",\n    \"http://example.com/upload/2025/01/11/photo2.jpg\"\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/item/stocktaking/detail/{{detailId}}", "host": ["{{baseUrl}}"], "path": ["item", "stocktaking", "detail", "{{detailId}}"]}, "description": "录入盘点结果和上传照片"}}], "description": "P0级别核心功能接口，直接影响小程序核心功能"}, {"name": "P1级别-重要功能", "item": [{"name": "5. 获取盘点进度", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/item/stocktaking/{{stocktakingId}}/progress", "host": ["{{baseUrl}}"], "path": ["item", "stocktaking", "{{stocktakingId}}", "progress"]}, "description": "查看整体盘点进度信息"}}, {"name": "6. 获取个人盘点进度", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/item/stocktaking/my-progress", "host": ["{{baseUrl}}"], "path": ["item", "stocktaking", "my-progress"]}, "description": "查看个人盘点进度信息"}}], "description": "P1级别重要功能，影响用户体验和功能完整性"}, {"name": "P2级别-优化功能", "item": [{"name": "7. 获取个人盘点记录-今日", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/item/stocktaking/my-records?timeRange=today", "host": ["{{baseUrl}}"], "path": ["item", "stocktaking", "my-records"], "query": [{"key": "timeRange", "value": "today"}]}, "description": "查看今日个人盘点记录"}}, {"name": "7. 获取个人盘点记录-本周", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/item/stocktaking/my-records?timeRange=week", "host": ["{{baseUrl}}"], "path": ["item", "stocktaking", "my-records"], "query": [{"key": "timeRange", "value": "week"}]}, "description": "查看本周个人盘点记录"}}, {"name": "7. 获取个人盘点记录-自定义时间", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/item/stocktaking/my-records?timeRange=custom&startDate=2025-01-01&endDate=2025-01-11", "host": ["{{baseUrl}}"], "path": ["item", "stocktaking", "my-records"], "query": [{"key": "timeRange", "value": "custom"}, {"key": "startDate", "value": "2025-01-01"}, {"key": "endDate", "value": "2025-01-11"}]}, "description": "查看自定义时间范围的个人盘点记录"}}, {"name": "8. 获取物品盘点历史", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/item/stocktaking/item-history/{{itemId}}", "host": ["{{baseUrl}}"], "path": ["item", "stocktaking", "item-history", "{{itemId}}"]}, "description": "查看特定物品的盘点历史"}}], "description": "P2级别优化功能，提升用户体验"}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "token", "value": "your_jwt_token_here", "type": "string"}, {"key": "stocktakingId", "value": "test_stocktaking_id", "type": "string"}, {"key": "itemId", "value": "test_item_id", "type": "string"}, {"key": "itemCode", "value": "TEST001", "type": "string"}, {"key": "detailId", "value": "test_detail_id", "type": "string"}, {"key": "warehouseId", "value": "1", "type": "string"}]}