package com.jingfang.wh_item_requisition.module.dto;

import com.jingfang.wh_item_requisition.module.entity.ItemRequisition;
import com.jingfang.wh_item_requisition.module.entity.ItemRequisitionDetail;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 物品领用单DTO
 */
@Data
public class ItemRequisitionDto implements Serializable {
    
    /**
     * 领用单主表信息
     */
    private ItemRequisition main;
    
    /**
     * 领用单明细列表
     */
    private List<ItemRequisitionDetail> details;
    
    private static final long serialVersionUID = 1L;
} 