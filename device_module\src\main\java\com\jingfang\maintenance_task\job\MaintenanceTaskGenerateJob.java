package com.jingfang.maintenance_task.job;

import com.jingfang.maintenance_task.module.service.MaintenanceTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 维护任务生成定时任务
 */
@Slf4j
@Component("maintenanceTaskGenerateJob")
public class MaintenanceTaskGenerateJob {
    
    @Autowired
    private MaintenanceTaskService maintenanceTaskService;
    
    /**
     * 生成维护任务
     */
    public void generateTasks() {
        log.info("定时任务：开始生成维护任务");
        try {
            maintenanceTaskService.generateMaintenanceTasks();
            log.info("定时任务：维护任务生成完成");
        } catch (Exception e) {
            log.error("定时任务：维护任务生成失败", e);
        }
    }
    
    /**
     * 生成维护任务（带参数）
     */
    public void generateTasks(String params) {
        log.info("定时任务：开始生成维护任务，参数：{}", params);
        try {
            maintenanceTaskService.generateMaintenanceTasks();
            log.info("定时任务：维护任务生成完成");
        } catch (Exception e) {
            log.error("定时任务：维护任务生成失败", e);
        }
    }
} 