<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.collaboration.mapper.SpreadsheetCollaboratorMapper">
    
    <resultMap type="com.jingfang.collaboration.vo.CollaboratorVo" id="CollaboratorVoResult">
        <result property="id"                    column="id"                    />
        <result property="spreadsheetId"         column="spreadsheet_id"        />
        <result property="spreadsheetTitle"      column="spreadsheet_title"     />
        <result property="spreadsheetDescription" column="spreadsheet_description" />
        <result property="userId"                column="user_id"               />
        <result property="userName"              column="user_name"             />
        <result property="nickName"              column="nick_name"             />
        <result property="avatar"                column="avatar"                />
        <result property="deptId"                column="dept_id"               />
        <result property="deptName"              column="dept_name"             />
        <result property="permission"            column="permission"            />
        <result property="permissionName"        column="permission_name"       />
        <result property="inviteBy"              column="invite_by"             />
        <result property="inviteByName"          column="invite_by_name"        />
        <result property="inviteTime"            column="invite_time"           />
        <result property="acceptTime"            column="accept_time"           />
        <result property="status"                column="status"                />
        <result property="statusName"            column="status_name"           />
        <result property="message"               column="message"               />
        <result property="lastAccessTime"        column="last_access_time"      />
        <result property="isOnline"              column="is_online"             />
        <result property="remark"                column="remark"                />
    </resultMap>

    <resultMap type="com.jingfang.collaboration.vo.OnlineUserVo" id="OnlineUserVoResult">
        <result property="userId"           column="user_id"          />
        <result property="userName"         column="user_name"        />
        <result property="nickName"         column="nick_name"        />
        <result property="avatar"           column="avatar"           />
        <result property="deptName"         column="dept_name"        />
        <result property="permission"       column="permission"       />
        <result property="permissionName"   column="permission_name"  />
        <result property="sessionId"        column="session_id"       />
        <result property="currentPosition"  column="current_position" />
        <result property="cursorColor"      column="cursor_color"     />
        <result property="onlineTime"       column="online_time"      />
        <result property="lastActiveTime"   column="last_active_time" />
    </resultMap>

    <!-- 查询表格的协作者列表 -->
    <select id="selectCollaboratorsBySpreadsheetId" parameterType="string" resultMap="CollaboratorVoResult">
        SELECT 
            sc.id,
            sc.spreadsheet_id,
            sc.user_id,
            sc.user_name,
            sc.nick_name,
            u.avatar,
            sc.dept_id,
            sc.dept_name,
            sc.permission,
            CASE sc.permission
                WHEN 'owner' THEN '所有者'
                WHEN 'editor' THEN '编辑者'
                WHEN 'commenter' THEN '评论者'
                WHEN 'viewer' THEN '查看者'
                ELSE '未知'
            END as permission_name,
            sc.invite_by,
            sc.invite_by_name,
            sc.invite_time,
            sc.accept_time,
            sc.status,
            CASE sc.status
                WHEN '0' THEN '待接受'
                WHEN '1' THEN '已接受'
                WHEN '2' THEN '已拒绝'
                WHEN '3' THEN '已移除'
                ELSE '未知'
            END as status_name,
            sc.last_access_time,
            sc.is_online,
            sc.remark
        FROM collaboration_spreadsheet_collaborator sc
        LEFT JOIN sys_user u ON sc.user_id = u.user_id
        WHERE sc.spreadsheet_id = #{spreadsheetId} 
        AND sc.del_flag = 0
        ORDER BY 
            CASE sc.permission
                WHEN 'owner' THEN 1
                WHEN 'editor' THEN 2
                WHEN 'commenter' THEN 3
                WHEN 'viewer' THEN 4
                ELSE 5
            END,
            sc.invite_time ASC
    </select>

    <!-- 查询用户在指定表格的权限 -->
    <select id="selectUserPermission" parameterType="map" resultType="string">
        SELECT permission
        FROM collaboration_spreadsheet_collaborator
        WHERE spreadsheet_id = #{spreadsheetId} 
        AND user_id = #{userId}
        AND status = '1'
        AND del_flag = 0
        LIMIT 1
    </select>

    <!-- 查询表格的在线用户列表 -->
    <select id="selectOnlineUsers" parameterType="string" resultMap="OnlineUserVoResult">
        SELECT 
            sc.user_id,
            sc.user_name,
            sc.nick_name,
            u.avatar,
            sc.dept_name,
            sc.permission,
            CASE sc.permission
                WHEN 'owner' THEN '所有者'
                WHEN 'editor' THEN '编辑者'
                WHEN 'commenter' THEN '评论者'
                WHEN 'viewer' THEN '查看者'
                ELSE '未知'
            END as permission_name,
            '' as session_id,
            '' as current_position,
            '' as cursor_color,
            sc.last_access_time as online_time,
            sc.last_access_time as last_active_time
        FROM collaboration_spreadsheet_collaborator sc
        LEFT JOIN sys_user u ON sc.user_id = u.user_id
        WHERE sc.spreadsheet_id = #{spreadsheetId} 
        AND sc.status = '1'
        AND sc.is_online = '1'
        AND sc.del_flag = 0
        ORDER BY sc.last_access_time DESC
    </select>

    <!-- 查询用户的协作邀请列表 -->
    <select id="selectUserInvitations" parameterType="map" resultMap="CollaboratorVoResult">
        SELECT
            sc.id,
            sc.spreadsheet_id,
            s.title as spreadsheet_title,
            s.description as spreadsheet_description,
            sc.user_id,
            sc.user_name,
            sc.nick_name,
            u.avatar,
            sc.dept_id,
            sc.dept_name,
            sc.permission,
            CASE sc.permission
                WHEN 'owner' THEN '所有者'
                WHEN 'editor' THEN '编辑者'
                WHEN 'commenter' THEN '评论者'
                WHEN 'viewer' THEN '查看者'
                ELSE '未知'
            END as permission_name,
            sc.invite_by,
            sc.invite_by_name,
            sc.invite_time,
            sc.accept_time,
            sc.status,
            CASE sc.status
                WHEN '0' THEN '待接受'
                WHEN '1' THEN '已接受'
                WHEN '2' THEN '已拒绝'
                WHEN '3' THEN '已移除'
                ELSE '未知'
            END as status_name,
            sc.remark as message,
            sc.last_access_time,
            sc.is_online,
            sc.remark
        FROM collaboration_spreadsheet_collaborator sc
        LEFT JOIN collaboration_spreadsheet s ON sc.spreadsheet_id = s.id
        LEFT JOIN sys_user u ON sc.user_id = u.user_id
        WHERE sc.user_id = #{userId}
        AND sc.del_flag = 0
        <if test="spreadsheetTitle != null and spreadsheetTitle != ''">
            AND s.title LIKE CONCAT('%', #{spreadsheetTitle}, '%')
        </if>
        <if test="status != null and status != ''">
            AND sc.status = #{status}
        </if>
        ORDER BY sc.invite_time DESC
    </select>

    <!-- 批量更新用户在线状态 -->
    <update id="updateUserOnlineStatus" parameterType="map">
        UPDATE collaboration_spreadsheet_collaborator
        SET is_online = #{isOnline},
            last_access_time = NOW(),
            update_time = NOW()
        WHERE spreadsheet_id = #{spreadsheetId}
        AND status = '1'
        AND del_flag = 0
        <if test="userIds != null and userIds.size() > 0">
            AND user_id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
    </update>

</mapper>
