# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.9
  # 版权年份
  copyrightYear: 2025
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/resourceRepo/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math
# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.jingfang: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  data:
    # redis 配置
    redis:
      # 地址
      host: localhost
      # 端口，默认为6379
      port: 6379
      # 数据库索引
      database: 0
      # 密码
      password:
      # 连接超时时间
      timeout: 10s
      lettuce:
        pool:
          # 连接池中的最小空闲连接
          min-idle: 0
          # 连接池中的最大空闲连接
          max-idle: 8
          # 连接池的最大数据库连接数
          max-active: 8
          # #连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 3000

# MyBatis配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.jingfang.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: delFlag # 逻辑删除字段名
      logic-delete-value: 1       # 删除后的值
      logic-not-delete-value: 0   # 未删除的值

# PageHelper分页插件 - 已禁用，使用MyBatis-Plus分页插件
# pagehelper:
#   helperDialect: mysql
#   supportMethodsArguments: true
#   params: count=countSql

# Springdoc配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    tags-sorter: alpha
  group-configs:
    - group: 'default'
      display-name: '测试模块'
      paths-to-match: '/**'
      packages-to-scan: com.jingfang.web.controller.tool

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# flowable相关表
flowable:
  # true 会对数据库中所有表进行更新操作。如果表不存在，则自动创建(建议开发时使用)
  database-schema-update: true #数据库中不存在流程表时，将该值设置为true
  # 关闭定时任务JOB
  async-executor-activate: false
  # 禁用异步历史执行器
  async-history-executor-activate: false

# 微信小程序配置
wx:
  # 小程序 appid
  appid: wx5b0af1c175200dd6
  # 小程序 secret
  secret: b5e2ffcd0901166df1059a9c17b9e514

# MQTT配置
mqtt:
  server-uri: tcp://192.168.110.135:10883
  client-id: device_monitor_client
  username: admin
  password: 429498517
  device:
    status:
      request:
        topic: /sys/thing/node/status/get/device_monitor_client
      response:
        topic: /sys/thing/node/status/get_reply/device_monitor_client
      timeout: 10
    property:
      request:
        topic: /sys/thing/node/property/get/device_monitor_client
      response:
        topic: /sys/thing/node/property/get_reply/device_monitor_client
      write:
        topic: /sys/thing/node/property/set/device_monitor_client
        response:
          topic: /sys/thing/node/property/set_reply/device_monitor_client
      timeout: 15
