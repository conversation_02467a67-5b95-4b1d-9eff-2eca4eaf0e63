package com.jingfang.asset_inbound.mapper;

import com.jingfang.asset_inbound.module.entity.AssetInbound;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jingfang.asset_inbound.module.request.AssetInboundSearchRequest;
import com.jingfang.asset_inbound.module.vo.AssetInboundVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【asset_inbound(资产入库单主表)】的数据库操作Mapper
* @createDate 2025-05-07 13:59:19
* @Entity com.jingfang.asset_inbound.module.entity.AssetInbound
*/
public interface AssetInboundMapper extends BaseMapper<AssetInbound> {
    /**
     * 查询入库单列表带管理人员名称
     * @param page 分页参数
     * @param request 查询条件
     * @return 入库单列表
     */
    IPage<AssetInboundVo> selectInboundListWithManager(Page<AssetInboundVo> page, AssetInboundSearchRequest request);
    
    /**
     * 通过ID查询入库单详情带管理人员名称
     * @param inboundId 入库单ID
     * @return 入库单详情
     */
    AssetInboundVo selectInboundDetailById(String inboundId);

    /**
     * 统计本月入库单数
     */
    Long countMonthlyInbound(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 统计本月入库资产数
     */
    Long countMonthlyInboundAssets(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 统计本月入库资产值
     */
    BigDecimal sumMonthlyInboundValue(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 按状态统计入库单数量
     */
    List<Map<String, Object>> countInboundByStatus();

    /**
     * 统计入库趋势数据
     */
    List<Map<String, Object>> selectInboundTrends(@Param("startDate") String startDate, @Param("endDate") String endDate);
}




