package com.jingfang.asset_inbound.module.dto;

import com.jingfang.asset_inbound.module.entity.AssetInbound;
import com.jingfang.asset_inbound.module.entity.AssetInboundDetail;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * 资产入库DTO
 * 用于接收前端提交的入库单数据
 */
@Data
public class AssetInboundDto {
    
    /**
     * 入库单主信息
     */
    private AssetInbound main;
    
    /**
     * 入库明细列表
     */
    @NotNull(message = "入库明细不能为空")
    @Size(min = 1, message = "至少需要一条入库明细")
    private List<AssetInboundDetail> details;
} 