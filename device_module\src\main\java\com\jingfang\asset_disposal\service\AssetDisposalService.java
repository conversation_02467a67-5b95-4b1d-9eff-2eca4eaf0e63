package com.jingfang.asset_disposal.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jingfang.asset_disposal.module.entity.AssetDisposal;
import com.jingfang.asset_disposal.module.dto.AssetDisposalDto;
import com.jingfang.asset_disposal.module.dto.DisposalApprovalDto;
import com.jingfang.asset_disposal.module.dto.AssetDisposalExecutionDto;
import com.jingfang.asset_disposal.module.request.AssetDisposalSearchRequest;
import com.jingfang.asset_disposal.module.vo.*;

import java.util.List;
import java.util.Map;

/**
 * 资产处置服务接口
 */
public interface AssetDisposalService extends IService<AssetDisposal> {
    
    /**
     * 创建处置申请
     */
    boolean createDisposalRequest(AssetDisposalDto dto);
    
    /**
     * 提交审核
     */
    boolean submitForApproval(String disposalId);
    
    /**
     * 审批处置申请
     */
    boolean approveDisposal(DisposalApprovalDto dto);
    
    /**
     * 执行处置
     */
    boolean executeDisposal(String disposalId, AssetDisposalExecutionDto dto);
    
    /**
     * 查询处置申请列表
     */
    IPage<AssetDisposalVo> selectDisposalList(AssetDisposalSearchRequest request);
    
    /**
     * 查询处置详情
     */
    AssetDisposalDetailVo getDisposalDetail(String disposalId);
    
    /**
     * 查询待审批列表
     */
    List<AssetDisposalVo> getPendingApprovalList(Long userId);
    
    /**
     * 取消处置申请
     */
    boolean cancelDisposal(String disposalId, String reason);
    
    /**
     * 获取处置统计信息
     */
    Map<String, Object> getDisposalStatistics();
} 