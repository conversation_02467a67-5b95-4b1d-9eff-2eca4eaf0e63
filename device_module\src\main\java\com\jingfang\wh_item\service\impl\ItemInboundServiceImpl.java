package com.jingfang.wh_item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingfang.common.utils.BusinessCodeGenerator;
import com.jingfang.common.utils.bean.BeanUtils;
import com.jingfang.common.utils.poi.ExcelUtil;
import com.jingfang.wh_item.mapper.ItemInboundDetailMapper;
import com.jingfang.wh_item.mapper.ItemInboundMapper;
import com.jingfang.wh_item.mapper.ItemInboundOperationLogMapper;
import com.jingfang.wh_item.module.dto.ItemInboundDto;
import com.jingfang.wh_item.module.entity.ItemInbound;
import com.jingfang.wh_item.module.entity.ItemInboundDetail;
import com.jingfang.wh_item.module.entity.ItemInboundOperationLog;
import com.jingfang.wh_item.module.request.ItemInboundSearchRequest;
import com.jingfang.wh_item.module.vo.ItemInboundDetailVo;
import com.jingfang.wh_item.module.vo.ItemInboundVo;
import com.jingfang.wh_item.service.ItemInboundService;
import com.jingfang.wh_item.service.ItemService;
import com.jingfang.wh_item.service.ItemStockSyncService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 物品入库业务实现类
 */
@Slf4j
@Service
public class ItemInboundServiceImpl extends ServiceImpl<ItemInboundMapper, ItemInbound> implements ItemInboundService {

    @Resource
    private ItemInboundMapper inboundMapper;

    @Resource
    private ItemInboundDetailMapper detailMapper;

    @Resource
    private ItemInboundOperationLogMapper logMapper;

    @Resource
    private ItemService itemService;

    @Resource
    private BusinessCodeGenerator codeGenerator;

    @Resource
    private ItemStockSyncService itemStockSyncService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addItemInbound(ItemInboundDto inboundDto, String username) {
        // 1. 构建并保存入库单主表信息
        ItemInbound inbound = new ItemInbound();
        BeanUtils.copyProperties(inboundDto.getMain(), inbound);

        // 设置初始状态为草稿状态(1)
        inbound.setCreateTime(new Date());
        inbound.setUpdateTime(new Date());
        inbound.setDelFlag("0");

        // 保存入库单主表
        inboundMapper.insert(inbound);
        String inboundId = inbound.getInboundId();

        // 2. 保存入库单明细
        if (inboundDto.getDetails() != null && !inboundDto.getDetails().isEmpty()) {
            for (ItemInboundDetail detail : inboundDto.getDetails()) {
                // 校验物品是否存在
                validateItemExists(detail.getItemId());
                
                // 设置入库单ID
                detail.setInboundId(inboundId);
                detail.setCreateTime(new Date());
                detail.setUpdateTime(new Date());

                // 计算金额
                if (detail.getUnitPrice() != null && detail.getQuantity() != null) {
                    detail.setAmount(detail.getUnitPrice().multiply(detail.getQuantity()));
                }

                // 保存明细
                detailMapper.insert(detail);
            }
        }

        // 3. 记录操作日志
        recordOperationLog(inboundId, 1, "创建入库单", username);

        return inboundId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateItemInbound(String inboundId, ItemInboundDto inboundDto, String username) {
        // 1. 查询入库单
        ItemInbound inbound = inboundMapper.selectById(inboundId);
        if (inbound == null) {
            throw new RuntimeException("入库单不存在");
        }

        // 2. 校验当前状态（只允许编辑草稿(1)和退回(5)状态的入库单）
        if (inbound.getStatus() != 1 && inbound.getStatus() != 5) {
            throw new RuntimeException("当前状态不允许编辑");
        }

        // 3. 更新入库单主表信息
        ItemInbound newInbound = inboundDto.getMain();
        newInbound.setInboundId(inboundId);
        newInbound.setStatus(inbound.getStatus()); // 保持状态不变
        newInbound.setCreatorId(inbound.getCreatorId()); // 保持制单人不变
        newInbound.setCreateTime(inbound.getCreateTime()); // 保留创建时间
        newInbound.setHandlerId(inbound.getHandlerId());
        newInbound.setHandleTime(inbound.getHandleTime());
        newInbound.setHandleRemark(inbound.getHandleRemark());
        newInbound.setAuditorId(inbound.getAuditorId());
        newInbound.setAuditTime(inbound.getAuditTime());
        newInbound.setAuditRemark(inbound.getAuditRemark());
        newInbound.setUpdateTime(new Date());
        newInbound.setDelFlag("0");

        inboundMapper.updateById(newInbound);

        // 4. 删除原入库单明细
        detailMapper.deleteByInboundId(inboundId);

        // 5. 保存新的入库单明细
        if (inboundDto.getDetails() != null && !inboundDto.getDetails().isEmpty()) {
            for (ItemInboundDetail detail : inboundDto.getDetails()) {
                // 校验物品是否存在
                validateItemExists(detail.getItemId());

                // 设置入库单ID
                detail.setInboundId(inboundId);
                detail.setCreateTime(new Date());
                detail.setUpdateTime(new Date());

                // 计算金额
                if (detail.getUnitPrice() != null && detail.getQuantity() != null) {
                    detail.setAmount(detail.getUnitPrice().multiply(detail.getQuantity()));
                }

                // 保存明细
                detailMapper.insert(detail);
            }
        }

        // 6. 记录操作日志
        recordOperationLog(inboundId, 2, "编辑入库单", username);
    }

    @Override
    public ItemInboundVo getInboundDetail(String inboundId) {
        // 使用关联查询获取管理人员名称
        ItemInboundVo vo = inboundMapper.selectInboundDetailById(inboundId);
        if (vo == null) {
            throw new RuntimeException("入库单不存在");
        }

        // 设置状态名称
        setStatusName(vo);

        // 设置入库类型名称
        setInboundTypeName(vo);

        // 查询并设置入库明细
        List<ItemInboundDetailVo> details = detailMapper.selectDetailsByInboundId(inboundId);
        vo.setDetails(details);

        // 查询并设置操作日志
        LambdaQueryWrapper<ItemInboundOperationLog> logWrapper = new LambdaQueryWrapper<>();
        logWrapper.eq(ItemInboundOperationLog::getInboundId, inboundId);
        logWrapper.orderByDesc(ItemInboundOperationLog::getOperationTime);
        List<ItemInboundOperationLog> logs = logMapper.selectList(logWrapper);
        vo.setLogs(logs);

        return vo;
    }

    @Override
    public IPage<ItemInboundVo> selectInboundList(ItemInboundSearchRequest request) {
        // 使用关联查询获取管理人员名称
        Page<ItemInboundVo> page = new Page<>(request.getPageNum(), request.getPageSize());
        IPage<ItemInboundVo> result = inboundMapper.selectInboundListWithManager(page, request);

        // 设置状态名称和入库类型名称
        for (ItemInboundVo vo : result.getRecords()) {
            setStatusName(vo);
            setInboundTypeName(vo);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitInbound(String inboundId, String username) {
        // 1. 查询入库单
        ItemInbound inbound = inboundMapper.selectById(inboundId);
        if (inbound == null) {
            throw new RuntimeException("入库单不存在");
        }

        // 2. 校验当前状态，只有草稿(1)或退回(5)状态可以提交
        if (inbound.getStatus() != 1 && inbound.getStatus() != 5) {
            throw new RuntimeException("当前状态不允许提交");
        }

        // 3. 更新状态为待确认(2)
        inbound.setStatus(2);
        inbound.setUpdateTime(new Date());
        inboundMapper.updateById(inbound);

        // 4. 记录操作日志
        recordOperationLog(inboundId, 3, "提交入库单", username);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleInbound(String inboundId, String remark, String username) {
        // 1. 查询入库单
        ItemInbound inbound = inboundMapper.selectById(inboundId);
        if (inbound == null) {
            throw new RuntimeException("入库单不存在");
        }

        // 2. 校验当前状态，只有待确认(2)状态可以确认
        if (inbound.getStatus() != 2) {
            throw new RuntimeException("当前状态不允许经手人确认");
        }

        // 3. 获取当前用户ID
        Long userId = getUserId(username);

        // 4. 更新状态为待审核(3)，并记录经手人信息
        inbound.setStatus(3);
        inbound.setHandlerId(userId);
        inbound.setHandleTime(new Date());
        inbound.setHandleRemark(remark);
        inbound.setUpdateTime(new Date());
        inboundMapper.updateById(inbound);

        // 5. 记录操作日志
        recordOperationLog(inboundId, 4, "经手人确认：" + (remark != null ? remark : ""), username);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditInbound(String inboundId, Integer status, String remark, String username) {
        // 1. 查询入库单
        ItemInbound inbound = inboundMapper.selectById(inboundId);
        if (inbound == null) {
            throw new RuntimeException("入库单不存在");
        }

        // 2. 校验当前状态，只有待审核(3)状态可以审核
        if (inbound.getStatus() != 3) {
            throw new RuntimeException("当前状态不允许审核");
        }

        // 3. 校验审核结果参数
        if (status != 4 && status != 5) {
            throw new RuntimeException("无效的审核结果");
        }

        // 4. 获取当前用户ID
        Long userId = getUserId(username);

        // 5. 更新状态和审核信息
        inbound.setStatus(status);
        inbound.setAuditorId(userId);
        inbound.setAuditTime(new Date());
        inbound.setAuditRemark(remark);
        inbound.setUpdateTime(new Date());
        inboundMapper.updateById(inbound);

        // 6. 记录操作日志
        recordOperationLog(inboundId, status == 4 ? 5 : 6,
                status == 4 ? "审核通过：" + remark : "审核退回：" + remark, username);

        // 7. 如果审核通过，则自动更新库存
        if (status == 4) {
            try {
                updateItemStock(inboundId);

                // 更新库存后，记录操作日志
                recordOperationLog(inboundId, 7, "根据入库单自动更新库存", username);

                log.info("入库单[{}]审核通过，已自动完成库存更新", inboundId);
            } catch (Exception e) {
                log.error("入库单[{}]自动更新库存失败: {}", inboundId, e.getMessage(), e);
                throw new RuntimeException("审核通过，但自动更新库存失败: " + e.getMessage());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteInbound(String[] inboundIds, String username) {
        Long userId = getUserId(username);

        for (String inboundId : inboundIds) {
            // 1. 查询入库单
            ItemInbound inbound = inboundMapper.selectById(inboundId);
            if (inbound == null) {
                continue;
            }

            // 2. 校验状态（只允许删除草稿(1)和退回(5)状态的入库单）
            if (inbound.getStatus() != 1 && inbound.getStatus() != 5) {
                throw new RuntimeException("入库单[" + inboundId + "]当前状态不允许删除");
            }

            // 3. 逻辑删除入库单
            inbound.setDelFlag("1");
            inbound.setUpdateTime(new Date());
            inboundMapper.updateById(inbound);

            // 4. 记录操作日志
            recordOperationLog(inboundId, 9, "删除入库单", username);
        }
    }


    /**
     * 校验物品是否存在
     */
    private void validateItemExists(String itemId) {
        // 调用物品服务校验物品是否存在
        boolean exists = itemService.existsById(itemId);
        if (!exists) {
            throw new RuntimeException("物品[" + itemId + "]不存在");
        }
    }

    /**
     * 更新物品库存
     */
    private void updateItemStock(String inboundId) {
        // 1. 查询入库单明细
        LambdaQueryWrapper<ItemInboundDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ItemInboundDetail::getInboundId, inboundId);
        List<ItemInboundDetail> details = detailMapper.selectList(queryWrapper);

        if (details == null || details.isEmpty()) {
            log.warn("入库单[{}]没有明细数据，无法更新库存", inboundId);
            return;
        }

        // 2. 遍历明细，更新库存
        for (ItemInboundDetail detail : details) {
            // 获取仓库ID，明细中必须指定仓库ID
            Integer warehouseId = detail.getWarehouseId();
            
            if (warehouseId == null) {
                log.error("入库单[{}]明细[物品ID:{}]未指定仓库ID，无法更新库存", inboundId, detail.getItemId());
                throw new RuntimeException("物品[" + detail.getItemId() + "]未指定仓库，无法更新库存");
            }
            
            // 调用物品服务更新库存
            boolean success = itemService.updateItemStock(
                    detail.getItemId(),
                    warehouseId,
                    detail.getQuantity().intValue(),
                    1, // 入库操作类型
                    inboundId,
                    "入库单入库",
                    detail.getShelfLocation() // 传递货架位置
            );

            if (!success) {
                throw new RuntimeException("物品[" + detail.getItemId() + "]在仓库[" + warehouseId + "]库存更新失败");
            }

            log.info("根据入库单明细更新库存成功: 物品ID[{}]，仓库[{}]，数量[{}]", 
                    detail.getItemId(), warehouseId, detail.getQuantity());
        }
    }

    /**
     * 记录操作日志
     */
    private void recordOperationLog(String inboundId, Integer operationType, String operationContent, String username) {
        ItemInboundOperationLog log = new ItemInboundOperationLog();
        log.setInboundId(inboundId);
        log.setOperationType(operationType);
        log.setOperationContent(operationContent);
        log.setOperationTime(new Date());
        log.setOperatorId(getUserId(username));
        log.setOperatorName(username);
        logMapper.insert(log);
    }

    /**
     * 获取用户ID
     */
    private Long getUserId(String username) {
        // 这里应该调用系统的用户服务来获取用户ID
        // 为了示例，这里简单返回null，实际实现时应替换为真实的用户查询逻辑
        return null;
    }

    /**
     * 设置状态名称
     */
    private void setStatusName(ItemInboundVo vo) {
        if (vo.getStatus() != null) {
            switch(vo.getStatus()) {
                case 1:
                    vo.setStatusName("草稿");
                    break;
                case 2:
                    vo.setStatusName("待确认");
                    break;
                case 3:
                    vo.setStatusName("待审核");
                    break;
                case 4:
                    vo.setStatusName("已通过");
                    break;
                case 5:
                    vo.setStatusName("已退回");
                    break;
                default:
                    vo.setStatusName("未知");
            }
        }
    }

    /**
     * 设置入库类型名称
     */
    private void setInboundTypeName(ItemInboundVo vo) {
        if (vo.getInboundType() != null) {
            switch(vo.getInboundType()) {
                case 1:
                    vo.setInboundTypeName("采购入库");
                    break;
                case 2:
                    vo.setInboundTypeName("调拨入库");
                    break;
                case 3:
                    vo.setInboundTypeName("盘盈入库");
                    break;
                case 4:
                    vo.setInboundTypeName("其他入库");
                    break;
                default:
                    vo.setInboundTypeName("未知");
            }
        }
    }

}