package com.jingfang.collaboration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.collaboration.domain.SpreadsheetOperation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 表格操作记录Mapper接口
 */
@Mapper
public interface SpreadsheetOperationMapper extends BaseMapper<SpreadsheetOperation> {
    
    /**
     * 查询表格操作记录列表
     */
    IPage<SpreadsheetOperation> selectOperationList(IPage<SpreadsheetOperation> page, 
                                                   @Param("spreadsheetId") String spreadsheetId,
                                                   @Param("userId") Long userId,
                                                   @Param("operationType") String operationType);
}
