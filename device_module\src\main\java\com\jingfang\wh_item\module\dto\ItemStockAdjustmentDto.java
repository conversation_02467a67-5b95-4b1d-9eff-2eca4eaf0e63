package com.jingfang.wh_item.module.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 库存调整DTO
 */
@Data
public class ItemStockAdjustmentDto implements Serializable {
    
    /**
     * 物品ID
     */
    private String itemId;
    
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    
    /**
     * 调整后的库存数量
     */
    private BigDecimal newQuantity;
    
    /**
     * 调整原因
     */
    private String reason;
    
    /**
     * 货架位置（可选，如果需要同时更新货架位置）
     */
    private String shelfLocation;
    
    private static final long serialVersionUID = 1L;
} 