package com.jingfang.wh_item.module.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 库存盘点DTO
 */
@Data
public class ItemStocktakingDto implements Serializable {
    
    /**
     * 盘点单ID
     */
    private String stocktakingId;
    
    /**
     * 盘点名称
     */
    @NotBlank(message = "盘点名称不能为空")
    private String stocktakingName;
    
    /**
     * 盘点类型(1-全盘，2-抽盘，3-循环盘点)
     */
    @NotNull(message = "盘点类型不能为空")
    private Integer stocktakingType;
    
    /**
     * 仓库ID(全盘时为空)
     */
    private Integer warehouseId;
    
    /**
     * 计划开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planStartTime;
    
    /**
     * 计划结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planEndTime;
    
    /**
     * 备注
     */
    private String remark;
    
    private static final long serialVersionUID = 1L;
} 