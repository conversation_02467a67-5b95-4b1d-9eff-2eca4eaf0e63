package com.jingfang.device_module.mqtt.service;

import com.alibaba.fastjson2.JSON;
import com.jingfang.device_module.mqtt.dto.DevicePropertyRequest;
import com.jingfang.device_module.mqtt.dto.DevicePropertyResponse;
import com.jingfang.device_module.mqtt.dto.DevicePropertyWriteRequest;
import com.jingfang.device_module.mqtt.dto.DevicePropertyWriteResponse;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * MQTT设备属性查询服务
 */
@Slf4j
@Service
public class MqttDevicePropertyService {

    @Value("${mqtt.server-uri:tcp://***************:10883}")
    private String serverUri;

    @Value("${mqtt.client-id:device_monitor_client}")
    private String clientId;

    @Value("${mqtt.username:admin}")
    private String username;

    @Value("${mqtt.password:429498517}")
    private String password;

    @Value("${mqtt.device.property.request.topic:/sys/thing/node/property/get/device_monitor_client}")
    private String requestTopic;

    @Value("${mqtt.device.property.response.topic:/sys/thing/node/property/get_reply/device_monitor_client}")
    private String responseTopic;

    @Value("${mqtt.device.property.write.topic:/sys/thing/node/property/set/device_monitor_client}")
    private String writeRequestTopic;

    @Value("${mqtt.device.property.write.response.topic:/sys/thing/node/property/set_reply/device_monitor_client}")
    private String writeResponseTopic;

    @Value("${mqtt.device.property.timeout:15}")
    private int timeoutSeconds;

    private MqttClient mqttClient;

    // 存储待处理的读取请求
    private final Map<String, CompletableFuture<DevicePropertyResponse>> pendingRequests = new ConcurrentHashMap<>();

    // 存储待处理的写入请求
    private final Map<String, CompletableFuture<DevicePropertyWriteResponse>> pendingWriteRequests = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        try {
            log.info("开始初始化MQTT设备属性查询服务...");
            log.info("MQTT配置 - 服务器: {}, 客户端ID: {}, 用户名: {}", serverUri, clientId, username);

            mqttClient = new MqttClient(serverUri, clientId + "_property");

            MqttConnectOptions options = new MqttConnectOptions();
            options.setUserName(username);
            options.setPassword(password.toCharArray());
            options.setCleanSession(true);
            options.setConnectionTimeout(30);
            options.setKeepAliveInterval(60);
            options.setAutomaticReconnect(true);

            mqttClient.connect(options);
            log.info("MQTT客户端连接成功");

            // 订阅读取响应主题
            mqttClient.subscribe(responseTopic, 1, (topic, message) -> {
                String payload = new String(message.getPayload());
                log.info("收到MQTT设备属性响应: {}", payload);

                try {
                    DevicePropertyResponse response = JSON.parseObject(payload, DevicePropertyResponse.class);
                    if (response != null && response.getId() != null) {
                        log.info("解析MQTT属性响应成功，请求ID: {}, 响应代码: {}", response.getId(), response.getCode());
                        CompletableFuture<DevicePropertyResponse> future = pendingRequests.remove(response.getId());
                        if (future != null) {
                            future.complete(response);
                            log.info("成功完成请求ID: {} 的Future", response.getId());
                        } else {
                            log.warn("未找到请求ID: {} 对应的Future", response.getId());
                        }
                    } else {
                        log.warn("MQTT属性响应解析失败或缺少ID字段: {}", payload);
                    }
                } catch (Exception e) {
                    log.error("处理MQTT设备属性响应失败", e);
                }
            });

            log.info("已订阅响应主题: {}", responseTopic);

            // 订阅写入响应主题
            mqttClient.subscribe(writeResponseTopic, 1, (topic, message) -> {
                String payload = new String(message.getPayload());
                log.info("收到MQTT设备属性写入响应: {}", payload);

                try {
                    DevicePropertyWriteResponse response = JSON.parseObject(payload, DevicePropertyWriteResponse.class);
                    if (response != null && response.getId() != null) {
                        log.info("解析MQTT属性写入响应成功，请求ID: {}, 响应代码: {}", response.getId(), response.getCode());
                        CompletableFuture<DevicePropertyWriteResponse> future = pendingWriteRequests.remove(response.getId());
                        if (future != null) {
                            future.complete(response);
                            log.info("成功完成写入请求ID: {} 的Future", response.getId());
                        } else {
                            log.warn("未找到写入请求ID: {} 对应的Future", response.getId());
                        }
                    } else {
                        log.warn("MQTT属性写入响应解析失败或缺少ID字段: {}", payload);
                    }
                } catch (Exception e) {
                    log.error("处理MQTT设备属性写入响应失败", e);
                }
            });

            log.info("已订阅写入响应主题: {}", writeResponseTopic);
            log.info("MQTT设备属性查询服务初始化完成");
        } catch (Exception e) {
            log.error("MQTT设备属性查询服务初始化失败", e);
        }
    }

    @PreDestroy
    public void destroy() {
        try {
            if (mqttClient != null && mqttClient.isConnected()) {
                mqttClient.disconnect();
                mqttClient.close();
            }
        } catch (Exception e) {
            log.error("关闭MQTT客户端失败", e);
        }
    }

    /**
     * 查询设备属性
     * @param deviceIpAddress 设备IP地址
     * @param propertyNames 属性名称列表
     * @return 设备属性响应
     */
    public CompletableFuture<DevicePropertyResponse> queryDeviceProperties(String deviceIpAddress, List<String> propertyNames) {
        String requestId = UUID.randomUUID().toString();
        
        // 构建请求对象
        DevicePropertyRequest request = new DevicePropertyRequest(requestId);
        
        DevicePropertyRequest.DevicePropertyParam param = new DevicePropertyRequest.DevicePropertyParam();
        param.setClientID(deviceIpAddress);
        
        List<DevicePropertyRequest.PropertyInfo> properties = new ArrayList<>();
        for (String propertyName : propertyNames) {
            DevicePropertyRequest.PropertyInfo property = new DevicePropertyRequest.PropertyInfo();
            property.setName(propertyName);
            properties.add(property);
        }
        param.setProperties(properties);
        
        request.setParams(Arrays.asList(param));

        CompletableFuture<DevicePropertyResponse> future = new CompletableFuture<>();
        pendingRequests.put(requestId, future);

        log.info("当前待处理属性查询请求数量: {}", pendingRequests.size());

        try {
            String jsonMessage = JSON.toJSONString(request);
            log.info("发送MQTT设备属性查询请求: {}", jsonMessage);

            MqttMessage message = new MqttMessage(jsonMessage.getBytes());
            message.setQos(1);

            mqttClient.publish(requestTopic, message);
            log.info("MQTT设备属性查询请求已发送，请求ID: {}, 设备IP: {}, 属性数量: {}", 
                    requestId, deviceIpAddress, propertyNames.size());

            // 设置超时处理
            CompletableFuture.delayedExecutor(timeoutSeconds, TimeUnit.SECONDS).execute(() -> {
                CompletableFuture<DevicePropertyResponse> timeoutFuture = pendingRequests.remove(requestId);
                if (timeoutFuture != null && !timeoutFuture.isDone()) {
                    log.warn("MQTT设备属性查询超时，请求ID: {}", requestId);
                    timeoutFuture.completeExceptionally(new RuntimeException("MQTT设备属性查询超时"));
                }
            });

        } catch (Exception e) {
            pendingRequests.remove(requestId);
            future.completeExceptionally(e);
            log.error("发送MQTT设备属性查询请求失败，请求ID: {}", requestId, e);
        }

        return future;
    }

    /**
     * 写入设备属性
     * @param deviceIpAddress 设备IP地址
     * @param propertyName 属性名称
     * @param propertyValue 属性值
     * @return 写入响应
     */
    public CompletableFuture<DevicePropertyWriteResponse> writeDeviceProperty(String deviceIpAddress, String propertyName, String propertyValue) {
        String requestId = UUID.randomUUID().toString();

        // 构建写入请求对象
        DevicePropertyWriteRequest request = new DevicePropertyWriteRequest(deviceIpAddress, propertyName, propertyValue);
        request.setId(requestId);

        CompletableFuture<DevicePropertyWriteResponse> future = new CompletableFuture<>();
        pendingWriteRequests.put(requestId, future);

        try {
            String jsonMessage = JSON.toJSONString(request);
            log.info("发送MQTT设备属性写入请求: {}", jsonMessage);

            MqttMessage message = new MqttMessage(jsonMessage.getBytes());
            message.setQos(1);

            mqttClient.publish(writeRequestTopic, message);
            log.info("MQTT设备属性写入请求已发送，请求ID: {}, 设备IP: {}, 属性: {}={}",
                    requestId, deviceIpAddress, propertyName, propertyValue);

            // 设置超时处理
            CompletableFuture.delayedExecutor(timeoutSeconds, TimeUnit.SECONDS).execute(() -> {
                CompletableFuture<DevicePropertyWriteResponse> timeoutFuture = pendingWriteRequests.remove(requestId);
                if (timeoutFuture != null && !timeoutFuture.isDone()) {
                    log.warn("MQTT设备属性写入超时，请求ID: {}", requestId);
                    timeoutFuture.completeExceptionally(new RuntimeException("MQTT设备属性写入超时"));
                }
            });

        } catch (Exception e) {
            pendingWriteRequests.remove(requestId);
            log.error("发送MQTT设备属性写入请求失败", e);
            future.completeExceptionally(e);
        }

        return future;
    }

    /**
     * 批量写入设备属性
     * @param deviceIpAddress 设备IP地址
     * @param properties 属性列表
     * @return 写入响应
     */
    public CompletableFuture<DevicePropertyWriteResponse> writeDeviceProperties(String deviceIpAddress, List<DevicePropertyWriteRequest.PropertyWriteInfo> properties) {
        String requestId = UUID.randomUUID().toString();

        // 构建写入请求对象
        DevicePropertyWriteRequest request = new DevicePropertyWriteRequest(deviceIpAddress, properties);
        request.setId(requestId);

        CompletableFuture<DevicePropertyWriteResponse> future = new CompletableFuture<>();
        pendingWriteRequests.put(requestId, future);

        try {
            String jsonMessage = JSON.toJSONString(request);
            log.info("发送MQTT设备属性批量写入请求: {}", jsonMessage);

            MqttMessage message = new MqttMessage(jsonMessage.getBytes());
            message.setQos(1);

            mqttClient.publish(writeRequestTopic, message);
            log.info("MQTT设备属性批量写入请求已发送，请求ID: {}, 设备IP: {}, 属性数量: {}",
                    requestId, deviceIpAddress, properties.size());

            // 设置超时处理
            CompletableFuture.delayedExecutor(timeoutSeconds, TimeUnit.SECONDS).execute(() -> {
                CompletableFuture<DevicePropertyWriteResponse> timeoutFuture = pendingWriteRequests.remove(requestId);
                if (timeoutFuture != null && !timeoutFuture.isDone()) {
                    log.warn("MQTT设备属性批量写入超时，请求ID: {}", requestId);
                    timeoutFuture.completeExceptionally(new RuntimeException("MQTT设备属性批量写入超时"));
                }
            });

        } catch (Exception e) {
            pendingWriteRequests.remove(requestId);
            log.error("发送MQTT设备属性批量写入请求失败", e);
            future.completeExceptionally(e);
        }

        return future;
    }

    /**
     * 测试MQTT连接状态
     */
    public boolean testMqttConnection() {
        try {
            log.info("测试MQTT属性查询连接状态...");
            log.info("MQTT客户端状态: {}", mqttClient != null && mqttClient.isConnected() ? "已连接" : "未连接");
            log.info("请求主题: {}", requestTopic);
            log.info("响应主题: {}", responseTopic);
            log.info("超时时间: {} 秒", timeoutSeconds);
            return mqttClient != null && mqttClient.isConnected();
        } catch (Exception e) {
            log.error("测试MQTT属性查询连接状态失败", e);
            return false;
        }
    }
}
