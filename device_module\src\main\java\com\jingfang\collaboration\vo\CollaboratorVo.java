package com.jingfang.collaboration.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 协作者视图对象
 */
@Data
public class CollaboratorVo {
    
    /**
     * 协作记录ID
     */
    private String id;
    
    /**
     * 表格ID
     */
    private String spreadsheetId;

    /**
     * 表格标题
     */
    private String spreadsheetTitle;

    /**
     * 表格描述
     */
    private String spreadsheetDescription;
    
    /**
     * 协作者用户ID
     */
    private Long userId;
    
    /**
     * 协作者用户名
     */
    private String userName;
    
    /**
     * 协作者昵称
     */
    private String nickName;
    
    /**
     * 协作者头像
     */
    private String avatar;
    
    /**
     * 协作者部门ID
     */
    private Long deptId;
    
    /**
     * 协作者部门名称
     */
    private String deptName;
    
    /**
     * 权限类型（owner:所有者 editor:编辑者 commenter:评论者 viewer:查看者）
     */
    private String permission;
    
    /**
     * 权限名称
     */
    private String permissionName;
    
    /**
     * 邀请者ID
     */
    private Long inviteBy;
    
    /**
     * 邀请者姓名
     */
    private String inviteByName;
    
    /**
     * 邀请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date inviteTime;
    
    /**
     * 接受邀请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date acceptTime;
    
    /**
     * 状态（0待接受 1已接受 2已拒绝 3已移除）
     */
    private String status;
    
    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 邀请消息
     */
    private String message;
    
    /**
     * 最后访问时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastAccessTime;
    
    /**
     * 是否在线（0离线 1在线）
     */
    private String isOnline;
    
    /**
     * 备注
     */
    private String remark;
}
