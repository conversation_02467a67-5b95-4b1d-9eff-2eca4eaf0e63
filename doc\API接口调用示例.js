/**
 * 设备参数检测模块 - API接口调用示例
 * 
 * 本文件提供了所有API接口的详细调用示例
 * 前端开发人员可以参考这些示例进行开发
 */

// ================================
// 1. 设备管理相关接口
// ================================

/**
 * 获取设备列表
 */
async function getDeviceListExample() {
  try {
    const query = {
      pageNum: 1,
      pageSize: 10,
      deviceName: '', // 可选：设备名称搜索
      deviceType: '', // 可选：设备类型筛选
      deviceStatus: '' // 可选：设备状态筛选
    };
    
    const response = await getDeviceList(query);
    console.log('设备列表:', response);
    
    // 处理响应数据
    if (response.code === 200) {
      const devices = response.rows;
      const total = response.total;
      
      devices.forEach(device => {
        console.log(`设备: ${device.deviceName}, 状态: ${device.deviceStatus === 1 ? '在线' : '离线'}`);
      });
    }
  } catch (error) {
    console.error('获取设备列表失败:', error);
  }
}

/**
 * 添加新设备
 */
async function addDeviceExample() {
  try {
    const deviceData = {
      deviceName: "新设备001",
      deviceType: 1,
      deviceModel: "Model-X100",
      ipAddress: "*************",
      devicePort: 502,
      location: "车间B-区域1",
      manufacturer: "设备制造商A",
      serialNumber: "SN20240001",
      installationDate: "2024-01-15"
    };
    
    const response = await addDevice(deviceData);
    console.log('添加设备结果:', response);
    
    if (response.code === 200) {
      console.log('设备添加成功');
      // 刷新设备列表
      getDeviceListExample();
    }
  } catch (error) {
    console.error('添加设备失败:', error);
  }
}

/**
 * 获取设备详情
 */
async function getDeviceDetailExample(deviceId) {
  try {
    const response = await getDeviceDetail(deviceId);
    console.log('设备详情:', response);
    
    if (response.code === 200) {
      const device = response.data;
      console.log(`设备名称: ${device.deviceName}`);
      console.log(`IP地址: ${device.ipAddress}`);
      console.log(`设备状态: ${device.deviceStatus === 1 ? '在线' : '离线'}`);
    }
  } catch (error) {
    console.error('获取设备详情失败:', error);
  }
}

// ================================
// 2. 设备参数相关接口
// ================================

/**
 * 获取设备运行参数
 */
async function getDeviceNormalDataExample(deviceId) {
  try {
    const query = {
      pageNum: 1,
      pageSize: 20
    };
    
    const response = await getDeviceNormalData(deviceId, query);
    console.log('设备运行参数:', response);
    
    if (response.code === 200) {
      const params = response.rows;
      
      params.forEach(param => {
        console.log(`参数: ${param.paramName}, 当前值: ${param.paramValue}`);
        
        // 检查参数是否在正常范围内
        if (param.rangeStart && param.rangeEnd) {
          const numValue = parseFloat(param.paramValue);
          if (!isNaN(numValue)) {
            const isNormal = numValue >= param.rangeStart && numValue <= param.rangeEnd;
            console.log(`  正常范围: ${param.rangeStart}-${param.rangeEnd}, 状态: ${isNormal ? '正常' : '异常'}`);
          }
        }
      });
    }
  } catch (error) {
    console.error('获取设备运行参数失败:', error);
  }
}

/**
 * 获取设备告警参数
 */
async function getDeviceAlertDataExample(deviceId) {
  try {
    const query = {
      pageNum: 1,
      pageSize: 20
    };
    
    const response = await getDeviceAlertData(deviceId, query);
    console.log('设备告警参数:', response);
    
    if (response.code === 200) {
      const alerts = response.rows;
      
      alerts.forEach(alert => {
        const status = alert.alertValue ? '告警' : '正常';
        console.log(`告警项: ${alert.paramName}, 状态: ${status}`);
      });
      
      // 统计告警数量
      const alertCount = alerts.filter(alert => alert.alertValue).length;
      console.log(`总告警数量: ${alertCount}/${alerts.length}`);
    }
  } catch (error) {
    console.error('获取设备告警参数失败:', error);
  }
}

// ================================
// 3. 设备状态相关接口
// ================================

/**
 * 获取设备在线状态
 */
async function getDeviceStatusExample(deviceId) {
  try {
    const response = await getDeviceStatus(deviceId);
    console.log('设备状态:', response);
    
    if (response.code === 200) {
      const status = response.data;
      console.log(`设备ID: ${status.deviceId}`);
      console.log(`在线状态: ${status.status === 1 ? '在线' : '离线'}`);
      console.log(`最后更新时间: ${status.lastUpdateTime}`);
    }
  } catch (error) {
    console.error('获取设备状态失败:', error);
  }
}

// ================================
// 4. 测试接口
// ================================

/**
 * 测试MQTT连接状态
 */
async function testMqttConnectionExample() {
  try {
    const response = await fetch('/api/device/property/test/mqtt-connection');
    const result = await response.json();
    console.log('MQTT连接测试:', result);
    
    if (result.code === 200) {
      console.log('MQTT连接正常');
    } else {
      console.log('MQTT连接异常:', result.msg);
    }
  } catch (error) {
    console.error('MQTT连接测试失败:', error);
  }
}

/**
 * 测试设备参数查询
 */
async function testDeviceParamsExample(deviceId) {
  try {
    const response = await fetch(`/api/device/property/test/normal-params?deviceId=${deviceId}`);
    const result = await response.json();
    console.log('设备参数测试:', result);
    
    if (result.code === 200) {
      console.log('设备参数查询正常');
      console.log('参数数据:', result.data);
    } else {
      console.log('设备参数查询异常:', result.msg);
    }
  } catch (error) {
    console.error('设备参数测试失败:', error);
  }
}

/**
 * 测试MQTT属性查询
 */
async function testMqttPropertyExample() {
  try {
    const deviceIp = '*************';
    const properties = '冷却水进水温度,冷却水进水流量';
    
    const response = await fetch(`/api/device/property/test/mqtt?deviceIp=${deviceIp}&properties=${properties}`);
    const result = await response.json();
    console.log('MQTT属性查询测试:', result);
    
    if (result.code === 200) {
      console.log('MQTT属性查询正常');
      console.log('查询结果:', result.data);
    } else {
      console.log('MQTT属性查询异常:', result.msg);
    }
  } catch (error) {
    console.error('MQTT属性查询测试失败:', error);
  }
}

// ================================
// 5. 实际使用示例
// ================================

/**
 * 设备详情页面数据加载示例
 */
async function loadDeviceDetailPageExample(deviceId) {
  console.log(`加载设备详情页面，设备ID: ${deviceId}`);
  
  try {
    // 1. 获取设备基本信息
    const deviceDetail = await getDeviceDetail(deviceId);
    if (deviceDetail.code !== 200) {
      throw new Error('获取设备信息失败');
    }
    
    const device = deviceDetail.data;
    console.log('设备信息加载完成:', device.deviceName);
    
    // 2. 检查设备是否在线
    if (device.deviceStatus === 1) {
      console.log('设备在线，开始加载参数数据');
      
      // 3. 并行加载运行参数和告警参数
      const [normalData, alertData] = await Promise.all([
        getDeviceNormalData(deviceId, { pageNum: 1, pageSize: 50 }),
        getDeviceAlertData(deviceId, { pageNum: 1, pageSize: 50 })
      ]);
      
      if (normalData.code === 200) {
        console.log(`运行参数加载完成，共${normalData.rows.length}个参数`);
      }
      
      if (alertData.code === 200) {
        const alertCount = alertData.rows.filter(item => item.alertValue).length;
        console.log(`告警参数加载完成，共${alertCount}个告警`);
      }
      
      // 4. 启动自动刷新
      console.log('启动自动刷新定时器');
      setInterval(() => {
        getDeviceNormalDataExample(deviceId);
        getDeviceAlertDataExample(deviceId);
      }, 5000); // 5秒刷新一次
      
    } else {
      console.log('设备离线，不加载参数数据');
    }
    
  } catch (error) {
    console.error('加载设备详情页面失败:', error);
  }
}

/**
 * 设备列表页面数据加载示例
 */
async function loadDeviceListPageExample() {
  console.log('加载设备列表页面');
  
  try {
    // 1. 加载设备列表
    const deviceList = await getDeviceList({ pageNum: 1, pageSize: 20 });
    
    if (deviceList.code === 200) {
      console.log(`设备列表加载完成，共${deviceList.total}台设备`);
      
      // 2. 统计在线设备数量
      const onlineCount = deviceList.rows.filter(device => device.deviceStatus === 1).length;
      console.log(`在线设备: ${onlineCount}/${deviceList.rows.length}`);
      
      // 3. 定期刷新设备状态
      setInterval(async () => {
        console.log('刷新设备状态...');
        const updatedList = await getDeviceList({ pageNum: 1, pageSize: 20 });
        if (updatedList.code === 200) {
          const newOnlineCount = updatedList.rows.filter(device => device.deviceStatus === 1).length;
          console.log(`状态更新完成，在线设备: ${newOnlineCount}/${updatedList.rows.length}`);
        }
      }, 30000); // 30秒刷新一次
    }
    
  } catch (error) {
    console.error('加载设备列表页面失败:', error);
  }
}

// ================================
// 6. 错误处理示例
// ================================

/**
 * 带错误处理的API调用示例
 */
async function robustApiCallExample(deviceId) {
  const maxRetries = 3;
  let retryCount = 0;
  
  while (retryCount < maxRetries) {
    try {
      const response = await getDeviceNormalData(deviceId, { pageNum: 1, pageSize: 20 });
      
      if (response.code === 200) {
        console.log('API调用成功');
        return response.data;
      } else {
        throw new Error(`API返回错误: ${response.msg}`);
      }
      
    } catch (error) {
      retryCount++;
      console.warn(`API调用失败，重试第${retryCount}次:`, error.message);
      
      if (retryCount >= maxRetries) {
        console.error('API调用最终失败，已达到最大重试次数');
        throw error;
      }
      
      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
    }
  }
}

// ================================
// 使用说明
// ================================

/*
使用方法：

1. 在浏览器控制台中运行这些示例函数
2. 根据实际需求修改参数
3. 观察控制台输出了解API响应格式
4. 将这些示例集成到你的Vue组件中

示例：
// 测试MQTT连接
testMqttConnectionExample();

// 加载设备列表
loadDeviceListPageExample();

// 加载设备详情（假设设备ID为1）
loadDeviceDetailPageExample(1);

// 测试设备参数查询
testDeviceParamsExample(1);
*/
