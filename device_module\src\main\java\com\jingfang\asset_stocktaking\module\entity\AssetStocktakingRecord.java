package com.jingfang.asset_stocktaking.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 资产盘点记录实体类
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@TableName(value = "asset_stocktaking_record")
public class AssetStocktakingRecord implements Serializable {

    /**
     * 盘点记录ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String recordId;

    /**
     * 盘点任务ID
     */
    private String taskId;

    /**
     * 资产ID
     */
    private String assetId;

    /**
     * 资产编码
     */
    private String assetCode;

    /**
     * 发现状态：1-找到，0-未找到
     */
    private Integer foundStatus;

    /**
     * 实际位置
     */
    private String actualLocation;

    /**
     * 实际状态
     */
    private Integer actualStatus;

    /**
     * 盘点人ID
     */
    private Long inventoryUserId;

    /**
     * 盘点时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inventoryTime;

    /**
     * 备注
     */
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 发现状态常量
     */
    public static final class FoundStatus {
        /** 未找到 */
        public static final int NOT_FOUND = 0;
        /** 找到 */
        public static final int FOUND = 1;
    }

    /**
     * 资产状态常量（与资产台账保持一致）
     */
    public static final class AssetStatus {
        /** 正常 */
        public static final int NORMAL = 1;
        /** 维修中 */
        public static final int UNDER_REPAIR = 2;
        /** 报废 */
        public static final int SCRAPPED = 3;
        /** 闲置 */
        public static final int IDLE = 4;
        /** 借出 */
        public static final int LENT_OUT = 5;
    }

    /**
     * 判断资产是否找到
     * 
     * @return true-找到，false-未找到
     */
    public boolean isFound() {
        return FoundStatus.FOUND == this.foundStatus;
    }

    /**
     * 判断是否为异常记录（未找到或状态异常）
     * 
     * @return true-异常，false-正常
     */
    public boolean isAbnormal() {
        return !isFound() || (actualStatus != null && actualStatus == AssetStatus.SCRAPPED);
    }
}
