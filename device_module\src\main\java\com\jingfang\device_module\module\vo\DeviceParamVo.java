package com.jingfang.device_module.module.vo;

import lombok.Data;

@Data
public class DeviceParamVo {

    private Long paramId;

    private String paramKey;

    private String paramName;

    private Double rangeStart;

    private Double rangeEnd;

    private String paramValue;

    private boolean alertValue;

    /**
     * 读写类型（0-只读，1-只写，2-读写）
     */
    private Integer rwType;

    /**
     * 参数类型（0-一般参数，1-告警参数）
     */
    private Integer paramType;

}
