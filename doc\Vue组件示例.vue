<template>
  <div class="device-detail-page">
    <!-- 设备基本信息卡片 -->
    <el-card class="device-info-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <h2>{{ deviceInfo.deviceName || '设备详情' }}</h2>
          <el-tag 
            :type="deviceInfo.deviceStatus === 1 ? 'success' : 'danger'"
            size="large"
          >
            {{ deviceInfo.deviceStatus === 1 ? '在线' : '离线' }}
          </el-tag>
        </div>
      </template>
      
      <el-descriptions :column="3" border>
        <el-descriptions-item label="设备型号">
          {{ deviceInfo.deviceModel }}
        </el-descriptions-item>
        <el-descriptions-item label="IP地址">
          {{ deviceInfo.ipAddress }}
        </el-descriptions-item>
        <el-descriptions-item label="端口">
          {{ deviceInfo.devicePort }}
        </el-descriptions-item>
        <el-descriptions-item label="安装位置">
          {{ deviceInfo.location }}
        </el-descriptions-item>
        <el-descriptions-item label="生产厂商">
          {{ deviceInfo.manufacturer }}
        </el-descriptions-item>
        <el-descriptions-item label="序列号">
          {{ deviceInfo.serialNumber }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 参数监控标签页 -->
    <el-card class="params-card" shadow="hover">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <!-- 运行数据标签页 -->
        <el-tab-pane label="运行数据" name="operation">
          <div class="tab-header">
            <span>实时运行参数</span>
            <div class="tab-actions">
              <el-switch
                v-model="autoRefresh"
                active-text="自动刷新"
                @change="handleAutoRefreshChange"
              />
              <el-button 
                :icon="Refresh" 
                @click="refreshNormalData"
                :loading="normalDataLoading"
                size="small"
              >
                手动刷新
              </el-button>
            </div>
          </div>
          
          <el-table 
            :data="normalParams" 
            v-loading="normalDataLoading"
            stripe
            border
          >
            <el-table-column prop="paramName" label="参数名称" width="200" />
            <el-table-column prop="paramValue" label="当前值" width="150">
              <template #default="{ row }">
                <span :class="getValueClass(row)">{{ row.paramValue }}</span>
              </template>
            </el-table-column>
            <el-table-column label="正常范围" width="200">
              <template #default="{ row }">
                <span v-if="row.rangeStart && row.rangeEnd">
                  {{ row.rangeStart }} - {{ row.rangeEnd }} {{ row.paramUnit }}
                </span>
                <span v-else>--</span>
              </template>
            </el-table-column>
            <el-table-column label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row)" size="small">
                  {{ getStatusText(row) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 告警数据标签页 -->
        <el-tab-pane label="告警数据" name="alert">
          <div class="tab-header">
            <span>设备告警状态</span>
            <el-button 
              :icon="Refresh" 
              @click="refreshAlertData"
              :loading="alertDataLoading"
              size="small"
            >
              刷新告警
            </el-button>
          </div>
          
          <el-table 
            :data="alertParams" 
            v-loading="alertDataLoading"
            stripe
            border
          >
            <el-table-column prop="paramName" label="告警项目" width="200" />
            <el-table-column prop="alertValue" label="状态" width="150">
              <template #default="{ row }">
                <el-tag 
                  :type="row.alertValue ? 'danger' : 'success'"
                  :class="row.alertValue ? 'alert-blink' : ''"
                >
                  {{ row.alertValue ? '告警' : '正常' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="描述">
              <template #default="{ row }">
                <span v-if="row.alertValue" class="alert-text">
                  {{ row.paramName }}状态异常，请及时处理
                </span>
                <span v-else class="normal-text">
                  {{ row.paramName }}状态正常
                </span>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { 
  getDeviceDetail, 
  getDeviceNormalData, 
  getDeviceAlertData 
} from '@/api/monitor/device'

// 路由参数
const route = useRoute()
const deviceId = ref(route.params.id)

// 响应式数据
const deviceInfo = reactive({})
const normalParams = ref([])
const alertParams = ref([])
const activeTab = ref('operation')
const autoRefresh = ref(true)
const normalDataLoading = ref(false)
const alertDataLoading = ref(false)

// 定时器
let refreshTimer = null
const refreshInterval = 5000 // 5秒刷新间隔

// 页面加载时获取数据
onMounted(async () => {
  await loadDeviceInfo()
  await loadDeviceParams()
  handleAutoRefreshChange()
})

// 页面卸载时清理定时器
onUnmounted(() => {
  stopAutoRefresh()
})

// 监听设备状态变化
watch(() => deviceInfo.deviceStatus, (newStatus) => {
  if (newStatus === 0) {
    // 设备离线时停止自动刷新
    autoRefresh.value = false
    stopAutoRefresh()
    ElMessage.warning('设备已离线，停止自动刷新')
  }
})

/**
 * 加载设备基本信息
 */
async function loadDeviceInfo() {
  try {
    const response = await getDeviceDetail(deviceId.value)
    if (response.code === 200) {
      Object.assign(deviceInfo, response.data)
    } else {
      ElMessage.error('获取设备信息失败')
    }
  } catch (error) {
    console.error('加载设备信息失败:', error)
    ElMessage.error('获取设备信息失败，请检查网络连接')
  }
}

/**
 * 加载设备参数数据
 */
async function loadDeviceParams() {
  await Promise.all([
    refreshNormalData(),
    refreshAlertData()
  ])
}

/**
 * 刷新运行参数数据
 */
async function refreshNormalData() {
  if (normalDataLoading.value) return
  
  try {
    normalDataLoading.value = true
    const response = await getDeviceNormalData(deviceId.value, {
      pageNum: 1,
      pageSize: 50
    })
    
    if (response.code === 200) {
      normalParams.value = response.rows || []
    } else {
      ElMessage.error('获取运行参数失败')
    }
  } catch (error) {
    console.error('获取运行参数失败:', error)
    ElMessage.error('获取运行参数失败，请检查设备连接')
    // 出错时停止自动刷新
    autoRefresh.value = false
    stopAutoRefresh()
  } finally {
    normalDataLoading.value = false
  }
}

/**
 * 刷新告警参数数据
 */
async function refreshAlertData() {
  if (alertDataLoading.value) return
  
  try {
    alertDataLoading.value = true
    const response = await getDeviceAlertData(deviceId.value, {
      pageNum: 1,
      pageSize: 50
    })
    
    if (response.code === 200) {
      alertParams.value = response.rows || []
      
      // 检查是否有新告警
      const alertCount = alertParams.value.filter(item => item.alertValue).length
      if (alertCount > 0) {
        ElMessage.warning(`检测到 ${alertCount} 个告警项目`)
      }
    } else {
      ElMessage.error('获取告警参数失败')
    }
  } catch (error) {
    console.error('获取告警参数失败:', error)
    ElMessage.error('获取告警参数失败')
  } finally {
    alertDataLoading.value = false
  }
}

/**
 * 标签页切换处理
 */
function handleTabChange(tabName) {
  activeTab.value = tabName
  // 切换到运行数据标签页时重新启动自动刷新
  if (tabName === 'operation' && autoRefresh.value) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

/**
 * 自动刷新开关处理
 */
function handleAutoRefreshChange() {
  if (autoRefresh.value && deviceInfo.deviceStatus === 1 && activeTab.value === 'operation') {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

/**
 * 启动自动刷新
 */
function startAutoRefresh() {
  stopAutoRefresh() // 先停止现有定时器
  
  if (deviceInfo.deviceStatus === 1 && activeTab.value === 'operation') {
    refreshTimer = setInterval(() => {
      if (!normalDataLoading.value && !alertDataLoading.value) {
        refreshNormalData()
        refreshAlertData()
      }
    }, refreshInterval)
    console.log('自动刷新已启动')
  }
}

/**
 * 停止自动刷新
 */
function stopAutoRefresh() {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
    console.log('自动刷新已停止')
  }
}

/**
 * 获取参数值样式类
 */
function getValueClass(row) {
  if (!row.rangeStart || !row.rangeEnd) return 'param-value-normal'
  
  const numValue = parseFloat(row.paramValue)
  if (isNaN(numValue)) return 'param-value-normal'
  
  if (numValue < row.rangeStart || numValue > row.rangeEnd) {
    return 'param-value-abnormal'
  }
  return 'param-value-normal'
}

/**
 * 获取状态标签类型
 */
function getStatusType(row) {
  if (!row.rangeStart || !row.rangeEnd) return 'info'
  
  const numValue = parseFloat(row.paramValue)
  if (isNaN(numValue)) return 'info'
  
  if (numValue < row.rangeStart || numValue > row.rangeEnd) {
    return 'danger'
  }
  return 'success'
}

/**
 * 获取状态文本
 */
function getStatusText(row) {
  if (!row.rangeStart || !row.rangeEnd) return '未知'
  
  const numValue = parseFloat(row.paramValue)
  if (isNaN(numValue)) return '未知'
  
  if (numValue < row.rangeStart || numValue > row.rangeEnd) {
    return '异常'
  }
  return '正常'
}
</script>

<style scoped>
.device-detail-page {
  padding: 20px;
}

.device-info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: #303133;
}

.params-card {
  min-height: 500px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.tab-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* 参数值样式 */
.param-value-normal {
  color: #67C23A;
  font-weight: bold;
}

.param-value-abnormal {
  color: #F56C6C;
  font-weight: bold;
  animation: blink 1s infinite;
}

/* 告警闪烁效果 */
.alert-blink {
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.6; }
}

.alert-text {
  color: #F56C6C;
  font-weight: bold;
}

.normal-text {
  color: #67C23A;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .device-detail-page {
    padding: 10px;
  }
  
  .tab-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .tab-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
