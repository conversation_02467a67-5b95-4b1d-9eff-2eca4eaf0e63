package com.jingfang.web.controller.device;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.common.core.page.TableDataInfo;
import com.jingfang.device_module.module.dto.DeviceAddDto;
import com.jingfang.device_module.module.dto.DeviceInfoDto;
import com.jingfang.device_module.module.dto.NormalParamAddDto;
import com.jingfang.device_module.module.entity.DeviceInfo;
import com.jingfang.device_module.module.vo.DeviceInfoVo;
import com.jingfang.device_module.module.vo.DeviceParamVo;
import com.jingfang.device_module.request.DeviceInfoSearchRequest;
import com.jingfang.device_module.service.DeviceInfoService;
import com.jingfang.device_module.service.DeviceParamService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/device")
public class DeviceController extends BaseController {

    @Resource
    private DeviceInfoService deviceService;

    @Resource
    private DeviceParamService paramService;


//    @PostMapping("/list")
//    public Page<DeviceInfo> list(@RequestBody DeviceInfoSearchRequest request) {
//        return deviceService.showList(request);
//    }


    @PostMapping("/add")
    public AjaxResult addNewDevice(@RequestBody DeviceInfoDto dto) {
        if(deviceService.adeNewDevice(dto)){
            return AjaxResult.success("add success");
        }else {
            return AjaxResult.error("add failed");
        }
    }

    @GetMapping("/add/picture")
    public AjaxResult addPictureUrl(Long deviceId,String pictureUrl) {
        return AjaxResult.success(deviceService.addPictureUrl(deviceId,pictureUrl));
    }

    @DeleteMapping("/delete/picture")
    public AjaxResult deletePictureUrl(Long deviceId,String pictureUrl) {
        return AjaxResult.success(deviceService.deletePictureUrl(deviceId,pictureUrl));
    }

    @PostMapping("/list")
    public Page<DeviceInfoVo> showDeviceInfoList(@RequestBody DeviceInfoSearchRequest request){
        try {
            return deviceService.showDeviceInfoList(request);
        }catch (Exception e){
            logger.error(e.getMessage());
            return null;
        }
    }


    @GetMapping("/list/detail")
    public AjaxResult showDeviceInfoDetail(Long deviceId){
        try {
            return AjaxResult.success(deviceService.showDeviceBaseInfo(deviceId));
        }catch (Exception e){
            logger.error(e.getMessage());
            return AjaxResult.error("show device info failed");
        }
    }


    @GetMapping("/refresh")
    public AjaxResult refreshCatch(){
        return AjaxResult.success(deviceService.refreshCatch());
    }


    @PostMapping("/list/detail/add/normal")
    public AjaxResult addDeviceParamTypeOne(@RequestBody NormalParamAddDto dto){
        try {
            return AjaxResult.success(paramService.addDeviceParamTypeOne(dto));
        }catch (Exception e){
            logger.error(e.getMessage());
            return AjaxResult.error("add failed");
        }
    }

    @PostMapping("/list/detail/add/alert")
    public AjaxResult addDeviceParamTypeTwo(@RequestBody DeviceAddDto dto){
        try {
            return AjaxResult.success(paramService.addDeviceParamTypeTwo(dto));
        }catch (Exception e){
            logger.error(e.getMessage());
            return AjaxResult.error("add failed");
        }
    }

    @GetMapping("/list/detail/normal")
    public TableDataInfo showDeviceParamTypeOne(Long deviceId){
        List<DeviceParamVo> vos = paramService.showDeviceParamTypeOne(deviceId);
        return getDataTable(vos);
    }

    @GetMapping("/list/detail/alert")
    public TableDataInfo showDeviceParamTypeTwo(Long deviceId){
        List<DeviceParamVo> vos = paramService.showDeviceParamTypeTwo(deviceId);
        return getDataTable(vos);
    }

    /**
     * 获取设备告警信息（新接口）
     */
    @GetMapping("/alert/info")
    public TableDataInfo getDeviceAlertInfo(Long deviceId){
        try {
            List<DeviceParamVo> vos = paramService.getDeviceAlertInfo(deviceId);
            return getDataTable(vos);
        } catch (Exception e) {
            logger.error("获取设备告警信息失败", e);
            return getDataTable(new ArrayList<>());
        }
    }

    /**
     * 获取设备控制参数
     */
    @GetMapping("/control/params")
    public TableDataInfo getDeviceControlParams(Long deviceId){
        try {
            List<DeviceParamVo> vos = paramService.getDeviceControlParams(deviceId);
            return getDataTable(vos);
        } catch (Exception e) {
            logger.error("获取设备控制参数失败", e);
            return getDataTable(new ArrayList<>());
        }
    }

    /**
     * 写入设备参数
     */
    @PostMapping("/param/write")
    public AjaxResult writeDeviceParam(@RequestParam Long deviceId,
                                     @RequestParam String paramName,
                                     @RequestParam String paramValue){
        try {
            boolean success = paramService.writeDeviceParam(deviceId, paramName, paramValue);
            if (success) {
                return AjaxResult.success("参数写入成功");
            } else {
                return AjaxResult.error("参数写入失败");
            }
        } catch (Exception e) {
            logger.error("写入设备参数失败", e);
            return AjaxResult.error("参数写入异常: " + e.getMessage());
        }
    }

    /**
     * 批量写入设备参数
     */
    @PostMapping("/params/write")
    public AjaxResult writeDeviceParams(@RequestParam Long deviceId,
                                      @RequestBody Map<String, String> paramMap){
        try {
            boolean success = paramService.writeDeviceParams(deviceId, paramMap);
            if (success) {
                return AjaxResult.success("参数批量写入成功");
            } else {
                return AjaxResult.error("参数批量写入失败");
            }
        } catch (Exception e) {
            logger.error("批量写入设备参数失败", e);
            return AjaxResult.error("参数批量写入异常: " + e.getMessage());
        }
    }

    @GetMapping("/aaa/bbb/ccc")
    public AjaxResult test01(){
        paramService.modbusConnectTest();
        return AjaxResult.success("11");
    }


}
