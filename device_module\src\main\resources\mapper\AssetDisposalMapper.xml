<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.asset_disposal.mapper.AssetDisposalMapper">

    <resultMap id="BaseResultMap" type="com.jingfang.asset_disposal.module.entity.AssetDisposal">
        <id property="disposalId" column="disposal_id" jdbcType="VARCHAR"/>
        <result property="disposalTitle" column="disposal_title" jdbcType="VARCHAR"/>
        <result property="assetId" column="asset_id" jdbcType="VARCHAR"/>
        <result property="disposalType" column="disposal_type" jdbcType="INTEGER"/>
        <result property="disposalReason" column="disposal_reason" jdbcType="LONGVARCHAR"/>
        <result property="applicantId" column="applicant_id" jdbcType="BIGINT"/>
        <result property="applyTime" column="apply_time" jdbcType="TIMESTAMP"/>
        <result property="expectedDisposalTime" column="expected_disposal_time" jdbcType="TIMESTAMP"/>
        <result property="currentValue" column="current_value" jdbcType="DECIMAL"/>
        <result property="disposalValue" column="disposal_value" jdbcType="DECIMAL"/>
        <result property="buyerInfo" column="buyer_info" jdbcType="VARCHAR"/>
        <result property="transferDeptId" column="transfer_dept_id" jdbcType="BIGINT"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        disposal_id, disposal_title, asset_id, disposal_type, disposal_reason, applicant_id,
        apply_time, expected_disposal_time, current_value, disposal_value, buyer_info,
        transfer_dept_id, status, create_time, update_time, create_by, update_by, deleted
    </sql>

    <!-- 分页查询资产处置申请 -->
    <select id="selectDisposalList" resultType="com.jingfang.asset_disposal.module.vo.AssetDisposalVo">
        SELECT 
            d.disposal_id,
            d.disposal_title,
            d.asset_id,
            al.asset_name,
            d.disposal_type,
            CASE d.disposal_type
                WHEN 1 THEN '报废'
                WHEN 2 THEN '调拨'
                WHEN 3 THEN '出售'
                WHEN 4 THEN '其他'
                ELSE '未知'
            END as disposal_type_name,
            d.disposal_reason,
            d.applicant_id,
            su.nick_name as applicant_name,
            d.apply_time,
            d.expected_disposal_time,
            d.current_value,
            d.disposal_value,
            d.buyer_info,
            d.transfer_dept_id,
            sd.dept_name as transfer_dept_name,
            d.status,
            CASE d.status
                WHEN 1 THEN '草稿'
                WHEN 2 THEN '待审核'
                WHEN 3 THEN '审核中'
                WHEN 4 THEN '审核通过'
                WHEN 5 THEN '审核拒绝'
                WHEN 6 THEN '处置中'
                WHEN 7 THEN '已完成'
                WHEN 8 THEN '已取消'
                ELSE '未知'
            END as status_name,
            d.create_time,
            d.create_by
        FROM asset_disposal d
        LEFT JOIN asset_ledger al ON d.asset_id = al.asset_id
        LEFT JOIN sys_user su ON d.applicant_id = su.user_id
        LEFT JOIN sys_dept sd ON d.transfer_dept_id = sd.dept_id
        WHERE d.deleted = 0
        <if test="request.disposalId != null and request.disposalId != ''">
            AND d.disposal_id = #{request.disposalId}
        </if>
        <if test="request.disposalTitle != null and request.disposalTitle != ''">
            AND d.disposal_title LIKE CONCAT('%', #{request.disposalTitle}, '%')
        </if>
        <if test="request.assetId != null and request.assetId != ''">
            AND d.asset_id = #{request.assetId}
        </if>
        <if test="request.assetName != null and request.assetName != ''">
            AND al.asset_name LIKE CONCAT('%', #{request.assetName}, '%')
        </if>
        <if test="request.disposalType != null">
            AND d.disposal_type = #{request.disposalType}
        </if>
        <if test="request.applicantId != null">
            AND d.applicant_id = #{request.applicantId}
        </if>
        <if test="request.statusList != null and request.statusList.size() > 0">
            AND d.status IN
            <foreach collection="request.statusList" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="request.applyTimeStart != null">
            AND d.apply_time >= #{request.applyTimeStart}
        </if>
        <if test="request.applyTimeEnd != null">
            AND d.apply_time &lt;= #{request.applyTimeEnd}
        </if>
        <if test="request.expectedDisposalTimeStart != null">
            AND d.expected_disposal_time >= #{request.expectedDisposalTimeStart}
        </if>
        <if test="request.expectedDisposalTimeEnd != null">
            AND d.expected_disposal_time &lt;= #{request.expectedDisposalTimeEnd}
        </if>
        <if test="request.transferDeptId != null">
            AND d.transfer_dept_id = #{request.transferDeptId}
        </if>
        <if test="request.createBy != null and request.createBy != ''">
            AND d.create_by = #{request.createBy}
        </if>
        ORDER BY d.create_time DESC
    </select>

    <!-- 根据ID查询资产处置详情 -->
    <select id="selectDisposalById" resultType="com.jingfang.asset_disposal.module.vo.AssetDisposalVo">
        SELECT 
            d.disposal_id,
            d.disposal_title,
            d.asset_id,
            al.asset_name,
            d.disposal_type,
            CASE d.disposal_type
                WHEN 1 THEN '报废'
                WHEN 2 THEN '调拨'
                WHEN 3 THEN '出售'
                WHEN 4 THEN '其他'
                ELSE '未知'
            END as disposal_type_name,
            d.disposal_reason,
            d.applicant_id,
            su.nick_name as applicant_name,
            d.apply_time,
            d.expected_disposal_time,
            d.current_value,
            d.disposal_value,
            d.buyer_info,
            d.transfer_dept_id,
            sd.dept_name as transfer_dept_name,
            d.status,
            CASE d.status
                WHEN 1 THEN '草稿'
                WHEN 2 THEN '待审核'
                WHEN 3 THEN '审核中'
                WHEN 4 THEN '审核通过'
                WHEN 5 THEN '审核拒绝'
                WHEN 6 THEN '处置中'
                WHEN 7 THEN '已完成'
                WHEN 8 THEN '已取消'
                ELSE '未知'
            END as status_name,
            d.create_time,
            d.create_by
        FROM asset_disposal d
        LEFT JOIN asset_ledger al ON d.asset_id = al.asset_id
        LEFT JOIN sys_user su ON d.applicant_id = su.user_id
        LEFT JOIN sys_dept sd ON d.transfer_dept_id = sd.dept_id
        WHERE d.deleted = 0 AND d.disposal_id = #{disposalId}
    </select>

    <!-- 查询待审批的处置申请 -->
    <select id="selectPendingApprovalList" resultType="com.jingfang.asset_disposal.module.vo.AssetDisposalVo">
        SELECT 
            d.disposal_id,
            d.disposal_title,
            d.asset_id,
            al.asset_name,
            d.disposal_type,
            CASE d.disposal_type
                WHEN 1 THEN '报废'
                WHEN 2 THEN '调拨'
                WHEN 3 THEN '出售'
                WHEN 4 THEN '其他'
                ELSE '未知'
            END as disposal_type_name,
            d.disposal_reason,
            d.applicant_id,
            su.nick_name as applicant_name,
            d.apply_time,
            d.expected_disposal_time,
            d.current_value,
            d.disposal_value,
            d.status,
            CASE d.status
                WHEN 1 THEN '草稿'
                WHEN 2 THEN '待审核'
                WHEN 3 THEN '审核中'
                WHEN 4 THEN '审核通过'
                WHEN 5 THEN '审核拒绝'
                WHEN 6 THEN '处置中'
                WHEN 7 THEN '已完成'
                WHEN 8 THEN '已取消'
                ELSE '未知'
            END as status_name,
            d.create_time,
            d.create_by
        FROM asset_disposal d
        LEFT JOIN asset_ledger al ON d.asset_id = al.asset_id
        LEFT JOIN sys_user su ON d.applicant_id = su.user_id
        LEFT JOIN asset_disposal_approval da ON d.disposal_id = da.disposal_id
        WHERE d.deleted = 0 
            AND d.status IN (2, 3)
            AND da.approver_id = #{userId}
            AND da.approval_status = 1
        ORDER BY d.create_time DESC
    </select>

    <!-- 更新处置状态 -->
    <update id="updateDisposalStatus">
        UPDATE asset_disposal 
        SET status = #{status}, update_time = NOW()
        WHERE disposal_id = #{disposalId}
    </update>

    <!-- 统计各状态的处置数量 -->
    <select id="selectDisposalStatistics" resultType="map">
        SELECT
            status,
            COUNT(*) as count
        FROM asset_disposal
        WHERE deleted = 0
        GROUP BY status
    </select>

    <!-- 工作台统计查询 -->
    <select id="countPendingApproval" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM asset_disposal
        WHERE deleted = 0
        AND status IN (2, 3)
    </select>

    <select id="countProcessing" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM asset_disposal
        WHERE deleted = 0
        AND status = 6
    </select>

    <select id="countMonthlyCompleted" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM asset_disposal
        WHERE deleted = 0
        AND status = 7
        AND update_time >= #{startDate}
        AND update_time &lt; #{endDate}
    </select>

    <select id="sumMonthlyDisposalValue" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(disposal_value), 0)
        FROM asset_disposal
        WHERE deleted = 0
        AND status = 7
        AND update_time >= #{startDate}
        AND update_time &lt; #{endDate}
    </select>

    <select id="selectDisposalByType" resultType="java.util.Map">
        SELECT
            disposal_type,
            CASE disposal_type
                WHEN 1 THEN '报废'
                WHEN 2 THEN '调拨'
                WHEN 3 THEN '出售'
                WHEN 4 THEN '其他'
                ELSE '未知'
            END as typeName,
            COUNT(1) as count,
            COALESCE(SUM(disposal_value), 0) as totalValue
        FROM asset_disposal
        WHERE deleted = 0
        GROUP BY disposal_type
    </select>

    <select id="selectDisposalTrends" resultType="java.util.Map">
        SELECT
            DATE_FORMAT(create_time, '%Y-%m-%d') as date,
            COUNT(1) as applicationCount,
            SUM(CASE WHEN status = 7 THEN 1 ELSE 0 END) as completedCount,
            COALESCE(SUM(CASE WHEN status = 7 THEN disposal_value ELSE 0 END), 0) as disposalValue
        FROM asset_disposal
        WHERE deleted = 0
        AND create_time >= #{startDate}
        AND create_time &lt; #{endDate}
        GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d')
        ORDER BY date
    </select>

</mapper>