package com.jingfang.maintenance_task.module.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.jingfang.common.utils.SecurityUtils;
import com.jingfang.common.utils.StringUtils;
import com.jingfang.common.utils.uuid.IdUtils;
import com.jingfang.maintenance_plan.mapper.MaintenancePlanMapper;
import com.jingfang.maintenance_plan.mapper.MaintenancePlanPartMapper;
import com.jingfang.maintenance_plan.module.entity.MaintenancePlan;
import com.jingfang.maintenance_plan.module.entity.MaintenancePlanPart;
import com.jingfang.maintenance_task.module.dto.MaintenanceTaskDto;
import com.jingfang.maintenance_task.module.entity.MaintenanceTask;
import com.jingfang.maintenance_task.module.entity.MaintenanceTaskPart;
import com.jingfang.maintenance_task.module.mapper.MaintenanceTaskMapper;
import com.jingfang.maintenance_task.module.mapper.MaintenanceTaskPartMapper;
import com.jingfang.maintenance_task.module.request.MaintenanceTaskSearchRequest;
import com.jingfang.maintenance_task.module.service.MaintenanceTaskService;
import com.jingfang.maintenance_task.module.vo.MaintenanceTaskVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 维护任务服务实现类
 */
@Slf4j
@Service
public class MaintenanceTaskServiceImpl extends ServiceImpl<MaintenanceTaskMapper, MaintenanceTask> implements MaintenanceTaskService {
    
    @Autowired
    private MaintenanceTaskMapper maintenanceTaskMapper;
    
    @Autowired
    private MaintenanceTaskPartMapper maintenanceTaskPartMapper;
    
    @Autowired
    private MaintenancePlanMapper maintenancePlanMapper;
    
    @Autowired
    private MaintenancePlanPartMapper maintenancePlanPartMapper;
    
    @Override
    public IPage<MaintenanceTaskVo> getTaskPage(MaintenanceTaskSearchRequest request) {
        Page<MaintenanceTaskVo> page = new Page<>(request.getPageNum(), request.getPageSize());
        Long currentUserId = SecurityUtils.getUserId();
        return maintenanceTaskMapper.selectTaskPage(page, request, currentUserId);
    }
    
    @Override
    public MaintenanceTaskVo getTaskById(String taskId) {
        MaintenanceTaskVo taskVo = maintenanceTaskMapper.selectTaskById(taskId);
        if (taskVo != null) {
            // 查询备品备件使用记录
            List<MaintenanceTaskVo.MaintenanceTaskPartVo> partList = maintenanceTaskPartMapper.selectPartsByTaskId(taskId);
            taskVo.setPartList(partList);
        }
        return taskVo;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addTask(MaintenanceTaskDto taskDto) {
        String taskId = IdUtils.fastSimpleUUID();
        taskDto.setTaskId(taskId);
        
        // 转换为实体类
        MaintenanceTask task = taskDto.toEntity();
        task.setTaskId(taskId);
        task.setStatus(1); // 待执行
        task.setCreateTime(new Date());
        task.setCreateBy(SecurityUtils.getUsername());
        task.setDeleted(0);
        
        // 保存任务
        this.save(task);
        
        // 保存备品备件使用记录
        if (!CollectionUtils.isEmpty(taskDto.getPartList())) {
            List<MaintenanceTaskPart> partList = taskDto.getPartList().stream().map(partDto -> {
                MaintenanceTaskPart part = new MaintenanceTaskPart();
                part.setRecordId(IdUtils.fastSimpleUUID());
                part.setTaskId(taskId);
                part.setPartId(partDto.getPartId());
                part.setPlannedQuantity(partDto.getPlannedQuantity());
                part.setUseStatus(1); // 计划使用
                part.setRemark(partDto.getRemark());
                part.setCreateTime(new Date());
                part.setCreateBy(SecurityUtils.getUsername());
                part.setDeleted(0);
                return part;
            }).collect(Collectors.toList());
            
            maintenanceTaskPartMapper.batchInsert(partList);
        }
        
        return taskId;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTask(MaintenanceTaskDto taskDto) {
        MaintenanceTask task = taskDto.toEntity();
        task.setUpdateTime(new Date());
        task.setUpdateBy(SecurityUtils.getUsername());
        
        // 更新任务
        boolean result = this.updateById(task);
        
        if (result && !CollectionUtils.isEmpty(taskDto.getPartList())) {
            // 删除原有备品备件记录
            maintenanceTaskPartMapper.deleteByTaskId(taskDto.getTaskId());
            
            // 重新插入备品备件记录
            List<MaintenanceTaskPart> partList = taskDto.getPartList().stream().map(partDto -> {
                MaintenanceTaskPart part = new MaintenanceTaskPart();
                part.setRecordId(StringUtils.isNotEmpty(partDto.getRecordId()) ? partDto.getRecordId() : IdUtils.fastSimpleUUID());
                part.setTaskId(taskDto.getTaskId());
                part.setPartId(partDto.getPartId());
                part.setPlannedQuantity(partDto.getPlannedQuantity());
                part.setActualQuantity(partDto.getActualQuantity());
                part.setUseStatus(partDto.getUseStatus());
                part.setRemark(partDto.getRemark());
                part.setUpdateTime(new Date());
                part.setUpdateBy(SecurityUtils.getUsername());
                part.setDeleted(0);
                return part;
            }).collect(Collectors.toList());
            
            maintenanceTaskPartMapper.batchInsert(partList);
        }
        
        return result;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTask(String taskId) {
        // 删除备品备件记录
        maintenanceTaskPartMapper.deleteByTaskId(taskId);
        
        // 删除任务
        return this.removeById(taskId);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTasks(List<String> taskIds) {
        if (CollectionUtils.isEmpty(taskIds)) {
            return false;
        }
        
        // 批量删除备品备件记录
        for (String taskId : taskIds) {
            maintenanceTaskPartMapper.deleteByTaskId(taskId);
        }
        
        // 批量删除任务
        return this.removeByIds(taskIds);
    }
    
    @Override
    public boolean startTask(String taskId) {
        MaintenanceTask task = new MaintenanceTask();
        task.setTaskId(taskId);
        task.setStatus(2); // 执行中
        task.setActualStartTime(new Date());
        task.setExecutorId(SecurityUtils.getUserId());
        task.setUpdateTime(new Date());
        task.setUpdateBy(SecurityUtils.getUsername());
        
        return this.updateById(task);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDraft(MaintenanceTaskDto taskDto) {
        MaintenanceTask task = taskDto.toEntity();
        task.setStatus(3); // 草稿
        task.setUpdateTime(new Date());
        task.setUpdateBy(SecurityUtils.getUsername());
        
        boolean result = this.updateById(task);
        
        // 更新备品备件使用记录
        if (result && !CollectionUtils.isEmpty(taskDto.getPartList())) {
            List<MaintenanceTaskPart> partList = taskDto.getPartList().stream().map(partDto -> {
                MaintenanceTaskPart part = new MaintenanceTaskPart();
                part.setRecordId(partDto.getRecordId());
                part.setActualQuantity(partDto.getActualQuantity());
                part.setUseStatus(partDto.getUseStatus());
                part.setRemark(partDto.getRemark());
                part.setUpdateTime(new Date());
                part.setUpdateBy(SecurityUtils.getUsername());
                return part;
            }).collect(Collectors.toList());
            
            maintenanceTaskPartMapper.batchUpdate(partList);
        }
        
        return result;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submitTask(MaintenanceTaskDto taskDto) {
        MaintenanceTask task = taskDto.toEntity();
        task.setStatus(4); // 待审核
        task.setActualEndTime(new Date());
        task.setUpdateTime(new Date());
        task.setUpdateBy(SecurityUtils.getUsername());
        
        boolean result = this.updateById(task);
        
        // 更新备品备件使用记录
        if (result && !CollectionUtils.isEmpty(taskDto.getPartList())) {
            List<MaintenanceTaskPart> partList = taskDto.getPartList().stream().map(partDto -> {
                MaintenanceTaskPart part = new MaintenanceTaskPart();
                part.setRecordId(partDto.getRecordId());
                part.setActualQuantity(partDto.getActualQuantity());
                part.setUseStatus(2); // 已使用
                part.setRemark(partDto.getRemark());
                part.setUpdateTime(new Date());
                part.setUpdateBy(SecurityUtils.getUsername());
                return part;
            }).collect(Collectors.toList());
            
            maintenanceTaskPartMapper.batchUpdate(partList);
        }
        
        return result;
    }
    
    @Override
    public boolean reviewTask(String taskId, Integer reviewResult, String reviewComment) {
        MaintenanceTask task = new MaintenanceTask();
        task.setTaskId(taskId);
        task.setStatus(reviewResult == 1 ? 5 : 6); // 1-审核通过, 0-审核不通过
        task.setReviewerId(SecurityUtils.getUserId());
        task.setReviewTime(new Date());
        task.setReviewComment(reviewComment);
        task.setUpdateTime(new Date());
        task.setUpdateBy(SecurityUtils.getUsername());
        
        // 如果审核通过，状态改为已完成
        if (reviewResult == 1) {
            task.setStatus(7); // 已完成
        }
        
        return this.updateById(task);
    }
    
    @Override
    public boolean delegateTask(String taskId, Integer responsibleType, Long responsibleId, String delegateReason) {
        return maintenanceTaskMapper.delegateTask(taskId, responsibleType, responsibleId, 
                delegateReason, SecurityUtils.getUserId(), SecurityUtils.getUsername()) > 0;
    }
    
    @Override
    public boolean cancelTask(String taskId, String cancelReason) {
        MaintenanceTask task = new MaintenanceTask();
        task.setTaskId(taskId);
        task.setStatus(8); // 已取消
        task.setRemark(cancelReason);
        task.setUpdateTime(new Date());
        task.setUpdateBy(SecurityUtils.getUsername());
        
        return this.updateById(task);
    }
    
    @Override
    public List<MaintenanceTaskVo> getMyTasks(List<Integer> statusList) {
        Long userId = SecurityUtils.getUserId();
        return maintenanceTaskMapper.selectTasksByUser(userId, statusList);
    }
    
    @Override
    public List<MaintenanceTaskVo> getPendingReviewTasks() {
        Long userId = SecurityUtils.getUserId();
        return maintenanceTaskMapper.selectPendingReviewTasks(userId);
    }
    
    @Override
    public List<MaintenanceTaskVo> getUpcomingTasks(Integer days) {
        return maintenanceTaskMapper.selectUpcomingTasks(days);
    }
    
    @Override
    public List<MaintenanceTaskVo> getOverdueTasks() {
        return maintenanceTaskMapper.selectOverdueTasks();
    }
    
    @Override
    public List<MaintenanceTaskVo> getTasksByPlanId(String planId) {
        return maintenanceTaskMapper.selectTasksByPlanId(planId);
    }
    
    @Override
    public List<MaintenanceTaskVo> getTasksByAssetId(String assetId) {
        return maintenanceTaskMapper.selectTasksByAssetId(assetId);
    }
    
    @Override
    public Map<String, Integer> getTaskStatisticsByAssetId(String assetId) {
        Map<String, Integer> statistics = new HashMap<>();
        
        // 查询该资产的所有任务
        List<MaintenanceTaskVo> tasks = maintenanceTaskMapper.selectTasksByAssetId(assetId);
        
        // 统计各种状态的任务数量
        int totalTasks = tasks.size();
        int pendingTasks = 0;
        int inProgressTasks = 0;
        int completedTasks = 0;
        int overdueTasks = 0;
        
        Date now = new Date();
        for (MaintenanceTaskVo task : tasks) {
            switch (task.getStatus()) {
                case 1: // 待执行
                    pendingTasks++;
                    if (task.getScheduledTime() != null && task.getScheduledTime().before(now)) {
                        overdueTasks++;
                    }
                    break;
                case 2: // 执行中
                    inProgressTasks++;
                    if (task.getScheduledTime() != null && task.getScheduledTime().before(now)) {
                        overdueTasks++;
                    }
                    break;
                case 7: // 已完成
                    completedTasks++;
                    break;
            }
        }
        
        statistics.put("totalTasks", totalTasks);
        statistics.put("pendingTasks", pendingTasks);
        statistics.put("inProgressTasks", inProgressTasks);
        statistics.put("completedTasks", completedTasks);
        statistics.put("overdueTasks", overdueTasks);
        
        return statistics;
    }
    
    @Override
    public Map<String, Integer> getTaskStatistics() {
        Long userId = SecurityUtils.getUserId();
        Map<String, Integer> statistics = new HashMap<>();
        
        statistics.put("pending", maintenanceTaskMapper.countTasksByStatus(1, userId));
        statistics.put("inProgress", maintenanceTaskMapper.countTasksByStatus(2, userId));
        statistics.put("draft", maintenanceTaskMapper.countTasksByStatus(3, userId));
        statistics.put("pendingReview", maintenanceTaskMapper.countTasksByStatus(4, userId));
        statistics.put("completed", maintenanceTaskMapper.countTasksByStatus(7, userId));
        statistics.put("overdue", maintenanceTaskMapper.selectOverdueTasks().size());
        
        return statistics;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateMaintenanceTasks() {
        log.info("开始生成维护任务...");
        
        try {
            // 查询所有启用的维护计划
            LambdaQueryWrapper<MaintenancePlan> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MaintenancePlan::getStatus, 1) // 启用状态
                   .eq(MaintenancePlan::getDeleted, 0);
            
            List<MaintenancePlan> planList = maintenancePlanMapper.selectList(wrapper);
            
            if (CollectionUtils.isEmpty(planList)) {
                log.info("没有找到启用的维护计划");
                return;
            }
            
            Date currentTime = new Date();
            List<MaintenanceTask> tasksToGenerate = new ArrayList<>();
            
            for (MaintenancePlan plan : planList) {
                // 检查是否需要生成任务
                if (shouldGenerateTask(plan, currentTime)) {
                    MaintenanceTask task = createTaskFromPlan(plan, currentTime);
                    tasksToGenerate.add(task);
                }
            }
            
            if (!CollectionUtils.isEmpty(tasksToGenerate)) {
                // 批量插入任务
                maintenanceTaskMapper.batchInsert(tasksToGenerate);
                
                // 为每个任务生成备品备件记录，并更新维护计划的下次维护时间
                for (MaintenanceTask task : tasksToGenerate) {
                    generateTaskParts(task.getTaskId(), task.getPlanId());
                    
                    // 更新维护计划的下次维护时间
                    updatePlanNextMaintenanceTime(task.getPlanId());
                }
                
                log.info("成功生成{}个维护任务", tasksToGenerate.size());
            } else {
                log.info("当前没有需要生成的维护任务");
            }
            
        } catch (Exception e) {
            log.error("生成维护任务失败", e);
            throw e;
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean generateTaskFromPlan(String planId) {
        MaintenancePlan plan = maintenancePlanMapper.selectById(planId);
        if (plan == null || plan.getStatus() != 1) {
            return false;
        }
        
        Date currentTime = new Date();
        MaintenanceTask task = createTaskFromPlan(plan, currentTime);
        
        // 保存任务
        this.save(task);
        
        // 生成备品备件记录
        generateTaskParts(task.getTaskId(), planId);
        
        // 更新维护计划的下次维护时间
        updatePlanNextMaintenanceTime(planId);
        
        return true;
    }
    
    /**
     * 判断是否需要生成任务
     */
    private boolean shouldGenerateTask(MaintenancePlan plan, Date currentTime) {
        // 获取计划的下次维护时间
        Date nextMaintenanceTime = plan.getNextMaintenanceTime();
        
        // 如果没有设置下次维护时间，则计算一个
        if (nextMaintenanceTime == null) {
            nextMaintenanceTime = calculateNextMaintenanceTime(plan);
        }
        
        // 如果下次维护时间已到或已过，则需要生成任务
        return nextMaintenanceTime != null && !nextMaintenanceTime.after(currentTime);
    }
    
    /**
     * 计算下次维护时间
     */
    private Date calculateNextMaintenanceTime(MaintenancePlan plan) {
        // 如果已经有下次维护时间，直接返回
        if (plan.getNextMaintenanceTime() != null) {
            return plan.getNextMaintenanceTime();
        }
        
        // 否则基于创建时间计算
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(plan.getCreateTime());
        
        Integer cycleValue = plan.getCycleValue();
        Integer cycleType = plan.getCycleType();
        
        switch (cycleType) {
            case 1: // 按天
                calendar.add(Calendar.DAY_OF_MONTH, cycleValue);
                break;
            case 2: // 按周
                calendar.add(Calendar.WEEK_OF_YEAR, cycleValue);
                break;
            case 3: // 按月
                calendar.add(Calendar.MONTH, cycleValue);
                break;
            case 4: // 按年
                calendar.add(Calendar.YEAR, cycleValue);
                break;
            default:
                return null;
        }
        
        return calendar.getTime();
    }
    
    /**
     * 根据维护计划创建任务
     */
    private MaintenanceTask createTaskFromPlan(MaintenancePlan plan, Date currentTime) {
        MaintenanceTask task = new MaintenanceTask();
        task.setTaskId(IdUtils.fastSimpleUUID());
        task.setPlanId(plan.getPlanId());
        
        // 生成任务标题，格式：计划名称 - 年月日
        String dateStr = new java.text.SimpleDateFormat("yyyy-MM-dd").format(currentTime);
        task.setTaskTitle(plan.getPlanName() + " - " + dateStr);
        
        task.setAssetId(plan.getAssetId());
        task.setMaintenanceItems(plan.getMaintenanceItems());
        task.setScheduledTime(plan.getNextMaintenanceTime() != null ? plan.getNextMaintenanceTime() : currentTime);
        task.setResponsibleType(plan.getResponsibleType());
        task.setResponsibleId(plan.getResponsibleId());
        task.setStatus(1); // 待执行
        task.setPriority(2); // 中等优先级
        task.setCreateTime(currentTime);
        task.setCreateBy("system");
        task.setDeleted(0);
        
        return task;
    }
    
    /**
     * 生成任务备品备件记录
     */
    private void generateTaskParts(String taskId, String planId) {
        // 查询计划的备品备件
        LambdaQueryWrapper<MaintenancePlanPart> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MaintenancePlanPart::getPlanId, planId)
               .eq(MaintenancePlanPart::getDeleted, 0);
        
        List<MaintenancePlanPart> planParts = maintenancePlanPartMapper.selectList(wrapper);
        
        if (!CollectionUtils.isEmpty(planParts)) {
            List<MaintenanceTaskPart> taskParts = planParts.stream().map(planPart -> {
                MaintenanceTaskPart taskPart = new MaintenanceTaskPart();
                taskPart.setRecordId(IdUtils.fastSimpleUUID());
                taskPart.setTaskId(taskId);
                taskPart.setPartId(planPart.getPartId());
                taskPart.setPlannedQuantity(planPart.getRequiredQuantity()); // 修正方法名
                taskPart.setUseStatus(1); // 计划使用
                taskPart.setCreateTime(new Date());
                taskPart.setCreateBy("system");
                taskPart.setDeleted(0);
                return taskPart;
            }).collect(Collectors.toList());
            
            maintenanceTaskPartMapper.batchInsert(taskParts);
        }
    }
    
    /**
     * 更新维护计划的下次维护时间
     */
    private void updatePlanNextMaintenanceTime(String planId) {
        try {
            MaintenancePlan plan = maintenancePlanMapper.selectById(planId);
            if (plan != null) {
                // 基于当前时间计算下次维护时间
                Date nextTime = calculateNextMaintenanceTimeFromNow(plan);
                
                // 更新维护计划
                MaintenancePlan updatePlan = new MaintenancePlan();
                updatePlan.setPlanId(planId);
                updatePlan.setNextMaintenanceTime(nextTime);
                updatePlan.setUpdateTime(new Date());
                updatePlan.setUpdateBy("system");
                
                maintenancePlanMapper.updateById(updatePlan);
            }
        } catch (Exception e) {
            log.error("更新维护计划下次维护时间失败，planId: {}", planId, e);
        }
    }
    
    /**
     * 基于当前时间计算下次维护时间
     */
    private Date calculateNextMaintenanceTimeFromNow(MaintenancePlan plan) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date()); // 使用当前时间作为基准
        
        Integer cycleValue = plan.getCycleValue();
        Integer cycleType = plan.getCycleType();
        
        switch (cycleType) {
            case 1: // 按天
                calendar.add(Calendar.DAY_OF_MONTH, cycleValue);
                break;
            case 2: // 按周
                calendar.add(Calendar.WEEK_OF_YEAR, cycleValue);
                break;
            case 3: // 按月
                calendar.add(Calendar.MONTH, cycleValue);
                break;
            case 4: // 按年
                calendar.add(Calendar.YEAR, cycleValue);
                break;
            default:
                return null;
        }
        
        return calendar.getTime();
    }
} 