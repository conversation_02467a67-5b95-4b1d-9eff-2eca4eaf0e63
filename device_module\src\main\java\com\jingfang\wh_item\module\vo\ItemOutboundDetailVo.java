package com.jingfang.wh_item.module.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ItemOutboundDetailVo {
    private Long detailId;
    private String outboundId;
    private String itemId;
    
    // 从item_base_info表关联查询的字段
    private String itemName;
    private String itemCode;
    private Integer itemType;
    private String itemTypeName;
    private String specModel;
    private String unit;
    private String imageUrl;
    
    private String batchNo;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date productionDate;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expiryDate;

    private BigDecimal quantity;
    private BigDecimal unitPrice;
    private BigDecimal amount;
    private Integer warehouseId;
    private String warehouseName;
    private String shelfLocation;
    private String remark;
} 