<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.device_module.mapper.DeviceInfoMapper">

    <resultMap id="BaseResultMap" type="com.jingfang.device_module.module.entity.DeviceInfo">
            <id property="id" column="id" />
            <result property="deviceName" column="device_name" />
            <result property="deviceModel" column="device_model" />
            <result property="ipAddress" column="ip_address" />
            <result property="devicePort" column="device_port" />
            <result property="location" column="location" />
            <result property="manufacturer" column="manufacturer" />
            <result property="serialNumber" column="serial_number" />
            <result property="installationDate" column="installation_date" />
            <result property="createdAt" column="created_at" />
            <result property="updatedAt" column="updated_at" />
    </resultMap>

    <sql id="Base_Column_List">
        id,device_name,device_model,ip_address,device_port,status,
        location,manufacturer,serial_number,installation_date,created_at,
        updated_at
    </sql>

    <insert id="addPictureUrl">
        insert into device_picture(device_id,picture_url)
        values (#{deviceId},#{url})
    </insert>

    <delete id="deletePictureUrl">
        delete from device_picture
        where
            device_id = #{deviceId} and picture_url = #{url}
    </delete>

    <select id="getDevicePictureUrls" resultType="java.lang.String">
        select picture_url
        from device_picture
        where device_id = #{deviceId}
    </select>
</mapper>
