<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.asset_disposal.mapper.AssetDisposalAttachmentMapper">

    <resultMap id="BaseResultMap" type="com.jingfang.asset_disposal.module.entity.AssetDisposalAttachment">
        <id property="attachmentId" column="attachment_id" jdbcType="VARCHAR"/>
        <result property="disposalId" column="disposal_id" jdbcType="VARCHAR"/>
        <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
        <result property="fileType" column="file_type" jdbcType="VARCHAR"/>
        <result property="fileSize" column="file_size" jdbcType="BIGINT"/>
        <result property="storagePath" column="storage_path" jdbcType="VARCHAR"/>
        <result property="uploadTime" column="upload_time" jdbcType="TIMESTAMP"/>
        <result property="uploadBy" column="upload_by" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 根据处置单ID查询附件 -->
    <select id="selectAttachmentsByDisposalId" resultType="com.jingfang.asset_disposal.module.dto.AssetDisposalAttachmentDto">
        SELECT 
            file_name,
            file_type,
            file_size,
            storage_path
        FROM asset_disposal_attachment
        WHERE disposal_id = #{disposalId}
        ORDER BY upload_time
    </select>

    <!-- 批量保存附件 -->
    <insert id="batchInsertAttachments">
        INSERT INTO asset_disposal_attachment 
        (attachment_id, disposal_id, file_name, file_type, file_size, storage_path, upload_time, upload_by)
        VALUES
        <foreach collection="attachmentList" item="attachment" separator=",">
            (UUID(), #{disposalId}, #{attachment.fileName}, #{attachment.fileType}, 
             #{attachment.fileSize}, #{attachment.storagePath}, NOW(), 'system')
        </foreach>
    </insert>

    <!-- 删除处置单的所有附件 -->
    <delete id="deleteAttachmentsByDisposalId">
        DELETE FROM asset_disposal_attachment 
        WHERE disposal_id = #{disposalId}
    </delete>

</mapper> 