package com.jingfang.wh_item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingfang.common.utils.bean.BeanUtils;
import com.jingfang.wh_item.mapper.ItemOutboundDetailMapper;
import com.jingfang.wh_item.mapper.ItemOutboundMapper;
import com.jingfang.wh_item.mapper.ItemOutboundOperationLogMapper;
import com.jingfang.wh_item.module.dto.ItemOutboundDto;
import com.jingfang.wh_item.module.entity.ItemOutbound;
import com.jingfang.wh_item.module.entity.ItemOutboundDetail;
import com.jingfang.wh_item.module.entity.ItemOutboundOperationLog;
import com.jingfang.wh_item.module.request.ItemOutboundSearchRequest;
import com.jingfang.wh_item.module.vo.ItemOutboundDetailVo;
import com.jingfang.wh_item.module.vo.ItemOutboundVo;
import com.jingfang.wh_item.service.ItemOutboundService;
import com.jingfang.wh_item.service.ItemService;
import com.jingfang.wh_item.service.ItemStockSyncService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 物品出库业务实现类
 */
@Slf4j
@Service
public class ItemOutboundServiceImpl extends ServiceImpl<ItemOutboundMapper, ItemOutbound> implements ItemOutboundService {

    @Resource
    private ItemOutboundMapper outboundMapper;

    @Resource
    private ItemOutboundDetailMapper detailMapper;

    @Resource
    private ItemOutboundOperationLogMapper logMapper;

    @Resource
    private ItemService itemService;

    @Resource
    private ItemStockSyncService itemStockSyncService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addItemOutbound(ItemOutboundDto outboundDto, String username) {
        // 1. 构建并保存出库单主表信息
        ItemOutbound outbound = new ItemOutbound();
        BeanUtils.copyProperties(outboundDto.getMain(), outbound);

        // 设置初始状态为草稿状态(1)
        outbound.setCreateTime(new Date());
        outbound.setUpdateTime(new Date());
        outbound.setDelFlag("0");

        // 2. 校验库存是否充足
        for (ItemOutboundDetail detail : outboundDto.getDetails()) {
            validateItemExists(detail.getItemId());
            validateStock(detail.getItemId(), detail.getWarehouseId(), detail.getQuantity());
        }

        // 保存出库单主表
        outboundMapper.insert(outbound);
        String outboundId = outbound.getOutboundId();

        // 3. 保存出库单明细
        if (outboundDto.getDetails() != null && !outboundDto.getDetails().isEmpty()) {
            for (ItemOutboundDetail detail : outboundDto.getDetails()) {
                // 设置出库单ID
                detail.setOutboundId(outboundId);
                detail.setCreateTime(new Date());
                detail.setUpdateTime(new Date());

                // 计算金额
                if (detail.getUnitPrice() != null && detail.getQuantity() != null) {
                    detail.setAmount(detail.getUnitPrice().multiply(detail.getQuantity()));
                }

                // 保存明细
                detailMapper.insert(detail);
            }
        }

        // 4. 记录操作日志
        recordOperationLog(outboundId, 1, "创建出库单", username);

        log.info("创建出库单成功，ID：{}", outboundId);
        return outboundId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateItemOutbound(String outboundId, ItemOutboundDto outboundDto, String username) {
        // 1. 查询出库单
        ItemOutbound outbound = outboundMapper.selectById(outboundId);
        if (outbound == null) {
            throw new RuntimeException("出库单不存在");
        }

        // 2. 校验当前状态，只有草稿(1)或退回(5)状态可以编辑
        if (outbound.getStatus() != 1 && outbound.getStatus() != 5) {
            throw new RuntimeException("当前状态不允许编辑");
        }

        // 3. 校验库存是否充足
        for (ItemOutboundDetail detail : outboundDto.getDetails()) {
            validateItemExists(detail.getItemId());
            validateStock(detail.getItemId(), detail.getWarehouseId(), detail.getQuantity());
        }

        // 4. 更新主表信息
        ItemOutbound updateOutbound = outboundDto.getMain();
        updateOutbound.setOutboundId(outboundId);
        updateOutbound.setUpdateTime(new Date());
        outboundMapper.updateById(updateOutbound);

        // 5. 删除原有明细
        LambdaQueryWrapper<ItemOutboundDetail> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(ItemOutboundDetail::getOutboundId, outboundId);
        detailMapper.delete(deleteWrapper);

        // 6. 重新保存明细
        if (outboundDto.getDetails() != null && !outboundDto.getDetails().isEmpty()) {
            for (ItemOutboundDetail detail : outboundDto.getDetails()) {
                // 校验物品是否存在
                validateItemExists(detail.getItemId());

                // 设置出库单ID
                detail.setOutboundId(outboundId);
                detail.setCreateTime(new Date());
                detail.setUpdateTime(new Date());

                // 计算金额
                if (detail.getUnitPrice() != null && detail.getQuantity() != null) {
                    detail.setAmount(detail.getUnitPrice().multiply(detail.getQuantity()));
                }

                // 保存明细
                detailMapper.insert(detail);
            }
        }

        // 7. 记录操作日志
        recordOperationLog(outboundId, 2, "编辑出库单", username);
    }

    @Override
    public ItemOutboundVo getOutboundDetail(String outboundId) {
        // 使用关联查询获取管理人员名称
        ItemOutboundVo vo = outboundMapper.selectOutboundDetailById(outboundId);
        if (vo == null) {
            throw new RuntimeException("出库单不存在");
        }

        // 设置状态名称
        setStatusName(vo);

        // 设置出库类型名称
        setOutboundTypeName(vo);

        // 查询并设置出库明细
        List<ItemOutboundDetailVo> details = detailMapper.selectDetailsByOutboundId(outboundId);
        vo.setDetails(details);

        // 查询并设置操作日志
        LambdaQueryWrapper<ItemOutboundOperationLog> logWrapper = new LambdaQueryWrapper<>();
        logWrapper.eq(ItemOutboundOperationLog::getOutboundId, outboundId);
        logWrapper.orderByDesc(ItemOutboundOperationLog::getOperationTime);
        List<ItemOutboundOperationLog> logs = logMapper.selectList(logWrapper);
        vo.setLogs(logs);

        return vo;
    }

    @Override
    public IPage<ItemOutboundVo> selectOutboundList(ItemOutboundSearchRequest request) {
        // 使用关联查询获取管理人员名称
        Page<ItemOutboundVo> page = new Page<>(request.getPageNum(), request.getPageSize());
        IPage<ItemOutboundVo> result = outboundMapper.selectOutboundListWithManager(page, request);

        // 设置状态名称和出库类型名称
        for (ItemOutboundVo vo : result.getRecords()) {
            setStatusName(vo);
            setOutboundTypeName(vo);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitOutbound(String outboundId, String username) {
        // 1. 查询出库单
        ItemOutbound outbound = outboundMapper.selectById(outboundId);
        if (outbound == null) {
            throw new RuntimeException("出库单不存在");
        }

        // 2. 校验当前状态，只有草稿(1)或退回(5)状态可以提交
        if (outbound.getStatus() != 1 && outbound.getStatus() != 5) {
            throw new RuntimeException("当前状态不允许提交");
        }

        // 3. 再次校验库存是否充足
        validateOutboundStock(outboundId);

        // 4. 更新状态为待确认(2)
        outbound.setStatus(2);
        outbound.setUpdateTime(new Date());
        outboundMapper.updateById(outbound);

        // 5. 记录操作日志
        recordOperationLog(outboundId, 3, "提交出库单", username);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleOutbound(String outboundId, String remark, String username) {
        // 1. 查询出库单
        ItemOutbound outbound = outboundMapper.selectById(outboundId);
        if (outbound == null) {
            throw new RuntimeException("出库单不存在");
        }

        // 2. 校验当前状态，只有待确认(2)状态可以确认
        if (outbound.getStatus() != 2) {
            throw new RuntimeException("当前状态不允许经手人确认");
        }

        // 3. 获取当前用户ID
        Long userId = getUserId(username);

        // 4. 更新状态为待审核(3)，并记录经手人信息
        outbound.setStatus(3);
        outbound.setHandlerId(userId);
        outbound.setHandleTime(new Date());
        outbound.setHandleRemark(remark);
        outbound.setUpdateTime(new Date());
        outboundMapper.updateById(outbound);

        // 5. 记录操作日志
        recordOperationLog(outboundId, 4, "经手人确认：" + (remark != null ? remark : ""), username);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditOutbound(String outboundId, Integer status, String remark, String username) {
        // 1. 查询出库单
        ItemOutbound outbound = outboundMapper.selectById(outboundId);
        if (outbound == null) {
            throw new RuntimeException("出库单不存在");
        }

        // 2. 校验当前状态，只有待审核(3)状态可以审核
        if (outbound.getStatus() != 3) {
            throw new RuntimeException("当前状态不允许审核");
        }

        // 3. 校验审核结果参数
        if (status != 4 && status != 5) {
            throw new RuntimeException("无效的审核结果");
        }

        // 4. 获取当前用户ID
        Long userId = getUserId(username);

        // 5. 更新状态和审核信息
        outbound.setStatus(status);
        outbound.setAuditorId(userId);
        outbound.setAuditTime(new Date());
        outbound.setAuditRemark(remark);
        outbound.setUpdateTime(new Date());
        outboundMapper.updateById(outbound);

        // 6. 记录操作日志
        recordOperationLog(outboundId, status == 4 ? 5 : 6,
                status == 4 ? "审核通过：" + remark : "审核退回：" + remark, username);

        // 7. 如果审核通过，则自动扣减库存
        if (status == 4) {
            try {
                reduceItemStock(outboundId);

                // 扣减库存后，记录操作日志
                recordOperationLog(outboundId, 7, "根据出库单自动扣减库存", username);

                log.info("出库单[{}]审核通过，已自动完成库存扣减", outboundId);
            } catch (Exception e) {
                log.error("出库单[{}]自动扣减库存失败: {}", outboundId, e.getMessage(), e);
                throw new RuntimeException("审核通过，但自动扣减库存失败: " + e.getMessage());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOutbound(String[] outboundIds, String username) {
        Long userId = getUserId(username);

        for (String outboundId : outboundIds) {
            // 1. 查询出库单
            ItemOutbound outbound = outboundMapper.selectById(outboundId);
            if (outbound == null) {
                continue;
            }

            // 2. 校验状态（只允许删除草稿(1)和退回(5)状态的出库单）
            if (outbound.getStatus() != 1 && outbound.getStatus() != 5) {
                throw new RuntimeException("出库单[" + outboundId + "]当前状态不允许删除");
            }

            // 3. 逻辑删除出库单
            outbound.setDelFlag("1");
            outbound.setUpdateTime(new Date());
            outboundMapper.updateById(outbound);

            // 4. 记录操作日志
            recordOperationLog(outboundId, 9, "删除出库单", username);
        }
    }

    /**
     * 校验物品是否存在
     */
    private void validateItemExists(String itemId) {
        // 调用物品服务校验物品是否存在
        boolean exists = itemService.existsById(itemId);
        if (!exists) {
            throw new RuntimeException("物品[" + itemId + "]不存在");
        }
    }

    /**
     * 校验库存是否充足
     */
    private void validateStock(String itemId, Integer warehouseId, java.math.BigDecimal quantity) {
        // 调用物品服务校验库存是否充足
        boolean sufficient = itemService.checkStockSufficient(itemId, warehouseId, quantity.intValue());
        if (!sufficient) {
            throw new RuntimeException("物品[" + itemId + "]在仓库[" + warehouseId + "]库存不足");
        }
    }

    /**
     * 校验出库单整体库存是否充足
     */
    private void validateOutboundStock(String outboundId) {
        // 1. 查询出库单明细
        LambdaQueryWrapper<ItemOutboundDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ItemOutboundDetail::getOutboundId, outboundId);
        List<ItemOutboundDetail> details = detailMapper.selectList(queryWrapper);

        if (details == null || details.isEmpty()) {
            throw new RuntimeException("出库单没有明细数据");
        }

        // 2. 遍历明细，校验库存
        for (ItemOutboundDetail detail : details) {
            validateStock(detail.getItemId(), detail.getWarehouseId(), detail.getQuantity());
        }
    }

    /**
     * 扣减物品库存
     */
    private void reduceItemStock(String outboundId) {
        // 1. 查询出库单明细
        LambdaQueryWrapper<ItemOutboundDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ItemOutboundDetail::getOutboundId, outboundId);
        List<ItemOutboundDetail> details = detailMapper.selectList(queryWrapper);

        if (details == null || details.isEmpty()) {
            log.warn("出库单[{}]没有明细数据，无法扣减库存", outboundId);
            return;
        }

        // 2. 遍历明细，扣减库存
        for (ItemOutboundDetail detail : details) {
            // 获取仓库ID，明细中必须指定仓库ID
            Integer warehouseId = detail.getWarehouseId();
            
            if (warehouseId == null) {
                log.error("出库单[{}]明细[物品ID:{}]未指定仓库ID，无法扣减库存", outboundId, detail.getItemId());
                throw new RuntimeException("物品[" + detail.getItemId() + "]未指定仓库，无法扣减库存");
            }
            
            // 调用物品服务扣减库存
            boolean success = itemService.updateItemStock(
                    detail.getItemId(),
                    warehouseId,
                    -detail.getQuantity().intValue(), // 出库为负数
                    2, // 出库操作类型
                    outboundId,
                    "出库单出库",
                    detail.getShelfLocation() // 传递货架位置
            );

            if (!success) {
                throw new RuntimeException("物品[" + detail.getItemId() + "]在仓库[" + warehouseId + "]库存扣减失败");
            }

            log.info("根据出库单明细扣减库存成功: 物品ID[{}]，仓库[{}]，数量[{}]", 
                    detail.getItemId(), warehouseId, detail.getQuantity());
        }
    }

    /**
     * 记录操作日志
     */
    private void recordOperationLog(String outboundId, Integer operationType, String operationContent, String username) {
        ItemOutboundOperationLog log = new ItemOutboundOperationLog();
        log.setOutboundId(outboundId);
        log.setOperationType(operationType);
        log.setOperationContent(operationContent);
        log.setOperationTime(new Date());
        log.setOperatorId(getUserId(username));
        log.setOperatorName(username);
        logMapper.insert(log);
    }

    /**
     * 获取用户ID（这里需要根据实际情况实现）
     */
    private Long getUserId(String username) {
        // 这里应该根据username查询用户ID，暂时返回默认值
        // 实际项目中需要调用用户服务获取用户ID
        return 1L;
    }

    /**
     * 设置状态名称
     */
    private void setStatusName(ItemOutboundVo vo) {
        if (vo.getStatus() != null) {
            switch (vo.getStatus()) {
                case 1:
                    vo.setStatusName("草稿");
                    break;
                case 2:
                    vo.setStatusName("待确认");
                    break;
                case 3:
                    vo.setStatusName("待审核");
                    break;
                case 4:
                    vo.setStatusName("审核通过");
                    break;
                case 5:
                    vo.setStatusName("已退回");
                    break;
                default:
                    vo.setStatusName("未知");
                    break;
            }
        }
    }

    /**
     * 设置出库类型名称
     */
    private void setOutboundTypeName(ItemOutboundVo vo) {
        if (vo.getOutboundType() != null) {
            switch (vo.getOutboundType()) {
                case 1:
                    vo.setOutboundTypeName("销售出库");
                    break;
                case 2:
                    vo.setOutboundTypeName("领用出库");
                    break;
                case 3:
                    vo.setOutboundTypeName("调拨出库");
                    break;
                case 4:
                    vo.setOutboundTypeName("其他出库");
                    break;
                default:
                    vo.setOutboundTypeName("未知");
                    break;
            }
        }
    }
}