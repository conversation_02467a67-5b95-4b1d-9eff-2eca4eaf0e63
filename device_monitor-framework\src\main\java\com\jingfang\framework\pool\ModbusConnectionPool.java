package com.jingfang.framework.pool;

import com.ghgande.j2mod.modbus.facade.ModbusTCPMaster;
import com.jingfang.framework.manager.factory.ModbusConnectionFactory;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;

public class ModbusConnectionPool {

    private GenericObjectPool<ModbusTCPMaster> pool;

    public ModbusConnectionPool(String ipAddress, int port, GenericObjectPoolConfig<ModbusTCPMaster> config) {

        // 创建连接池
        ModbusConnectionFactory factory = new ModbusConnectionFactory(ipAddress, port);
        pool = new GenericObjectPool<>(factory, config);
    }

    // 获取一个连接
    public ModbusTCPMaster borrowConnection() throws Exception {
        return pool.borrowObject();
    }

    // 归还一个连接
    public void returnConnection(ModbusTCPMaster master) {
        pool.returnObject(master);
    }

    // 销毁连接池
    public void closePool() {
        pool.close();
    }
}
