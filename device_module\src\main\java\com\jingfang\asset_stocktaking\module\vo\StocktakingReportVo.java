package com.jingfang.asset_stocktaking.module.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;

import java.util.Date;
import java.util.List;

/**
 * 盘点报告视图对象
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
public class StocktakingReportVo implements Serializable {

    /**
     * 盘点计划ID
     */
    private String planId;

    /**
     * 盘点计划名称
     */
    private String planName;

    /**
     * 报告生成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportTime;

    /**
     * 盘点汇总信息
     */
    private SummaryInfo summaryInfo;

    /**
     * 差异统计信息
     */
    private DifferenceStatistics differenceStatistics;

    /**
     * 部门统计信息
     */
    private List<DeptStatistics> deptStatisticsList;



    private static final long serialVersionUID = 1L;
}
