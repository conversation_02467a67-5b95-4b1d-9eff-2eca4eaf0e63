package com.jingfang.wh_item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jingfang.wh_item.module.entity.ItemStocktakingDetail;
import com.jingfang.wh_item.module.vo.ItemStocktakingDetailVo;
import com.jingfang.wh_item.module.vo.ItemStocktakingVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 库存盘点明细Mapper接口
 */
@Mapper
public interface ItemStocktakingDetailMapper extends BaseMapper<ItemStocktakingDetail> {
    
    /**
     * 查询盘点明细列表
     */
    List<ItemStocktakingDetailVo> selectStocktakingDetailList(@Param("stocktakingId") String stocktakingId);
    
    /**
     * 批量插入盘点明细
     */
    int batchInsert(@Param("details") List<ItemStocktakingDetail> details);
    
    /**
     * 统计盘点进度
     */
    ItemStocktakingVo selectStocktakingProgress(@Param("stocktakingId") String stocktakingId);
} 