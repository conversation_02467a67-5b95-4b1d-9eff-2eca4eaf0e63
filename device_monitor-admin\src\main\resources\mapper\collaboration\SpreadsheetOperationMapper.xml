<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.collaboration.mapper.SpreadsheetOperationMapper">
    
    <resultMap type="com.jingfang.collaboration.domain.SpreadsheetOperation" id="SpreadsheetOperationResult">
        <result property="id"                column="id"                />
        <result property="spreadsheetId"     column="spreadsheet_id"    />
        <result property="userId"            column="user_id"           />
        <result property="userName"          column="user_name"         />
        <result property="nickName"          column="nick_name"         />
        <result property="operationType"     column="operation_type"    />
        <result property="operationDesc"     column="operation_desc"    />
        <result property="operationDetail"   column="operation_detail"  />
        <result property="operationPosition" column="operation_position"/>
        <result property="beforeData"        column="before_data"       />
        <result property="afterData"         column="after_data"        />
        <result property="operationTime"     column="operation_time"    />
        <result property="clientIp"          column="client_ip"         />
        <result property="userAgent"         column="user_agent"        />
        <result property="sessionId"         column="session_id"        />
        <result property="remark"            column="remark"            />
    </resultMap>

    <!-- 查询表格操作记录列表 -->
    <select id="selectOperationList" parameterType="map" resultMap="SpreadsheetOperationResult">
        SELECT 
            id,
            spreadsheet_id,
            user_id,
            user_name,
            nick_name,
            operation_type,
            operation_desc,
            operation_detail,
            operation_position,
            before_data,
            after_data,
            operation_time,
            client_ip,
            user_agent,
            session_id,
            remark
        FROM collaboration_spreadsheet_operation
        WHERE 1=1
        <if test="spreadsheetId != null and spreadsheetId != ''">
            AND spreadsheet_id = #{spreadsheetId}
        </if>
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="operationType != null and operationType != ''">
            AND operation_type = #{operationType}
        </if>
        ORDER BY operation_time DESC
    </select>

</mapper>
