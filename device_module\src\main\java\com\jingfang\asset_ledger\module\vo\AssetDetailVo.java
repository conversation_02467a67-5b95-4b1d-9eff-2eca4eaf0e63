package com.jingfang.asset_ledger.module.vo;

import com.jingfang.asset_part_relation.module.vo.AssetPartRelationVo;
import com.jingfang.maintenance_plan.module.vo.MaintenancePlanVo;
import com.jingfang.maintenance_task.module.vo.MaintenanceTaskVo;
import lombok.Data;

import java.util.List;

@Data
public class AssetDetailVo {

    private AssetDetailBaseInfoVo baseInfo;

    private AssetDetailMaintainInfoVo maintainInfo;

    /**
     * 关联的备品备件列表
     */
    private List<AssetPartRelationVo> relatedParts;
    
    /**
     * 关联的维护计划列表
     */
    private List<MaintenancePlanVo> maintenancePlans;
    
    /**
     * 关联的维护任务列表（最近的任务）
     */
    private List<MaintenanceTaskVo> maintenanceTasks;
    
    /**
     * 维护统计信息
     */
    private MaintenanceStatistics maintenanceStatistics;
    

}
