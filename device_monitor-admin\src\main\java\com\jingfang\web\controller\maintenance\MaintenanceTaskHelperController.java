package com.jingfang.web.controller.maintenance;


import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.system.service.ISysDeptService;
import com.jingfang.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 维护任务辅助控制器
 */
@RestController
@RequestMapping("/maintenance/task/helper")
public class MaintenanceTaskHelperController extends BaseController {
    
    @Autowired
    private ISysUserService userService;
    
    @Autowired
    private ISysDeptService deptService;
    
    /**
     * 获取用户列表
     */
    @PreAuthorize("@ss.hasPermi('maintenance:task:list')")
    @GetMapping("/users")
    public AjaxResult getUserList() {
        return success(userService.selectUserList(null));
    }
    
    /**
     * 获取部门列表
     */
    @PreAuthorize("@ss.hasPermi('maintenance:task:list')")
    @GetMapping("/depts")
    public AjaxResult getDeptList() {
        return success(deptService.selectDeptList(null));
    }
    
    /**
     * 获取任务状态选项
     */
    @GetMapping("/statusOptions")
    public AjaxResult getStatusOptions() {
        return success(new Object[][]{
            {1, "待执行"},
            {2, "执行中"},
            {3, "草稿"},
            {4, "待审核"},
            {5, "审核通过"},
            {6, "审核不通过"},
            {7, "已完成"},
            {8, "已取消"}
        });
    }
    
    /**
     * 获取优先级选项
     */
    @GetMapping("/priorityOptions")
    public AjaxResult getPriorityOptions() {
        return success(new Object[][]{
            {1, "低"},
            {2, "中"},
            {3, "高"},
            {4, "紧急"}
        });
    }
    
    /**
     * 获取负责人类型选项
     */
    @GetMapping("/responsibleTypeOptions")
    public AjaxResult getResponsibleTypeOptions() {
        return success(new Object[][]{
            {1, "个人"},
            {2, "部门"}
        });
    }
    
    /**
     * 获取使用状态选项
     */
    @GetMapping("/useStatusOptions")
    public AjaxResult getUseStatusOptions() {
        return success(new Object[][]{
            {1, "计划使用"},
            {2, "已使用"},
            {3, "未使用"}
        });
    }
} 