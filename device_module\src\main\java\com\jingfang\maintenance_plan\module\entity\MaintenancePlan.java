package com.jingfang.maintenance_plan.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 维护计划表
 * @TableName maintenance_plan
 */
@TableName(value = "maintenance_plan")
@Data
public class MaintenancePlan implements Serializable {
    
    /**
     * 维护计划ID
     */
    @TableId(type = IdType.INPUT)
    private String planId;
    
    /**
     * 计划名称
     */
    private String planName;
    
    /**
     * 资产ID
     */
    private String assetId;
    
    /**
     * 维护事项描述
     */
    private String maintenanceItems;
    
    /**
     * 维护周期类型(1-按天, 2-按周, 3-按月, 4-按年)
     */
    private Integer cycleType;
    
    /**
     * 维护周期值
     */
    private Integer cycleValue;
    
    /**
     * 负责人类型(1-个人, 2-部门)
     */
    private Integer responsibleType;
    
    /**
     * 负责人ID（用户ID或部门ID）
     */
    private Long responsibleId;
    
    /**
     * 计划状态(1-启用, 0-停用)
     */
    private Integer status;
    
    /**
     * 下次维护时间
     */
    private Date nextMaintenanceTime;
    
    /**
     * 备注说明
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 是否删除(0-否, 1-是)
     */
    private Integer deleted;
    
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
} 