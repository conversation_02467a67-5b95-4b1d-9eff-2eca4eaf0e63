package com.jingfang.wh_item.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 消耗品特有属性表
 * @TableName item_consumable_attr
 */
@TableName(value ="item_consumable_attr")
@Data
public class ItemConsumableAttr implements Serializable {
    /**
     * 属性ID
     */
    @TableId(type = IdType.INPUT)
    private String attrId;

    /**
     * 物品ID
     */
    private String itemId;

    /**
     * 是否有有效期(0-无, 1-有)
     */
    @TableField("has_expiry")
    private Integer hasExpiry;

    /**
     * 有效期/保质期(天)
     */
    @TableField("expiry_period")
    private Integer expiryPeriod;

    /**
     * 生产日期
     */
    @TableField("production_date")
    private Date productionDate;

    /**
     * 存储条件
     */
    @TableField("storage_condition")
    private String storageCondition;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
} 