-- ========================================
-- 设备参数测试数据示例
-- 基于简化后的device_param表结构
-- ========================================

-- 清理测试数据
DELETE FROM device_param WHERE param_name LIKE 'TEST_%';

-- ========================================
-- 示例设备参数配置
-- 假设设备ID为1的设备需要监控以下参数
-- ========================================

-- 运行参数示例
INSERT INTO device_param (device_id, param_name, param_unit, range_start, range_end, del_flag) VALUES
-- 温度相关参数
(1, '冷却水进水温度', '°C', 5.0, 35.0, 0),
(1, '冷却水出水温度', '°C', 10.0, 40.0, 0),
(1, '环境温度', '°C', -10.0, 50.0, 0),

-- 流量相关参数
(1, '冷却水进水流量', 'L/min', 10.0, 100.0, 0),
(1, '冷却水出水流量', 'L/min', 10.0, 100.0, 0),

-- 压力相关参数
(1, '系统压力', 'MPa', 0.1, 2.0, 0),
(1, '进水压力', 'MPa', 0.2, 1.5, 0),

-- 电气参数
(1, '输入电压', 'V', 200.0, 240.0, 0),
(1, '输入电流', 'A', 1.0, 50.0, 0),
(1, '功率', 'kW', 0.5, 30.0, 0),

-- 运行状态参数
(1, '运行状态', '', NULL, NULL, 0),
(1, '设备模式', '', NULL, NULL, 0);

-- 告警参数示例（通过参数名称关键字识别）
INSERT INTO device_param (device_id, param_name, param_unit, range_start, range_end, del_flag) VALUES
-- 温度告警
(1, '高温告警', '', NULL, NULL, 0),
(1, '低温告警', '', NULL, NULL, 0),

-- 压力告警
(1, '高压告警', '', NULL, NULL, 0),
(1, '低压告警', '', NULL, NULL, 0),

-- 流量告警
(1, '低流量告警', '', NULL, NULL, 0),

-- 系统故障
(1, '系统故障', '', NULL, NULL, 0),
(1, '通信异常', '', NULL, NULL, 0),
(1, '传感器故障', '', NULL, NULL, 0);

-- ========================================
-- 示例设备2的参数配置
-- ========================================

-- 电机控制器参数
INSERT INTO device_param (device_id, param_name, param_unit, range_start, range_end, del_flag) VALUES
-- 运行参数
(2, '电机转速', 'rpm', 0.0, 3000.0, 0),
(2, '电机电流', 'A', 0.0, 100.0, 0),
(2, '电机电压', 'V', 350.0, 400.0, 0),
(2, '电机温度', '°C', 20.0, 80.0, 0),
(2, '负载率', '%', 0.0, 100.0, 0),

-- 告警参数
(2, '过载告警', '', NULL, NULL, 0),
(2, '过热告警', '', NULL, NULL, 0),
(2, '电机故障', '', NULL, NULL, 0);

-- ========================================
-- 示例设备3的参数配置
-- ========================================

-- 环境监测设备参数
INSERT INTO device_param (device_id, param_name, param_unit, range_start, range_end, del_flag) VALUES
-- 运行参数
(3, '环境温度', '°C', -20.0, 60.0, 0),
(3, '环境湿度', '%', 0.0, 100.0, 0),
(3, '大气压力', 'kPa', 80.0, 110.0, 0),
(3, '风速', 'm/s', 0.0, 30.0, 0),
(3, '降雨量', 'mm', 0.0, 200.0, 0),

-- 告警参数
(3, '温度异常', '', NULL, NULL, 0),
(3, '湿度异常', '', NULL, NULL, 0),
(3, '传感器故障', '', NULL, NULL, 0);

-- ========================================
-- 查看配置结果
-- ========================================

-- 查看所有运行参数（不包含告警关键字）
SELECT 
    device_id,
    param_name,
    param_unit,
    CONCAT(IFNULL(range_start, ''), ' - ', IFNULL(range_end, '')) as normal_range,
    '运行参数' as param_type
FROM device_param 
WHERE del_flag = 0 
  AND param_name NOT LIKE '%告警%' 
  AND param_name NOT LIKE '%报警%' 
  AND param_name NOT LIKE '%故障%' 
  AND param_name NOT LIKE '%异常%'
ORDER BY device_id, param_name;

-- 查看所有告警参数（包含告警关键字）
SELECT 
    device_id,
    param_name,
    param_unit,
    '告警参数' as param_type
FROM device_param 
WHERE del_flag = 0 
  AND (param_name LIKE '%告警%' 
       OR param_name LIKE '%报警%' 
       OR param_name LIKE '%故障%' 
       OR param_name LIKE '%异常%')
ORDER BY device_id, param_name;

-- 统计各设备的参数数量
SELECT 
    device_id,
    COUNT(*) as total_params,
    COUNT(CASE WHEN param_name NOT LIKE '%告警%' 
               AND param_name NOT LIKE '%报警%' 
               AND param_name NOT LIKE '%故障%' 
               AND param_name NOT LIKE '%异常%' THEN 1 END) as normal_params,
    COUNT(CASE WHEN param_name LIKE '%告警%' 
               OR param_name LIKE '%报警%' 
               OR param_name LIKE '%故障%' 
               OR param_name LIKE '%异常%' THEN 1 END) as alert_params
FROM device_param 
WHERE del_flag = 0
GROUP BY device_id
ORDER BY device_id;

-- ========================================
-- 使用说明：
-- 1. 参数名称必须与MQTT网关中的属性名称完全一致
-- 2. 运行参数和告警参数通过参数名称关键字自动区分
-- 3. range_start和range_end用于判断参数值是否正常
-- 4. param_unit会自动添加到参数值后面显示
-- 5. 可以根据实际设备调整参数配置
-- ========================================
