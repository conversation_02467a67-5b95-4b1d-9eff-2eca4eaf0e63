<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.maintenance_task.module.mapper.MaintenanceTaskMapper">

    <resultMap id="MaintenanceTaskVoResult" type="com.jingfang.maintenance_task.module.vo.MaintenanceTaskVo">
        <id property="taskId" column="task_id"/>
        <result property="planId" column="plan_id"/>
        <result property="planName" column="plan_name"/>
        <result property="taskTitle" column="task_title"/>
        <result property="assetId" column="asset_id"/>
        <result property="assetName" column="asset_name"/>
        <result property="assetCode" column="asset_id"/>
        <result property="assetLocation" column="asset_location"/>
        <result property="maintenanceItems" column="maintenance_items"/>
        <result property="scheduledTime" column="scheduled_time"/>
        <result property="actualStartTime" column="actual_start_time"/>
        <result property="actualEndTime" column="actual_end_time"/>
        <result property="responsibleType" column="responsible_type"/>
        <result property="responsibleTypeName" column="responsible_type_name"/>
        <result property="responsibleId" column="responsible_id"/>
        <result property="responsibleName" column="responsible_name"/>
        <result property="executorId" column="executor_id"/>
        <result property="executorName" column="executor_name"/>
        <result property="status" column="status"/>
        <result property="statusName" column="status_name"/>
        <result property="priority" column="priority"/>
        <result property="priorityName" column="priority_name"/>
        <result property="checkResult" column="check_result"/>
        <result property="resultDescription" column="result_description"/>
        <result property="problemDescription" column="problem_description"/>
        <result property="solution" column="solution"/>
        <result property="reviewerId" column="reviewer_id"/>
        <result property="reviewerName" column="reviewer_name"/>
        <result property="reviewTime" column="review_time"/>
        <result property="reviewComment" column="review_comment"/>
        <result property="delegateReason" column="delegate_reason"/>
        <result property="delegateBy" column="delegate_by"/>
        <result property="delegateByName" column="delegate_by_name"/>
        <result property="delegateTime" column="delegate_time"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="overdue" column="overdue"/>
        <result property="overdueDays" column="overdue_days"/>
    </resultMap>

    <sql id="selectMaintenanceTaskVo">
        SELECT 
            mt.task_id,
            mt.plan_id,
            mp.plan_name,
            mt.task_title,
            mt.asset_id,
            a.asset_name,
            a.asset_id as asset_code,
            a.spec_model,
            a.detail_location as asset_location,
            mt.maintenance_items,
            mt.scheduled_time,
            mt.actual_start_time,
            mt.actual_end_time,
            mt.responsible_type,
            CASE mt.responsible_type 
                WHEN 1 THEN '个人' 
                WHEN 2 THEN '部门' 
                ELSE '未知' 
            END as responsible_type_name,
            mt.responsible_id,
            CASE mt.responsible_type 
                WHEN 1 THEN u1.nick_name 
                WHEN 2 THEN d1.dept_name 
                ELSE '未分配' 
            END as responsible_name,
            mt.executor_id,
            u2.nick_name as executor_name,
            mt.status,
            CASE mt.status 
                WHEN 1 THEN '待执行'
                WHEN 2 THEN '执行中'
                WHEN 3 THEN '草稿'
                WHEN 4 THEN '待审核'
                WHEN 5 THEN '审核通过'
                WHEN 6 THEN '审核不通过'
                WHEN 7 THEN '已完成'
                WHEN 8 THEN '已取消'
                ELSE '未知'
            END as status_name,
            mt.priority,
            CASE mt.priority 
                WHEN 1 THEN '低'
                WHEN 2 THEN '中'
                WHEN 3 THEN '高'
                WHEN 4 THEN '紧急'
                ELSE '中'
            END as priority_name,
            mt.check_result,
            mt.result_description,
            mt.problem_description,
            mt.solution,
            mt.reviewer_id,
            u3.nick_name as reviewer_name,
            mt.review_time,
            mt.review_comment,
            mt.delegate_reason,
            mt.delegate_by,
            u4.nick_name as delegate_by_name,
            mt.delegate_time,
            mt.remark,
            mt.create_time,
            mt.update_time,
            mt.create_by,
            mt.update_by,
            CASE 
                WHEN mt.scheduled_time &lt; NOW() AND mt.status IN (1, 2) THEN 1
                ELSE 0 
            END as overdue,
            CASE 
                WHEN mt.scheduled_time &lt; NOW() AND mt.status IN (1, 2)
                THEN DATEDIFF(NOW(), mt.scheduled_time)
                ELSE 0 
            END as overdue_days
        FROM maintenance_task mt
        LEFT JOIN maintenance_plan mp ON mt.plan_id = mp.plan_id
        LEFT JOIN asset_ledger a ON mt.asset_id = a.asset_id
        LEFT JOIN sys_user u1 ON mt.responsible_type = 1 AND mt.responsible_id = u1.user_id
        LEFT JOIN sys_dept d1 ON mt.responsible_type = 2 AND mt.responsible_id = d1.dept_id
        LEFT JOIN sys_user u2 ON mt.executor_id = u2.user_id
        LEFT JOIN sys_user u3 ON mt.reviewer_id = u3.user_id
        LEFT JOIN sys_user u4 ON mt.delegate_by = u4.user_id
        WHERE mt.deleted = 0
    </sql>

    <select id="selectTaskPage" resultMap="MaintenanceTaskVoResult">
        <include refid="selectMaintenanceTaskVo"/>
        <where>
            <if test="request.taskTitle != null and request.taskTitle != ''">
                AND mt.task_title LIKE CONCAT('%', #{request.taskTitle}, '%')
            </if>
            <if test="request.planId != null and request.planId != ''">
                AND mt.plan_id = #{request.planId}
            </if>
            <if test="request.assetId != null and request.assetId != ''">
                AND mt.asset_id = #{request.assetId}
            </if>
            <if test="request.assetName != null and request.assetName != ''">
                AND a.asset_name LIKE CONCAT('%', #{request.assetName}, '%')
            </if>
            <if test="request.responsibleType != null">
                AND mt.responsible_type = #{request.responsibleType}
            </if>
            <if test="request.responsibleId != null">
                AND mt.responsible_id = #{request.responsibleId}
            </if>
            <if test="request.executorId != null">
                AND mt.executor_id = #{request.executorId}
            </if>
            <if test="request.statusList != null and request.statusList.size() > 0">
                AND mt.status IN
                <foreach collection="request.statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="request.priorityList != null and request.priorityList.size() > 0">
                AND mt.priority IN
                <foreach collection="request.priorityList" item="priority" open="(" separator="," close=")">
                    #{priority}
                </foreach>
            </if>
            <if test="request.scheduledTimeStart != null">
                AND mt.scheduled_time >= #{request.scheduledTimeStart}
            </if>
            <if test="request.scheduledTimeEnd != null">
                AND mt.scheduled_time &lt;= #{request.scheduledTimeEnd}
            </if>
            <if test="request.actualStartTimeStart != null">
                AND mt.actual_start_time >= #{request.actualStartTimeStart}
            </if>
            <if test="request.actualStartTimeEnd != null">
                AND mt.actual_start_time &lt;= #{request.actualStartTimeEnd}
            </if>
            <if test="request.actualEndTimeStart != null">
                AND mt.actual_end_time >= #{request.actualEndTimeStart}
            </if>
            <if test="request.actualEndTimeEnd != null">
                AND mt.actual_end_time &lt;= #{request.actualEndTimeEnd}
            </if>
            <if test="request.overdue != null and request.overdue">
                AND mt.scheduled_time &lt; NOW() AND mt.status IN (1, 2)
            </if>
            <if test="request.createTimeStart != null">
                AND mt.create_time >= #{request.createTimeStart}
            </if>
            <if test="request.createTimeEnd != null">
                AND mt.create_time &lt;= #{request.createTimeEnd}
            </if>
            <if test="request.createBy != null and request.createBy != ''">
                AND mt.create_by = #{request.createBy}
            </if>
            <if test="request.myTasks != null and request.myTasks and currentUserId != null">
                AND (
                    (mt.responsible_type = 1 AND mt.responsible_id = #{currentUserId})
                    OR mt.executor_id = #{currentUserId}
                )
            </if>
            <if test="request.pendingReview != null and request.pendingReview and currentUserId != null">
                AND mt.status = 4 AND mt.reviewer_id = #{currentUserId}
            </if>
        </where>
        <choose>
            <when test="request.orderBy != null and request.orderBy != ''">
                ORDER BY ${request.orderBy}
                <if test="request.orderDirection != null and request.orderDirection != ''">
                    ${request.orderDirection}
                </if>
            </when>
            <otherwise>
                ORDER BY mt.create_time DESC
            </otherwise>
        </choose>
    </select>

    <select id="selectTaskById" resultMap="MaintenanceTaskVoResult">
        <include refid="selectMaintenanceTaskVo"/>
        AND mt.task_id = #{taskId}
    </select>

    <select id="selectTasksByUser" resultMap="MaintenanceTaskVoResult">
        <include refid="selectMaintenanceTaskVo"/>
        AND (
            (mt.responsible_type = 1 AND mt.responsible_id = #{userId})
            OR mt.executor_id = #{userId}
        )
        <if test="statusList != null and statusList.size() > 0">
            AND mt.status IN
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        ORDER BY mt.scheduled_time ASC
    </select>

    <select id="selectTasksByDept" resultMap="MaintenanceTaskVoResult">
        <include refid="selectMaintenanceTaskVo"/>
        AND mt.responsible_type = 2 AND mt.responsible_id = #{deptId}
        <if test="statusList != null and statusList.size() > 0">
            AND mt.status IN
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        ORDER BY mt.scheduled_time ASC
    </select>

    <select id="selectUpcomingTasks" resultMap="MaintenanceTaskVoResult">
        <include refid="selectMaintenanceTaskVo"/>
        AND mt.status IN (1, 2)
        AND mt.scheduled_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL #{days} DAY)
        ORDER BY mt.scheduled_time ASC
    </select>

    <select id="selectOverdueTasks" resultMap="MaintenanceTaskVoResult">
        <include refid="selectMaintenanceTaskVo"/>
        AND mt.scheduled_time &lt; NOW() 
        AND mt.status IN (1, 2)
        ORDER BY mt.scheduled_time ASC
    </select>

    <select id="selectPendingReviewTasks" resultMap="MaintenanceTaskVoResult">
        <include refid="selectMaintenanceTaskVo"/>
        AND mt.status = 4
        <if test="reviewerId != null">
            AND mt.reviewer_id = #{reviewerId}
        </if>
        ORDER BY mt.actual_end_time ASC
    </select>

    <select id="selectTasksByPlanId" resultMap="MaintenanceTaskVoResult">
        <include refid="selectMaintenanceTaskVo"/>
        AND mt.plan_id = #{planId}
        ORDER BY mt.create_time DESC
    </select>

    <select id="selectTasksByAssetId" resultMap="MaintenanceTaskVoResult">
        <include refid="selectMaintenanceTaskVo"/>
        AND mt.asset_id = #{assetId}
        ORDER BY mt.create_time DESC
    </select>

    <select id="countTasksByStatus" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM maintenance_task mt
        WHERE mt.deleted = 0
        AND mt.status = #{status}
        <if test="userId != null">
            AND (
                (mt.responsible_type = 1 AND mt.responsible_id = #{userId})
                OR mt.executor_id = #{userId}
            )
        </if>
    </select>

    <select id="countUserTasks" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM maintenance_task mt
        WHERE mt.deleted = 0
        AND (
            (mt.responsible_type = 1 AND mt.responsible_id = #{userId})
            OR mt.executor_id = #{userId}
        )
        <if test="statusList != null and statusList.size() > 0">
            AND mt.status IN
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
    </select>

    <select id="countDeptTasks" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM maintenance_task mt
        WHERE mt.deleted = 0
        AND mt.responsible_type = 2 
        AND mt.responsible_id = #{deptId}
        <if test="statusList != null and statusList.size() > 0">
            AND mt.status IN
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
    </select>

    <select id="selectTasksToGenerate" resultType="com.jingfang.maintenance_plan.module.entity.MaintenancePlan">
        SELECT 
            mp.plan_id,
            mp.plan_name,
            mp.asset_id,
            mp.maintenance_items,
            mp.responsible_type,
            mp.responsible_id,
            mp.cycle_type,
            mp.cycle_value,
            mp.next_maintenance_time,
            mp.create_time
        FROM maintenance_plan mp
        WHERE mp.status = 1 
        AND mp.deleted = 0
        AND (
            mp.next_maintenance_time IS NULL 
            OR mp.next_maintenance_time &lt;= #{currentTime}
        )
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO maintenance_task (
            task_id, plan_id, task_title, asset_id, maintenance_items,
            scheduled_time, responsible_type, responsible_id, status, priority,
            create_time, create_by, deleted
        ) VALUES
        <foreach collection="taskList" item="task" separator=",">
            (
                #{task.taskId}, #{task.planId}, #{task.taskTitle}, #{task.assetId}, #{task.maintenanceItems},
                #{task.scheduledTime}, #{task.responsibleType}, #{task.responsibleId}, #{task.status}, #{task.priority},
                #{task.createTime}, #{task.createBy}, #{task.deleted}
            )
        </foreach>
    </insert>

    <update id="updateTaskStatus">
        UPDATE maintenance_task 
        SET status = #{status}, 
            update_time = NOW(), 
            update_by = #{updateBy}
        WHERE task_id = #{taskId}
    </update>

    <update id="delegateTask">
        UPDATE maintenance_task 
        SET responsible_type = #{responsibleType},
            responsible_id = #{responsibleId},
            delegate_reason = #{delegateReason},
            delegate_by = #{delegateBy},
            delegate_time = NOW(),
            update_time = NOW(),
            update_by = #{updateBy}
        WHERE task_id = #{taskId}
    </update>

</mapper> 