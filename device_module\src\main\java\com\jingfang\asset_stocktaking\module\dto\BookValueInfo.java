package com.jingfang.asset_stocktaking.module.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 账面信息数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
public class BookValueInfo implements Serializable {
    
    /**
     * 资产名称
     */
    private String assetName;
    
    /**
     * 资产编码
     */
    private String assetCode;
    
    /**
     * 资产状态
     */
    private Integer assetStatus;
    
    /**
     * 存放位置
     */
    private String location;
    
    /**
     * 使用部门
     */
    private String deptName;
    
    /**
     * 管理人员
     */
    private String managerName;
    
    /**
     * 资产价值
     */
    private Double assetValue;

    private static final long serialVersionUID = 1L;
}
