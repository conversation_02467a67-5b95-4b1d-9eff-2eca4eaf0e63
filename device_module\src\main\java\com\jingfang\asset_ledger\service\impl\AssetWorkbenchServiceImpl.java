package com.jingfang.asset_ledger.service.impl;

import com.jingfang.asset_disposal.mapper.AssetDisposalMapper;
import com.jingfang.asset_inbound.mapper.AssetInboundMapper;
import com.jingfang.asset_ledger.mapper.AssetBaseInfoMapper;
import com.jingfang.asset_ledger.module.vo.AssetWorkbenchVo;
import com.jingfang.asset_ledger.service.AssetWorkbenchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 资产工作台服务实现类
 */
@Slf4j
@Service
public class AssetWorkbenchServiceImpl implements AssetWorkbenchService {
    
    @Resource
    private AssetBaseInfoMapper assetBaseInfoMapper;
    
    @Resource
    private AssetInboundMapper assetInboundMapper;
    
    @Resource
    private AssetDisposalMapper assetDisposalMapper;
    
    @Override
    public AssetWorkbenchVo.AssetOverviewVo getAssetOverview() {
        AssetWorkbenchVo.AssetOverviewVo overview = new AssetWorkbenchVo.AssetOverviewVo();
        
        try {
            // 获取当前月份的开始和结束日期
            LocalDate now = LocalDate.now();
            LocalDate monthStart = now.withDayOfMonth(1);
            LocalDate monthEnd = now.plusMonths(1).withDayOfMonth(1);
            
            String startDate = monthStart.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String endDate = monthEnd.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            
            // 资产总数
            Long totalCount = assetBaseInfoMapper.countTotalAssets();
            overview.setTotalCount(totalCount != null ? totalCount : 0L);
            
            // 资产总值
            BigDecimal totalValue = assetBaseInfoMapper.sumTotalAssetValue();
            overview.setTotalValue(totalValue != null ? totalValue : BigDecimal.ZERO);
            
            // 本月新增资产数
            Long monthlyNewCount = assetBaseInfoMapper.countMonthlyNewAssets(startDate, endDate);
            overview.setMonthlyNewCount(monthlyNewCount != null ? monthlyNewCount : 0L);
            
            // 本月新增资产值
            BigDecimal monthlyNewValue = assetBaseInfoMapper.sumMonthlyNewAssetValue(startDate, endDate);
            overview.setMonthlyNewValue(monthlyNewValue != null ? monthlyNewValue : BigDecimal.ZERO);
            
            // 待处置资产数
            Long pendingDisposalCount = assetDisposalMapper.countPendingApproval();
            overview.setPendingDisposalCount(pendingDisposalCount != null ? pendingDisposalCount : 0L);
            
            // 按状态统计资产数量
            List<Map<String, Object>> statusStats = assetBaseInfoMapper.countAssetsByStatus();
            long normalCount = 0L, repairCount = 0L, scrapCount = 0L;
            
            for (Map<String, Object> stat : statusStats) {
                Integer status = (Integer) stat.get("status");
                Long count = ((Number) stat.get("count")).longValue();
                
                if (status != null) {
                    switch (status) {
                        case 1: // 正常
                            normalCount = count;
                            break;
                        case 2: // 维修
                            repairCount = count;
                            break;
                        case 3: // 报废
                            scrapCount = count;
                            break;
                    }
                }
            }
            
            overview.setNormalStatusCount(normalCount);
            overview.setRepairStatusCount(repairCount);
            overview.setScrapStatusCount(scrapCount);
            
            // 计算资产利用率（正常状态资产数 / 总资产数）
            if (totalCount > 0) {
                BigDecimal utilizationRate = BigDecimal.valueOf(normalCount)
                    .divide(BigDecimal.valueOf(totalCount), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
                overview.setUtilizationRate(utilizationRate);
            } else {
                overview.setUtilizationRate(BigDecimal.ZERO);
            }
            
        } catch (Exception e) {
            log.error("获取资产概览数据失败", e);
        }
        
        return overview;
    }
    
    @Override
    public AssetWorkbenchVo.AssetTrendVo getAssetTrends(int days) {
        AssetWorkbenchVo.AssetTrendVo trends = new AssetWorkbenchVo.AssetTrendVo();
        
        try {
            LocalDate endDate = LocalDate.now().plusDays(1);
            LocalDate startDate = endDate.minusDays(days);
            
            String startDateStr = startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String endDateStr = endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            
            List<Map<String, Object>> trendData = assetBaseInfoMapper.selectAssetTrends(startDateStr, endDateStr);
            
            // 这里简化处理，实际应该按日期聚合数据
            if (!trendData.isEmpty()) {
                Map<String, Object> latestData = trendData.get(trendData.size() - 1);
                trends.setDate(endDate.minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                trends.setNewCount(((Number) latestData.getOrDefault("newCount", 0)).longValue());
                trends.setNewValue((BigDecimal) latestData.getOrDefault("newValue", BigDecimal.ZERO));
            }
            
        } catch (Exception e) {
            log.error("获取资产趋势数据失败", e);
        }
        
        return trends;
    }
    
    @Override
    public AssetWorkbenchVo.AssetInOutStatisticsVo getInOutStatistics() {
        AssetWorkbenchVo.AssetInOutStatisticsVo statistics = new AssetWorkbenchVo.AssetInOutStatisticsVo();
        
        try {
            // 获取当前月份的开始和结束日期
            LocalDate now = LocalDate.now();
            LocalDate monthStart = now.withDayOfMonth(1);
            LocalDate monthEnd = now.plusMonths(1).withDayOfMonth(1);
            
            String startDate = monthStart.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String endDate = monthEnd.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            
            // 本月入库统计
            Long monthlyInboundCount = assetInboundMapper.countMonthlyInbound(startDate, endDate);
            statistics.setMonthlyInboundCount(monthlyInboundCount != null ? monthlyInboundCount : 0L);
            
            Long monthlyInboundAssetCount = assetInboundMapper.countMonthlyInboundAssets(startDate, endDate);
            statistics.setMonthlyInboundAssetCount(monthlyInboundAssetCount != null ? monthlyInboundAssetCount : 0L);
            
            BigDecimal monthlyInboundValue = assetInboundMapper.sumMonthlyInboundValue(startDate, endDate);
            statistics.setMonthlyInboundValue(monthlyInboundValue != null ? monthlyInboundValue : BigDecimal.ZERO);
            
            // 待处理入库单统计
            List<Map<String, Object>> statusStats = assetInboundMapper.countInboundByStatus();
            long pendingConfirm = 0L, pendingAudit = 0L;
            
            for (Map<String, Object> stat : statusStats) {
                Integer status = (Integer) stat.get("status");
                Long count = ((Number) stat.get("count")).longValue();
                
                if (status != null) {
                    switch (status) {
                        case 2: // 待确认
                            pendingConfirm = count;
                            break;
                        case 3: // 待审核
                            pendingAudit = count;
                            break;
                    }
                }
            }
            
            statistics.setPendingConfirmCount(pendingConfirm);
            statistics.setPendingAuditCount(pendingAudit);
            
            // 入库趋势数据（最近7天）
            LocalDate trendEndDate = LocalDate.now().plusDays(1);
            LocalDate trendStartDate = trendEndDate.minusDays(7);
            
            String trendStartDateStr = trendStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String trendEndDateStr = trendEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            
            List<Map<String, Object>> trendData = assetInboundMapper.selectInboundTrends(trendStartDateStr, trendEndDateStr);
            List<AssetWorkbenchVo.InboundTrendVo> inboundTrends = trendData.stream().map(data -> {
                AssetWorkbenchVo.InboundTrendVo trend = new AssetWorkbenchVo.InboundTrendVo();
                trend.setDate((String) data.get("date"));
                trend.setInboundCount(((Number) data.getOrDefault("inboundCount", 0)).longValue());
                trend.setAssetCount(((Number) data.getOrDefault("assetCount", 0)).longValue());
                trend.setAssetValue((BigDecimal) data.getOrDefault("assetValue", BigDecimal.ZERO));
                return trend;
            }).collect(Collectors.toList());
            
            statistics.setInboundTrends(inboundTrends);
            
        } catch (Exception e) {
            log.error("获取入库出库统计数据失败", e);
        }
        
        return statistics;
    }
    
    @Override
    public AssetWorkbenchVo.AssetDisposalStatisticsVo getDisposalStatistics() {
        AssetWorkbenchVo.AssetDisposalStatisticsVo statistics = new AssetWorkbenchVo.AssetDisposalStatisticsVo();
        
        try {
            // 获取当前月份的开始和结束日期
            LocalDate now = LocalDate.now();
            LocalDate monthStart = now.withDayOfMonth(1);
            LocalDate monthEnd = now.plusMonths(1).withDayOfMonth(1);
            
            String startDate = monthStart.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String endDate = monthEnd.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            
            // 待审核和处置中统计
            Long pendingApprovalCount = assetDisposalMapper.countPendingApproval();
            statistics.setPendingApprovalCount(pendingApprovalCount != null ? pendingApprovalCount : 0L);
            
            Long processingCount = assetDisposalMapper.countProcessing();
            statistics.setProcessingCount(processingCount != null ? processingCount : 0L);
            
            // 本月完成处置统计
            Long monthlyCompletedCount = assetDisposalMapper.countMonthlyCompleted(startDate, endDate);
            statistics.setMonthlyCompletedCount(monthlyCompletedCount != null ? monthlyCompletedCount : 0L);
            
            BigDecimal monthlyDisposalValue = assetDisposalMapper.sumMonthlyDisposalValue(startDate, endDate);
            statistics.setMonthlyDisposalValue(monthlyDisposalValue != null ? monthlyDisposalValue : BigDecimal.ZERO);
            
            // 按类型统计处置数据
            List<Map<String, Object>> typeData = assetDisposalMapper.selectDisposalByType();
            List<AssetWorkbenchVo.DisposalTypeStatisticsVo> typeStatistics = typeData.stream().map(data -> {
                AssetWorkbenchVo.DisposalTypeStatisticsVo typeStat = new AssetWorkbenchVo.DisposalTypeStatisticsVo();
                typeStat.setDisposalType((Integer) data.get("disposal_type"));
                typeStat.setTypeName((String) data.get("typeName"));
                typeStat.setCount(((Number) data.getOrDefault("count", 0)).longValue());
                typeStat.setTotalValue((BigDecimal) data.getOrDefault("totalValue", BigDecimal.ZERO));
                return typeStat;
            }).collect(Collectors.toList());
            
            statistics.setTypeStatistics(typeStatistics);
            
            // 处置趋势数据（最近7天）
            LocalDate trendEndDate = LocalDate.now().plusDays(1);
            LocalDate trendStartDate = trendEndDate.minusDays(7);
            
            String trendStartDateStr = trendStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String trendEndDateStr = trendEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            
            List<Map<String, Object>> trendData = assetDisposalMapper.selectDisposalTrends(trendStartDateStr, trendEndDateStr);
            List<AssetWorkbenchVo.DisposalTrendVo> disposalTrends = trendData.stream().map(data -> {
                AssetWorkbenchVo.DisposalTrendVo trend = new AssetWorkbenchVo.DisposalTrendVo();
                trend.setDate((String) data.get("date"));
                trend.setApplicationCount(((Number) data.getOrDefault("applicationCount", 0)).longValue());
                trend.setCompletedCount(((Number) data.getOrDefault("completedCount", 0)).longValue());
                trend.setDisposalValue((BigDecimal) data.getOrDefault("disposalValue", BigDecimal.ZERO));
                return trend;
            }).collect(Collectors.toList());
            
            statistics.setDisposalTrends(disposalTrends);
            
        } catch (Exception e) {
            log.error("获取处置统计数据失败", e);
        }
        
        return statistics;
    }
    
    @Override
    public AssetWorkbenchVo getWorkbenchData() {
        AssetWorkbenchVo workbench = new AssetWorkbenchVo();
        
        try {
            // 获取概览数据
            workbench.setOverview(getAssetOverview());
            
            // 获取入库出库统计
            workbench.setInOutStatistics(getInOutStatistics());
            
            // 获取处置统计
            workbench.setDisposalStatistics(getDisposalStatistics());
            
            // 获取趋势数据（最近30天）
            List<AssetWorkbenchVo.AssetTrendVo> trends = new ArrayList<>();
            // 这里可以扩展为获取多天的趋势数据
            trends.add(getAssetTrends(30));
            workbench.setTrends(trends);
            
        } catch (Exception e) {
            log.error("获取工作台数据失败", e);
        }
        
        return workbench;
    }
}
