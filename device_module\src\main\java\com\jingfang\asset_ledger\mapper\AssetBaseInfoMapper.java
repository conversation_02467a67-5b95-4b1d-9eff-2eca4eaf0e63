package com.jingfang.asset_ledger.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.asset_ledger.module.dto.AttachmentDto;
import com.jingfang.asset_ledger.module.entity.AssetBaseInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jingfang.asset_ledger.module.request.AssetSearchRequest;
import com.jingfang.asset_ledger.module.vo.AssetBaseInfoVo;
import com.jingfang.asset_ledger.module.vo.AssetDetailBaseInfoVo;
import com.jingfang.common.core.domain.entity.UserLite;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【asset_ledger】的数据库操作Mapper
* @createDate 2025-04-16 16:41:33
* @Entity com.jingfang.assetLedger.module.entity.AssetLedger
*/
@Mapper
public interface AssetBaseInfoMapper extends BaseMapper<AssetBaseInfo> {

    int saveManagerIds(@Param("assetId") String assetId,@Param("managerIds") List<Long> managerIds);

    int saveAttachmentList(@Param("assetId") String assetId,@Param("attachments") List<AttachmentDto> attachmentList);

    int savePictures(@Param("assetId")String assetId,@Param("urls") List<String> pictureUrls);

    IPage<AssetBaseInfoVo> selectAssetList(IPage<AssetBaseInfoVo> page,@Param("request") AssetSearchRequest request);

    AssetDetailBaseInfoVo selectBaseInfoById(String assetId);

    List<UserLite> selectManagersByAssetId(String assetId);

    List<String> selectPictureUrlsByAssetId(String assetId);

    List<AttachmentDto> selectAttachmentByAssetId(String assetId);

    int deleteManagerIds(@Param("assetId") String assetId);

    int deleteAttachmentList(@Param("assetId") String assetId);

    int deletePictureList(@Param("assetId") String assetId);

    int deleteBaseInfo(@Param("assetId") String assetId);

    /**
     * 统计资产总数
     */
    Long countTotalAssets();

    /**
     * 统计资产总值
     */
    BigDecimal sumTotalAssetValue();

    /**
     * 统计本月新增资产数
     */
    Long countMonthlyNewAssets(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 统计本月新增资产值
     */
    BigDecimal sumMonthlyNewAssetValue(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 按状态统计资产数量
     */
    List<Map<String, Object>> countAssetsByStatus();

    /**
     * 统计资产趋势数据
     */
    List<Map<String, Object>> selectAssetTrends(@Param("startDate") String startDate, @Param("endDate") String endDate);
}




