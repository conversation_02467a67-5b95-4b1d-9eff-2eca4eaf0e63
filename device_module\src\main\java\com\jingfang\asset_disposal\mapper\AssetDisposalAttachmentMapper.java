package com.jingfang.asset_disposal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jingfang.asset_disposal.module.entity.AssetDisposalAttachment;
import com.jingfang.asset_disposal.module.dto.AssetDisposalAttachmentDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资产处置附件Mapper接口
 */
@Mapper
public interface AssetDisposalAttachmentMapper extends BaseMapper<AssetDisposalAttachment> {
    
    /**
     * 根据处置单ID查询附件
     */
    List<AssetDisposalAttachmentDto> selectAttachmentsByDisposalId(@Param("disposalId") String disposalId);
    
    /**
     * 批量保存附件
     */
    int batchInsertAttachments(@Param("disposalId") String disposalId, 
                              @Param("attachmentList") List<AssetDisposalAttachmentDto> attachmentList);
    
    /**
     * 删除处置单的所有附件
     */
    int deleteAttachmentsByDisposalId(@Param("disposalId") String disposalId);
} 