package com.jingfang.wh_item.module.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 库存查看VO
 */
@Data
public class ItemStockViewVo implements Serializable {
    
    /**
     * 物品ID
     */
    private String itemId;
    
    /**
     * 物品名称
     */
    private String itemName;
    
    /**
     * 物品编码
     */
    private String itemCode;
    
    /**
     * 物品类型(1-消耗品, 2-备品备件)
     */
    private Integer itemType;
    
    /**
     * 物品类型名称
     */
    private String itemTypeName;
    
    /**
     * 规格型号
     */
    private String specModel;
    
    /**
     * 单位
     */
    private String unit;
    
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    
    /**
     * 仓库名称
     */
    private String warehouseName;
    
    /**
     * 货架位置/库位
     */
    private String shelfLocation;
    
    /**
     * 当前库存数量
     */
    private BigDecimal currentQuantity;
    
    /**
     * 安全库存（仓库级）
     */
    private BigDecimal safetyStock;
    
    /**
     * 企业级安全库存
     */
    private BigDecimal enterpriseSafetyStock;
    
    /**
     * 总库存数量（所有仓库库存之和）
     */
    private BigDecimal totalQuantity;
    
    /**
     * 库存状态(1-正常, 2-不足, 3-过剩)
     */
    private Integer stockStatus;
    
    /**
     * 库存状态名称
     */
    private String stockStatusName;
    
    /**
     * 库存详情列表（按物品展示模式时使用，包含该物品在各个仓库的库存情况）
     */
    private List<ItemStockDetailVo> stockDetails;
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 库存详情内部类
     */
    @Data
    public static class ItemStockDetailVo implements Serializable {
        
        /**
         * 仓库ID
         */
        private Integer warehouseId;
        
        /**
         * 仓库名称
         */
        private String warehouseName;
        
        /**
         * 货架位置/库位
         */
        private String shelfLocation;
        
        /**
         * 当前库存数量
         */
        private BigDecimal currentQuantity;
        
        /**
         * 安全库存（仓库级）
         */
        private BigDecimal safetyStock;
        
        /**
         * 库存状态(1-正常, 2-不足, 3-过剩)
         */
        private Integer stockStatus;
        
        /**
         * 库存状态名称
         */
        private String stockStatusName;
        
        private static final long serialVersionUID = 1L;
    }
} 