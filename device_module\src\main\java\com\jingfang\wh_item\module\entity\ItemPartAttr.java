package com.jingfang.wh_item.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 备品备件特有属性表
 * @TableName item_part_attr
 */
@TableName(value ="item_part_attr")
@Data
public class ItemPartAttr implements Serializable {
    /**
     * 属性ID
     */
    @TableId(type = IdType.INPUT)
    private String attrId;

    /**
     * 物品ID
     */
    private String itemId;

    /**
     * 适用设备型号
     */
    @TableField("applicable_device")
    private String applicableDevice;

    /**
     * 备件分类(1-关键, 2-常用, 3-次要)
     */
    @TableField("part_category")
    private Integer partCategory;

    /**
     * 建议更换周期(天)
     */
    @TableField("replacement_cycle")
    private Integer replacementCycle;

    /**
     * 维修历史关联
     */
    @TableField("maintenance_history")
    private String maintenanceHistory;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
} 