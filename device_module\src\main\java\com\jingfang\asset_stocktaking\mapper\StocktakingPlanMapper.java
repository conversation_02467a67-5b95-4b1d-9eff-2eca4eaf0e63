package com.jingfang.asset_stocktaking.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.asset_stocktaking.module.entity.AssetStocktakingPlan;
import com.jingfang.asset_stocktaking.module.request.PlanSearchRequest;
import com.jingfang.asset_stocktaking.module.vo.StocktakingPlanVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 盘点计划数据访问层
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Mapper
public interface StocktakingPlanMapper extends BaseMapper<AssetStocktakingPlan> {

    /**
     * 分页查询盘点计划列表
     * 
     * @param page 分页对象
     * @param request 查询条件
     * @return 盘点计划列表
     */
    IPage<StocktakingPlanVo> selectPlanList(IPage<StocktakingPlanVo> page, @Param("request") PlanSearchRequest request);

    /**
     * 根据ID查询盘点计划详情
     * 
     * @param planId 计划ID
     * @return 盘点计划详情
     */
    StocktakingPlanVo selectPlanById(@Param("planId") String planId);

    /**
     * 查询盘点计划统计信息
     * 
     * @param planId 计划ID
     * @return 统计信息
     */
    StocktakingPlanVo.PlanStatistics selectPlanStatistics(@Param("planId") String planId);

    /**
     * 根据状态查询盘点计划
     * 
     * @param status 状态
     * @return 盘点计划列表
     */
    List<AssetStocktakingPlan> selectPlanByStatus(@Param("status") Integer status);

    /**
     * 根据负责人查询盘点计划
     * 
     * @param responsibleUserId 负责人ID
     * @return 盘点计划列表
     */
    List<AssetStocktakingPlan> selectPlanByResponsibleUser(@Param("responsibleUserId") Long responsibleUserId);

    /**
     * 查询指定时间范围内的盘点计划
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 盘点计划列表
     */
    List<AssetStocktakingPlan> selectPlanByDateRange(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 统计各状态的盘点计划数量
     * 
     * @return 状态统计
     */
    List<java.util.Map<String, Object>> countPlanByStatus();

    /**
     * 查询即将到期的盘点计划
     * 
     * @param days 提前天数
     * @return 即将到期的计划列表
     */
    List<AssetStocktakingPlan> selectExpiringPlans(@Param("days") Integer days);

    /**
     * 查询逾期的盘点计划
     * 
     * @return 逾期计划列表
     */
    List<AssetStocktakingPlan> selectOverduePlans();

    /**
     * 更新计划状态
     * 
     * @param planId 计划ID
     * @param status 新状态
     * @param updateBy 更新人
     * @return 影响行数
     */
    int updatePlanStatus(@Param("planId") String planId, @Param("status") Integer status, @Param("updateBy") String updateBy);

    /**
     * 批量更新计划状态
     * 
     * @param planIds 计划ID列表
     * @param status 新状态
     * @param updateBy 更新人
     * @return 影响行数
     */
    int batchUpdatePlanStatus(@Param("planIds") List<String> planIds, @Param("status") Integer status, @Param("updateBy") String updateBy);

    /**
     * 检查计划名称是否重复
     * 
     * @param planName 计划名称
     * @param excludePlanId 排除的计划ID（编辑时使用）
     * @return 重复数量
     */
    int checkPlanNameExists(@Param("planName") String planName, @Param("excludePlanId") String excludePlanId);

    /**
     * 查询用户有权限的盘点计划
     * 
     * @param userId 用户ID
     * @param deptId 部门ID
     * @return 盘点计划列表
     */
    List<AssetStocktakingPlan> selectPlanByUserPermission(@Param("userId") Long userId, @Param("deptId") Long deptId);
}
