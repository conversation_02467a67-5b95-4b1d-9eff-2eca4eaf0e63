package com.jingfang.wh_item.module.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 库存盘点查询请求
 */
@Data
public class ItemStocktakingSearchRequest implements Serializable {
    
    /**
     * 页码
     */
    private Integer pageNum = 1;
    
    /**
     * 每页大小
     */
    private Integer pageSize = 10;
    
    /**
     * 盘点单号
     */
    private String stocktakingCode;
    
    /**
     * 盘点名称
     */
    private String stocktakingName;
    
    /**
     * 盘点类型(1-全盘，2-抽盘，3-循环盘点)
     */
    private Integer stocktakingType;
    
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    
    /**
     * 状态(0-草稿，1-进行中，2-已完成，3-已审核)
     */
    private Integer status;
    
    /**
     * 创建人姓名
     */
    private String creatorName;
    
    /**
     * 计划开始时间-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planStartTimeBegin;
    
    /**
     * 计划开始时间-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planStartTimeEnd;
    
    /**
     * 创建时间-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeBegin;
    
    /**
     * 创建时间-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeEnd;
    
    private static final long serialVersionUID = 1L;
} 