package com.jingfang.collaboration.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 在线表格实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("collaboration_spreadsheet")
public class Spreadsheet {
    
    /**
     * 表格ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    
    /**
     * 表格标题
     */
    private String title;
    
    /**
     * 表格描述
     */
    private String description;
    
    /**
     * 表格数据（JSON格式存储Luckysheet数据）
     */
    private String data;
    
    /**
     * 创建者ID
     */
    private Long createBy;
    
    /**
     * 创建者姓名
     */
    private String createByName;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 更新者ID
     */
    private Long updateBy;
    
    /**
     * 更新者姓名
     */
    private String updateByName;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    
    /**
     * 状态（0正常 1停用）
     */
    private String status;
    
    /**
     * 是否公开（0私有 1公开）
     */
    private String isPublic;
    
    /**
     * 分享链接token
     */
    private String shareToken;
    
    /**
     * 分享密码
     */
    private String sharePassword;
    
    /**
     * 分享过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date shareExpireTime;
    
    /**
     * 版本号
     */
    private Integer version;
    
    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private Integer delFlag;
    
    /**
     * 备注
     */
    private String remark;
}
