package com.jingfang.web.websocket;

import com.alibaba.fastjson2.JSON;
import com.jingfang.framework.websocket.SemaphoreUtils;
import com.jingfang.framework.websocket.WebSocketUsers;
import jakarta.websocket.*;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;

/**
 * 协作表格 WebSocket 服务器
 * 
 * <AUTHOR>
 */
@Component
@ServerEndpoint("/websocket/spreadsheet/{spreadsheetId}")
public class SpreadsheetWebSocketServer
{
    /**
     * 日志控制器
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(SpreadsheetWebSocketServer.class);

    /**
     * 默认最多允许同时在线人数100
     */
    public static int socketMaxOnlineCount = 100;

    private static Semaphore socketSemaphore = new Semaphore(socketMaxOnlineCount);

    /**
     * 存储表格会话信息：spreadsheetId -> Map<sessionId, Session>
     */
    private static Map<String, Map<String, Session>> spreadsheetSessions = new ConcurrentHashMap<>();

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("spreadsheetId") String spreadsheetId) throws Exception
    {
        boolean semaphoreFlag = false;
        // 尝试获取信号量
        semaphoreFlag = SemaphoreUtils.tryAcquire(socketSemaphore);
        if (!semaphoreFlag)
        {
            // 未获取到信号量
            LOGGER.error("当前在线人数超过限制数: {}", socketMaxOnlineCount);
            sendMessage(session, createMessage("error", "当前在线人数超过限制数：" + socketMaxOnlineCount, null));
            session.close();
            return;
        }

        // 添加到表格会话管理
        spreadsheetSessions.computeIfAbsent(spreadsheetId, k -> new ConcurrentHashMap<>())
                .put(session.getId(), session);

        // 添加到全局用户管理
        WebSocketUsers.put(session.getId(), session);

        LOGGER.info("用户连接到表格 {} - 会话ID: {}", spreadsheetId, session.getId());
        LOGGER.info("表格 {} 当前在线人数: {}", spreadsheetId, 
                spreadsheetSessions.getOrDefault(spreadsheetId, new ConcurrentHashMap<>()).size());

        // 发送连接成功消息
        sendMessage(session, createMessage("connected", "连接成功", Map.of("spreadsheetId", spreadsheetId)));

        // 通知其他用户有新用户加入
        broadcastToOthers(spreadsheetId, session, createMessage("user_joined", "有新用户加入", 
                Map.of("sessionId", session.getId())));
    }

    /**
     * 连接关闭时处理
     */
    @OnClose
    public void onClose(Session session, @PathParam("spreadsheetId") String spreadsheetId)
    {
        LOGGER.info("用户断开表格 {} 连接 - 会话ID: {}", spreadsheetId, session.getId());

        // 从表格会话管理中移除
        Map<String, Session> sessions = spreadsheetSessions.get(spreadsheetId);
        if (sessions != null) {
            sessions.remove(session.getId());
            if (sessions.isEmpty()) {
                spreadsheetSessions.remove(spreadsheetId);
            }
        }

        // 从全局用户管理中移除
        boolean removeFlag = WebSocketUsers.remove(session.getId());
        if (removeFlag) {
            // 释放信号量
            SemaphoreUtils.release(socketSemaphore);
        }

        // 通知其他用户有用户离开
        broadcastToOthers(spreadsheetId, session, createMessage("user_left", "有用户离开", 
                Map.of("sessionId", session.getId())));
    }

    /**
     * 抛出异常时处理
     */
    @OnError
    public void onError(Session session, Throwable exception, @PathParam("spreadsheetId") String spreadsheetId) throws Exception
    {
        if (session.isOpen()) {
            session.close();
        }
        
        String sessionId = session.getId();
        LOGGER.error("表格 {} WebSocket连接异常 - 会话ID: {}", spreadsheetId, sessionId, exception);

        // 清理会话
        Map<String, Session> sessions = spreadsheetSessions.get(spreadsheetId);
        if (sessions != null) {
            sessions.remove(sessionId);
            if (sessions.isEmpty()) {
                spreadsheetSessions.remove(spreadsheetId);
            }
        }

        WebSocketUsers.remove(sessionId);
        SemaphoreUtils.release(socketSemaphore);
    }

    /**
     * 服务器接收到客户端消息时调用的方法
     */
    @OnMessage
    public void onMessage(String message, Session session, @PathParam("spreadsheetId") String spreadsheetId)
    {
        try {
            LOGGER.info("收到表格 {} 消息: {}", spreadsheetId, message);
            
            Map<String, Object> messageData = JSON.parseObject(message, Map.class);
            String type = (String) messageData.get("type");
            
            switch (type) {
                case "cell_update":
                    handleCellUpdate(spreadsheetId, session, messageData);
                    break;
                case "cursor_move":
                    handleCursorMove(spreadsheetId, session, messageData);
                    break;
                case "selection_change":
                    handleSelectionChange(spreadsheetId, session, messageData);
                    break;
                case "heartbeat":
                    handleHeartbeat(spreadsheetId, session);
                    break;
                default:
                    LOGGER.warn("未知的消息类型: {}", type);
            }
        } catch (Exception e) {
            LOGGER.error("处理WebSocket消息失败", e);
            sendMessage(session, createMessage("error", "消息处理失败: " + e.getMessage(), null));
        }
    }

    /**
     * 处理单元格更新
     */
    private void handleCellUpdate(String spreadsheetId, Session session, Map<String, Object> messageData) {
        // 广播给其他用户
        broadcastToOthers(spreadsheetId, session, createMessage("cell_update", "单元格更新", messageData));
    }

    /**
     * 处理光标移动
     */
    private void handleCursorMove(String spreadsheetId, Session session, Map<String, Object> messageData) {
        // 广播给其他用户
        broadcastToOthers(spreadsheetId, session, createMessage("cursor_move", "光标移动", messageData));
    }

    /**
     * 处理选择区域变化
     */
    private void handleSelectionChange(String spreadsheetId, Session session, Map<String, Object> messageData) {
        // 广播给其他用户
        broadcastToOthers(spreadsheetId, session, createMessage("selection_change", "选择区域变化", messageData));
    }

    /**
     * 处理心跳
     */
    private void handleHeartbeat(String spreadsheetId, Session session) {
        sendMessage(session, createMessage("heartbeat_response", "心跳响应", 
                Map.of("timestamp", System.currentTimeMillis())));
    }

    /**
     * 向指定会话发送消息
     */
    private void sendMessage(Session session, String message) {
        WebSocketUsers.sendMessageToUserByText(session, message);
    }

    /**
     * 广播消息给表格中的其他用户（排除发送者）
     */
    private void broadcastToOthers(String spreadsheetId, Session senderSession, String message) {
        Map<String, Session> sessions = spreadsheetSessions.get(spreadsheetId);
        if (sessions != null) {
            sessions.values().stream()
                    .filter(session -> !session.getId().equals(senderSession.getId()))
                    .forEach(session -> sendMessage(session, message));
        }
    }

    /**
     * 创建消息
     */
    private String createMessage(String type, String message, Map<String, Object> data) {
        Map<String, Object> messageMap = Map.of(
                "type", type,
                "message", message,
                "data", data != null ? data : Map.of(),
                "timestamp", System.currentTimeMillis()
        );
        return JSON.toJSONString(messageMap);
    }

    /**
     * 获取表格在线用户数
     */
    public static int getSpreadsheetOnlineCount(String spreadsheetId) {
        Map<String, Session> sessions = spreadsheetSessions.get(spreadsheetId);
        return sessions != null ? sessions.size() : 0;
    }
}
