package com.jingfang.asset_part_relation.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 资产备品备件关联表
 * @TableName asset_part_relation
 */
@TableName(value = "asset_part_relation")
@Data
public class AssetPartRelation implements Serializable {
    
    /**
     * 关联ID
     */
    @TableId(type = IdType.INPUT)
    private String relationId;
    
    /**
     * 资产ID
     */
    private String assetId;
    
    /**
     * 备品备件ID（物品ID，且物品类型为2）
     */
    private String partId;
    
    /**
     * 关联类型(1-必需, 2-推荐, 3-可选)
     */
    private Integer relationType;
    
    /**
     * 建议库存数量
     */
    private Integer suggestedQuantity;
    
    /**
     * 备注说明
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 是否删除(0-否, 1-是)
     */
    private Integer deleted;
    
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
} 