package com.jingfang.asset_stocktaking.module.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 差异处理信息数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
public class DifferenceHandleInfo implements Serializable {
    
    /**
     * 处理方式：1-台账更新，2-资产入库，3-资产核销，4-位置调整，5-状态更新
     */
    private Integer handleType;
    
    /**
     * 处理人员ID
     */
    private Long handleUserId;
    
    /**
     * 处理时间
     */
    private String handleTime;
    
    /**
     * 处理结果
     */
    private String handleResult;
    
    /**
     * 审批流程ID
     */
    private String approvalId;
    
    /**
     * 是否需要审批
     */
    private Boolean needApproval;

    private static final long serialVersionUID = 1L;
}
