package com.jingfang.asset_stocktaking.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 资产盘点任务实体类
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@TableName(value = "asset_stocktaking_task")
public class AssetStocktakingTask implements Serializable {

    /**
     * 盘点任务ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String taskId;

    /**
     * 盘点计划ID
     */
    private String planId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 分配给的用户ID
     */
    private Long assignedUserId;

    /**
     * 资产范围（JSON格式）
     */
    private String assetScope;

    /**
     * 预期盘点数量
     */
    private Integer expectedCount;

    /**
     * 实际盘点数量
     */
    private Integer actualCount;

    /**
     * 状态：1-待执行，2-执行中，3-已完成
     */
    private Integer status;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 任务状态常量
     */
    public static final class Status {
        /** 待执行 */
        public static final int PENDING = 1;
        /** 执行中 */
        public static final int IN_PROGRESS = 2;
        /** 已完成 */
        public static final int COMPLETED = 3;
    }

    /**
     * 计算完成进度百分比
     * 
     * @return 完成进度（0-100）
     */
    public double getProgressPercentage() {
        if (expectedCount == null || expectedCount == 0) {
            return 0.0;
        }
        if (actualCount == null) {
            return 0.0;
        }
        return Math.min(100.0, (actualCount.doubleValue() / expectedCount.doubleValue()) * 100.0);
    }

    /**
     * 判断任务是否已完成
     * 
     * @return true-已完成，false-未完成
     */
    public boolean isCompleted() {
        return Status.COMPLETED == this.status;
    }

    /**
     * 判断任务是否进行中
     * 
     * @return true-进行中，false-未进行
     */
    public boolean isInProgress() {
        return Status.IN_PROGRESS == this.status;
    }
}
