package com.jingfang.asset_inbound.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingfang.asset_inbound.module.entity.AssetInboundOperationLog;
import com.jingfang.asset_inbound.service.AssetInboundOperationLogService;
import com.jingfang.asset_inbound.mapper.AssetInboundOperationLogMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【asset_inbound_operation_log(入库单操作日志表)】的数据库操作Service实现
* @createDate 2025-05-07 14:08:37
*/
@Service
public class AssetInboundOperationLogServiceImpl extends ServiceImpl<AssetInboundOperationLogMapper, AssetInboundOperationLog>
    implements AssetInboundOperationLogService{

}




