package com.jingfang.asset_stocktaking.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jingfang.asset_stocktaking.module.dto.StocktakingRecordDto;
import com.jingfang.asset_stocktaking.module.entity.AssetStocktakingRecord;
import com.jingfang.asset_stocktaking.module.request.RecordSearchRequest;
import com.jingfang.asset_stocktaking.module.vo.StocktakingRecordVo;

import java.util.List;

/**
 * 盘点记录服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface StocktakingRecordService extends IService<AssetStocktakingRecord> {

    /**
     * 创建盘点记录
     * 
     * @param recordDto 记录数据
     * @return 是否成功
     */
    boolean createRecord(StocktakingRecordDto recordDto);

    /**
     * 批量创建盘点记录
     * 
     * @param recordDto 包含批量记录的数据
     * @return 是否成功
     */
    boolean batchCreateRecords(StocktakingRecordDto recordDto);

    /**
     * 编辑盘点记录
     * 
     * @param recordDto 记录数据
     * @return 是否成功
     */
    boolean editRecord(StocktakingRecordDto recordDto);

    /**
     * 删除盘点记录
     * 
     * @param recordId 记录ID
     * @return 是否成功
     */
    boolean deleteRecord(String recordId);

    /**
     * 批量删除盘点记录
     * 
     * @param recordIds 记录ID列表
     * @return 是否成功
     */
    boolean batchDeleteRecords(List<String> recordIds);

    /**
     * 分页查询盘点记录列表
     * 
     * @param request 查询条件
     * @return 分页结果
     */
    IPage<StocktakingRecordVo> selectRecordList(RecordSearchRequest request);

    /**
     * 根据ID查询盘点记录详情
     * 
     * @param recordId 记录ID
     * @return 记录详情
     */
    StocktakingRecordVo selectRecordById(String recordId);

    /**
     * 扫码盘点
     * 
     * @param taskId 任务ID
     * @param scanContent 扫码内容
     * @param userId 盘点人ID
     * @return 盘点结果
     */
    StocktakingRecordVo scanInventory(String taskId, String scanContent, Long userId);

    /**
     * 手动录入盘点结果
     * 
     * @param recordDto 记录数据
     * @return 是否成功
     */
    boolean manualInventory(StocktakingRecordDto recordDto);

    /**
     * 根据任务ID查询记录列表
     * 
     * @param taskId 任务ID
     * @return 记录列表
     */
    List<AssetStocktakingRecord> selectRecordByTaskId(String taskId);

    /**
     * 根据资产ID查询记录列表
     * 
     * @param assetId 资产ID
     * @return 记录列表
     */
    List<AssetStocktakingRecord> selectRecordByAssetId(String assetId);

    /**
     * 查询异常记录
     * 
     * @param taskId 任务ID
     * @return 异常记录列表
     */
    List<AssetStocktakingRecord> selectAbnormalRecords(String taskId);

    /**
     * 查询差异记录
     * 
     * @param taskId 任务ID
     * @return 差异记录列表
     */
    List<StocktakingRecordVo> selectDifferenceRecords(String taskId);

    /**
     * 统计任务的记录情况
     * 
     * @param taskId 任务ID
     * @return 记录统计
     */
    java.util.Map<String, Object> countRecordsByTask(String taskId);

    /**
     * 统计计划的记录情况
     * 
     * @param planId 计划ID
     * @return 记录统计
     */
    java.util.Map<String, Object> countRecordsByPlan(String planId);

    /**
     * 查询重复盘点的资产
     * 
     * @param planId 计划ID
     * @return 重复盘点的资产列表
     */
    List<java.util.Map<String, Object>> selectDuplicateRecords(String planId);

    /**
     * 查询未盘点的资产
     * 
     * @param taskId 任务ID
     * @return 未盘点的资产列表
     */
    List<java.util.Map<String, Object>> selectMissingAssets(String taskId);

    /**
     * 检查资产是否已盘点
     * 
     * @param assetId 资产ID
     * @param taskId 任务ID
     * @return 是否已盘点
     */
    boolean checkAssetInventoried(String assetId, String taskId);

    /**
     * 根据资产编码查询记录
     * 
     * @param assetCode 资产编码
     * @param taskId 任务ID
     * @return 盘点记录
     */
    AssetStocktakingRecord selectRecordByAssetCode(String assetCode, String taskId);

    /**
     * 查询任务的盘点进度
     * 
     * @param taskId 任务ID
     * @return 进度信息
     */
    java.util.Map<String, Object> selectTaskInventoryProgress(String taskId);

    /**
     * 批量更新记录状态
     * 
     * @param recordIds 记录ID列表
     * @param foundStatus 发现状态
     * @return 是否成功
     */
    boolean batchUpdateFoundStatus(List<String> recordIds, Integer foundStatus);

    /**
     * 验证记录数据
     * 
     * @param recordDto 记录数据
     * @return 验证结果
     */
    boolean validateRecordData(StocktakingRecordDto recordDto);

    /**
     * 导入盘点记录
     * 
     * @param taskId 任务ID
     * @param records 记录列表
     * @return 导入结果
     */
    java.util.Map<String, Object> importRecords(String taskId, List<StocktakingRecordDto> records);

    /**
     * 导出盘点记录
     * 
     * @param request 查询条件
     * @return 记录列表
     */
    List<StocktakingRecordVo> exportRecords(RecordSearchRequest request);
}
