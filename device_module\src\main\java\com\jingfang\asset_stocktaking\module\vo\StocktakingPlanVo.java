package com.jingfang.asset_stocktaking.module.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 盘点计划视图对象
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
public class StocktakingPlanVo implements Serializable {

    /**
     * 盘点计划ID
     */
    private String planId;

    /**
     * 盘点计划名称
     */
    private String planName;

    /**
     * 盘点类型：1-全盘，2-部分盘点
     */
    private Integer planType;

    /**
     * 盘点类型描述
     */
    private String planTypeDesc;

    /**
     * 盘点范围（JSON格式）
     */
    private String planScope;

    /**
     * 计划开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    /**
     * 计划结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    /**
     * 负责人ID
     */
    private Long responsibleUserId;

    /**
     * 负责人姓名
     */
    private String responsibleUserName;

    /**
     * 状态：1-草稿，2-待审批，3-执行中，4-已完成，5-已取消
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 计划统计信息
     */
    private PlanStatistics statistics;

    /**
     * 计划统计信息内部类
     */
    @Data
    public static class PlanStatistics implements Serializable {
        
        /**
         * 总任务数
         */
        private Integer totalTasks;
        
        /**
         * 已完成任务数
         */
        private Integer completedTasks;
        
        /**
         * 进行中任务数
         */
        private Integer inProgressTasks;
        
        /**
         * 待执行任务数
         */
        private Integer pendingTasks;
        
        /**
         * 总资产数
         */
        private Integer totalAssets;
        
        /**
         * 已盘点资产数
         */
        private Integer inventoriedAssets;
        
        /**
         * 差异资产数
         */
        private Integer differenceAssets;
        
        /**
         * 完成进度百分比
         */
        private Double progressPercentage;
        
        /**
         * 预计完成时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date estimatedCompletionTime;

        private static final long serialVersionUID = 1L;
    }

    private static final long serialVersionUID = 1L;
}
