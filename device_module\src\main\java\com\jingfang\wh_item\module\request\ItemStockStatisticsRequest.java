package com.jingfang.wh_item.module.request;

import lombok.Data;

/**
 * 物品库存统计查询请求
 */
@Data
public class ItemStockStatisticsRequest {
    
    /**
     * 统计类型(1-按仓库统计, 2-按物品统计)
     */
    private Integer statisticsType;
    
    /**
     * 物品ID
     */
    private String itemId;
    
    /**
     * 物品名称
     */
    private String itemName;
    
    /**
     * 物品编码
     */
    private String itemCode;
    
    /**
     * 物品类型(1-消耗品, 2-备品备件)
     */
    private Integer itemType;
    
    /**
     * 规格型号
     */
    private String specModel;
    
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    
    /**
     * 库存状态(1-正常, 2-不足, 3-过剩)
     */
    private Integer stockStatus;
    
    /**
     * 是否只显示有库存的记录(true-只显示库存>0的记录, false-显示所有记录)
     */
    private Boolean onlyWithStock;
} 