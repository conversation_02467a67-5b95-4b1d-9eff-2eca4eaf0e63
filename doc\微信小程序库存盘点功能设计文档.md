# 微信小程序库存盘点功能设计文档

本文档详细描述了微信小程序端库存盘点功能的设计和实现方案，专注于移动端的核心盘点作业功能。

## 一、功能概述

### 1.1 设计目标

微信小程序库存盘点功能旨在为现场盘点人员提供便捷、高效的移动端盘点工具，通过扫码、拍照等移动设备特有功能，提升盘点作业的准确性和效率。

### 1.2 功能范围

本小程序专注于盘点执行环节，包括：
- **盘点执行相关功能**：盘点任务查看、扫码盘点、盘点结果录入
- **现场操作功能**：拍照记录
- **移动端查询功能**：盘点进度查询、历史记录查询

### 1.3 用户角色

- **盘点员**：执行具体盘点作业的现场人员
- **盘点组长**：负责盘点任务分配和进度监控的管理人员

## 二、功能模块设计

### 2.1 盘点执行相关功能

#### 2.1.1 盘点任务查看

**功能描述**：
- 查看分配给当前用户的盘点任务
- 显示任务基本信息和执行状态
- 支持任务筛选和搜索

**界面设计**：
```
┌─────────────────────────────┐
│  我的盘点任务               │
├─────────────────────────────┤
│ 🔍 搜索任务                 │
├─────────────────────────────┤
│ 📋 2024年第一季度全盘       │
│    状态：进行中             │
│    进度：45/120 (37.5%)     │
│    截止：2024-03-15         │
│    [查看详情] [开始盘点]    │
├─────────────────────────────┤
│ 📋 A仓库抽盘               │
│    状态：待开始             │
│    进度：0/35 (0%)          │
│    截止：2024-03-20         │
│    [查看详情] [开始盘点]    │
└─────────────────────────────┘
```

**接口需求**：
- `GET /item/stocktaking/my-tasks` - 获取我的盘点任务列表
- `GET /item/stocktaking/{stocktakingId}` - 获取盘点任务详情
- `GET /item/stocktaking/{stocktakingId}/details` - 获取盘点明细列表

#### 2.1.2 扫码盘点

**功能描述**：
- 扫描物品条码快速定位待盘点物品
- 自动填充物品基本信息
- 支持手动输入物品编码
- 显示账面数量和货架位置

**操作流程**：
```
扫码 → 识别物品 → 显示信息 → 录入实盘数量 → 确认提交
```

**界面设计**：
```
┌─────────────────────────────┐
│  扫码盘点                   │
├─────────────────────────────┤
│ [📷 扫描条码]               │
│ [⌨️ 手动输入]               │
├─────────────────────────────┤
│ 物品信息：                  │
│ 名称：螺丝刀                │
│ 编码：SD001                 │
│ 规格：十字头 6mm            │
│ 位置：A-01-01               │
│ 账面数量：50 把             │
├─────────────────────────────┤
│ 实盘数量：[____] 把         │
│ 差异原因：[选择原因▼]       │
│ [📷 拍照] [💾 保存] [⏭️ 下一个] │
└─────────────────────────────┘
```

**技术实现**：
- 使用 `wx.scanCode` 实现条码扫描
- 调用后端接口获取物品信息
- 本地缓存常用差异原因

#### 2.1.3 盘点结果录入

**功能描述**：
- 录入物品实际盘点数量
- 选择或输入差异原因
- 支持批量录入相同差异原因
- 实时计算和显示差异数量

**数据结构**：
```json
{
  "detailId": "string",
  "itemId": "string", 
  "itemName": "string",
  "itemCode": "string",
  "bookQuantity": 50.00,
  "actualQuantity": 48.00,
  "differenceQuantity": -2.00,
  "differenceReason": "自然损耗",
  "photos": ["photo1.jpg", "photo2.jpg"],
  "checkTime": "2024-03-15 14:30:00"
}
```

**接口需求**：
- `POST /item/stocktaking/record` - 录入盘点结果
- `PUT /item/stocktaking/detail/{detailId}` - 更新盘点明细

### 2.2 现场操作功能

#### 2.2.1 拍照记录

**功能描述**：
- 拍摄物品现场照片作为盘点凭证
- 支持多张照片上传
- 自动压缩图片减少存储空间
- 照片与盘点记录关联

**拍照场景**：
- **物品现场照片**：记录物品实际存放状态
- **货架位置照片**：记录物品在货架上的位置
- **损坏物品照片**：记录损坏或异常物品状态
- **盘点凭证照片**：其他需要记录的现场情况

**技术实现**：
```javascript
// 拍照功能实现
wx.chooseMedia({
  count: 3,
  mediaType: ['image'],
  sourceType: ['album', 'camera'],
  camera: 'back',
  success: (res) => {
    // 压缩图片
    this.compressImages(res.tempFiles);
  }
});
```

**接口需求**：
- `POST /common/upload` - 上传图片文件
- `PUT /item/stocktaking/detail/{detailId}/photos` - 关联照片到盘点记录

### 2.3 移动端查询功能

#### 2.3.1 盘点进度查询

**功能描述**：
- 查看整体盘点进度
- 查看个人盘点进度
- 查看各仓库盘点进度
- 实时更新进度数据

**界面设计**：
```
┌─────────────────────────────┐
│  盘点进度                   │
├─────────────────────────────┤
│ 📊 整体进度                 │
│    总计：500 项             │
│    已完成：320 项 (64%)     │
│    有差异：45 项 (9%)       │
│    ████████░░ 64%           │
├─────────────────────────────┤
│ 👤 我的进度                 │
│    分配：120 项             │
│    已完成：85 项 (71%)      │
│    有差异：12 项 (10%)      │
│    ████████░░ 71%           │
├─────────────────────────────┤
│ 🏢 仓库进度                 │
│    A仓库：████████░░ 75%    │
│    B仓库：██████░░░░ 60%    │
│    C仓库：████████░░ 80%    │
└─────────────────────────────┘
```

**接口需求**：
- `GET /item/stocktaking/{stocktakingId}/progress` - 获取盘点进度
- `GET /item/stocktaking/my-progress` - 获取个人进度

#### 2.3.2 历史记录查询

**功能描述**：
- 查看个人盘点历史记录
- 查看物品盘点历史
- 查看差异记录详情
- 查看历史照片记录

**查询维度**：
- **按时间查询**：今日、本周、本月、自定义时间段
- **按状态查询**：已完成、有差异、待处理
- **按物品查询**：特定物品的盘点历史
- **按仓库查询**：特定仓库的盘点记录

**界面设计**：
```
┌─────────────────────────────┐
│  历史记录                   │
├─────────────────────────────┤
│ 📅 [今日▼] 🔍 [搜索]        │
├─────────────────────────────┤
│ 📦 螺丝刀 (SD001)           │
│    时间：14:30              │
│    账面：50 → 实盘：48      │
│    差异：-2 (自然损耗)      │
│    📷 2张照片               │
├─────────────────────────────┤
│ 📦 扳手 (BS002)             │
│    时间：14:25              │
│    账面：30 → 实盘：30      │
│    差异：0 (无差异)         │
│    📷 1张照片               │
└─────────────────────────────┘
```

**接口需求**：
- `GET /item/stocktaking/my-records` - 获取个人盘点记录
- `GET /item/stocktaking/item-history/{itemId}` - 获取物品盘点历史

## 三、技术架构设计

### 3.1 技术栈选择

**前端技术**：
- **开发框架**：微信小程序原生开发
- **UI组件**：WeUI组件库
- **状态管理**：小程序原生数据绑定
- **本地存储**：wx.storage

**核心API**：
- **扫码功能**：wx.scanCode
- **拍照功能**：wx.chooseMedia
- **网络请求**：wx.request
- **文件上传**：wx.uploadFile

### 3.2 数据存储策略

**本地存储**：
```javascript
// 本地缓存数据结构
const localStorageKeys = {
  userInfo: 'stocktaking_user_info',
  currentTask: 'stocktaking_current_task',
  offlineData: 'stocktaking_offline_data',
  commonReasons: 'stocktaking_common_reasons'
};
```

**离线支持**：
- 缓存盘点任务数据
- 本地存储盘点结果
- 网络恢复时自动同步
- 数据冲突处理机制

### 3.3 接口设计

**基础接口**：
```javascript
// API接口列表
const apiEndpoints = {
  // 任务管理
  getMyTasks: 'GET /item/stocktaking/my-tasks',
  getTaskDetail: 'GET /item/stocktaking/{stocktakingId}',
  getTaskDetails: 'GET /item/stocktaking/{stocktakingId}/details',
  
  // 盘点执行
  recordResult: 'POST /item/stocktaking/record',
  updateDetail: 'PUT /item/stocktaking/detail/{detailId}',
  
  // 进度查询
  getProgress: 'GET /item/stocktaking/{stocktakingId}/progress',
  getMyProgress: 'GET /item/stocktaking/my-progress',
  
  // 历史记录
  getMyRecords: 'GET /item/stocktaking/my-records',
  getItemHistory: 'GET /item/stocktaking/item-history/{itemId}',
  
  // 文件上传
  uploadPhoto: 'POST /common/upload'
};
```

## 四、用户体验设计

### 4.1 操作流程优化

**快速盘点流程**：
```
打开小程序 → 选择任务 → 扫码 → 录入数量 → 拍照(可选) → 保存 → 下一个
```

**批量操作支持**：
- 批量设置相同差异原因
- 批量上传照片
- 批量提交盘点结果

### 4.2 交互设计原则

**简洁高效**：
- 减少操作步骤
- 大按钮设计便于操作
- 关键信息突出显示

**容错性强**：
- 数据本地缓存
- 操作可撤销
- 异常情况友好提示

**反馈及时**：
- 实时显示操作结果
- 进度条显示同步状态
- 成功/失败状态明确提示

## 五、实施计划

### 5.1 开发阶段

**第一阶段**（核心功能）：
- 盘点任务查看
- 扫码盘点基础功能
- 盘点结果录入

**第二阶段**（增强功能）：
- 拍照记录功能
- 离线支持
- 进度查询

**第三阶段**（优化功能）：
- 历史记录查询
- 用户体验优化
- 性能优化

### 5.2 测试验证

**功能测试**：
- 扫码识别准确性测试
- 数据同步完整性测试
- 离线功能可靠性测试

**用户体验测试**：
- 现场作业流程测试
- 不同网络环境测试
- 多用户并发测试

## 六、总结

本微信小程序库存盘点功能设计专注于移动端盘点作业的核心需求，通过扫码、拍照等移动设备特有功能，为盘点人员提供便捷高效的作业工具。设计遵循简洁、高效、可靠的原则，确保在各种现场环境下都能稳定运行，显著提升库存盘点的效率和准确性。
