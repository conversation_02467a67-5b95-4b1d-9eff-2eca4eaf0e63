package com.jingfang.asset_inbound.module.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 入库附件表
 * @TableName asset_inbound_attachment
 */
@TableName(value ="asset_inbound_attachment")
@Data
public class AssetInboundAttachment implements Serializable {
    /**
     * 
     */
    private String inboundId;

    /**
     * 
     */
    private String fileName;

    /**
     * 
     */
    private String fileType;

    /**
     * 
     */
    private String storagePath;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}