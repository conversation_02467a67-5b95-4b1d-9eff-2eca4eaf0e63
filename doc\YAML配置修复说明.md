# YAML配置修复说明

## 问题描述

在启动后端时遇到以下错误：

```
org.yaml.snakeyaml.constructor.DuplicateKeyException: while constructing a mapping
found duplicate key write
```

## 问题原因

在 `application.yml` 文件中，MQTT配置部分存在重复的 `write` 键：

```yaml
# 错误的配置
property:
  write:
    topic: /sys/thing/node/property/set/device_monitor_client
  write:  # 重复的键
    response:
      topic: /sys/thing/node/property/set_reply/device_monitor_client
```

## 解决方案

将重复的 `write` 键合并为一个正确的层级结构：

```yaml
# 正确的配置
property:
  request:
    topic: /sys/thing/node/property/get/device_monitor_client
  response:
    topic: /sys/thing/node/property/get_reply/device_monitor_client
  write:
    topic: /sys/thing/node/property/set/device_monitor_client
    response:
      topic: /sys/thing/node/property/set_reply/device_monitor_client
  timeout: 15
```

## 修复内容

### 1. 修复YAML配置文件

**文件**: `device_monitor-admin/src/main/resources/application.yml`

**修改前**:
```yaml
property:
  write:
    topic: /sys/thing/node/property/set/device_monitor_client
  write:  # 重复键
    response:
      topic: /sys/thing/node/property/set_reply/device_monitor_client
```

**修改后**:
```yaml
property:
  write:
    topic: /sys/thing/node/property/set/device_monitor_client
    response:
      topic: /sys/thing/node/property/set_reply/device_monitor_client
```

### 2. 验证Java配置属性

确认Java代码中的配置属性注解正确：

```java
@Value("${mqtt.device.property.write.topic:/sys/thing/node/property/set/device_monitor_client}")
private String writeRequestTopic;

@Value("${mqtt.device.property.write.response.topic:/sys/thing/node/property/set_reply/device_monitor_client}")
private String writeResponseTopic;
```

## 完整的MQTT配置结构

```yaml
mqtt:
  server-uri: tcp://192.168.110.135:10883
  client-id: device_monitor_client
  username: admin
  password: 429498517
  device:
    status:
      request:
        topic: /sys/thing/node/status/get/device_monitor_client
      response:
        topic: /sys/thing/node/status/get_reply/device_monitor_client
      timeout: 10
    property:
      request:
        topic: /sys/thing/node/property/get/device_monitor_client
      response:
        topic: /sys/thing/node/property/get_reply/device_monitor_client
      write:
        topic: /sys/thing/node/property/set/device_monitor_client
        response:
          topic: /sys/thing/node/property/set_reply/device_monitor_client
      timeout: 15
```

## 验证修复

修复后，应用程序应该能够正常启动，不再出现YAML解析错误。

### 启动日志检查

正常启动后，应该能看到类似以下的日志：

```
MQTT客户端连接成功
已订阅MQTT设备属性响应主题: /sys/thing/node/property/get_reply/device_monitor_client
已订阅MQTT设备属性写入响应主题: /sys/thing/node/property/set_reply/device_monitor_client
MQTT设备属性查询服务初始化完成
```

## 注意事项

1. **YAML语法**: 确保YAML文件中没有重复的键
2. **缩进一致**: 使用空格而不是制表符，保持缩进一致
3. **配置验证**: 启动后检查配置是否正确加载
4. **主题配置**: 确保MQTT主题配置与实际使用的主题一致

修复完成后，MQTT写数据功能应该能够正常工作！
