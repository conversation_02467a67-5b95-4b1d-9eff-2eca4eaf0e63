package com.jingfang.device_module.module.dto;


import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

@Data
public class DeviceAddDto {

    @TableId
    private Long deviceId;

    private String paramKey;

    private String paramName;

    private Integer dataType;

    private Integer storageType;

    private Integer categoryId;

    private Integer paramAddress;

    /**
     * 读写类型（0-只读，1-只写，2-读写）
     */
    private Integer rwType;

    /**
     * 参数类型（0-一般参数，1-告警参数）
     */
    private Integer paramType;

}
