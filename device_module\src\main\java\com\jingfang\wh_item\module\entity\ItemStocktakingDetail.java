package com.jingfang.wh_item.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("item_stocktaking_detail")
public class ItemStocktakingDetail implements Serializable {

    @TableId(type = IdType.INPUT)
    private String detailId;

    private String stocktakingId;

    private String itemId;

    private Integer warehouseId;

    private String shelfLocation;

    private BigDecimal bookQuantity;

    private BigDecimal actualQuantity;

    private BigDecimal differenceQuantity;

    private String differenceReason;

    private Long checkerId;

    private String checkerName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    private Integer status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 照片URL列表，JSON格式存储
     */
    private String photos;
}
