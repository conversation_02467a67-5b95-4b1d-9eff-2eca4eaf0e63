package com.jingfang.asset_ledger.module.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@TableName(value ="asset_ledger")
@Data
public class AssetBaseInfoVo {

    /**
     * 资产编号
     */
    private String assetId;

    /**
     * 办理状态
     */
    private int confirmStatus;

    /**
     * 资产状态
     */
    private Integer assetStatus;

    /**
     * 资产分类ID
     */
    private Integer categoryId;

    /**
     * 资产名称
     */
    private String assetName;

    /**
     * 规格型号
     */
    private String specModel;

    /**
     * 使用组织
     */
    private String deptName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
