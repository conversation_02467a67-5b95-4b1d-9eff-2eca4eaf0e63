package com.jingfang.web.controller.asset.relation;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.asset_part_relation.module.dto.AssetPartRelationDto;
import com.jingfang.asset_part_relation.module.request.AssetPartRelationSearchRequest;
import com.jingfang.asset_part_relation.module.vo.AssetPartRelationVo;
import com.jingfang.asset_part_relation.service.AssetPartRelationService;
import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 资产备品备件关联控制器
 */
@Slf4j
@RestController
@RequestMapping("/asset/part/relation")
public class AssetPartRelationController extends BaseController {
    
    @Resource
    private AssetPartRelationService relationService;
    
    /**
     * 新增资产备品备件关联
     */
    @PreAuthorize("@ss.hasPermi('asset:part:relation:add')")
    @PostMapping
    public AjaxResult add(@RequestBody @Validated AssetPartRelationDto relationDto) {
        try {
            log.info("新增资产备品备件关联请求参数：{}", relationDto);
            boolean success = relationService.addRelation(relationDto, getNickName());
            if (success) {
                return AjaxResult.success("新增关联成功");
            } else {
                return AjaxResult.error("新增关联失败");
            }
        } catch (Exception e) {
            log.error("新增资产备品备件关联异常：", e);
            return AjaxResult.error("新增关联失败：" + e.getMessage());
        }
    }
    
    /**
     * 批量新增资产备品备件关联
     */
    @PreAuthorize("@ss.hasPermi('asset:part:relation:add')")
    @PostMapping("/batch/{assetId}")
    public AjaxResult batchAdd(
            @PathVariable("assetId") String assetId,
            @RequestBody List<AssetPartRelationDto> relationDtos) {
        try {
            log.info("批量新增资产备品备件关联，资产ID：{}，关联数量：{}", assetId, relationDtos.size());
            boolean success = relationService.batchAddRelations(assetId, relationDtos, getNickName());
            if (success) {
                return AjaxResult.success("批量新增关联成功");
            } else {
                return AjaxResult.error("批量新增关联失败");
            }
        } catch (Exception e) {
            log.error("批量新增资产备品备件关联异常：", e);
            return AjaxResult.error("批量新增关联失败：" + e.getMessage());
        }
    }
    
    /**
     * 修改资产备品备件关联
     */
    @PreAuthorize("@ss.hasPermi('asset:part:relation:edit')")
    @PutMapping
    public AjaxResult edit(@RequestBody @Validated AssetPartRelationDto relationDto) {
        try {
            log.info("修改资产备品备件关联请求参数：{}", relationDto);
            boolean success = relationService.updateRelation(relationDto, getNickName());
            if (success) {
                return AjaxResult.success("修改关联成功");
            } else {
                return AjaxResult.error("修改关联失败");
            }
        } catch (Exception e) {
            log.error("修改资产备品备件关联异常：", e);
            return AjaxResult.error("修改关联失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除资产备品备件关联
     */
    @PreAuthorize("@ss.hasPermi('asset:part:relation:remove')")
    @DeleteMapping("/{relationIds}")
    public AjaxResult remove(@PathVariable String relationIds) {
        try {
            log.info("删除资产备品备件关联，ID：{}", relationIds);
            String[] ids = relationIds.split(",");
            boolean success = relationService.deleteRelations(ids, getNickName());
            if (success) {
                return AjaxResult.success("删除关联成功");
            } else {
                return AjaxResult.error("删除关联失败");
            }
        } catch (Exception e) {
            log.error("删除资产备品备件关联异常：", e);
            return AjaxResult.error("删除关联失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询资产备品备件关联列表
     */
    @PreAuthorize("@ss.hasPermi('asset:part:relation:list')")
    @PostMapping("/list")
    public AjaxResult list(@RequestBody AssetPartRelationSearchRequest request) {
        try {
            IPage<AssetPartRelationVo> page = relationService.selectRelationList(request);
            return AjaxResult.success(page);
        } catch (Exception e) {
            log.error("查询资产备品备件关联列表异常：", e);
            return AjaxResult.error("查询关联列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据资产ID查询关联的备品备件列表
     */
    @PreAuthorize("@ss.hasPermi('asset:part:relation:query')")
    @GetMapping("/parts/{assetId}")
    public AjaxResult getPartsByAssetId(@PathVariable("assetId") String assetId) {
        try {
            log.info("查询资产关联的备品备件列表，资产ID：{}", assetId);
            List<AssetPartRelationVo> parts = relationService.selectPartsByAssetId(assetId);
            return AjaxResult.success(parts);
        } catch (Exception e) {
            log.error("查询资产关联的备品备件列表异常：", e);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据备品备件ID查询关联的资产列表
     */
    @PreAuthorize("@ss.hasPermi('asset:part:relation:query')")
    @GetMapping("/assets/{partId}")
    public AjaxResult getAssetsByPartId(@PathVariable("partId") String partId) {
        try {
            log.info("查询备品备件关联的资产列表，备品备件ID：{}", partId);
            List<AssetPartRelationVo> assets = relationService.selectAssetsByPartId(partId);
            return AjaxResult.success(assets);
        } catch (Exception e) {
            log.error("查询备品备件关联的资产列表异常：", e);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 检查资产和备品备件是否已关联
     */
    @PreAuthorize("@ss.hasPermi('asset:part:relation:query')")
    @GetMapping("/check/{assetId}/{partId}")
    public AjaxResult checkRelationExists(
            @PathVariable("assetId") String assetId,
            @PathVariable("partId") String partId) {
        try {
            boolean exists = relationService.checkRelationExists(assetId, partId);
            return AjaxResult.success(exists);
        } catch (Exception e) {
            log.error("检查关联关系异常：", e);
            return AjaxResult.error("检查失败：" + e.getMessage());
        }
    }
} 