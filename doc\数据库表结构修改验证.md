# 数据库表结构修改验证

## 修改内容总结

根据您提供的实际`device_param`表结构，我已经对相关代码进行了以下修改：

### 1. 实际表结构
```sql
CREATE TABLE `device_param` (
  `param_id` bigint NOT NULL AUTO_INCREMENT,
  `device_id` bigint NOT NULL,
  `param_name` varchar(64) NOT NULL COMMENT '参数名',
  `param_unit` varchar(50) DEFAULT NULL COMMENT '参数单位',
  `range_start` decimal(10,4) DEFAULT NULL COMMENT '正常运行范围',
  `range_end` decimal(10,4) DEFAULT NULL COMMENT '正常运行范围',
  `del_flag` tinyint NOT NULL DEFAULT '0',
  PRIMARY KEY (`param_id`)
)
```

### 2. 实体类修改 (DeviceParam.java)

**删除的字段**:
- `paramKey` - 参数键
- `storageType` - 存储类型（Modbus相关）
- `paramAddress` - 参数地址（Modbus相关）
- `dataType` - 数据类型
- `categoryId` - 参数分类

**保留的字段**:
- `paramId` - 参数ID
- `deviceId` - 设备ID
- `paramName` - 参数名称（用于MQTT查询）
- `paramUnit` - 参数单位
- `rangeStart` - 正常范围下限
- `rangeEnd` - 正常范围上限
- `delFlag` - 删除标志

### 3. Mapper文件修改 (DeviceParamMapper.xml)

**更新的映射**:
```xml
<resultMap id="BaseResultMap" type="com.jingfang.device_module.module.entity.DeviceParam">
    <id property="paramId" column="param_id" />
    <result property="deviceId" column="device_id" />
    <result property="paramName" column="param_name" />
    <result property="paramUnit" column="param_unit" />
    <result property="rangeStart" column="range_start" />
    <result property="rangeEnd" column="range_end" />
    <result property="delFlag" column="del_flag" />
</resultMap>

<sql id="Base_Column_List">
    param_id,device_id,param_name,param_unit,range_start,range_end,del_flag
</sql>
```

### 4. 服务层修改 (DeviceParamServiceImpl.java)

**运行参数查询** (`showDeviceParamTypeOne`):
- 改为使用MQTT方式查询
- 移除Modbus相关代码
- 使用`param_name`作为MQTT属性名称
- 移除了对`categoryId`字段的依赖
- 自动根据值类型格式化参数值（不再依赖`dataType`字段）

**告警参数查询** (`showDeviceParamTypeTwo`):
- 通过参数名称关键字筛选告警参数（包含"告警"、"报警"、"故障"、"异常"）
- 暂时返回配置的告警参数，不查询实际值
- 后续可扩展为MQTT方式

**参数值格式化**:
- 自动检测数值类型（整数/小数）
- 支持布尔值显示（是/否）
- 自动添加参数单位

## 验证步骤

### 1. 启动应用验证

```bash
# 启动应用
mvn spring-boot:run

# 查看启动日志，确认没有SQL错误
```

### 2. 测试API接口

```bash
# 测试MQTT连接
curl "http://localhost:8080/device/property/test/mqtt-connection"

# 测试设备运行参数（需要先有设备数据）
curl "http://localhost:8080/device/property/test/normal-params?deviceId=1"

# 测试MQTT属性查询
curl "http://localhost:8080/device/property/test/mqtt?deviceIp=************&properties=温度,湿度"
```

### 3. 前端页面验证

1. 访问设备管理页面
2. 进入设备详情页面
3. 切换到"运行数据"标签页
4. 查看是否能正常显示参数数据

## 可能需要的数据库调整

如果您的`device_param`表结构与代码不完全匹配，可能需要执行以下SQL：

```sql
-- 查看当前表结构
DESCRIBE device_param;

-- 如果缺少device_id字段，需要添加
ALTER TABLE device_param ADD COLUMN device_id BIGINT COMMENT '设备ID';

-- 如果需要添加外键约束
ALTER TABLE device_param ADD CONSTRAINT fk_device_param_device_id 
FOREIGN KEY (device_id) REFERENCES device_info(id);

-- 更新现有数据的device_id（如果有数据的话）
-- UPDATE device_param SET device_id = ? WHERE param_id = ?;
```

## 注意事项

1. **参数名称**: 确保`param_name`字段的值与MQTT网关中的属性名称完全一致
2. **参数分类**: 由于删除了`category_id`字段，现在通过参数名称关键字来区分：
   - 运行参数：所有不包含告警关键字的参数
   - 告警参数：参数名称包含"告警"、"报警"、"故障"、"异常"等关键字的参数
3. **数据类型**: 由于删除了`data_type`字段，现在自动根据MQTT返回的值类型进行格式化：
   - 数值类型：自动判断整数或小数显示
   - 布尔类型：显示为"是"或"否"
   - 其他类型：直接显示字符串值
4. **参数单位**: `param_unit`字段会自动添加到参数值后面

## 后续扩展

1. **告警参数MQTT支持**: 可以扩展`showDeviceParamTypeTwo`方法支持MQTT查询
2. **参数写入**: 可以添加通过MQTT向设备写入参数的功能
3. **批量操作**: 可以添加批量配置设备参数的功能

## 故障排查

如果遇到问题，请检查：

1. **数据库连接**: 确认数据库表结构正确
2. **MQTT连接**: 使用测试接口验证MQTT连接状态
3. **日志信息**: 查看应用启动和运行日志
4. **参数配置**: 确认设备参数配置正确

修改完成后，您的设备参数采集功能应该能够正常使用MQTT方式进行数据获取了！
