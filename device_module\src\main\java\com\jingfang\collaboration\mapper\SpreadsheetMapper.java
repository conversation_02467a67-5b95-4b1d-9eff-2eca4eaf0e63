package com.jingfang.collaboration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.collaboration.domain.Spreadsheet;
import com.jingfang.collaboration.vo.SpreadsheetVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 表格Mapper接口
 */
@Mapper
public interface SpreadsheetMapper extends BaseMapper<Spreadsheet> {
    
    /**
     * 查询表格列表（包含协作者信息）
     */
    IPage<SpreadsheetVo> selectSpreadsheetList(IPage<SpreadsheetVo> page, @Param("userId") Long userId, 
                                               @Param("title") String title, @Param("status") String status);
    
    /**
     * 查询表格详情（包含协作者信息）
     */
    SpreadsheetVo selectSpreadsheetDetail(@Param("id") String id, @Param("userId") Long userId);
    
    /**
     * 查询用户有权限访问的表格列表
     */
    IPage<SpreadsheetVo> selectUserAccessibleSpreadsheets(IPage<SpreadsheetVo> page, @Param("userId") Long userId,
                                                          @Param("title") String title, @Param("status") String status);
}
