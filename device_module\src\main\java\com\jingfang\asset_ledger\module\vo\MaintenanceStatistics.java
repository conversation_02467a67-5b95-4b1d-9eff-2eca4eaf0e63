package com.jingfang.asset_ledger.module.vo;

import lombok.Data;

import java.util.Date;

/**
 * 维护统计信息VO
 */
@Data
public class MaintenanceStatistics {
    /**
     * 维护计划总数
     */
    private Integer totalPlans;

    /**
     * 启用的维护计划数
     */
    private Integer activePlans;

    /**
     * 维护任务总数
     */
    private Integer totalTasks;

    /**
     * 待执行任务数
     */
    private Integer pendingTasks;

    /**
     * 执行中任务数
     */
    private Integer inProgressTasks;

    /**
     * 已完成任务数
     */
    private Integer completedTasks;

    /**
     * 逾期任务数
     */
    private Integer overdueTasks;

    /**
     * 最近一次维护时间
     */
    private Date lastMaintenanceTime;

    /**
     * 下次计划维护时间
     */
    private Date nextMaintenanceTime;
}