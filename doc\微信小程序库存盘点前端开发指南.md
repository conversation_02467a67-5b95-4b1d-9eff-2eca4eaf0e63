# 微信小程序库存盘点前端开发指南

## 一、项目概述

### 1.1 项目目标
开发一款微信小程序，为现场盘点人员提供便捷、高效的移动端盘点工具，通过扫码、拍照等移动设备特有功能，提升盘点作业的准确性和效率。

### 1.2 技术栈
- **开发框架**：微信小程序原生开发
- **UI组件库**：WeUI / Vant Weapp（推荐）
- **状态管理**：小程序原生数据绑定
- **本地存储**：wx.storage
- **网络请求**：wx.request（封装统一请求方法）

### 1.3 用户角色
- **盘点员**：执行具体盘点作业的现场人员
- **盘点组长**：负责盘点任务分配和进度监控的管理人员

## 二、功能模块设计

### 2.1 页面结构
```
小程序页面结构
├── pages/
│   ├── index/                 # 首页（任务列表）
│   ├── task-detail/           # 任务详情页
│   ├── scan-stocktaking/      # 扫码盘点页
│   ├── item-detail/           # 物品详情页
│   ├── progress/              # 进度查询页
│   ├── history/               # 历史记录页
│   └── profile/               # 个人中心页
├── components/                # 公共组件
│   ├── task-card/             # 任务卡片组件
│   ├── progress-bar/          # 进度条组件
│   ├── status-tag/            # 状态标签组件
│   └── photo-upload/          # 照片上传组件
└── utils/                     # 工具类
    ├── request.js             # 网络请求封装
    ├── storage.js             # 本地存储封装
    └── common.js              # 通用工具方法
```

### 2.2 核心功能模块

#### 2.2.1 任务管理模块
**页面**：`pages/index/` 和 `pages/task-detail/`

**功能描述**：
- 显示用户分配的盘点任务列表
- 查看任务详情和盘点明细
- 开始盘点作业

**主要接口**：
- `GET /item/stocktaking/my-tasks` - 获取我的盘点任务
- `GET /item/stocktaking/{stocktakingId}` - 获取任务详情
- `GET /item/stocktaking/{stocktakingId}/details` - 获取盘点明细

#### 2.2.2 扫码盘点模块
**页面**：`pages/scan-stocktaking/` 和 `pages/item-detail/`

**功能描述**：
- 扫描物品条码快速定位物品
- 显示物品信息和账面数量
- 录入实盘数量和差异原因
- 拍照记录现场情况

**主要接口**：
- `GET /item/by-code/{itemCode}` - 根据条码查询物品
- `GET /item/stocktaking/detail/by-item` - 查找盘点明细
- `PUT /item/stocktaking/detail/{detailId}` - 更新盘点明细
- `POST /common/upload` - 上传照片

#### 2.2.3 进度查询模块
**页面**：`pages/progress/`

**功能描述**：
- 查看整体盘点进度
- 查看个人盘点进度
- 实时更新进度数据

**主要接口**：
- `GET /item/stocktaking/{stocktakingId}/progress` - 获取盘点进度
- `GET /item/stocktaking/my-progress` - 获取个人进度

#### 2.2.4 历史记录模块
**页面**：`pages/history/`

**功能描述**：
- 查看个人盘点历史记录
- 查看物品盘点历史
- 支持时间筛选和搜索

**主要接口**：
- `GET /item/stocktaking/my-records` - 获取个人记录
- `GET /item/stocktaking/item-history/{itemId}` - 获取物品历史

## 三、详细开发规范

### 3.1 网络请求封装

#### 3.1.1 创建 `utils/request.js`
```javascript
// 统一请求封装
const BASE_URL = 'https://your-domain.com/api'; // 替换为实际API地址

// 请求拦截器
const request = (options) => {
  return new Promise((resolve, reject) => {
    // 显示加载提示
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    wx.request({
      url: BASE_URL + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        'Authorization': wx.getStorageSync('token') || '',
        ...options.header
      },
      success: (res) => {
        wx.hideLoading();
        if (res.data.code === 200) {
          resolve(res.data);
        } else {
          wx.showToast({
            title: res.data.msg || '请求失败',
            icon: 'none'
          });
          reject(res.data);
        }
      },
      fail: (err) => {
        wx.hideLoading();
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
        reject(err);
      }
    });
  });
};

// 导出请求方法
module.exports = {
  get: (url, data) => request({ url, method: 'GET', data }),
  post: (url, data) => request({ url, method: 'POST', data }),
  put: (url, data) => request({ url, method: 'PUT', data }),
  delete: (url, data) => request({ url, method: 'DELETE', data })
};
```

#### 3.1.2 创建 `utils/api.js`
```javascript
// API接口定义
const request = require('./request.js');

const api = {
  // 任务管理
  getMyTasks: () => request.get('/item/stocktaking/my-tasks'),
  getTaskDetail: (stocktakingId) => request.get(`/item/stocktaking/${stocktakingId}`),
  getTaskDetails: (stocktakingId) => request.get(`/item/stocktaking/${stocktakingId}/details`),
  
  // 物品查询
  getItemByCode: (itemCode) => request.get(`/item/by-code/${itemCode}`),
  getDetailByItem: (stocktakingId, itemId, warehouseId) => 
    request.get(`/item/stocktaking/detail/by-item?stocktakingId=${stocktakingId}&itemId=${itemId}&warehouseId=${warehouseId}`),
  
  // 盘点操作
  updateDetail: (detailId, data) => request.put(`/item/stocktaking/detail/${detailId}`, data),
  recordResult: (data) => request.post('/item/stocktaking/record', data),
  
  // 进度查询
  getProgress: (stocktakingId) => request.get(`/item/stocktaking/${stocktakingId}/progress`),
  getMyProgress: () => request.get('/item/stocktaking/my-progress'),
  
  // 历史记录
  getMyRecords: (timeRange, startDate, endDate) => {
    let url = `/item/stocktaking/my-records?timeRange=${timeRange}`;
    if (startDate) url += `&startDate=${startDate}`;
    if (endDate) url += `&endDate=${endDate}`;
    return request.get(url);
  },
  getItemHistory: (itemId) => request.get(`/item/stocktaking/item-history/${itemId}`),
  
  // 文件上传
  uploadPhoto: (filePath) => {
    return new Promise((resolve, reject) => {
      wx.uploadFile({
        url: 'https://your-domain.com/api/common/upload',
        filePath: filePath,
        name: 'file',
        header: {
          'Authorization': wx.getStorageSync('token') || ''
        },
        success: (res) => {
          const data = JSON.parse(res.data);
          if (data.code === 200) {
            resolve(data);
          } else {
            reject(data);
          }
        },
        fail: reject
      });
    });
  }
};

module.exports = api;
```

### 3.2 本地存储封装

#### 3.2.1 创建 `utils/storage.js`
```javascript
// 本地存储封装
const storage = {
  // 存储键名定义
  keys: {
    USER_INFO: 'stocktaking_user_info',
    CURRENT_TASK: 'stocktaking_current_task',
    OFFLINE_DATA: 'stocktaking_offline_data',
    COMMON_REASONS: 'stocktaking_common_reasons',
    TOKEN: 'token'
  },
  
  // 设置数据
  set(key, value) {
    try {
      wx.setStorageSync(key, value);
      return true;
    } catch (e) {
      console.error('存储失败:', e);
      return false;
    }
  },
  
  // 获取数据
  get(key, defaultValue = null) {
    try {
      return wx.getStorageSync(key) || defaultValue;
    } catch (e) {
      console.error('读取失败:', e);
      return defaultValue;
    }
  },
  
  // 删除数据
  remove(key) {
    try {
      wx.removeStorageSync(key);
      return true;
    } catch (e) {
      console.error('删除失败:', e);
      return false;
    }
  },
  
  // 清空所有数据
  clear() {
    try {
      wx.clearStorageSync();
      return true;
    } catch (e) {
      console.error('清空失败:', e);
      return false;
    }
  }
};

module.exports = storage;
```

### 3.3 公共组件开发

#### 3.3.1 状态标签组件 `components/status-tag/`
```javascript
// components/status-tag/index.js
Component({
  properties: {
    status: {
      type: Number,
      value: 0
    },
    type: {
      type: String,
      value: 'stocktaking' // stocktaking | detail
    }
  },
  
  data: {
    statusConfig: {
      stocktaking: {
        0: { text: '草稿', color: '#909399' },
        1: { text: '进行中', color: '#E6A23C' },
        2: { text: '已完成', color: '#409EFF' },
        3: { text: '已审核', color: '#67C23A' }
      },
      detail: {
        0: { text: '待盘点', color: '#909399' },
        1: { text: '已盘点', color: '#67C23A' },
        2: { text: '有差异', color: '#E6A23C' }
      }
    }
  },
  
  computed: {
    currentConfig() {
      return this.data.statusConfig[this.data.type][this.data.status] || 
             { text: '未知', color: '#909399' };
    }
  }
});
```

#### 3.3.2 进度条组件 `components/progress-bar/`
```javascript
// components/progress-bar/index.js
Component({
  properties: {
    current: {
      type: Number,
      value: 0
    },
    total: {
      type: Number,
      value: 100
    },
    showText: {
      type: Boolean,
      value: true
    },
    color: {
      type: String,
      value: '#409EFF'
    }
  },
  
  computed: {
    percentage() {
      if (this.data.total === 0) return 0;
      return Math.round((this.data.current / this.data.total) * 100);
    },
    
    progressText() {
      return `${this.data.current}/${this.data.total} (${this.percentage}%)`;
    }
  }
});
```

### 3.4 核心页面开发

#### 3.4.1 首页开发 `pages/index/`
```javascript
// pages/index/index.js
const api = require('../../utils/api.js');
const storage = require('../../utils/storage.js');

Page({
  data: {
    taskList: [],
    loading: false,
    refreshing: false
  },
  
  onLoad() {
    this.loadTasks();
  },
  
  onShow() {
    // 每次显示页面时刷新数据
    this.loadTasks();
  },
  
  // 加载任务列表
  async loadTasks() {
    try {
      this.setData({ loading: true });
      const res = await api.getMyTasks();
      this.setData({ 
        taskList: res.data || [],
        loading: false 
      });
    } catch (error) {
      console.error('加载任务失败:', error);
      this.setData({ loading: false });
    }
  },
  
  // 下拉刷新
  async onPullDownRefresh() {
    this.setData({ refreshing: true });
    await this.loadTasks();
    this.setData({ refreshing: false });
    wx.stopPullDownRefresh();
  },
  
  // 查看任务详情
  onTaskTap(e) {
    const { stocktakingId } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/task-detail/index?stocktakingId=${stocktakingId}`
    });
  },
  
  // 开始盘点
  onStartStocktaking(e) {
    const { stocktakingId } = e.currentTarget.dataset;
    // 保存当前任务到本地存储
    storage.set(storage.keys.CURRENT_TASK, stocktakingId);
    wx.navigateTo({
      url: `/pages/scan-stocktaking/index?stocktakingId=${stocktakingId}`
    });
  }
});
```

#### 3.4.2 扫码盘点页开发 `pages/scan-stocktaking/`
```javascript
// pages/scan-stocktaking/index.js
const api = require('../../utils/api.js');

Page({
  data: {
    stocktakingId: '',
    currentItem: null,
    currentDetail: null,
    actualQuantity: '',
    differenceReason: '',
    photos: [],
    commonReasons: [
      '自然损耗',
      '盘点错误',
      '系统错误',
      '物品损坏',
      '其他原因'
    ]
  },
  
  onLoad(options) {
    this.setData({
      stocktakingId: options.stocktakingId
    });
  },
  
  // 扫码功能
  onScanCode() {
    wx.scanCode({
      success: async (res) => {
        try {
          // 根据扫码结果查询物品信息
          const itemRes = await api.getItemByCode(res.result);
          const item = itemRes.data;
          
          // 查找对应的盘点明细
          const detailRes = await api.getDetailByItem(
            this.data.stocktakingId,
            item.itemId,
            item.warehouseId // 需要根据实际情况确定仓库ID
          );
          
          this.setData({
            currentItem: item,
            currentDetail: detailRes.data,
            actualQuantity: detailRes.data.actualQuantity || ''
          });
          
        } catch (error) {
          wx.showToast({
            title: '未找到对应物品',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '扫码失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 手动输入物品编码
  onManualInput() {
    wx.showModal({
      title: '输入物品编码',
      editable: true,
      placeholderText: '请输入物品编码',
      success: async (res) => {
        if (res.confirm && res.content) {
          // 处理逻辑同扫码
          // ...
        }
      }
    });
  },
  
  // 拍照功能
  onTakePhoto() {
    wx.chooseMedia({
      count: 3,
      mediaType: ['image'],
      sourceType: ['camera'],
      camera: 'back',
      success: async (res) => {
        try {
          const uploadPromises = res.tempFiles.map(file => 
            api.uploadPhoto(file.tempFilePath)
          );
          
          const uploadResults = await Promise.all(uploadPromises);
          const photoUrls = uploadResults.map(result => result.data.url);
          
          this.setData({
            photos: [...this.data.photos, ...photoUrls]
          });
          
        } catch (error) {
          wx.showToast({
            title: '照片上传失败',
            icon: 'none'
          });
        }
      }
    });
  },
  
  // 保存盘点结果
  async onSaveResult() {
    if (!this.data.currentDetail) {
      wx.showToast({
        title: '请先扫码选择物品',
        icon: 'none'
      });
      return;
    }
    
    if (!this.data.actualQuantity) {
      wx.showToast({
        title: '请输入实盘数量',
        icon: 'none'
      });
      return;
    }
    
    try {
      const updateData = {
        actualQuantity: parseFloat(this.data.actualQuantity),
        differenceReason: this.data.differenceReason,
        photos: this.data.photos
      };
      
      await api.updateDetail(this.data.currentDetail.detailId, updateData);
      
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });
      
      // 清空当前数据，准备下一个物品
      this.setData({
        currentItem: null,
        currentDetail: null,
        actualQuantity: '',
        differenceReason: '',
        photos: []
      });
      
    } catch (error) {
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  }
});
```

## 四、页面布局设计

### 4.1 首页布局 `pages/index/index.wxml`
```xml
<view class="container">
  <!-- 头部信息 -->
  <view class="header">
    <view class="title">库存盘点</view>
    <view class="user-info">
      <text>{{userInfo.nickName}}</text>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions">
    <view class="action-item" bindtap="onScanCode">
      <image src="/images/scan.png" class="action-icon"></image>
      <text>扫码盘点</text>
    </view>
    <view class="action-item" bindtap="onViewProgress">
      <image src="/images/progress.png" class="action-icon"></image>
      <text>查看进度</text>
    </view>
    <view class="action-item" bindtap="onViewHistory">
      <image src="/images/history.png" class="action-icon"></image>
      <text>历史记录</text>
    </view>
  </view>

  <!-- 任务列表 -->
  <view class="task-section">
    <view class="section-title">我的盘点任务</view>
    <view class="task-list">
      <view
        class="task-item"
        wx:for="{{taskList}}"
        wx:key="stocktakingId"
        bindtap="onTaskTap"
        data-stocktaking-id="{{item.stocktakingId}}"
      >
        <view class="task-header">
          <view class="task-name">{{item.stocktakingName}}</view>
          <status-tag status="{{item.status}}" type="stocktaking"></status-tag>
        </view>
        <view class="task-info">
          <view class="info-item">
            <text class="label">进度：</text>
            <progress-bar
              current="{{item.completedItems}}"
              total="{{item.totalItems}}"
              show-text="{{true}}"
            ></progress-bar>
          </view>
          <view class="info-item">
            <text class="label">截止时间：</text>
            <text class="value">{{item.planEndTime}}</text>
          </view>
        </view>
        <view class="task-actions">
          <button
            class="action-btn primary"
            bindtap="onStartStocktaking"
            data-stocktaking-id="{{item.stocktakingId}}"
            wx:if="{{item.status === 1}}"
          >
            开始盘点
          </button>
          <button
            class="action-btn default"
            bindtap="onViewDetail"
            data-stocktaking-id="{{item.stocktakingId}}"
          >
            查看详情
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{taskList.length === 0 && !loading}}">
    <image src="/images/empty.png" class="empty-icon"></image>
    <text class="empty-text">暂无盘点任务</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>
</view>
```

### 4.2 扫码盘点页布局 `pages/scan-stocktaking/index.wxml`
```xml
<view class="container">
  <!-- 扫码区域 -->
  <view class="scan-section">
    <view class="scan-area" bindtap="onScanCode">
      <image src="/images/scan-big.png" class="scan-icon"></image>
      <text class="scan-text">点击扫描物品条码</text>
    </view>
    <button class="manual-btn" bindtap="onManualInput">手动输入</button>
  </view>

  <!-- 物品信息 -->
  <view class="item-section" wx:if="{{currentItem}}">
    <view class="section-title">物品信息</view>
    <view class="item-info">
      <view class="info-row">
        <text class="label">物品名称：</text>
        <text class="value">{{currentItem.itemName}}</text>
      </view>
      <view class="info-row">
        <text class="label">物品编码：</text>
        <text class="value">{{currentItem.itemCode}}</text>
      </view>
      <view class="info-row">
        <text class="label">规格型号：</text>
        <text class="value">{{currentItem.specModel}}</text>
      </view>
      <view class="info-row">
        <text class="label">货架位置：</text>
        <text class="value">{{currentDetail.shelfLocation}}</text>
      </view>
      <view class="info-row">
        <text class="label">账面数量：</text>
        <text class="value highlight">{{currentDetail.bookQuantity}} {{currentItem.unit}}</text>
      </view>
    </view>
  </view>

  <!-- 盘点录入 -->
  <view class="input-section" wx:if="{{currentDetail}}">
    <view class="section-title">盘点录入</view>
    <view class="input-form">
      <view class="form-item">
        <text class="form-label">实盘数量：</text>
        <input
          class="form-input"
          type="digit"
          placeholder="请输入实盘数量"
          value="{{actualQuantity}}"
          bindinput="onQuantityInput"
        />
        <text class="form-unit">{{currentItem.unit}}</text>
      </view>

      <view class="form-item" wx:if="{{actualQuantity && actualQuantity != currentDetail.bookQuantity}}">
        <text class="form-label">差异原因：</text>
        <picker
          class="form-picker"
          range="{{commonReasons}}"
          value="{{reasonIndex}}"
          bindchange="onReasonChange"
        >
          <view class="picker-text">
            {{differenceReason || '请选择差异原因'}}
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="form-label">现场照片：</text>
        <view class="photo-section">
          <view class="photo-list">
            <image
              class="photo-item"
              src="{{item}}"
              wx:for="{{photos}}"
              wx:key="index"
              bindtap="onPreviewPhoto"
              data-index="{{index}}"
            ></image>
            <view class="photo-add" bindtap="onTakePhoto">
              <text class="add-icon">+</text>
              <text class="add-text">拍照</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section" wx:if="{{currentDetail}}">
    <button class="save-btn" bindtap="onSaveResult">保存结果</button>
    <button class="next-btn" bindtap="onNextItem">下一个</button>
  </view>
</view>
```

### 4.3 进度查询页布局 `pages/progress/index.wxml`
```xml
<view class="container">
  <!-- 整体进度 -->
  <view class="progress-section">
    <view class="section-title">整体进度</view>
    <view class="progress-card">
      <view class="progress-header">
        <text class="progress-title">{{taskDetail.stocktakingName}}</text>
        <status-tag status="{{taskDetail.status}}" type="stocktaking"></status-tag>
      </view>
      <view class="progress-stats">
        <view class="stat-item">
          <text class="stat-number">{{overallProgress.totalItems}}</text>
          <text class="stat-label">总计</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{overallProgress.completedItems}}</text>
          <text class="stat-label">已完成</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{overallProgress.differenceItems}}</text>
          <text class="stat-label">有差异</text>
        </view>
      </view>
      <progress-bar
        current="{{overallProgress.completedItems}}"
        total="{{overallProgress.totalItems}}"
        show-text="{{true}}"
        color="#67C23A"
      ></progress-bar>
    </view>
  </view>

  <!-- 个人进度 -->
  <view class="progress-section">
    <view class="section-title">我的进度</view>
    <view class="progress-card">
      <view class="progress-stats">
        <view class="stat-item">
          <text class="stat-number">{{personalProgress.totalAssigned}}</text>
          <text class="stat-label">分配给我</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{personalProgress.completed}}</text>
          <text class="stat-label">已完成</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{personalProgress.withDifference}}</text>
          <text class="stat-label">有差异</text>
        </view>
      </view>
      <progress-bar
        current="{{personalProgress.completed}}"
        total="{{personalProgress.totalAssigned}}"
        show-text="{{true}}"
        color="#409EFF"
      ></progress-bar>
    </view>
  </view>

  <!-- 仓库进度 -->
  <view class="progress-section" wx:if="{{overallProgress.warehouseProgress}}">
    <view class="section-title">仓库进度</view>
    <view class="warehouse-list">
      <view
        class="warehouse-item"
        wx:for="{{overallProgress.warehouseProgress}}"
        wx:key="warehouseId"
      >
        <view class="warehouse-header">
          <text class="warehouse-name">{{item.warehouseName}}</text>
          <text class="warehouse-rate">{{item.completionRate}}%</text>
        </view>
        <progress-bar
          current="{{item.completedItems}}"
          total="{{item.totalItems}}"
          show-text="{{false}}"
          color="#E6A23C"
        ></progress-bar>
      </view>
    </view>
  </view>
</view>
```

## 五、样式设计规范

### 5.1 全局样式 `app.wxss`
```css
/* 全局样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'Segoe UI', Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

.container {
  padding: 20rpx;
  min-height: 100vh;
}

/* 通用按钮样式 */
.btn {
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.btn-primary {
  background-color: #409EFF;
  color: white;
}

.btn-success {
  background-color: #67C23A;
  color: white;
}

.btn-warning {
  background-color: #E6A23C;
  color: white;
}

.btn-default {
  background-color: white;
  color: #606266;
  border: 1rpx solid #DCDFE6;
}

/* 通用卡片样式 */
.card {
  background-color: white;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 通用标题样式 */
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20rpx;
}

/* 通用表单样式 */
.form-item {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.form-label {
  font-size: 28rpx;
  color: #606266;
  width: 160rpx;
  flex-shrink: 0;
}

.form-input {
  flex: 1;
  padding: 16rpx;
  border: 1rpx solid #DCDFE6;
  border-radius: 8rpx;
  font-size: 28rpx;
}

/* 状态颜色 */
.text-primary { color: #409EFF; }
.text-success { color: #67C23A; }
.text-warning { color: #E6A23C; }
.text-danger { color: #F56C6C; }
.text-info { color: #909399; }

/* 背景颜色 */
.bg-primary { background-color: #409EFF; }
.bg-success { background-color: #67C23A; }
.bg-warning { background-color: #E6A23C; }
.bg-danger { background-color: #F56C6C; }
.bg-info { background-color: #909399; }
```

## 六、开发注意事项

### 6.1 性能优化
1. **图片优化**：使用适当的图片格式和尺寸
2. **数据缓存**：合理使用本地存储减少网络请求
3. **懒加载**：长列表使用虚拟滚动或分页加载
4. **代码分包**：合理使用小程序分包功能

### 6.2 用户体验
1. **加载状态**：所有异步操作都要有加载提示
2. **错误处理**：网络错误、业务错误的友好提示
3. **离线支持**：关键数据支持离线缓存
4. **操作反馈**：用户操作要有明确的反馈

### 6.3 兼容性
1. **机型适配**：考虑不同屏幕尺寸的适配
2. **系统版本**：兼容不同微信版本
3. **网络环境**：弱网环境下的体验优化

## 七、测试要点

### 7.1 功能测试
- [ ] 扫码功能正常
- [ ] 照片上传成功
- [ ] 数据保存正确
- [ ] 进度显示准确
- [ ] 历史记录完整

### 7.2 兼容性测试
- [ ] iOS设备测试
- [ ] Android设备测试
- [ ] 不同微信版本测试
- [ ] 弱网环境测试

### 7.3 用户体验测试
- [ ] 操作流程顺畅
- [ ] 界面响应及时
- [ ] 错误提示友好
- [ ] 数据同步正常

## 八、部署发布

### 8.1 发布前检查
1. 代码质量检查
2. 功能完整性测试
3. 性能优化验证
4. 用户体验测试

### 8.2 版本管理
1. 遵循语义化版本号
2. 维护更新日志
3. 做好版本回退准备

这份开发指南为微信小程序前端开发提供了完整的技术方案和实现细节，开发者可以按照指南进行高效开发。
```
