<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.collaboration.mapper.SpreadsheetMapper">
    
    <resultMap type="com.jingfang.collaboration.vo.SpreadsheetVo" id="SpreadsheetVoResult">
        <result property="id"                column="id"                />
        <result property="title"             column="title"             />
        <result property="description"       column="description"       />
        <result property="data"              column="data"              />
        <result property="createBy"          column="create_by"         />
        <result property="createByName"      column="create_by_name"    />
        <result property="createTime"        column="create_time"       />
        <result property="updateBy"          column="update_by"         />
        <result property="updateByName"      column="update_by_name"    />
        <result property="updateTime"        column="update_time"       />
        <result property="status"            column="status"            />
        <result property="isPublic"          column="is_public"         />
        <result property="shareToken"        column="share_token"       />
        <result property="shareExpireTime"   column="share_expire_time" />
        <result property="version"           column="version"           />
        <result property="remark"            column="remark"            />
        <result property="userPermission"    column="user_permission"   />
        <result property="collaboratorCount" column="collaborator_count"/>
        <result property="onlineUserCount"   column="online_user_count" />
    </resultMap>

    <!-- 查询表格列表（包含协作者信息） -->
    <select id="selectSpreadsheetList" parameterType="map" resultMap="SpreadsheetVoResult">
        SELECT 
            s.id,
            s.title,
            s.description,
            s.create_by,
            s.create_by_name,
            s.create_time,
            s.update_by,
            s.update_by_name,
            s.update_time,
            s.status,
            s.is_public,
            s.share_token,
            s.share_expire_time,
            s.version,
            s.remark,
            sc.permission as user_permission,
            COALESCE(collaborator_stats.collaborator_count, 0) as collaborator_count,
            COALESCE(online_stats.online_user_count, 0) as online_user_count
        FROM collaboration_spreadsheet s
        LEFT JOIN collaboration_spreadsheet_collaborator sc ON s.id = sc.spreadsheet_id 
            AND sc.user_id = #{userId} AND sc.status = '1' AND sc.del_flag = 0
        LEFT JOIN (
            SELECT 
                spreadsheet_id, 
                COUNT(*) as collaborator_count
            FROM collaboration_spreadsheet_collaborator 
            WHERE status = '1' AND del_flag = 0
            GROUP BY spreadsheet_id
        ) collaborator_stats ON s.id = collaborator_stats.spreadsheet_id
        LEFT JOIN (
            SELECT 
                spreadsheet_id, 
                COUNT(*) as online_user_count
            FROM collaboration_spreadsheet_collaborator 
            WHERE status = '1' AND is_online = '1' AND del_flag = 0
            GROUP BY spreadsheet_id
        ) online_stats ON s.id = online_stats.spreadsheet_id
        WHERE s.del_flag = 0
        <if test="title != null and title != ''">
            AND s.title LIKE CONCAT('%', #{title}, '%')
        </if>
        <if test="status != null and status != ''">
            AND s.status = #{status}
        </if>
        AND (s.create_by = #{userId} OR sc.user_id = #{userId})
        ORDER BY s.update_time DESC
    </select>

    <!-- 查询表格详情（包含协作者信息） -->
    <select id="selectSpreadsheetDetail" parameterType="map" resultMap="SpreadsheetVoResult">
        SELECT 
            s.id,
            s.title,
            s.description,
            s.data,
            s.create_by,
            s.create_by_name,
            s.create_time,
            s.update_by,
            s.update_by_name,
            s.update_time,
            s.status,
            s.is_public,
            s.share_token,
            s.share_expire_time,
            s.version,
            s.remark,
            sc.permission as user_permission,
            COALESCE(collaborator_stats.collaborator_count, 0) as collaborator_count,
            COALESCE(online_stats.online_user_count, 0) as online_user_count
        FROM collaboration_spreadsheet s
        LEFT JOIN collaboration_spreadsheet_collaborator sc ON s.id = sc.spreadsheet_id 
            AND sc.user_id = #{userId} AND sc.status = '1' AND sc.del_flag = 0
        LEFT JOIN (
            SELECT 
                spreadsheet_id, 
                COUNT(*) as collaborator_count
            FROM collaboration_spreadsheet_collaborator 
            WHERE status = '1' AND del_flag = 0
            GROUP BY spreadsheet_id
        ) collaborator_stats ON s.id = collaborator_stats.spreadsheet_id
        LEFT JOIN (
            SELECT 
                spreadsheet_id, 
                COUNT(*) as online_user_count
            FROM collaboration_spreadsheet_collaborator 
            WHERE status = '1' AND is_online = '1' AND del_flag = 0
            GROUP BY spreadsheet_id
        ) online_stats ON s.id = online_stats.spreadsheet_id
        WHERE s.id = #{id} AND s.del_flag = 0
    </select>

    <!-- 查询用户有权限访问的表格列表 -->
    <select id="selectUserAccessibleSpreadsheets" parameterType="map" resultMap="SpreadsheetVoResult">
        SELECT 
            s.id,
            s.title,
            s.description,
            s.create_by,
            s.create_by_name,
            s.create_time,
            s.update_by,
            s.update_by_name,
            s.update_time,
            s.status,
            s.is_public,
            s.share_token,
            s.share_expire_time,
            s.version,
            s.remark,
            sc.permission as user_permission,
            COALESCE(collaborator_stats.collaborator_count, 0) as collaborator_count,
            COALESCE(online_stats.online_user_count, 0) as online_user_count
        FROM collaboration_spreadsheet s
        INNER JOIN collaboration_spreadsheet_collaborator sc ON s.id = sc.spreadsheet_id 
            AND sc.user_id = #{userId} AND sc.status = '1' AND sc.del_flag = 0
        LEFT JOIN (
            SELECT 
                spreadsheet_id, 
                COUNT(*) as collaborator_count
            FROM collaboration_spreadsheet_collaborator 
            WHERE status = '1' AND del_flag = 0
            GROUP BY spreadsheet_id
        ) collaborator_stats ON s.id = collaborator_stats.spreadsheet_id
        LEFT JOIN (
            SELECT 
                spreadsheet_id, 
                COUNT(*) as online_user_count
            FROM collaboration_spreadsheet_collaborator 
            WHERE status = '1' AND is_online = '1' AND del_flag = 0
            GROUP BY spreadsheet_id
        ) online_stats ON s.id = online_stats.spreadsheet_id
        WHERE s.del_flag = 0
        <if test="title != null and title != ''">
            AND s.title LIKE CONCAT('%', #{title}, '%')
        </if>
        <if test="status != null and status != ''">
            AND s.status = #{status}
        </if>
        ORDER BY sc.last_access_time DESC, s.update_time DESC
    </select>

</mapper>
