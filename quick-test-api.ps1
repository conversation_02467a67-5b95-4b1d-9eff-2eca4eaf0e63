# 快速测试微信小程序库存盘点接口
param(
    [string]$BaseUrl = "http://localhost:8080",
    [string]$Token = "your_jwt_token_here"
)

# 设置请求头
$headers = @{
    'Content-Type' = 'application/json';
    'Authorization' = "Bearer $Token"
}

Write-Host "开始快速测试微信小程序库存盘点接口" -ForegroundColor Green
Write-Host "基础URL: $BaseUrl" -ForegroundColor Cyan
Write-Host ("=" * 60)

# 测试函数
function Test-Api {
    param(
        [string]$Name,
        [string]$Method,
        [string]$Url,
        [object]$Body = $null
    )
    
    Write-Host "`n测试: $Name" -ForegroundColor Yellow
    Write-Host "URL: $Method $Url" -ForegroundColor Gray
    
    try {
        $params = @{
            Uri = $Url
            Method = $Method
            Headers = $headers
        }
        
        if ($Body -and $Method -in @("POST", "PUT")) {
            $params.Body = ($Body | ConvertTo-Json -Depth 5)
        }
        
        $response = Invoke-RestMethod @params
        
        Write-Host "✓ 成功" -ForegroundColor Green
        Write-Host "响应: $($response | ConvertTo-Json -Depth 2)" -ForegroundColor Cyan
        
        return $response
    }
    catch {
        Write-Host "✗ 失败: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# 1. 测试获取我的盘点任务列表
Test-Api -Name "获取我的盘点任务列表" -Method "GET" -Url "$BaseUrl/item/stocktaking/my-tasks"

# 2. 测试根据物品条码查询物品信息
Test-Api -Name "根据物品条码查询物品信息" -Method "GET" -Url "$BaseUrl/item/by-code/TEST001"

# 3. 测试根据物品信息查找盘点明细
$detailUrl = "$BaseUrl/item/stocktaking/detail/by-item?stocktakingId=test_id&itemId=test_item&warehouseId=1"
Test-Api -Name "根据物品信息查找盘点明细" -Method "GET" -Url $detailUrl

# 4. 测试更新盘点明细
$updateData = @{
    actualQuantity = 95.00
    differenceReason = "测试差异原因"
    photos = @("http://example.com/photo1.jpg")
}
Test-Api -Name "更新盘点明细" -Method "PUT" -Url "$BaseUrl/item/stocktaking/detail/test_detail_id" -Body $updateData

# 5. 测试获取盘点进度
Test-Api -Name "获取盘点进度" -Method "GET" -Url "$BaseUrl/item/stocktaking/test_stocktaking_id/progress"

# 6. 测试获取个人盘点进度
Test-Api -Name "获取个人盘点进度" -Method "GET" -Url "$BaseUrl/item/stocktaking/my-progress"

# 7. 测试获取个人盘点记录
Test-Api -Name "获取个人盘点记录(今日)" -Method "GET" -Url "$BaseUrl/item/stocktaking/my-records?timeRange=today"

# 8. 测试获取物品盘点历史
Test-Api -Name "获取物品盘点历史" -Method "GET" -Url "$BaseUrl/item/stocktaking/item-history/test_item_id"

# 错误场景测试
Write-Host "`n=== 错误场景测试 ===" -ForegroundColor Red

# 测试不存在的物品条码
Test-Api -Name "查询不存在的物品条码" -Method "GET" -Url "$BaseUrl/item/by-code/NOTEXIST"

# 测试不存在的盘点明细
$errorDetailUrl = "$BaseUrl/item/stocktaking/detail/by-item?stocktakingId=not_exist&itemId=not_exist&warehouseId=999"
Test-Api -Name "查询不存在的盘点明细" -Method "GET" -Url $errorDetailUrl

Write-Host "`n快速测试完成!" -ForegroundColor Green
