package com.jingfang.wh_item_requisition.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jingfang.wh_item_requisition.module.dto.ItemRequisitionDto;
import com.jingfang.wh_item_requisition.module.entity.ItemRequisition;
import com.jingfang.wh_item_requisition.module.request.ItemRequisitionSearchRequest;
import com.jingfang.wh_item_requisition.module.vo.ItemRequisitionDetailVo;
import com.jingfang.wh_item_requisition.module.vo.ItemRequisitionVo;

/**
 * 物品领用单Service接口
 */
public interface ItemRequisitionService extends IService<ItemRequisition> {
    
    /**
     * 新增领用单
     *
     * @param requisitionDto 领用单信息
     * @param username 操作用户
     * @return 领用单ID
     */
    String addItemRequisition(ItemRequisitionDto requisitionDto, String username);
    
    /**
     * 修改领用单
     *
     * @param requisitionId 领用单ID
     * @param requisitionDto 领用单信息
     * @param username 操作用户
     */
    void updateItemRequisition(String requisitionId, ItemRequisitionDto requisitionDto, String username);
    
    /**
     * 提交领用单
     *
     * @param requisitionId 领用单ID
     * @param username 操作用户
     */
    void submitRequisition(String requisitionId, String username);
    
    /**
     * 确认领用单
     *
     * @param requisitionId 领用单ID
     * @param remark 确认备注
     * @param username 操作用户
     */
    void confirmRequisition(String requisitionId, String remark, String username);
    
    /**
     * 审核领用单
     *
     * @param requisitionId 领用单ID
     * @param status 审核结果(4-通过, 5-退回)
     * @param remark 审核备注
     * @param username 操作用户
     */
    void auditRequisition(String requisitionId, Integer status, String remark, String username);
    
    /**
     * 完成领用
     *
     * @param requisitionId 领用单ID
     * @param username 操作用户
     */
    void completeRequisition(String requisitionId, String username);
    
    /**
     * 删除领用单
     *
     * @param requisitionIds 领用单ID数组
     * @param username 操作用户
     */
    void deleteRequisition(String[] requisitionIds, String username);
    
    /**
     * 分页查询领用单列表
     *
     * @param request 查询条件
     * @return 分页结果
     */
    IPage<ItemRequisitionVo> selectRequisitionList(ItemRequisitionSearchRequest request);
    
    /**
     * 查询领用单详情
     *
     * @param requisitionId 领用单ID
     * @return 领用单详情
     */
    ItemRequisitionDetailVo getRequisitionDetail(String requisitionId);
} 