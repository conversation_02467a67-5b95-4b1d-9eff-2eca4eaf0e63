package com.jingfang.device_module.mqtt.dto;

import lombok.Data;

import java.util.List;

/**
 * MQTT设备属性查询请求DTO
 */
@Data
public class DevicePropertyRequest {
    
    /**
     * 请求ID，用于匹配响应
     */
    private String id;
    
    /**
     * 版本号
     */
    private String version;
    
    /**
     * 确认标志，固定为0
     */
    private Integer ack;
    
    /**
     * 参数列表
     */
    private List<DevicePropertyParam> params;
    
    /**
     * 设备属性参数
     */
    @Data
    public static class DevicePropertyParam {
        /**
         * 客户端ID（设备IP地址）
         */
        private String clientID;
        
        /**
         * 属性列表
         */
        private List<PropertyInfo> properties;
    }
    
    /**
     * 属性信息
     */
    @Data
    public static class PropertyInfo {
        /**
         * 属性名称
         */
        private String name;
    }
    
    /**
     * 构造方法
     */
    public DevicePropertyRequest() {
        this.version = "1.0";
        this.ack = 0;
    }
    
    /**
     * 构造方法
     * @param id 请求ID
     */
    public DevicePropertyRequest(String id) {
        this();
        this.id = id;
    }
}
