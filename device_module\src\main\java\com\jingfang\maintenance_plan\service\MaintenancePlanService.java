package com.jingfang.maintenance_plan.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jingfang.maintenance_plan.module.dto.MaintenancePlanDto;
import com.jingfang.maintenance_plan.module.entity.MaintenancePlan;
import com.jingfang.maintenance_plan.module.request.MaintenancePlanSearchRequest;
import com.jingfang.maintenance_plan.module.vo.MaintenancePlanVo;

import java.util.List;

/**
 * 维护计划Service接口
 */
public interface MaintenancePlanService extends IService<MaintenancePlan> {
    
    /**
     * 新增维护计划
     * 
     * @param planDto 维护计划信息
     * @param createBy 创建人
     * @return 是否成功
     */
    boolean addMaintenancePlan(MaintenancePlanDto planDto, String createBy);
    
    /**
     * 修改维护计划
     * 
     * @param planDto 维护计划信息
     * @param updateBy 更新人
     * @return 是否成功
     */
    boolean updateMaintenancePlan(MaintenancePlanDto planDto, String updateBy);
    
    /**
     * 删除维护计划
     * 
     * @param planIds 计划ID数组
     * @param updateBy 更新人
     * @return 是否成功
     */
    boolean deleteMaintenancePlans(String[] planIds, String updateBy);
    
    /**
     * 分页查询维护计划列表
     * 
     * @param request 查询条件
     * @return 维护计划列表
     */
    IPage<MaintenancePlanVo> selectMaintenancePlanList(MaintenancePlanSearchRequest request);
    
    /**
     * 根据ID查询维护计划详情
     * 
     * @param planId 计划ID
     * @return 维护计划详情
     */
    MaintenancePlanVo selectMaintenancePlanById(String planId);
    
    /**
     * 启用/停用维护计划
     * 
     * @param planId 计划ID
     * @param status 状态(1-启用, 0-停用)
     * @param updateBy 更新人
     * @return 是否成功
     */
    boolean updateMaintenancePlanStatus(String planId, Integer status, String updateBy);
    
    /**
     * 查询即将到期的维护计划
     * 
     * @param days 提前天数
     * @return 即将到期的维护计划列表
     */
    List<MaintenancePlanVo> selectUpcomingMaintenancePlans(Integer days);
    
    /**
     * 查询已过期的维护计划
     * 
     * @return 已过期的维护计划列表
     */
    List<MaintenancePlanVo> selectOverdueMaintenancePlans();
    
    /**
     * 根据资产ID查询维护计划
     * 
     * @param assetId 资产ID
     * @return 维护计划列表
     */
    List<MaintenancePlanVo> selectMaintenancePlansByAssetId(String assetId);
    
    /**
     * 根据负责人查询维护计划
     * 
     * @param responsibleType 负责人类型
     * @param responsibleId 负责人ID
     * @return 维护计划列表
     */
    List<MaintenancePlanVo> selectMaintenancePlansByResponsible(Integer responsibleType, Long responsibleId);
    
    /**
     * 计算下次维护时间
     * 
     * @param cycleType 周期类型
     * @param cycleValue 周期值
     * @param baseTime 基准时间
     * @return 下次维护时间
     */
    java.util.Date calculateNextMaintenanceTime(Integer cycleType, Integer cycleValue, java.util.Date baseTime);
} 