package com.jingfang.asset_disposal.module.dto;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 资产处置DTO
 */
@Data
public class AssetDisposalDto {
    
    /**
     * 处置单ID
     */
    private String disposalId;
    
    /**
     * 处置申请标题
     */
    @NotBlank(message = "处置标题不能为空")
    private String disposalTitle;
    
    /**
     * 资产ID
     */
    @NotBlank(message = "资产ID不能为空")
    private String assetId;
    
    /**
     * 处置类型(1-报废, 2-调拨, 3-出售, 4-其他)
     */
    @NotNull(message = "处置类型不能为空")
    private Integer disposalType;
    
    /**
     * 处置原因
     */
    @NotBlank(message = "处置原因不能为空")
    private String disposalReason;
    
    /**
     * 预期处置时间
     */
    private Date expectedDisposalTime;
    
    /**
     * 当前资产价值
     */
    private BigDecimal currentValue;
    
    /**
     * 处置价值
     */
    private BigDecimal disposalValue;
    
    /**
     * 买方信息（出售时使用）
     */
    private String buyerInfo;
    
    /**
     * 调拨目标部门ID（调拨时使用）
     */
    private Long transferDeptId;
    
    /**
     * 附件列表
     */
    private List<AssetDisposalAttachmentDto> attachmentList;
} 