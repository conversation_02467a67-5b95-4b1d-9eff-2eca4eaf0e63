package com.jingfang.web.controller.item;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.common.core.domain.model.LoginUser;
import com.jingfang.common.utils.BusinessCodeGenerator;
import com.jingfang.wh_item.module.dto.ItemInboundDto;
import com.jingfang.wh_item.module.entity.ItemInbound;
import com.jingfang.wh_item.module.request.ItemInboundSearchRequest;
import com.jingfang.wh_item.module.vo.ItemInboundVo;
import com.jingfang.wh_item.service.ItemInboundService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/item/inbound")
public class ItemInboundController extends BaseController {

    @Resource
    private ItemInboundService itemInboundService;

    @Resource
    private BusinessCodeGenerator codeGenerator;

    /**
     * 新增物品入库单
     */
    @PreAuthorize("@ss.hasPermi('item:inbound:add')")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody @Validated ItemInboundDto inboundDto) {
        try {
            LoginUser user = getLoginUser();
            log.info("新增入库单请求参数：{}", inboundDto);
            String inboundId = codeGenerator.generateCode("WPRK");
            ItemInbound itemInbound = inboundDto.getMain();
            itemInbound.setInboundId(inboundId);
            itemInbound.setHandlerId(user.getUserId());

            inboundDto.setMain(itemInbound);
            itemInboundService.addItemInbound(inboundDto, user.getUser().getNickName());
            return AjaxResult.success("新增入库单成功", inboundId);
        } catch (Exception e) {
            log.error("新增入库单异常：", e);
            return AjaxResult.error("新增入库单失败：" + e.getMessage());
        }
    }

    /**
     * 编辑物品入库单
     */
    @PreAuthorize("@ss.hasPermi('item:inbound:edit')")
    @PutMapping("/{inboundId}")
    public AjaxResult edit(
            @PathVariable("inboundId") String inboundId,
            @RequestBody @Validated ItemInboundDto inboundDto) {
        try {
            log.info("编辑入库单请求参数：inboundId={}, data={}", inboundId, inboundDto);
            itemInboundService.updateItemInbound(inboundId, inboundDto, getNickName());
            return AjaxResult.success("编辑入库单成功");
        } catch (Exception e) {
            log.error("编辑入库单异常：", e);
            return AjaxResult.error("编辑入库单失败：" + e.getMessage());
        }
    }

    /**
     * 获取入库单详情
     */
    @PreAuthorize("@ss.hasPermi('item:inbound:query')")
    @GetMapping("/{inboundId}")
    public AjaxResult getDetail(@PathVariable("inboundId") String inboundId) {
        try {
            log.info("获取入库单详情，ID: {}", inboundId);
            ItemInboundVo detail = itemInboundService.getInboundDetail(inboundId);
            log.info("入库单详情获取成功，ID: {}, 状态: {}, 制单人: {}, 经手人: {}, 审核人: {}",
                    inboundId, detail.getStatus(),
                    detail.getCreatorName(), detail.getHandlerName(), detail.getAuditorName());
            return AjaxResult.success(detail);
        } catch (Exception e) {
            log.error("获取入库单详情异常：", e);
            return AjaxResult.error("获取入库单详情失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询入库单列表
     */
    @PreAuthorize("@ss.hasPermi('item:inbound:list')")
    @PostMapping("/list")
    public AjaxResult list(@RequestBody ItemInboundSearchRequest request) {
        IPage<ItemInboundVo> vos = itemInboundService.selectInboundList(request);
        return AjaxResult.success(vos);
    }

    /**
     * 提交入库单待确认
     */
    @PreAuthorize("@ss.hasPermi('item:inbound:submit')")
    @PutMapping("/submit/{inboundId}")
    public AjaxResult submit(@PathVariable("inboundId") String inboundId) {
        try {
            itemInboundService.submitInbound(inboundId, getNickName());
            return AjaxResult.success("提交成功，等待经手人确认");
        } catch (Exception e) {
            log.error("提交入库单异常：", e);
            return AjaxResult.error("提交失败：" + e.getMessage());
        }
    }

    /**
     * 经手人确认入库单
     */
    @PreAuthorize("@ss.hasPermi('item:inbound:handle')")
    @PutMapping("/handle/{inboundId}")
    public AjaxResult handle(
            @PathVariable("inboundId") String inboundId,
            @RequestParam(value = "remark", required = false) String remark) {
        try {
            itemInboundService.handleInbound(inboundId, remark, getNickName());
            return AjaxResult.success("确认操作成功");
        } catch (Exception e) {
            log.error("经手人确认入库单异常：", e);
            return AjaxResult.error("确认操作失败：" + e.getMessage());
        }
    }

    /**
     * 审核入库单
     */
    @PreAuthorize("@ss.hasPermi('item:inbound:audit')")
    @PutMapping("/audit/{inboundId}/{status}")
    public AjaxResult audit(
            @PathVariable("inboundId") String inboundId,
            @PathVariable("status") Integer status,
            @RequestParam(value = "remark", required = false) String remark) {
        try {
            itemInboundService.auditInbound(inboundId, status, remark, getNickName());
            return AjaxResult.success("审核操作成功");
        } catch (Exception e) {
            log.error("审核入库单异常：", e);
            return AjaxResult.error("审核操作失败：" + e.getMessage());
        }
    }

    /**
     * 删除入库单
     */
    @PreAuthorize("@ss.hasPermi('item:inbound:remove')")
    @DeleteMapping("/{inboundIds}")
    public AjaxResult remove(@PathVariable String[] inboundIds) {
        try {
            itemInboundService.deleteInbound(inboundIds, getNickName());
            return AjaxResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除入库单异常：", e);
            return AjaxResult.error("删除失败：" + e.getMessage());
        }
    }

}