package com.jingfang.web.controller.asset.stocktaking;

import com.jingfang.asset_stocktaking.module.vo.DeptStatistics;
import com.jingfang.asset_stocktaking.module.vo.StocktakingReportVo;
import com.jingfang.asset_stocktaking.service.StocktakingReportService;
import com.jingfang.common.annotation.Log;
import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.common.enums.BusinessType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 盘点报告控制器
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@RestController
@RequestMapping("/asset/stocktaking/report")
public class StocktakingReportController extends BaseController {

    @Resource
    private StocktakingReportService reportService;

    /**
     * 生成盘点汇总报告
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:report:view')")
    @GetMapping("/summary/{planId}")
    public AjaxResult generateSummaryReport(@PathVariable("planId") String planId) {
        StocktakingReportVo report = reportService.generateSummaryReport(planId);
        if (report != null) {
            return success(report);
        }
        return error("生成盘点汇总报告失败");
    }

    /**
     * 生成差异明细报告
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:report:view')")
    @GetMapping("/difference/{planId}")
    public AjaxResult generateDifferenceDetailReport(@PathVariable("planId") String planId) {
        List<Map<String, Object>> details = reportService.generateDifferenceDetailReport(planId);
        return success(details);
    }

    /**
     * 生成部门统计报告
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:report:view')")
    @GetMapping("/dept/{planId}")
    public AjaxResult generateDeptStatisticsReport(@PathVariable("planId") String planId) {
        List<DeptStatistics> statistics = reportService.generateDeptStatisticsReport(planId);
        return success(statistics);
    }

    /**
     * 生成盘点进度报告
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:report:view')")
    @GetMapping("/progress/{planId}")
    public AjaxResult generateProgressReport(@PathVariable("planId") String planId) {
        Map<String, Object> progress = reportService.generateProgressReport(planId);
        return success(progress);
    }

    /**
     * 生成资产变动报告
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:report:view')")
    @GetMapping("/change/{planId}")
    public AjaxResult generateAssetChangeReport(@PathVariable("planId") String planId) {
        List<Map<String, Object>> changes = reportService.generateAssetChangeReport(planId);
        return success(changes);
    }



    /**
     * 生成自定义报告
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:report:view')")
    @PostMapping("/custom/{planId}")
    public AjaxResult generateCustomReport(@PathVariable("planId") String planId,
                                          @RequestBody Map<String, Object> reportConfig) {
        Map<String, Object> customReport = reportService.generateCustomReport(planId, reportConfig);
        return success(customReport);
    }

    /**
     * 获取报告模板列表
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:report:view')")
    @GetMapping("/templates")
    public AjaxResult getReportTemplates() {
        List<Map<String, Object>> templates = reportService.getReportTemplates();
        return success(templates);
    }

    /**
     * 保存报告模板
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:report:template')")
    @Log(title = "盘点报告", businessType = BusinessType.INSERT)
    @PostMapping("/template")
    public AjaxResult saveReportTemplate(@RequestParam String templateName,
                                        @RequestBody Map<String, Object> templateConfig) {
        if (reportService.saveReportTemplate(templateName, templateConfig)) {
            return success("保存报告模板成功");
        }
        return error("保存报告模板失败");
    }

    /**
     * 删除报告模板
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:report:template')")
    @Log(title = "盘点报告", businessType = BusinessType.DELETE)
    @DeleteMapping("/template/{templateId}")
    public AjaxResult deleteReportTemplate(@PathVariable("templateId") String templateId) {
        if (reportService.deleteReportTemplate(templateId)) {
            return success("删除报告模板成功");
        }
        return error("删除报告模板失败");
    }

    /**
     * 生成盘点统计图表数据
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:report:view')")
    @GetMapping("/chart/{planId}")
    public AjaxResult generateChartData(@PathVariable("planId") String planId) {
        Map<String, Object> chartData = reportService.generateChartData(planId);
        return success(chartData);
    }

    /**
     * 生成多计划对比报告
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:report:view')")
    @PostMapping("/comparison")
    public AjaxResult generateComparisonReport(@RequestBody List<String> planIds) {
        Map<String, Object> comparisonReport = reportService.generateComparisonReport(planIds);
        return success(comparisonReport);
    }

    /**
     * 生成趋势分析报告
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:report:view')")
    @GetMapping("/trend")
    public AjaxResult generateTrendAnalysisReport(@RequestParam String startDate,
                                                 @RequestParam String endDate) {
        Map<String, Object> trendReport = reportService.generateTrendAnalysisReport(startDate, endDate);
        return success(trendReport);
    }
}
