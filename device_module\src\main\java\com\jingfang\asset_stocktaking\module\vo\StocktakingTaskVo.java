package com.jingfang.asset_stocktaking.module.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 盘点任务视图对象
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
public class StocktakingTaskVo implements Serializable {

    /**
     * 盘点任务ID
     */
    private String taskId;

    /**
     * 盘点计划ID
     */
    private String planId;

    /**
     * 盘点计划名称
     */
    private String planName;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 分配给的用户ID
     */
    private Long assignedUserId;

    /**
     * 分配用户姓名
     */
    private String assignedUserName;

    /**
     * 资产范围（JSON格式）
     */
    private String assetScope;

    /**
     * 预期盘点数量
     */
    private Integer expectedCount;

    /**
     * 实际盘点数量
     */
    private Integer actualCount;

    /**
     * 状态：1-待执行，2-执行中，3-已完成
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 任务进度信息
     */
    private TaskProgress progress;

    /**
     * 任务进度信息内部类
     */
    @Data
    public static class TaskProgress implements Serializable {
        
        /**
         * 完成进度百分比
         */
        private Double progressPercentage;
        
        /**
         * 已找到资产数
         */
        private Integer foundAssets;
        
        /**
         * 未找到资产数
         */
        private Integer notFoundAssets;
        
        /**
         * 异常资产数
         */
        private Integer abnormalAssets;
        
        /**
         * 耗时（分钟）
         */
        private Long durationMinutes;
        
        /**
         * 预计剩余时间（分钟）
         */
        private Long estimatedRemainingMinutes;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 任务统计信息
     */
    private TaskStatistics statistics;

    /**
     * 任务统计信息内部类
     */
    @Data
    public static class TaskStatistics implements Serializable {
        
        /**
         * 盘点记录总数
         */
        private Integer totalRecords;
        
        /**
         * 正常记录数
         */
        private Integer normalRecords;
        
        /**
         * 异常记录数
         */
        private Integer abnormalRecords;
        
        /**
         * 差异记录数
         */
        private Integer differenceRecords;
        
        /**
         * 最后盘点时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date lastInventoryTime;

        private static final long serialVersionUID = 1L;
    }

    private static final long serialVersionUID = 1L;
}
