package com.jingfang.web.controller.maintenance;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.maintenance_plan.module.dto.MaintenancePlanDto;
import com.jingfang.maintenance_plan.module.request.MaintenancePlanSearchRequest;
import com.jingfang.maintenance_plan.module.vo.MaintenancePlanVo;
import com.jingfang.maintenance_plan.service.MaintenancePlanService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 维护计划控制器
 */
@Slf4j
@RestController
@RequestMapping("/maintenance/plan")
public class MaintenancePlanController extends BaseController {
    
    @Resource
    private MaintenancePlanService maintenancePlanService;
    
    /**
     * 新增维护计划
     */
    @PreAuthorize("@ss.hasPermi('maintenance:plan:add')")
    @PostMapping
    public AjaxResult add(@RequestBody @Validated MaintenancePlanDto planDto) {
        try {
            log.info("新增维护计划请求参数：{}", planDto);
            boolean success = maintenancePlanService.addMaintenancePlan(planDto, getNickName());
            if (success) {
                return AjaxResult.success("新增维护计划成功");
            } else {
                return AjaxResult.error("新增维护计划失败");
            }
        } catch (Exception e) {
            log.error("新增维护计划异常：", e);
            return AjaxResult.error("新增维护计划失败：" + e.getMessage());
        }
    }
    
    /**
     * 修改维护计划
     */
    @PreAuthorize("@ss.hasPermi('maintenance:plan:edit')")
    @PutMapping
    public AjaxResult edit(@RequestBody @Validated MaintenancePlanDto planDto) {
        try {
            log.info("修改维护计划请求参数：{}", planDto);
            boolean success = maintenancePlanService.updateMaintenancePlan(planDto, getNickName());
            if (success) {
                return AjaxResult.success("修改维护计划成功");
            } else {
                return AjaxResult.error("修改维护计划失败");
            }
        } catch (Exception e) {
            log.error("修改维护计划异常：", e);
            return AjaxResult.error("修改维护计划失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除维护计划
     */
    @PreAuthorize("@ss.hasPermi('maintenance:plan:remove')")
    @DeleteMapping("/{planIds}")
    public AjaxResult remove(@PathVariable String planIds) {
        try {
            log.info("删除维护计划，ID：{}", planIds);
            String[] ids = planIds.split(",");
            boolean success = maintenancePlanService.deleteMaintenancePlans(ids, getNickName());
            if (success) {
                return AjaxResult.success("删除维护计划成功");
            } else {
                return AjaxResult.error("删除维护计划失败");
            }
        } catch (Exception e) {
            log.error("删除维护计划异常：", e);
            return AjaxResult.error("删除维护计划失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询维护计划列表
     */
    @PreAuthorize("@ss.hasPermi('maintenance:plan:list')")
    @PostMapping("/list")
    public AjaxResult list(@RequestBody MaintenancePlanSearchRequest request) {
        try {
            IPage<MaintenancePlanVo> page = maintenancePlanService.selectMaintenancePlanList(request);
            return AjaxResult.success(page);
        } catch (Exception e) {
            log.error("查询维护计划列表异常：", e);
            return AjaxResult.error("查询维护计划列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据ID查询维护计划详情
     */
    @PreAuthorize("@ss.hasPermi('maintenance:plan:query')")
    @GetMapping("/{planId}")
    public AjaxResult getInfo(@PathVariable("planId") String planId) {
        try {
            log.info("查询维护计划详情，ID：{}", planId);
            MaintenancePlanVo planVo = maintenancePlanService.selectMaintenancePlanById(planId);
            return AjaxResult.success(planVo);
        } catch (Exception e) {
            log.error("查询维护计划详情异常：", e);
            return AjaxResult.error("查询维护计划详情失败：" + e.getMessage());
        }
    }
    
    /**
     * 启用/停用维护计划
     */
    @PreAuthorize("@ss.hasPermi('maintenance:plan:edit')")
    @PutMapping("/status/{planId}/{status}")
    public AjaxResult changeStatus(@PathVariable("planId") String planId, 
                                  @PathVariable("status") Integer status) {
        try {
            log.info("更新维护计划状态，ID：{}，状态：{}", planId, status);
            boolean success = maintenancePlanService.updateMaintenancePlanStatus(planId, status, getNickName());
            if (success) {
                String statusName = status == 1 ? "启用" : "停用";
                return AjaxResult.success(statusName + "维护计划成功");
            } else {
                return AjaxResult.error("更新维护计划状态失败");
            }
        } catch (Exception e) {
            log.error("更新维护计划状态异常：", e);
            return AjaxResult.error("更新维护计划状态失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询即将到期的维护计划
     */
    @PreAuthorize("@ss.hasPermi('maintenance:plan:query')")
    @GetMapping("/upcoming/{days}")
    public AjaxResult getUpcomingPlans(@PathVariable("days") Integer days) {
        try {
            log.info("查询即将到期的维护计划，提前天数：{}", days);
            List<MaintenancePlanVo> plans = maintenancePlanService.selectUpcomingMaintenancePlans(days);
            return AjaxResult.success(plans);
        } catch (Exception e) {
            log.error("查询即将到期的维护计划异常：", e);
            return AjaxResult.error("查询即将到期的维护计划失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询已过期的维护计划
     */
    @PreAuthorize("@ss.hasPermi('maintenance:plan:query')")
    @GetMapping("/overdue")
    public AjaxResult getOverduePlans() {
        try {
            log.info("查询已过期的维护计划");
            List<MaintenancePlanVo> plans = maintenancePlanService.selectOverdueMaintenancePlans();
            return AjaxResult.success(plans);
        } catch (Exception e) {
            log.error("查询已过期的维护计划异常：", e);
            return AjaxResult.error("查询已过期的维护计划失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据资产ID查询维护计划
     */
    @PreAuthorize("@ss.hasPermi('maintenance:plan:query')")
    @GetMapping("/asset/{assetId}")
    public AjaxResult getPlansByAssetId(@PathVariable("assetId") String assetId) {
        try {
            log.info("查询资产的维护计划，资产ID：{}", assetId);
            List<MaintenancePlanVo> plans = maintenancePlanService.selectMaintenancePlansByAssetId(assetId);
            return AjaxResult.success(plans);
        } catch (Exception e) {
            log.error("查询资产的维护计划异常：", e);
            return AjaxResult.error("查询资产的维护计划失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据负责人查询维护计划
     */
    @PreAuthorize("@ss.hasPermi('maintenance:plan:query')")
    @GetMapping("/responsible/{responsibleType}/{responsibleId}")
    public AjaxResult getPlansByResponsible(@PathVariable("responsibleType") Integer responsibleType,
                                          @PathVariable("responsibleId") Long responsibleId) {
        try {
            log.info("查询负责人的维护计划，类型：{}，ID：{}", responsibleType, responsibleId);
            List<MaintenancePlanVo> plans = maintenancePlanService.selectMaintenancePlansByResponsible(
                    responsibleType, responsibleId);
            return AjaxResult.success(plans);
        } catch (Exception e) {
            log.error("查询负责人的维护计划异常：", e);
            return AjaxResult.error("查询负责人的维护计划失败：" + e.getMessage());
        }
    }
} 