package com.jingfang.wechat.module.entity;

import com.jingfang.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 微信用户对象 sys_wx_user
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SysWxUser extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 微信用户ID */
    private Long wxUserId;

    /** 微信openid */
    private String openid;

    /** 微信unionid */
    private String unionid;

    /** 微信昵称 */
    private String nickName;

    /** 头像地址 */
    private String avatarUrl;

    /** 性别 */
    private String gender;

    /** 国家 */
    private String country;

    /** 省份 */
    private String province;

    /** 城市 */
    private String city;
}
