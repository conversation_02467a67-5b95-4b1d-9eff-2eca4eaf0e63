package com.jingfang.web.controller.wechat;

import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.wechat.module.request.WxBindBody;
import com.jingfang.wechat.module.request.WxLoginBody;
import com.jingfang.wechat.service.ISysWxLoginService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/wx")
public class WxLoginController extends BaseController {
    @Resource
    private ISysWxLoginService wxLoginService;

    /**
     * 微信小程序登录
     */
    @PostMapping("/login")
    public AjaxResult wxLogin(@RequestBody WxLoginBody wxLoginBody)
    {
        // 处理微信登录
        return wxLoginService.wxLogin(wxLoginBody.getCode(), wxLoginBody.getUserInfo());
    }

    /**
     * 绑定系统账号
     */
    @PostMapping("/bind")
    public AjaxResult bindAccount(@RequestBody WxBindBody wxBindBody)
    {
        // 绑定系统账号
        return wxLoginService.bindAccount(wxBindBody.getOpenid(), wxBindBody.getUsername(),
                wxBindBody.getPassword(), wxBindBody.getBindType());
    }

    /**
     * 解绑系统账号
     */
    @PostMapping("/unbind")
    public AjaxResult unbindAccount(@RequestBody WxBindBody wxBindBody)
    {
        // 解绑系统账号
        return wxLoginService.unbindAccount(wxBindBody.getOpenid(), wxBindBody.getUserId());
    }
}
