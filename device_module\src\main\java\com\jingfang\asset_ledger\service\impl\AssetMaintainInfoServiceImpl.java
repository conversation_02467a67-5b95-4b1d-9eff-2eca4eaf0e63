package com.jingfang.asset_ledger.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingfang.asset_ledger.module.entity.AssetMaintainInfo;
import com.jingfang.asset_ledger.module.vo.AssetDetailMaintainInfoVo;
import com.jingfang.asset_ledger.service.AssetMaintainInfoService;
import com.jingfang.asset_ledger.mapper.AssetMaintainInfoMapper;
import com.jingfang.common.utils.bean.BeanUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【asset_maintenance(资产维保信息)】的数据库操作Service实现
* @createDate 2025-05-06 15:10:04
*/
@Service
public class AssetMaintainInfoServiceImpl extends ServiceImpl<AssetMaintainInfoMapper, AssetMaintainInfo>
    implements AssetMaintainInfoService {





    @Override
    public AssetDetailMaintainInfoVo selectMaintainInfoById(String assetId) {
        LambdaQueryWrapper<AssetMaintainInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AssetMaintainInfo::getAssetId, assetId);
        AssetMaintainInfo info = this.baseMapper.selectOne(queryWrapper);
        AssetDetailMaintainInfoVo vo = new AssetDetailMaintainInfoVo();
        BeanUtils.copyBeanProp(vo,info);
        return vo;
    }
}




