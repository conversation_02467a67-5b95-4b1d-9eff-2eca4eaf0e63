package com.jingfang.wh_item.module.dto;

import com.jingfang.wh_item.module.entity.ItemBaseInfo;
import com.jingfang.wh_item.module.entity.ItemConsumableAttr;
import com.jingfang.wh_item.module.entity.ItemInventory;
import com.jingfang.wh_item.module.entity.ItemPartAttr;
import lombok.Data;

import java.io.Serializable;

/**
 * 物品信息DTO，用于前后端交互
 */
@Data
public class ItemDto implements Serializable {
    
    /**
     * 物品基本信息
     */
    private ItemBaseInfo baseInfo;
    
    /**
     * 库存信息
     */
    private ItemInventory inventory;
    
    /**
     * 消耗品属性（当itemType=1时有效）
     */
    private ItemConsumableAttr consumableAttr;
    
    /**
     * 备品备件属性（当itemType=2时有效）
     */
    private ItemPartAttr partAttr;
    
    private static final long serialVersionUID = 1L;
} 