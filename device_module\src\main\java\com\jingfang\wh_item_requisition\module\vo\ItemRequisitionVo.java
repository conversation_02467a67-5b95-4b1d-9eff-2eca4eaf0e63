package com.jingfang.wh_item_requisition.module.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 物品领用单列表VO
 */
@Data
public class ItemRequisitionVo implements Serializable {
    
    /**
     * 领用单ID
     */
    private String requisitionId;
    
    /**
     * 业务日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date businessDate;
    
    /**
     * 申请人ID
     */
    private Long applicantId;
    
    /**
     * 申请人姓名（关联查询sys_user表获取）
     */
    private String applicantName;
    
    /**
     * 申请部门ID
     */
    private Long deptId;
    
    /**
     * 申请部门名称（关联查询sys_dept表获取）
     */
    private String deptName;
    
    /**
     * 领用用途/说明
     */
    private String requisitionPurpose;
    
    /**
     * 状态(1-草稿, 2-待确认, 3-待审核, 4-已通过, 5-已退回, 6-已完成)
     */
    private Integer status;
    
    /**
     * 状态名称
     */
    private String statusName;
    
    /**
     * 制单人ID
     */
    private Long creatorId;
    
    /**
     * 制单人姓名（关联查询sys_user表获取）
     */
    private String creatorName;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 经手人ID
     */
    private Long handlerId;
    
    /**
     * 经手人姓名（关联查询sys_user表获取）
     */
    private String handlerName;
    
    /**
     * 经手确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date handleTime;
    
    /**
     * 审核人ID
     */
    private Long auditorId;
    
    /**
     * 审核人姓名（关联查询sys_user表获取）
     */
    private String auditorName;
    
    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditTime;
    
    /**
     * 明细数量（物品种类数）
     */
    private Integer detailCount;
    
    /**
     * 申请总数量
     */
    private BigDecimal totalRequisitionQuantity;
    
    /**
     * 批准总数量
     */
    private BigDecimal totalApprovedQuantity;
    
    /**
     * 实际领用总数量
     */
    private BigDecimal totalActualQuantity;
    
    private static final long serialVersionUID = 1L;
} 