package com.jingfang.wh_item.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@TableName(value = "item_outbound_operation_log")
@Data
public class ItemOutboundOperationLog implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long logId;

    private String outboundId;

    private Integer operationType;

    private String operationContent;

    private Date operationTime;

    private Long operatorId;

    private String operatorName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
} 