# 工作台功能测试指南

## 概述

本文档说明如何测试设备监控管理系统的工作台功能，工作台已从模拟数据更新为调用后端真实接口获取数据。

## 功能说明

### 已实现的功能

1. **资产概览统计**
   - 资产总数
   - 资产总值（自动转换为万元显示）
   - 本月新增资产数
   - 资产利用率

2. **待办事项统计**
   - 待审核处置申请
   - 待确认入库单
   - 待审核入库单
   - 待批准处置申请
   - 处置中申请

3. **系统状态监控**
   - 数据库状态
   - 缓存服务状态
   - 存储空间使用率
   - 网络状态

4. **用户活动统计**
   - 在线用户数
   - 今日访问量
   - 今日操作数
   - 错误次数

### 接口调用说明

工作台页面会调用以下后端接口：

1. **资产概览数据**: `GET /asset/workbench/overview`
2. **待办事项统计**: `GET /asset/workbench/todo-statistics`
3. **系统统计**: `GET /system/statistics` (可选，失败时使用默认值)
4. **用户活动统计**: `GET /system/user-activity` (可选，失败时使用默认值)

## 测试步骤

### 1. 启动后端服务

确保后端服务正常启动，资产工作台相关接口可用。

### 2. 启动前端服务

```bash
cd device_monitor-ui
npm run dev
```

### 3. 访问工作台

1. 登录系统
2. 系统会自动跳转到工作台页面（路由: `/workbench`）
3. 观察页面加载情况

### 4. 功能验证

#### 4.1 数据加载验证

- 页面加载时应显示加载动画
- 核心指标概览区域应显示真实的资产统计数据
- 待办事项区域应显示实际的待办数量
- 如果没有待办事项，应显示"暂无待办事项"

#### 4.2 数据刷新验证

- 点击页面右上角的"刷新数据"按钮
- 应显示刷新中状态
- 刷新完成后显示成功提示
- 数据应重新加载

#### 4.3 快捷操作验证

- 点击快捷操作区域的各个功能按钮
- 应正确跳转到对应的功能页面

#### 4.4 待办事项验证

- 点击待办事项列表中的项目
- 应跳转到对应的管理页面

## 预期结果

### 正常情况

1. **页面加载**：工作台页面正常加载，显示用户名和系统标题
2. **数据展示**：核心指标显示真实的资产统计数据
3. **待办事项**：显示实际的待办数量，点击可跳转
4. **数据刷新**：刷新功能正常工作
5. **响应式设计**：在不同屏幕尺寸下正常显示

### 异常情况处理

1. **接口调用失败**：
   - 资产概览接口失败：显示默认值（0）
   - 待办事项接口失败：显示"暂无待办事项"
   - 系统统计接口失败：保持默认的系统状态
   - 用户活动接口失败：保持默认的活动数据

2. **权限不足**：
   - 如果用户没有相应权限，接口会返回权限错误
   - 前端会显示相应的错误提示

## 数据格式说明

### 资产概览数据格式

```json
{
  "totalCount": 1500,
  "totalValue": 50000000.00,
  "monthlyNewCount": 50,
  "monthlyNewValue": 2000000.00,
  "utilizationRate": 93.33
}
```

### 待办事项数据格式

```json
{
  "pendingDisposal": 5,
  "pendingConfirm": 3,
  "pendingAudit": 2,
  "pendingApproval": 5,
  "processing": 2
}
```

## 注意事项

1. **权限配置**：确保用户具有以下权限：
   - `asset:workbench:view`
   - `asset:workbench:overview`
   - `asset:workbench:todo`

2. **数据库数据**：为了看到真实效果，建议在数据库中添加一些测试数据：
   - 资产台账数据
   - 入库单数据
   - 处置申请数据

3. **浏览器兼容性**：建议使用现代浏览器进行测试

## 故障排除

### 常见问题

1. **页面显示空白**：
   - 检查控制台是否有JavaScript错误
   - 确认后端服务是否正常启动

2. **数据显示为0**：
   - 检查数据库中是否有相关数据
   - 确认接口是否正常返回数据

3. **权限错误**：
   - 检查用户是否具有相应权限
   - 确认权限配置是否正确

4. **接口调用失败**：
   - 检查网络连接
   - 确认后端接口是否正常
   - 查看浏览器开发者工具的网络面板

### 调试方法

1. **查看控制台日志**：打开浏览器开发者工具，查看Console面板的错误信息
2. **查看网络请求**：在Network面板查看API请求的状态和响应
3. **检查后端日志**：查看后端服务的日志输出

## 后续扩展

工作台功能可以进一步扩展：

1. **图表展示**：添加资产趋势图表、状态分布饼图等
2. **实时更新**：使用WebSocket实现数据实时更新
3. **个性化配置**：允许用户自定义工作台布局
4. **更多模块**：集成设备管理、物品管理等其他模块的统计数据
