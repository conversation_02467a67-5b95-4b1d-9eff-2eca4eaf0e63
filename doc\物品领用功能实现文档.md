# 物品领用功能实现文档

本文档详细描述了物品领用功能的实现方式，以便于在微信小程序上复现该功能。

## 一、功能概述

物品领用功能是物资管理系统中的核心功能之一，用于记录和管理员工领用物品的流程。该功能包含完整的业务流程：从申请、确认、审核到最终完成领用，并自动更新库存数据。

### 1.1 业务流程

物品领用的完整业务流程如下：

1. **创建领用单**：用户创建领用单，填写申请人、申请部门、领用用途和领用物品明细
2. **提交领用单**：用户提交领用单，状态从"草稿"变为"待确认"
3. **经手人确认**：经手人确认领用单信息，状态从"待确认"变为"待审核"
4. **审核领用单**：审核人审核领用单，可以通过或退回
   - 审核通过：状态从"待审核"变为"已通过"，并自动扣减库存，状态自动更新为"已完成"
   - 审核退回：状态从"待审核"变为"已退回"，用户可以修改后重新提交
5. **完成领用**：审核通过后，系统自动完成领用流程，扣减相应库存

### 1.2 状态流转

领用单状态流转图：

```
[草稿(1)] ----提交----> [待确认(2)] ----确认----> [待审核(3)]
    ^                                             |
    |                                             v
    +-------------退回---------------- [已退回(5)] / [已通过(4)]
                                                  |
                                                  v
                                             [已完成(6)]
```

## 二、数据结构

### 2.1 数据库表设计

物品领用功能涉及以下数据库表：

1. **item_requisition**：领用单主表
2. **item_requisition_detail**：领用单明细表
3. **item_requisition_operation_log**：领用单操作日志表

#### 2.1.1 领用单主表 (item_requisition)

| 字段名 | 类型 | 说明 |
|-------|------|------|
| requisition_id | varchar | 领用单ID（主键） |
| business_date | date | 业务日期 |
| applicant_id | bigint | 申请人ID |
| dept_id | bigint | 申请部门ID |
| requisition_purpose | varchar | 领用用途/说明 |
| status | int | 状态(1-草稿, 2-待确认, 3-待审核, 4-已通过, 5-已退回, 6-已完成) |
| creator_id | bigint | 制单人ID |
| create_time | datetime | 创建时间 |
| handler_id | bigint | 经手人ID |
| handle_time | datetime | 经手确认时间 |
| handle_remark | varchar | 经手人备注 |
| auditor_id | bigint | 审核人ID |
| audit_time | datetime | 审核时间 |
| audit_remark | varchar | 审核备注 |
| update_time | datetime | 更新时间 |
| del_flag | char | 删除标志(0-正常, 1-删除) |

#### 2.1.2 领用单明细表 (item_requisition_detail)

| 字段名 | 类型 | 说明 |
|-------|------|------|
| detail_id | bigint | 明细ID（主键） |
| requisition_id | varchar | 领用单ID（外键） |
| item_id | varchar | 物品ID |
| warehouse_id | int | 仓库ID |
| shelf_location | varchar | 货架位置 |
| requisition_quantity | decimal | 申请数量 |
| approved_quantity | decimal | 批准数量 |
| actual_quantity | decimal | 实际领用数量 |
| remark | varchar | 备注 |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |

#### 2.1.3 领用单操作日志表 (item_requisition_operation_log)

| 字段名 | 类型 | 说明 |
|-------|------|------|
| log_id | bigint | 日志ID（主键） |
| requisition_id | varchar | 领用单ID（外键） |
| operation_type | int | 操作类型(1-创建, 2-提交, 3-确认, 4-审核通过, 5-审核退回, 6-完成, 7-删除) |
| operation_content | varchar | 操作内容 |
| operation_time | datetime | 操作时间 |
| operator_id | bigint | 操作人ID |
| operator_name | varchar | 操作人姓名 |

### 2.2 数据传输对象

#### 2.2.1 领用单DTO (ItemRequisitionDto)

用于前后端交互的数据传输对象：

```json
{
  "main": {
    "requisitionId": "string",    // 领用单ID（新增时可为空，系统自动生成）
    "businessDate": "2024-01-01", // 业务日期（必填）
    "applicantId": 1,             // 申请人ID（必填）
    "deptId": 1,                  // 申请部门ID（必填）
    "requisitionPurpose": "string" // 领用用途/说明
  },
  "details": [                    // 领用明细列表（必填，至少一条）
    {
      "itemId": "string",         // 物品ID（必填）
      "warehouseId": 1,           // 仓库ID（必填）
      "shelfLocation": "string",  // 货架位置
      "requisitionQuantity": 3.00, // 申请数量（必填）
      "remark": "string"          // 备注
    }
  ]
}
```

## 三、接口设计

### 3.1 后端接口

物品领用功能的后端接口路径为 `/item/requisition`，主要包括以下接口：

#### 3.1.1 新增领用单

- **接口**: `POST /item/requisition/add`
- **功能**: 创建新的领用单
- **权限**: `item:requisition:add`
- **参数**: ItemRequisitionDto（领用单信息）
- **返回**: 操作结果和领用单ID

#### 3.1.2 修改领用单

- **接口**: `PUT /item/requisition/{requisitionId}`
- **功能**: 修改领用单信息
- **权限**: `item:requisition:edit`
- **参数**: 
  - requisitionId: 领用单ID
  - ItemRequisitionDto: 领用单信息
- **返回**: 操作结果

#### 3.1.3 提交领用单

- **接口**: `PUT /item/requisition/{requisitionId}/submit`
- **功能**: 提交领用单，状态变为待确认
- **权限**: `item:requisition:submit`
- **参数**: requisitionId（领用单ID）
- **返回**: 操作结果

#### 3.1.4 确认领用单

- **接口**: `PUT /item/requisition/{requisitionId}/confirm`
- **功能**: 经手人确认领用单，状态变为待审核
- **权限**: `item:requisition:confirm`
- **参数**: 
  - requisitionId: 领用单ID
  - remark: 确认备注（可选）
- **返回**: 操作结果

#### 3.1.5 审核领用单

- **接口**: `PUT /item/requisition/{requisitionId}/audit`
- **功能**: 审核领用单，可通过或退回
- **权限**: `item:requisition:audit`
- **参数**: 
  - requisitionId: 领用单ID
  - status: 审核结果(4-通过, 5-退回)
  - remark: 审核备注（可选）
- **返回**: 操作结果

#### 3.1.6 查询领用单列表

- **接口**: `POST /item/requisition/list`
- **功能**: 分页查询领用单列表
- **权限**: `item:requisition:list`
- **参数**: ItemRequisitionSearchRequest（查询条件）
- **返回**: 分页数据

#### 3.1.7 查询领用单详情

- **接口**: `GET /item/requisition/{requisitionId}`
- **功能**: 查询领用单详情
- **权限**: `item:requisition:query`
- **参数**: requisitionId（领用单ID）
- **返回**: 领用单详情

#### 3.1.8 删除领用单

- **接口**: `DELETE /item/requisition/{requisitionIds}`
- **功能**: 删除领用单
- **权限**: `item:requisition:remove`
- **参数**: requisitionIds（领用单ID，多个用逗号分隔）
- **返回**: 操作结果

### 3.2 接口返回格式

所有接口都遵循统一的返回格式：

```json
{
  "code": 200,           // 状态码：200-成功，其他-失败
  "msg": "success",      // 消息
  "data": {}            // 数据（可选）
}
```

## 四、业务逻辑实现

### 4.1 核心业务流程

#### 4.1.1 创建领用单

1. **数据校验**：
   - 验证申请人ID和部门ID是否有效
   - 验证领用明细中的物品ID是否存在
   - 验证申请数量是否大于0

2. **生成领用单ID**：
   - 格式：REQ + UUID（去掉横线并转大写）
   - 示例：REQ123456789ABCDEF

3. **保存数据**：
   - 保存领用单主表信息，状态设为草稿(1)
   - 保存领用单明细信息
   - 记录操作日志

#### 4.1.2 状态流转逻辑

1. **提交领用单**：
   - 只有草稿(1)和已退回(5)状态可以提交
   - 状态更新为待确认(2)
   - 记录操作日志

2. **确认领用单**：
   - 只有待确认(2)状态可以确认
   - 状态更新为待审核(3)
   - 记录经手人信息和确认时间
   - 记录操作日志

3. **审核领用单**：
   - 只有待审核(3)状态可以审核
   - 审核通过：状态更新为已通过(4)，自动扣减库存，状态自动更新为已完成(6)
   - 审核退回：状态更新为已退回(5)
   - 记录审核人信息和审核时间
   - 记录操作日志

#### 4.1.3 库存扣减逻辑

当领用单审核通过时，系统会自动扣减库存：

1. **遍历领用明细**：
   - 获取每个明细的物品ID、仓库ID和申请数量
   - 检查库存是否充足

2. **扣减库存**：
   - 调用物品服务的库存更新接口
   - 操作类型为出库(2)
   - 关联单据ID为领用单ID

3. **更新状态**：
   - 库存扣减成功后，领用单状态自动更新为已完成(6)
   - 记录操作日志

### 4.2 权限控制

物品领用功能基于RuoYi框架的权限体系，主要权限包括：

- `item:requisition:add`：新增领用单
- `item:requisition:edit`：修改领用单
- `item:requisition:submit`：提交领用单
- `item:requisition:confirm`：确认领用单
- `item:requisition:audit`：审核领用单
- `item:requisition:list`：查询领用单列表
- `item:requisition:query`：查询领用单详情
- `item:requisition:remove`：删除领用单

### 4.3 数据校验规则

1. **必填字段校验**：
   - 业务日期、申请人ID、申请部门ID为必填
   - 领用明细至少包含一条记录
   - 物品ID、仓库ID、申请数量为必填

2. **业务规则校验**：
   - 申请数量必须大于0
   - 物品必须存在且未删除
   - 仓库必须存在且有效

3. **状态校验**：
   - 只有特定状态的领用单才能进行相应操作
   - 已完成或已删除的领用单不能修改

## 五、前端实现

### 5.1 页面结构

前端页面主要包括以下部分：

1. **查询条件区域**：
   - 基础查询：领用单号、业务日期、领用状态
   - 高级查询：申请人、申请部门、制单人、物品名称

2. **操作按钮区域**：
   - 新增、删除、导出等操作按钮

3. **数据表格区域**：
   - 显示领用单列表
   - 支持分页、排序、筛选

4. **详情弹窗**：
   - 显示领用单详细信息
   - 包含明细列表和操作日志

### 5.2 关键功能实现

#### 5.2.1 状态显示

根据领用单状态显示不同的标签颜色：

```javascript
const statusMap = {
  1: { label: '草稿', type: 'info' },
  2: { label: '待确认', type: 'warning' },
  3: { label: '待审核', type: 'primary' },
  4: { label: '已通过', type: 'success' },
  5: { label: '已退回', type: 'danger' },
  6: { label: '已完成', type: 'success' }
}
```

#### 5.2.2 操作按钮控制

根据领用单状态和用户权限控制操作按钮的显示：

```javascript
// 可编辑状态：草稿、已退回
const canEdit = [1, 5].includes(status)

// 可提交状态：草稿、已退回
const canSubmit = [1, 5].includes(status)

// 可确认状态：待确认
const canConfirm = status === 2

// 可审核状态：待审核
const canAudit = status === 3

// 可删除状态：草稿、已退回
const canDelete = [1, 5].includes(status)
```

## 六、微信小程序适配建议

### 6.1 界面设计建议

1. **简化操作流程**：
   - 合并相似功能，减少页面跳转
   - 使用底部弹窗代替新页面

2. **优化交互体验**：
   - 使用下拉刷新和上拉加载
   - 支持手势操作（滑动删除等）

3. **适配小屏幕**：
   - 精简表格列，只显示关键信息
   - 使用卡片式布局代替表格

### 6.2 功能简化建议

1. **状态流转简化**：
   - 可以考虑简化审核流程，直接从提交到审核
   - 或者提供快速审核功能

2. **权限简化**：
   - 基于用户角色自动判断权限
   - 减少复杂的权限配置

3. **数据同步**：
   - 支持离线操作，在线同步
   - 提供数据缓存机制

### 6.3 技术实现建议

1. **接口调用**：
   - 使用wx.request进行接口调用
   - 统一封装请求和响应处理

2. **数据管理**：
   - 使用全局状态管理（如Vuex）
   - 实现数据缓存和同步机制

3. **组件化开发**：
   - 封装通用组件（如状态标签、操作按钮等）
   - 提高代码复用性

## 七、总结

物品领用功能是一个完整的业务流程管理系统，包含了状态流转、权限控制、库存管理等多个方面。在微信小程序上实现时，需要根据移动端的特点进行适当的简化和优化，确保用户体验的同时保持业务逻辑的完整性。

关键实现要点：
1. 完整的状态流转机制
2. 严格的权限控制
3. 自动的库存扣减
4. 详细的操作日志
5. 灵活的查询和筛选功能
