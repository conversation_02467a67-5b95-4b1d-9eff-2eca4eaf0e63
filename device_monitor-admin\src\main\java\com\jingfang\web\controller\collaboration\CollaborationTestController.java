package com.jingfang.web.controller.collaboration;

import com.jingfang.common.core.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

/**
 * 协作模块测试Controller
 */
@RestController
@RequestMapping("/collaboration/test")
public class CollaborationTestController {
    
    @Autowired
    private DataSource dataSource;
    
    @GetMapping("/hello")
    public AjaxResult hello() {
        return AjaxResult.success("Hello, Collaboration Module!");
    }
    
    @GetMapping("/check-tables")
    public AjaxResult checkTables() {
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            List<String> tables = new ArrayList<>();
            
            // 检查协作相关的表
            String[] tableNames = {
                "collaboration_spreadsheet",
                "collaboration_spreadsheet_collaborator", 
                "collaboration_spreadsheet_operation"
            };
            
            for (String tableName : tableNames) {
                try (ResultSet rs = metaData.getTables(null, null, tableName, new String[]{"TABLE"})) {
                    if (rs.next()) {
                        tables.add(tableName + " ✓");
                    } else {
                        tables.add(tableName + " ✗ (不存在)");
                    }
                }
            }
            
            return AjaxResult.success("数据库表检查结果", tables);
            
        } catch (Exception e) {
            return AjaxResult.error("检查数据库表失败: " + e.getMessage());
        }
    }
}
