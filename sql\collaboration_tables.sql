-- 在线协作模块数据库表结构
-- 请先执行此SQL脚本创建数据库表

-- 1. 在线表格表
CREATE TABLE `collaboration_spreadsheet` (
  `id` varchar(64) NOT NULL COMMENT '表格ID',
  `title` varchar(100) NOT NULL COMMENT '表格标题',
  `description` varchar(500) DEFAULT NULL COMMENT '表格描述',
  `data` longtext COMMENT '表格数据（JSON格式存储Luckysheet数据）',
  `create_by` bigint(20) NOT NULL COMMENT '创建者ID',
  `create_by_name` varchar(64) NOT NULL COMMENT '创建者姓名',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新者ID',
  `update_by_name` varchar(64) DEFAULT NULL COMMENT '更新者姓名',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `status` char(1) NOT NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `is_public` char(1) NOT NULL DEFAULT '0' COMMENT '是否公开（0私有 1公开）',
  `share_token` varchar(64) DEFAULT NULL COMMENT '分享链接token',
  `share_password` varchar(32) DEFAULT NULL COMMENT '分享密码',
  `share_expire_time` datetime DEFAULT NULL COMMENT '分享过期时间',
  `version` int(11) NOT NULL DEFAULT '1' COMMENT '版本号',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_create_by` (`create_by`),
  KEY `idx_status` (`status`),
  KEY `idx_share_token` (`share_token`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='在线表格表';

-- 2. 表格协作者表
CREATE TABLE `collaboration_spreadsheet_collaborator` (
  `id` varchar(64) NOT NULL COMMENT '协作记录ID',
  `spreadsheet_id` varchar(64) NOT NULL COMMENT '表格ID',
  `user_id` bigint(20) NOT NULL COMMENT '协作者用户ID',
  `user_name` varchar(64) NOT NULL COMMENT '协作者用户名',
  `nick_name` varchar(64) DEFAULT NULL COMMENT '协作者昵称',
  `dept_id` bigint(20) DEFAULT NULL COMMENT '协作者部门ID',
  `dept_name` varchar(64) DEFAULT NULL COMMENT '协作者部门名称',
  `permission` varchar(20) NOT NULL COMMENT '权限类型（owner:所有者 editor:编辑者 commenter:评论者 viewer:查看者）',
  `invite_by` bigint(20) NOT NULL COMMENT '邀请者ID',
  `invite_by_name` varchar(64) NOT NULL COMMENT '邀请者姓名',
  `invite_time` datetime NOT NULL COMMENT '邀请时间',
  `accept_time` datetime DEFAULT NULL COMMENT '接受邀请时间',
  `status` char(1) NOT NULL DEFAULT '0' COMMENT '状态（0待接受 1已接受 2已拒绝 3已移除）',
  `last_access_time` datetime DEFAULT NULL COMMENT '最后访问时间',
  `is_online` char(1) NOT NULL DEFAULT '0' COMMENT '是否在线（0离线 1在线）',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_spreadsheet_user` (`spreadsheet_id`,`user_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_permission` (`permission`),
  KEY `idx_status` (`status`),
  KEY `idx_is_online` (`is_online`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表格协作者表';

-- 3. 表格操作记录表
CREATE TABLE `collaboration_spreadsheet_operation` (
  `id` varchar(64) NOT NULL COMMENT '操作记录ID',
  `spreadsheet_id` varchar(64) NOT NULL COMMENT '表格ID',
  `user_id` bigint(20) NOT NULL COMMENT '操作用户ID',
  `user_name` varchar(64) NOT NULL COMMENT '操作用户名',
  `nick_name` varchar(64) DEFAULT NULL COMMENT '操作用户昵称',
  `operation_type` varchar(20) NOT NULL COMMENT '操作类型（create:创建 edit:编辑 delete:删除 share:分享 invite:邀请 comment:评论）',
  `operation_desc` varchar(200) NOT NULL COMMENT '操作描述',
  `operation_detail` text COMMENT '操作详情（JSON格式存储具体操作内容）',
  `operation_position` varchar(50) DEFAULT NULL COMMENT '操作位置（如单元格位置）',
  `before_data` text COMMENT '操作前数据',
  `after_data` text COMMENT '操作后数据',
  `operation_time` datetime NOT NULL COMMENT '操作时间',
  `client_ip` varchar(50) DEFAULT NULL COMMENT '客户端IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `session_id` varchar(64) DEFAULT NULL COMMENT '会话ID',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_spreadsheet_id` (`spreadsheet_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operation_time` (`operation_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表格操作记录表';

-- 4. 插入权限菜单数据
INSERT INTO `sys_menu` VALUES 
(2100, '协作办公', 0, 6, 'collaboration', NULL, '', 1, 0, 'M', '0', '0', '', 'table', 'admin', '2024-01-01 00:00:00', '', NULL, '协作办公目录'),
(2101, '在线表格', 2100, 1, 'spreadsheet', 'collaboration/spreadsheet/index', '', 1, 0, 'C', '0', '0', 'collaboration:spreadsheet:list', 'table', 'admin', '2024-01-01 00:00:00', '', NULL, '在线表格菜单'),
(2102, '表格查询', 2101, 1, '', '', '', 1, 0, 'F', '0', '0', 'collaboration:spreadsheet:query', '#', 'admin', '2024-01-01 00:00:00', '', NULL, ''),
(2103, '表格新增', 2101, 2, '', '', '', 1, 0, 'F', '0', '0', 'collaboration:spreadsheet:add', '#', 'admin', '2024-01-01 00:00:00', '', NULL, ''),
(2104, '表格修改', 2101, 3, '', '', '', 1, 0, 'F', '0', '0', 'collaboration:spreadsheet:edit', '#', 'admin', '2024-01-01 00:00:00', '', NULL, ''),
(2105, '表格删除', 2101, 4, '', '', '', 1, 0, 'F', '0', '0', 'collaboration:spreadsheet:remove', '#', 'admin', '2024-01-01 00:00:00', '', NULL, ''),
(2106, '邀请协作', 2101, 5, '', '', '', 1, 0, 'F', '0', '0', 'collaboration:spreadsheet:invite', '#', 'admin', '2024-01-01 00:00:00', '', NULL, ''),
(2107, '协作管理', 2101, 6, '', '', '', 1, 0, 'F', '0', '0', 'collaboration:spreadsheet:manage', '#', 'admin', '2024-01-01 00:00:00', '', NULL, ''),
(2108, '分享表格', 2101, 7, '', '', '', 1, 0, 'F', '0', '0', 'collaboration:spreadsheet:share', '#', 'admin', '2024-01-01 00:00:00', '', NULL, '');

-- 5. 插入角色权限关联数据（管理员角色）
INSERT INTO `sys_role_menu` SELECT 1, menu_id FROM `sys_menu` WHERE menu_id BETWEEN 2100 AND 2108;
