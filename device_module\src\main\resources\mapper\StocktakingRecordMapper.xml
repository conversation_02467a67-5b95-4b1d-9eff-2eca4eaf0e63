<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.asset_stocktaking.mapper.StocktakingRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="StocktakingRecordVoResult" type="com.jingfang.asset_stocktaking.module.vo.StocktakingRecordVo">
        <id property="recordId" column="record_id"/>
        <result property="taskId" column="task_id"/>
        <result property="taskName" column="task_name"/>
        <result property="assetId" column="asset_id"/>
        <result property="assetCode" column="asset_code"/>
        <result property="assetName" column="asset_name"/>
        <result property="categoryName" column="category_name"/>
        <result property="foundStatus" column="found_status"/>
        <result property="foundStatusDesc" column="found_status_desc"/>
        <result property="actualLocation" column="actual_location"/>
        <result property="bookLocation" column="book_location"/>
        <result property="actualStatus" column="actual_status"/>
        <result property="actualStatusDesc" column="actual_status_desc"/>
        <result property="bookStatus" column="book_status"/>
        <result property="bookStatusDesc" column="book_status_desc"/>
        <result property="inventoryUserId" column="inventory_user_id"/>
        <result property="inventoryUserName" column="inventory_user_name"/>
        <result property="inventoryTime" column="inventory_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <!-- 分页查询盘点记录列表 -->
    <select id="selectRecordList" resultMap="StocktakingRecordVoResult">
        SELECT 
            r.record_id,
            r.task_id,
            t.task_name,
            r.asset_id,
            r.asset_code,
            a.asset_name,
            c.category_name,
            r.found_status,
            CASE r.found_status 
                WHEN 1 THEN '找到'
                WHEN 0 THEN '未找到'
                ELSE '未知'
            END as found_status_desc,
            r.actual_location,
            COALESCE(l.location_name, a.detail_location) as book_location,
            r.actual_status,
            CASE r.actual_status
                WHEN 1 THEN '正常'
                WHEN 2 THEN '维修中'
                WHEN 3 THEN '报废'
                WHEN 4 THEN '闲置'
                WHEN 5 THEN '借出'
                ELSE '未知'
            END as actual_status_desc,
            a.asset_status as book_status,
            CASE a.asset_status
                WHEN 1 THEN '正常'
                WHEN 2 THEN '维修中'
                WHEN 3 THEN '报废'
                WHEN 4 THEN '闲置'
                WHEN 5 THEN '借出'
                ELSE '未知'
            END as book_status_desc,
            r.inventory_user_id,
            u.nick_name as inventory_user_name,
            r.inventory_time,
            r.remark
        FROM asset_stocktaking_record r
        LEFT JOIN asset_stocktaking_task t ON r.task_id = t.task_id
        LEFT JOIN asset_ledger a ON r.asset_id = a.asset_id
        LEFT JOIN sys_dict_data c ON a.category_id = c.dict_value AND c.dict_type = 'asset_category'
        LEFT JOIN sys_dict_data l ON a.storage_location = l.dict_value AND l.dict_type = 'storage_location'
        LEFT JOIN sys_user u ON r.inventory_user_id = u.user_id
        <where>
            <if test="request.taskId != null and request.taskId != ''">
                AND r.task_id = #{request.taskId}
            </if>
            <if test="request.planId != null and request.planId != ''">
                AND t.plan_id = #{request.planId}
            </if>
            <if test="request.assetId != null and request.assetId != ''">
                AND r.asset_id = #{request.assetId}
            </if>
            <if test="request.assetCode != null and request.assetCode != ''">
                AND r.asset_code LIKE CONCAT('%', #{request.assetCode}, '%')
            </if>
            <if test="request.assetName != null and request.assetName != ''">
                AND a.asset_name LIKE CONCAT('%', #{request.assetName}, '%')
            </if>
            <if test="request.foundStatus != null">
                AND r.found_status = #{request.foundStatus}
            </if>
            <if test="request.actualStatus != null">
                AND r.actual_status = #{request.actualStatus}
            </if>
            <if test="request.inventoryUserId != null">
                AND r.inventory_user_id = #{request.inventoryUserId}
            </if>
            <if test="request.inventoryTimeBegin != null">
                AND r.inventory_time >= #{request.inventoryTimeBegin}
            </if>
            <if test="request.inventoryTimeEnd != null">
                AND r.inventory_time &lt;= #{request.inventoryTimeEnd}
            </if>
            <if test="request.categoryIds != null and request.categoryIds.size() > 0">
                AND a.category_id IN
                <foreach collection="request.categoryIds" item="categoryId" open="(" separator="," close=")">
                    #{categoryId}
                </foreach>
            </if>
            <if test="request.deptIds != null and request.deptIds.size() > 0">
                AND a.dept_id IN
                <foreach collection="request.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="request.locationIds != null and request.locationIds.size() > 0">
                AND a.storage_location IN
                <foreach collection="request.locationIds" item="locationId" open="(" separator="," close=")">
                    #{locationId}
                </foreach>
            </if>
            <if test="request.onlyAbnormal != null and request.onlyAbnormal == true">
                AND (r.found_status = 0 OR r.actual_status = 3)
            </if>
            <if test="request.onlyDifference != null and request.onlyDifference == true">
                AND (r.found_status = 0 OR r.actual_status != a.asset_status OR r.actual_location != COALESCE(l.location_name, a.detail_location))
            </if>
            <if test="request.onlyMyRecords != null and request.onlyMyRecords == true">
                AND r.inventory_user_id = #{request.inventoryUserId}
            </if>
            <if test="request.assetValueMin != null">
                AND a.asset_value >= #{request.assetValueMin}
            </if>
            <if test="request.assetValueMax != null">
                AND a.asset_value &lt;= #{request.assetValueMax}
            </if>
            <if test="request.keyword != null and request.keyword != ''">
                AND (r.asset_code LIKE CONCAT('%', #{request.keyword}, '%')
                     OR a.asset_name LIKE CONCAT('%', #{request.keyword}, '%')
                     OR r.actual_location LIKE CONCAT('%', #{request.keyword}, '%'))
            </if>
        </where>
        <choose>
            <when test="request.orderBy != null and request.orderBy != ''">
                ORDER BY ${request.orderBy}
                <if test="request.orderDirection != null and request.orderDirection != ''">
                    ${request.orderDirection}
                </if>
            </when>
            <otherwise>
                ORDER BY r.inventory_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 根据ID查询盘点记录详情 -->
    <select id="selectRecordById" resultMap="StocktakingRecordVoResult">
        SELECT 
            r.record_id,
            r.task_id,
            t.task_name,
            r.asset_id,
            r.asset_code,
            a.asset_name,
            c.category_name,
            r.found_status,
            CASE r.found_status 
                WHEN 1 THEN '找到'
                WHEN 0 THEN '未找到'
                ELSE '未知'
            END as found_status_desc,
            r.actual_location,
            COALESCE(l.location_name, a.detail_location) as book_location,
            r.actual_status,
            CASE r.actual_status
                WHEN 1 THEN '正常'
                WHEN 2 THEN '维修中'
                WHEN 3 THEN '报废'
                WHEN 4 THEN '闲置'
                WHEN 5 THEN '借出'
                ELSE '未知'
            END as actual_status_desc,
            a.asset_status as book_status,
            CASE a.asset_status
                WHEN 1 THEN '正常'
                WHEN 2 THEN '维修中'
                WHEN 3 THEN '报废'
                WHEN 4 THEN '闲置'
                WHEN 5 THEN '借出'
                ELSE '未知'
            END as book_status_desc,
            r.inventory_user_id,
            u.nick_name as inventory_user_name,
            r.inventory_time,
            r.remark
        FROM asset_stocktaking_record r
        LEFT JOIN asset_stocktaking_task t ON r.task_id = t.task_id
        LEFT JOIN asset_ledger a ON r.asset_id = a.asset_id
        LEFT JOIN sys_dict_data c ON a.category_id = c.dict_value AND c.dict_type = 'asset_category'
        LEFT JOIN sys_dict_data l ON a.storage_location = l.dict_value AND l.dict_type = 'storage_location'
        LEFT JOIN sys_user u ON r.inventory_user_id = u.user_id
        WHERE r.record_id = #{recordId}
    </select>

    <!-- 根据任务ID查询记录列表 -->
    <select id="selectRecordByTaskId" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingRecord">
        SELECT * FROM asset_stocktaking_record WHERE task_id = #{taskId}
    </select>

    <!-- 根据资产ID查询记录列表 -->
    <select id="selectRecordByAssetId" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingRecord">
        SELECT * FROM asset_stocktaking_record WHERE asset_id = #{assetId}
    </select>

    <!-- 根据盘点人查询记录列表 -->
    <select id="selectRecordByInventoryUser" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingRecord">
        SELECT * FROM asset_stocktaking_record WHERE inventory_user_id = #{inventoryUserId}
    </select>

    <!-- 查询异常记录 -->
    <select id="selectAbnormalRecords" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingRecord">
        SELECT * FROM asset_stocktaking_record 
        WHERE task_id = #{taskId} AND (found_status = 0 OR actual_status = 3)
    </select>

    <!-- 查询差异记录 -->
    <select id="selectDifferenceRecords" resultMap="StocktakingRecordVoResult">
        SELECT 
            r.record_id,
            r.task_id,
            t.task_name,
            r.asset_id,
            r.asset_code,
            a.asset_name,
            r.found_status,
            r.actual_location,
            COALESCE(l.location_name, a.detail_location) as book_location,
            r.actual_status,
            a.asset_status as book_status,
            r.inventory_user_id,
            u.nick_name as inventory_user_name,
            r.inventory_time,
            r.remark
        FROM asset_stocktaking_record r
        LEFT JOIN asset_stocktaking_task t ON r.task_id = t.task_id
        LEFT JOIN asset_ledger a ON r.asset_id = a.asset_id
        LEFT JOIN sys_dict_data l ON a.storage_location = l.dict_value AND l.dict_type = 'storage_location'
        LEFT JOIN sys_user u ON r.inventory_user_id = u.user_id
        WHERE r.task_id = #{taskId} 
        AND (r.found_status = 0 
             OR r.actual_status != a.asset_status 
             OR r.actual_location != COALESCE(l.location_name, a.detail_location))
    </select>

    <!-- 统计任务的记录情况 -->
    <select id="countRecordsByTask" resultType="java.util.Map">
        SELECT 
            COUNT(*) as totalRecords,
            SUM(CASE WHEN found_status = 1 THEN 1 ELSE 0 END) as foundRecords,
            SUM(CASE WHEN found_status = 0 THEN 1 ELSE 0 END) as notFoundRecords,
            SUM(CASE WHEN found_status = 0 OR actual_status = 3 THEN 1 ELSE 0 END) as abnormalRecords
        FROM asset_stocktaking_record 
        WHERE task_id = #{taskId}
    </select>

    <!-- 统计计划的记录情况 -->
    <select id="countRecordsByPlan" resultType="java.util.Map">
        SELECT 
            COUNT(r.*) as totalRecords,
            SUM(CASE WHEN r.found_status = 1 THEN 1 ELSE 0 END) as foundRecords,
            SUM(CASE WHEN r.found_status = 0 THEN 1 ELSE 0 END) as notFoundRecords,
            SUM(CASE WHEN r.found_status = 0 OR r.actual_status = 3 THEN 1 ELSE 0 END) as abnormalRecords
        FROM asset_stocktaking_record r
        INNER JOIN asset_stocktaking_task t ON r.task_id = t.task_id
        WHERE t.plan_id = #{planId}
    </select>

    <!-- 查询重复盘点的资产 -->
    <select id="selectDuplicateRecords" resultType="java.util.Map">
        SELECT 
            r.asset_id,
            r.asset_code,
            a.asset_name,
            COUNT(*) as recordCount
        FROM asset_stocktaking_record r
        INNER JOIN asset_stocktaking_task t ON r.task_id = t.task_id
        LEFT JOIN asset_ledger a ON r.asset_id = a.asset_id
        WHERE t.plan_id = #{planId}
        GROUP BY r.asset_id, r.asset_code, a.asset_name
        HAVING COUNT(*) > 1
    </select>

    <!-- 查询未盘点的资产 -->
    <select id="selectMissingAssets" resultType="java.util.Map">
        SELECT 
            a.asset_id,
            a.asset_code,
            a.asset_name
        FROM asset_ledger a
        WHERE a.asset_id IN (
            SELECT DISTINCT asset_id FROM asset_stocktaking_task 
            WHERE task_id = #{taskId}
        )
        AND a.asset_id NOT IN (
            SELECT DISTINCT asset_id FROM asset_stocktaking_record 
            WHERE task_id = #{taskId}
        )
    </select>

    <!-- 批量插入盘点记录 -->
    <insert id="batchInsertRecords">
        INSERT INTO asset_stocktaking_record (
            record_id, task_id, asset_id, asset_code, found_status, 
            actual_location, actual_status, inventory_user_id, inventory_time, remark
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (#{record.recordId}, #{record.taskId}, #{record.assetId}, #{record.assetCode}, 
             #{record.foundStatus}, #{record.actualLocation}, #{record.actualStatus}, 
             #{record.inventoryUserId}, #{record.inventoryTime}, #{record.remark})
        </foreach>
    </insert>

    <!-- 批量更新记录状态 -->
    <update id="batchUpdateFoundStatus">
        UPDATE asset_stocktaking_record 
        SET found_status = #{foundStatus}
        WHERE record_id IN
        <foreach collection="recordIds" item="recordId" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </update>

    <!-- 根据资产编码查询记录 -->
    <select id="selectRecordByAssetCode" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingRecord">
        SELECT * FROM asset_stocktaking_record 
        WHERE asset_code = #{assetCode} AND task_id = #{taskId}
    </select>

    <!-- 检查资产是否已盘点 -->
    <select id="checkAssetInventoried" resultType="boolean">
        SELECT COUNT(*) > 0 FROM asset_stocktaking_record 
        WHERE asset_id = #{assetId} AND task_id = #{taskId}
    </select>

    <!-- 查询任务的盘点进度 -->
    <select id="selectTaskInventoryProgress" resultType="java.util.Map">
        SELECT 
            t.expected_count as expectedCount,
            COUNT(r.record_id) as actualCount,
            CASE 
                WHEN t.expected_count = 0 THEN 0
                ELSE ROUND(COUNT(r.record_id) * 100.0 / t.expected_count, 2)
            END as progressPercentage
        FROM asset_stocktaking_task t
        LEFT JOIN asset_stocktaking_record r ON t.task_id = r.task_id
        WHERE t.task_id = #{taskId}
        GROUP BY t.task_id, t.expected_count
    </select>

    <!-- 删除任务的所有记录 -->
    <delete id="deleteRecordsByTaskId">
        DELETE FROM asset_stocktaking_record WHERE task_id = #{taskId}
    </delete>

</mapper>
