package com.jingfang.web.controller.asset.stocktaking;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.asset_stocktaking.module.dto.StocktakingTaskDto;
import com.jingfang.asset_stocktaking.module.entity.AssetStocktakingTask;
import com.jingfang.asset_stocktaking.module.request.TaskSearchRequest;
import com.jingfang.asset_stocktaking.module.vo.StocktakingTaskVo;
import com.jingfang.asset_stocktaking.service.StocktakingTaskService;
import com.jingfang.common.annotation.Log;
import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.common.core.page.TableDataInfo;
import com.jingfang.common.enums.BusinessType;
import com.jingfang.common.utils.SecurityUtils;
import com.jingfang.common.utils.poi.ExcelUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 盘点任务控制器
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@RestController
@RequestMapping("/asset/stocktaking/task")
public class StocktakingTaskController extends BaseController {

    @Resource
    private StocktakingTaskService taskService;

    /**
     * 查询盘点任务列表
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:task:view')")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody TaskSearchRequest request) {
        startPage();
        IPage<StocktakingTaskVo> page = taskService.selectTaskList(request);
        return getDataTable(page.getRecords());
    }

    /**
     * 导出盘点任务列表
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:task:export')")
    @Log(title = "盘点任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody TaskSearchRequest request) {
        request.setPageNum(1);
        request.setPageSize(10000);
        IPage<StocktakingTaskVo> page = taskService.selectTaskList(request);
        ExcelUtil<StocktakingTaskVo> util = new ExcelUtil<>(StocktakingTaskVo.class);
        util.exportExcel(response, page.getRecords(), "盘点任务数据");
    }

    /**
     * 获取盘点任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:task:view')")
    @GetMapping("/{taskId}")
    public AjaxResult getInfo(@PathVariable("taskId") String taskId) {
        StocktakingTaskVo taskVo = taskService.selectTaskById(taskId);
        return success(taskVo);
    }

    /**
     * 新增盘点任务
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:task:add')")
    @Log(title = "盘点任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody StocktakingTaskDto taskDto) {
        if (taskService.createTask(taskDto)) {
            return success("创建盘点任务成功");
        }
        return error("创建盘点任务失败");
    }

    /**
     * 修改盘点任务
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:task:edit')")
    @Log(title = "盘点任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody StocktakingTaskDto taskDto) {
        if (taskService.editTask(taskDto)) {
            return success("修改盘点任务成功");
        }
        return error("修改盘点任务失败");
    }

    /**
     * 删除盘点任务
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:task:remove')")
    @Log(title = "盘点任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{taskIds}")
    public AjaxResult remove(@PathVariable String[] taskIds) {
        if (taskService.batchDeleteTasks(Arrays.asList(taskIds))) {
            return success("删除盘点任务成功");
        }
        return error("删除盘点任务失败");
    }

    /**
     * 分发盘点任务
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:task:distribute')")
    @Log(title = "盘点任务", businessType = BusinessType.INSERT)
    @PostMapping("/distribute/{planId}")
    public AjaxResult distribute(@PathVariable("planId") String planId) {
        if (taskService.distributeTasksByPlan(planId)) {
            return success("分发盘点任务成功");
        }
        return error("分发盘点任务失败");
    }

    /**
     * 领取盘点任务
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:task:claim')")
    @Log(title = "盘点任务", businessType = BusinessType.UPDATE)
    @PostMapping("/{taskId}/claim")
    public AjaxResult claim(@PathVariable("taskId") String taskId) {
        Long userId = SecurityUtils.getUserId();
        if (taskService.claimTask(taskId, userId)) {
            return success("领取盘点任务成功");
        }
        return error("领取盘点任务失败");
    }

    /**
     * 开始执行任务
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:task:execute')")
    @Log(title = "盘点任务", businessType = BusinessType.UPDATE)
    @PostMapping("/{taskId}/start")
    public AjaxResult start(@PathVariable("taskId") String taskId) {
        if (taskService.startTask(taskId)) {
            return success("开始执行任务成功");
        }
        return error("开始执行任务失败");
    }

    /**
     * 完成任务
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:task:execute')")
    @Log(title = "盘点任务", businessType = BusinessType.UPDATE)
    @PostMapping("/{taskId}/complete")
    public AjaxResult complete(@PathVariable("taskId") String taskId) {
        if (taskService.completeTask(taskId)) {
            return success("完成任务成功");
        }
        return error("完成任务失败");
    }

    /**
     * 重新分配任务
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:task:reassign')")
    @Log(title = "盘点任务", businessType = BusinessType.UPDATE)
    @PostMapping("/{taskId}/reassign")
    public AjaxResult reassign(@PathVariable("taskId") String taskId, 
                              @RequestParam Long newAssignedUserId) {
        if (taskService.reassignTask(taskId, newAssignedUserId)) {
            return success("重新分配任务成功");
        }
        return error("重新分配任务失败");
    }

    /**
     * 查询任务进度
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:task:view')")
    @GetMapping("/{taskId}/progress")
    public AjaxResult getProgress(@PathVariable("taskId") String taskId) {
        StocktakingTaskVo.TaskProgress progress = taskService.calculateProgress(taskId);
        return success(progress);
    }

    /**
     * 查询任务统计信息
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:task:view')")
    @GetMapping("/{taskId}/statistics")
    public AjaxResult getStatistics(@PathVariable("taskId") String taskId) {
        StocktakingTaskVo.TaskStatistics statistics = taskService.getTaskStatistics(taskId);
        return success(statistics);
    }

    /**
     * 根据计划ID查询任务列表
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:task:view')")
    @GetMapping("/plan/{planId}")
    public AjaxResult getTasksByPlan(@PathVariable("planId") String planId) {
        List<AssetStocktakingTask> tasks = taskService.selectTaskByPlanId(planId);
        return success(tasks);
    }

    /**
     * 查询用户的待执行任务
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:task:view')")
    @GetMapping("/user/pending")
    public AjaxResult getPendingTasks() {
        Long userId = SecurityUtils.getUserId();
        List<AssetStocktakingTask> tasks = taskService.selectPendingTasksByUser(userId);
        return success(tasks);
    }

    /**
     * 查询用户的进行中任务
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:task:view')")
    @GetMapping("/user/inprogress")
    public AjaxResult getInProgressTasks() {
        Long userId = SecurityUtils.getUserId();
        List<AssetStocktakingTask> tasks = taskService.selectInProgressTasksByUser(userId);
        return success(tasks);
    }

    /**
     * 查询逾期任务
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:task:view')")
    @GetMapping("/overdue")
    public AjaxResult getOverdueTasks() {
        List<AssetStocktakingTask> tasks = taskService.selectOverdueTasks();
        return success(tasks);
    }

    /**
     * 统计计划下各状态的任务数量
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:task:view')")
    @GetMapping("/statistics/status/{planId}")
    public AjaxResult getStatusStatistics(@PathVariable("planId") String planId) {
        List<java.util.Map<String, Object>> statistics = taskService.countTaskByStatusInPlan(planId);
        return success(statistics);
    }

    /**
     * 查询计划的任务完成情况
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:task:view')")
    @GetMapping("/completion/{planId}")
    public AjaxResult getPlanTaskCompletion(@PathVariable("planId") String planId) {
        java.util.Map<String, Object> completion = taskService.selectPlanTaskCompletion(planId);
        return success(completion);
    }

    /**
     * 批量更新任务状态
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:task:edit')")
    @Log(title = "盘点任务", businessType = BusinessType.UPDATE)
    @PostMapping("/batch/status")
    public AjaxResult batchUpdateStatus(@RequestParam String[] taskIds, 
                                       @RequestParam Integer status) {
        if (taskService.batchUpdateTaskStatus(Arrays.asList(taskIds), status)) {
            return success("批量更新任务状态成功");
        }
        return error("批量更新任务状态失败");
    }

    /**
     * 更新任务进度
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:task:execute')")
    @Log(title = "盘点任务", businessType = BusinessType.UPDATE)
    @PostMapping("/{taskId}/progress")
    public AjaxResult updateProgress(@PathVariable("taskId") String taskId, 
                                    @RequestParam Integer actualCount) {
        if (taskService.updateTaskProgress(taskId, actualCount)) {
            return success("更新任务进度成功");
        }
        return error("更新任务进度失败");
    }
}
