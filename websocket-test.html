<!DOCTYPE html>
<html>
<head>
    <title>WebSocket连接测试</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .status { font-weight: bold; }
        .connected { color: green; }
        .disconnected { color: red; }
        .logs { height: 200px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; background: #f5f5f5; margin: 10px 0; }
        button { margin: 5px; padding: 8px 15px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>WebSocket连接测试</h1>
    
    <!-- 基础WebSocket测试 -->
    <div class="test-section">
        <h3>1. 基础WebSocket测试 (/websocket/message)</h3>
        <p>状态: <span id="basicStatus" class="status disconnected">未连接</span></p>
        <button onclick="connectBasic()">连接</button>
        <button onclick="disconnectBasic()">断开</button>
        <button onclick="sendBasicMessage()">发送消息</button>
        <div id="basicLogs" class="logs"></div>
    </div>

    <!-- 协作表格WebSocket测试 -->
    <div class="test-section">
        <h3>2. 协作表格WebSocket测试 (/websocket/spreadsheet/test-001)</h3>
        <p>状态: <span id="spreadsheetStatus" class="status disconnected">未连接</span></p>
        <button onclick="connectSpreadsheet()">连接</button>
        <button onclick="disconnectSpreadsheet()">断开</button>
        <button onclick="sendSpreadsheetMessage()">发送单元格更新</button>
        <button onclick="sendHeartbeat()">发送心跳</button>
        <div id="spreadsheetLogs" class="logs"></div>
    </div>

    <!-- 清空日志 -->
    <div class="test-section">
        <button onclick="clearAllLogs()">清空所有日志</button>
    </div>

    <script>
        let basicWs = null;
        let spreadsheetWs = null;

        // 基础WebSocket连接
        function connectBasic() {
            const url = 'ws://localhost:8080/websocket/message';
            logBasic('info', `尝试连接: ${url}`);
            
            try {
                basicWs = new WebSocket(url);
                
                basicWs.onopen = function() {
                    document.getElementById('basicStatus').textContent = '已连接';
                    document.getElementById('basicStatus').className = 'status connected';
                    logBasic('success', '✅ 基础WebSocket连接成功！');
                };
                
                basicWs.onmessage = function(event) {
                    logBasic('info', `📨 收到消息: ${event.data}`);
                };
                
                basicWs.onclose = function(event) {
                    document.getElementById('basicStatus').textContent = '未连接';
                    document.getElementById('basicStatus').className = 'status disconnected';
                    logBasic('error', `❌ 连接关闭: 代码=${event.code}, 原因=${event.reason || '无原因'}`);
                };
                
                basicWs.onerror = function(error) {
                    logBasic('error', `💥 连接错误: ${JSON.stringify(error)}`);
                };
                
            } catch (error) {
                logBasic('error', `💥 创建WebSocket失败: ${error.message}`);
            }
        }

        function disconnectBasic() {
            if (basicWs) {
                basicWs.close();
                basicWs = null;
            }
        }

        function sendBasicMessage() {
            if (basicWs && basicWs.readyState === WebSocket.OPEN) {
                const message = '你好，这是测试消息！';
                basicWs.send(message);
                logBasic('info', `📤 发送消息: ${message}`);
            } else {
                logBasic('error', '❌ WebSocket未连接');
            }
        }

        // 协作表格WebSocket连接
        function connectSpreadsheet() {
            const url = 'ws://localhost:8080/websocket/spreadsheet/test-001';
            logSpreadsheet('info', `尝试连接: ${url}`);
            
            try {
                spreadsheetWs = new WebSocket(url);
                
                spreadsheetWs.onopen = function() {
                    document.getElementById('spreadsheetStatus').textContent = '已连接';
                    document.getElementById('spreadsheetStatus').className = 'status connected';
                    logSpreadsheet('success', '✅ 协作表格WebSocket连接成功！');
                };
                
                spreadsheetWs.onmessage = function(event) {
                    logSpreadsheet('info', `📨 收到消息: ${event.data}`);
                };
                
                spreadsheetWs.onclose = function(event) {
                    document.getElementById('spreadsheetStatus').textContent = '未连接';
                    document.getElementById('spreadsheetStatus').className = 'status disconnected';
                    logSpreadsheet('error', `❌ 连接关闭: 代码=${event.code}, 原因=${event.reason || '无原因'}`);
                };
                
                spreadsheetWs.onerror = function(error) {
                    logSpreadsheet('error', `💥 连接错误: ${JSON.stringify(error)}`);
                };
                
            } catch (error) {
                logSpreadsheet('error', `💥 创建WebSocket失败: ${error.message}`);
            }
        }

        function disconnectSpreadsheet() {
            if (spreadsheetWs) {
                spreadsheetWs.close();
                spreadsheetWs = null;
            }
        }

        function sendSpreadsheetMessage() {
            if (spreadsheetWs && spreadsheetWs.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'cell_update',
                    data: {
                        row: 1,
                        col: 1,
                        value: 'Hello WebSocket!',
                        timestamp: Date.now()
                    }
                };
                spreadsheetWs.send(JSON.stringify(message));
                logSpreadsheet('info', `📤 发送单元格更新: ${JSON.stringify(message)}`);
            } else {
                logSpreadsheet('error', '❌ WebSocket未连接');
            }
        }

        function sendHeartbeat() {
            if (spreadsheetWs && spreadsheetWs.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'heartbeat',
                    timestamp: Date.now()
                };
                spreadsheetWs.send(JSON.stringify(message));
                logSpreadsheet('info', `📤 发送心跳: ${JSON.stringify(message)}`);
            } else {
                logSpreadsheet('error', '❌ WebSocket未连接');
            }
        }

        // 日志函数
        function logBasic(type, message) {
            const logs = document.getElementById('basicLogs');
            const time = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `${time} - ${message}`;
            logs.appendChild(div);
            logs.scrollTop = logs.scrollHeight;
        }

        function logSpreadsheet(type, message) {
            const logs = document.getElementById('spreadsheetLogs');
            const time = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `${time} - ${message}`;
            logs.appendChild(div);
            logs.scrollTop = logs.scrollHeight;
        }

        function clearAllLogs() {
            document.getElementById('basicLogs').innerHTML = '';
            document.getElementById('spreadsheetLogs').innerHTML = '';
        }

        // 页面加载时的提示
        window.onload = function() {
            logBasic('info', '页面加载完成，可以开始测试WebSocket连接');
            logSpreadsheet('info', '页面加载完成，可以开始测试WebSocket连接');
        };
    </script>
</body>
</html>
