<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.maintenance_plan.mapper.MaintenancePlanPartMapper">

    <resultMap id="MaintenancePlanPartVoMap" type="com.jingfang.maintenance_plan.module.vo.MaintenancePlanVo$MaintenancePlanPartVo">
        <id property="relationId" column="relation_id"/>
        <result property="partId" column="part_id"/>
        <result property="partName" column="part_name"/>
        <result property="partCode" column="part_code"/>
        <result property="specModel" column="spec_model"/>
        <result property="unit" column="unit"/>
        <result property="requiredQuantity" column="required_quantity"/>
        <result property="currentStock" column="current_stock"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <!-- 根据维护计划ID查询关联的备品备件 -->
    <select id="selectPartsByPlanId" resultMap="MaintenancePlanPartVoMap">
        SELECT 
            mpp.relation_id,
            mpp.part_id,
            ibi.item_name as part_name,
            ibi.item_code as part_code,
            ibi.spec_model,
            ibi.unit,
            mpp.required_quantity,
            ibi.total_stock as current_stock,
            mpp.remark
        FROM maintenance_plan_part mpp
        LEFT JOIN item_base_info ibi ON mpp.part_id = ibi.item_id
        WHERE mpp.deleted = 0 
        AND mpp.plan_id = #{planId}
        ORDER BY mpp.create_time ASC
    </select>

    <!-- 根据备品备件ID查询关联的维护计划 -->
    <select id="selectPlanIdsByPartId" resultType="java.lang.String">
        SELECT plan_id
        FROM maintenance_plan_part
        WHERE deleted = 0 AND part_id = #{partId}
    </select>

    <!-- 批量删除维护计划的备品备件关联 -->
    <update id="deleteByPlanId">
        UPDATE maintenance_plan_part 
        SET deleted = 1, update_by = #{updateBy}, update_time = NOW()
        WHERE plan_id = #{planId} AND deleted = 0
    </update>

    <!-- 检查维护计划和备品备件是否已关联 -->
    <select id="checkRelationExists" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM maintenance_plan_part
        WHERE deleted = 0 
        AND plan_id = #{planId} 
        AND part_id = #{partId}
    </select>

</mapper> 