package com.jingfang.asset_stocktaking.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingfang.asset_stocktaking.mapper.StocktakingTaskMapper;
import com.jingfang.asset_stocktaking.module.dto.StocktakingPlanDto;
import com.jingfang.asset_stocktaking.module.dto.StocktakingTaskDto;
import com.jingfang.asset_stocktaking.module.entity.AssetStocktakingPlan;
import com.jingfang.asset_stocktaking.module.entity.AssetStocktakingTask;
import com.jingfang.asset_stocktaking.module.request.TaskSearchRequest;
import com.jingfang.asset_stocktaking.module.vo.StocktakingTaskVo;
import com.jingfang.asset_stocktaking.service.StocktakingPlanService;
import com.jingfang.asset_stocktaking.service.StocktakingTaskService;
import com.jingfang.common.utils.SecurityUtils;
import com.jingfang.common.utils.StringUtils;
import com.jingfang.common.utils.uuid.IdUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 盘点任务服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Slf4j
@Service
public class StocktakingTaskServiceImpl extends ServiceImpl<StocktakingTaskMapper, AssetStocktakingTask> 
        implements StocktakingTaskService {

    @Resource
    private StocktakingTaskMapper taskMapper;

    @Resource
    @Lazy
    private StocktakingPlanService planService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean distributeTasksByPlan(String planId) {
        try {
            // 获取盘点计划
            AssetStocktakingPlan plan = planService.getById(planId);
            if (plan == null) {
                log.warn("盘点计划不存在: {}", planId);
                return false;
            }

            // 解析盘点范围
            StocktakingTaskDto.TaskDistributionConfig config = new StocktakingTaskDto.TaskDistributionConfig();
            config.setDistributionType(1); // 默认按部门分发
            config.setMaxAssetCount(50); // 默认每个任务最多50个资产
            config.setAutoAssign(true);

            // 根据盘点范围生成资产列表
            List<String> assetIds = planService.generateAssetListByScope(
                JSON.parseObject(plan.getPlanScope(), StocktakingPlanDto.class));

            if (assetIds == null || assetIds.isEmpty()) {
                log.warn("盘点计划没有找到可盘点的资产: {}", planId);
                return false;
            }

            // 生成任务
            List<AssetStocktakingTask> tasks = generateTasksByAssets(planId, assetIds, config);

            // 保存任务
            return saveBatch(tasks);
        } catch (Exception e) {
            log.error("分发盘点任务失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createTask(StocktakingTaskDto taskDto) {
        try {
            // 验证数据
            if (!validateTaskData(taskDto)) {
                return false;
            }

            // 创建任务实体
            AssetStocktakingTask task = new AssetStocktakingTask();
            BeanUtils.copyProperties(taskDto, task);
            
            task.setTaskId(IdUtils.fastSimpleUUID());
            task.setStatus(AssetStocktakingTask.Status.PENDING);
            task.setCreateBy(SecurityUtils.getUsername());
            task.setCreateTime(new Date());

            // 处理资产范围
            if (taskDto.getDistributionConfig() != null) {
                task.setAssetScope(JSON.toJSONString(taskDto.getDistributionConfig()));
            }

            return save(task);
        } catch (Exception e) {
            log.error("创建盘点任务失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean editTask(StocktakingTaskDto taskDto) {
        try {
            // 验证数据
            if (!validateTaskData(taskDto)) {
                return false;
            }

            // 检查任务是否存在
            AssetStocktakingTask existingTask = getById(taskDto.getTaskId());
            if (existingTask == null) {
                log.warn("盘点任务不存在: {}", taskDto.getTaskId());
                return false;
            }

            // 检查任务状态是否允许编辑
            if (existingTask.getStatus() != AssetStocktakingTask.Status.PENDING) {
                log.warn("盘点任务状态不允许编辑: {}", existingTask.getStatus());
                return false;
            }

            // 更新任务实体
            AssetStocktakingTask task = new AssetStocktakingTask();
            BeanUtils.copyProperties(taskDto, task);

            return updateById(task);
        } catch (Exception e) {
            log.error("编辑盘点任务失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTask(String taskId) {
        try {
            // 检查任务是否存在
            AssetStocktakingTask task = getById(taskId);
            if (task == null) {
                log.warn("盘点任务不存在: {}", taskId);
                return false;
            }

            // 检查任务状态是否允许删除
            if (task.getStatus() == AssetStocktakingTask.Status.IN_PROGRESS) {
                log.warn("执行中的盘点任务不允许删除: {}", taskId);
                return false;
            }

            return removeById(taskId);
        } catch (Exception e) {
            log.error("删除盘点任务失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteTasks(List<String> taskIds) {
        try {
            for (String taskId : taskIds) {
                if (!deleteTask(taskId)) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("批量删除盘点任务失败", e);
            return false;
        }
    }

    @Override
    public IPage<StocktakingTaskVo> selectTaskList(TaskSearchRequest request) {
        Page<StocktakingTaskVo> page = new Page<>(request.getPageNum(), request.getPageSize());
        return taskMapper.selectTaskList(page, request);
    }

    @Override
    public StocktakingTaskVo selectTaskById(String taskId) {
        StocktakingTaskVo taskVo = taskMapper.selectTaskById(taskId);
        if (taskVo != null) {
            // 查询进度信息
            StocktakingTaskVo.TaskProgress progress = taskMapper.selectTaskProgress(taskId);
            taskVo.setProgress(progress);
            
            // 查询统计信息
            StocktakingTaskVo.TaskStatistics statistics = taskMapper.selectTaskStatistics(taskId);
            taskVo.setStatistics(statistics);
        }
        return taskVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean claimTask(String taskId, Long userId) {
        try {
            AssetStocktakingTask task = getById(taskId);
            if (task == null || task.getStatus() != AssetStocktakingTask.Status.PENDING) {
                return false;
            }

            task.setAssignedUserId(userId);
            return updateById(task);
        } catch (Exception e) {
            log.error("领取盘点任务失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean startTask(String taskId) {
        try {
            return taskMapper.startTask(taskId) > 0;
        } catch (Exception e) {
            log.error("开始盘点任务失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean completeTask(String taskId) {
        try {
            return taskMapper.completeTask(taskId) > 0;
        } catch (Exception e) {
            log.error("完成盘点任务失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean reassignTask(String taskId, Long newAssignedUserId) {
        try {
            return taskMapper.reassignTask(taskId, newAssignedUserId) > 0;
        } catch (Exception e) {
            log.error("重新分配盘点任务失败", e);
            return false;
        }
    }

    @Override
    public StocktakingTaskVo.TaskProgress calculateProgress(String taskId) {
        return taskMapper.selectTaskProgress(taskId);
    }

    @Override
    public StocktakingTaskVo.TaskStatistics getTaskStatistics(String taskId) {
        return taskMapper.selectTaskStatistics(taskId);
    }

    @Override
    public List<AssetStocktakingTask> selectTaskByPlanId(String planId) {
        return taskMapper.selectTaskByPlanId(planId);
    }

    @Override
    public List<AssetStocktakingTask> selectPendingTasksByUser(Long userId) {
        return taskMapper.selectPendingTasksByUser(userId);
    }

    @Override
    public List<AssetStocktakingTask> selectInProgressTasksByUser(Long userId) {
        return taskMapper.selectInProgressTasksByUser(userId);
    }

    @Override
    public List<AssetStocktakingTask> selectOverdueTasks() {
        return taskMapper.selectOverdueTasks();
    }

    @Override
    public List<java.util.Map<String, Object>> countTaskByStatusInPlan(String planId) {
        return taskMapper.countTaskByStatusInPlan(planId);
    }

    @Override
    public java.util.Map<String, Object> selectPlanTaskCompletion(String planId) {
        return taskMapper.selectPlanTaskCompletion(planId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateTaskStatus(List<String> taskIds, Integer status) {
        try {
            return taskMapper.batchUpdateTaskStatus(taskIds, status) > 0;
        } catch (Exception e) {
            log.error("批量更新任务状态失败", e);
            return false;
        }
    }

    @Override
    public List<AssetStocktakingTask> generateTasksByAssets(String planId, List<String> assetIds, 
                                                           StocktakingTaskDto.TaskDistributionConfig config) {
        List<AssetStocktakingTask> tasks = new ArrayList<>();
        
        try {
            // 根据配置分组资产
            int maxAssetCount = config.getMaxAssetCount() != null ? config.getMaxAssetCount() : 50;
            List<List<String>> assetGroups = new ArrayList<>();
            
            for (int i = 0; i < assetIds.size(); i += maxAssetCount) {
                int endIndex = Math.min(i + maxAssetCount, assetIds.size());
                assetGroups.add(assetIds.subList(i, endIndex));
            }

            // 为每个分组创建任务
            for (int i = 0; i < assetGroups.size(); i++) {
                List<String> groupAssets = assetGroups.get(i);
                
                AssetStocktakingTask task = new AssetStocktakingTask();
                task.setTaskId(IdUtils.fastSimpleUUID());
                task.setPlanId(planId);
                task.setTaskName("盘点任务-" + (i + 1));
                task.setAssetScope(JSON.toJSONString(groupAssets));
                task.setExpectedCount(groupAssets.size());
                task.setActualCount(0);
                task.setStatus(AssetStocktakingTask.Status.PENDING);
                task.setCreateBy(SecurityUtils.getUsername());
                task.setCreateTime(new Date());

                // 如果配置了自动分配，则分配给负责人
                if (config.getAutoAssign() != null && config.getAutoAssign()) {
                    AssetStocktakingPlan plan = planService.getById(planId);
                    if (plan != null) {
                        task.setAssignedUserId(plan.getResponsibleUserId());
                    }
                }

                tasks.add(task);
            }
        } catch (Exception e) {
            log.error("生成盘点任务失败", e);
        }

        return tasks;
    }

    @Override
    public boolean validateTaskData(StocktakingTaskDto taskDto) {
        return !StringUtils.isEmpty(taskDto.getPlanId()) &&
               !StringUtils.isEmpty(taskDto.getTaskName()) &&
               taskDto.getAssignedUserId() != null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTaskProgress(String taskId, Integer actualCount) {
        try {
            return taskMapper.updateTaskProgress(taskId, actualCount) > 0;
        } catch (Exception e) {
            log.error("更新任务进度失败", e);
            return false;
        }
    }
}
