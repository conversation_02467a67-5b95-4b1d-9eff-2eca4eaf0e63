package com.jingfang.common.utils;

import jakarta.annotation.Resource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

@Component
public class BusinessCodeGenerator {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    // 日期格式化器
    private static final DateTimeFormatter YEAR_FORMATTER = DateTimeFormatter.ofPattern("yyyy");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("MMdd");

    // 默认序号格式
    private static final String DEFAULT_SEQUENCE_FORMAT = "%04d";

    /**
     * 生成业务编号
     * @param prefix 业务前缀，如 "RK"(入库)、"CK"(出库)等
     * @return 生成的业务编号
     */
    public String generateCode(String prefix) {
        LocalDate today = LocalDate.now();
        return generateCode(prefix, today, DEFAULT_SEQUENCE_FORMAT);
    }

    /**
     * 生成业务编号（指定日期）
     * @param prefix 业务前缀
     * @param date 指定日期
     * @return 生成的业务编号
     */
    public String generateCode(String prefix, LocalDate date) {
        return generateCode(prefix, date, DEFAULT_SEQUENCE_FORMAT);
    }

    /**
     * 生成业务编号（完全自定义）
     * @param prefix 业务前缀
     * @param date 指定日期
     * @param sequenceFormat 序号格式，如"%04d"表示4位数字，不足补0
     * @return 生成的业务编号
     */
    public String generateCode(String prefix, LocalDate date, String sequenceFormat) {
        String year = date.format(YEAR_FORMATTER);
        String dateStr = date.format(DATE_FORMATTER);

        // 构建Redis键，用于存储当天的序列号
        String redisKey = String.format("business_code:%s:%s-%s", prefix, year, dateStr);

        // 获取并递增序列号
        long sequence = getNextSequence(redisKey);

        // 格式化序列号
        String sequenceStr = String.format(sequenceFormat, sequence);

        // 拼接业务编号
        return String.format("%s-%s-%s-%s", prefix, year, dateStr, sequenceStr);
    }

    /**
     * 获取下一个序列号
     * @param key Redis键
     * @return 序列号
     */
    private long getNextSequence(String key) {
        RedisAtomicLong counter = new RedisAtomicLong(key, redisTemplate.getConnectionFactory());

        // 获取并递增
        long sequence = counter.incrementAndGet();

        // 如果是第一次，设置过期时间（48小时后过期，确保第二天的编号可以重新从1开始）
        if (sequence == 1) {
            counter.expire(48, TimeUnit.HOURS);
        }

        return sequence;
    }

}
