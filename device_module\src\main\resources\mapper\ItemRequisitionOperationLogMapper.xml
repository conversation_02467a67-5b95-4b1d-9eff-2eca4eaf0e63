<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.wh_item_requisition.mapper.ItemRequisitionOperationLogMapper">

    <!-- 根据领用单ID查询操作日志列表 -->
    <select id="selectByRequisitionId" resultType="com.jingfang.wh_item_requisition.module.entity.ItemRequisitionOperationLog">
        SELECT 
            log_id,
            requisition_id,
            operation_type,
            CASE operation_type 
                WHEN 1 THEN '创建' 
                WHEN 2 THEN '提交' 
                WHEN 3 THEN '确认' 
                WHEN 4 THEN '审核通过' 
                WHEN 5 THEN '审核退回' 
                WHEN 6 THEN '完成' 
                WHEN 7 THEN '删除' 
                ELSE '未知' 
            END as operation_type_name,
            operation_content,
            operation_time,
            operator_id,
            operator_name
        FROM item_requisition_operation_log 
        WHERE requisition_id = #{requisitionId}
        ORDER BY operation_time DESC
    </select>

</mapper> 