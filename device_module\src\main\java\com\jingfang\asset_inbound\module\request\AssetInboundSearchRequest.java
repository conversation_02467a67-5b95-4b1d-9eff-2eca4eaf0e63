package com.jingfang.asset_inbound.module.request;

import com.jingfang.common.request.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class AssetInboundSearchRequest extends PageRequest implements Serializable {


    /**
     * 单据状态(0:草稿,1:待审核,2:已审核,3:已完成,9:已取消)
     */
    private Integer status;

    /**
     * 业务日期
     */
    private Date businessDate;

    /**
     * 存放位置
     */
    private Integer storageLocation;
}
