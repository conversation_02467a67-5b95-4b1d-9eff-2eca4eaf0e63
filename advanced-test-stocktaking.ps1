# 高级微信小程序库存盘点接口测试脚本
# 使用配置文件和详细验证

param(
    [string]$ConfigFile = "test-config.json"
)

# 加载配置文件
if (-not (Test-Path $ConfigFile)) {
    Write-Host "配置文件不存在: $ConfigFile" -ForegroundColor Red
    exit 1
}

$config = Get-Content $ConfigFile | ConvertFrom-Json
$testConfig = $config.testConfig

Write-Host "加载配置文件: $ConfigFile" -ForegroundColor Green
Write-Host "基础URL: $($testConfig.baseUrl)" -ForegroundColor Cyan
Write-Host "超时时间: $($testConfig.timeout)秒" -ForegroundColor Cyan

# 全局变量
$Global:TestResults = @()
$Global:Headers = @{
    "Content-Type" = "application/json"
    "Authorization" = "Bearer $($testConfig.token)"
}

# 测试结果类
class TestResult {
    [string]$TestName
    [string]$Method
    [string]$Url
    [bool]$Success
    [int]$StatusCode
    [string]$ResponseBody
    [string]$ErrorMessage
    [datetime]$Timestamp
    [bool]$DataValidation
    [string]$ValidationDetails
}

# API测试函数
function Invoke-ApiTestAdvanced {
    param(
        [string]$TestName,
        [string]$Method,
        [string]$Url,
        [hashtable]$Body = $null,
        [object]$ExpectedResponse = $null
    )
    
    Write-Host "`n执行测试: $TestName" -ForegroundColor Yellow
    Write-Host "  方法: $Method" -ForegroundColor Gray
    Write-Host "  URL: $Url" -ForegroundColor Gray
    
    $result = [TestResult]::new()
    $result.TestName = $TestName
    $result.Method = $Method
    $result.Url = $Url
    $result.Timestamp = Get-Date
    
    try {
        $params = @{
            Uri = $Url
            Method = $Method
            Headers = $Global:Headers
            TimeoutSec = $testConfig.timeout
        }
        
        if ($Body -and $Method -in @("POST", "PUT", "PATCH")) {
            $params.Body = ($Body | ConvertTo-Json -Depth 10)
            Write-Host "  请求体: $($params.Body)" -ForegroundColor Gray
        }
        
        $response = Invoke-RestMethod @params
        $result.StatusCode = 200
        $result.Success = $true
        $result.ResponseBody = ($response | ConvertTo-Json -Depth 10)
        
        # 数据验证
        if ($ExpectedResponse) {
            $validation = Test-ResponseValidation -Response $response -Expected $ExpectedResponse
            $result.DataValidation = $validation.IsValid
            $result.ValidationDetails = $validation.Details
        } else {
            $result.DataValidation = $true
            $result.ValidationDetails = "无验证规则"
        }
        
        Write-Host "  ✓ 测试通过" -ForegroundColor Green
        if ($result.DataValidation) {
            Write-Host "  ✓ 数据验证通过" -ForegroundColor Green
        } else {
            Write-Host "  ✗ 数据验证失败: $($result.ValidationDetails)" -ForegroundColor Red
        }
        
        Write-Host "  响应: $($response | ConvertTo-Json -Depth 2)" -ForegroundColor Cyan
        
    }
    catch {
        $result.StatusCode = if ($_.Exception.Response) { $_.Exception.Response.StatusCode.value__ } else { 0 }
        $result.Success = $false
        $result.ErrorMessage = $_.Exception.Message
        $result.DataValidation = $false
        $result.ValidationDetails = "请求失败"
        
        Write-Host "  ✗ 测试失败" -ForegroundColor Red
        Write-Host "  错误: $($result.ErrorMessage)" -ForegroundColor Red
    }
    
    $Global:TestResults += $result
    return $result
}

# 响应验证函数
function Test-ResponseValidation {
    param(
        [object]$Response,
        [object]$Expected
    )
    
    $details = @()
    $isValid = $true
    
    # 验证状态码
    if ($Expected.code -and $Response.code -ne $Expected.code) {
        $details += "状态码不匹配: 期望 $($Expected.code), 实际 $($Response.code)"
        $isValid = $false
    }
    
    # 验证消息
    if ($Expected.msg -and $Response.msg -ne $Expected.msg) {
        $details += "消息不匹配: 期望 '$($Expected.msg)', 实际 '$($Response.msg)'"
        $isValid = $false
    }
    
    # 验证数据字段
    if ($Expected.dataFields -and $Response.data) {
        foreach ($field in $Expected.dataFields) {
            if ($null -eq $Response.data.$field) {
                $details += "缺少字段: $field"
                $isValid = $false
            }
        }
    }
    
    return @{
        IsValid = $isValid
        Details = ($details -join "; ")
    }
}

# 开始测试
Write-Host "`n开始执行高级接口测试" -ForegroundColor Magenta
Write-Host "=" * 80

# P0级别接口测试
Write-Host "`n=== P0级别接口测试 ===" -ForegroundColor Blue

# 1. 获取我的盘点任务列表
$url1 = "$($testConfig.baseUrl)/item/stocktaking/my-tasks"
Invoke-ApiTestAdvanced -TestName "获取我的盘点任务列表" -Method "GET" -Url $url1 -ExpectedResponse $testConfig.expectedResponses.myTasks

# 2. 根据物品条码查询物品信息
$url2 = "$($testConfig.baseUrl)/item/by-code/$($testConfig.testData.testItemCode)"
Invoke-ApiTestAdvanced -TestName "根据物品条码查询物品信息" -Method "GET" -Url $url2 -ExpectedResponse $testConfig.expectedResponses.itemByCode

# 3. 根据物品信息查找盘点明细
$queryParams = "stocktakingId=$($testConfig.testData.testStocktakingId)&itemId=$($testConfig.testData.testItemId)&warehouseId=$($testConfig.testData.testWarehouseId)"
$url3 = "$($testConfig.baseUrl)/item/stocktaking/detail/by-item?$queryParams"
Invoke-ApiTestAdvanced -TestName "根据物品信息查找盘点明细" -Method "GET" -Url $url3 -ExpectedResponse $testConfig.expectedResponses.stocktakingDetail

# 4. 更新盘点明细
$updateBody = @{
    actualQuantity = 95.00
    differenceReason = "自动化测试差异"
    photos = @("http://example.com/test1.jpg", "http://example.com/test2.jpg")
}
$url4 = "$($testConfig.baseUrl)/item/stocktaking/detail/$($testConfig.testData.testDetailId)"
Invoke-ApiTestAdvanced -TestName "更新盘点明细" -Method "PUT" -Url $url4 -Body $updateBody -ExpectedResponse $testConfig.expectedResponses.updateDetail

# P1级别接口测试
Write-Host "`n=== P1级别接口测试 ===" -ForegroundColor Blue

# 5. 获取盘点进度
$url5 = "$($testConfig.baseUrl)/item/stocktaking/$($testConfig.testData.testStocktakingId)/progress"
Invoke-ApiTestAdvanced -TestName "获取盘点进度" -Method "GET" -Url $url5 -ExpectedResponse $testConfig.expectedResponses.progress

# 6. 获取个人盘点进度
$url6 = "$($testConfig.baseUrl)/item/stocktaking/my-progress"
Invoke-ApiTestAdvanced -TestName "获取个人盘点进度" -Method "GET" -Url $url6 -ExpectedResponse $testConfig.expectedResponses.myProgress

# P2级别接口测试
Write-Host "`n=== P2级别接口测试 ===" -ForegroundColor Blue

# 7. 获取个人盘点记录
$timeRanges = @("today", "week", "month")
foreach ($range in $timeRanges) {
    $url7 = "$($testConfig.baseUrl)/item/stocktaking/my-records?timeRange=$range"
    Invoke-ApiTestAdvanced -TestName "获取个人盘点记录($range)" -Method "GET" -Url $url7 -ExpectedResponse $testConfig.expectedResponses.myRecords
}

# 8. 获取物品盘点历史
$url8 = "$($testConfig.baseUrl)/item/stocktaking/item-history/$($testConfig.testData.testItemId)"
Invoke-ApiTestAdvanced -TestName "获取物品盘点历史" -Method "GET" -Url $url8 -ExpectedResponse $testConfig.expectedResponses.itemHistory

# 错误场景测试
Write-Host "`n=== 错误场景测试 ===" -ForegroundColor Blue

# 物品条码不存在
$errorUrl1 = "$($testConfig.baseUrl)/item/by-code/NOTEXIST"
Invoke-ApiTestAdvanced -TestName "物品条码不存在" -Method "GET" -Url $errorUrl1 -ExpectedResponse $testConfig.errorScenarios.itemNotFound

# 盘点明细不存在
$errorParams = "stocktakingId=not_exist&itemId=not_exist&warehouseId=999"
$errorUrl2 = "$($testConfig.baseUrl)/item/stocktaking/detail/by-item?$errorParams"
Invoke-ApiTestAdvanced -TestName "盘点明细不存在" -Method "GET" -Url $errorUrl2 -ExpectedResponse $testConfig.errorScenarios.detailNotFound

# 更新不存在的明细
$errorBody = @{ actualQuantity = 100.00 }
$errorUrl3 = "$($testConfig.baseUrl)/item/stocktaking/detail/not_exist_detail"
Invoke-ApiTestAdvanced -TestName "更新不存在的明细" -Method "PUT" -Url $errorUrl3 -Body $errorBody -ExpectedResponse $testConfig.errorScenarios.updateFailed

# 生成测试报告
Write-Host "`n=== 测试报告 ===" -ForegroundColor Magenta

$totalTests = $Global:TestResults.Count
$passedTests = ($Global:TestResults | Where-Object { $_.Success }).Count
$failedTests = $totalTests - $passedTests
$dataValidationPassed = ($Global:TestResults | Where-Object { $_.DataValidation }).Count

Write-Host "总测试数: $totalTests" -ForegroundColor White
Write-Host "通过测试: $passedTests" -ForegroundColor Green
Write-Host "失败测试: $failedTests" -ForegroundColor Red
Write-Host "数据验证通过: $dataValidationPassed" -ForegroundColor Cyan
Write-Host "通过率: $([math]::Round(($passedTests / $totalTests) * 100, 2))%" -ForegroundColor Yellow
Write-Host "数据验证率: $([math]::Round(($dataValidationPassed / $totalTests) * 100, 2))%" -ForegroundColor Yellow

# 导出详细报告
$reportFile = "advanced-test-report-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
$Global:TestResults | ConvertTo-Json -Depth 10 | Out-File -FilePath $reportFile -Encoding UTF8
Write-Host "`n详细测试报告已保存到: $reportFile" -ForegroundColor Cyan

Write-Host "`n高级测试完成!" -ForegroundColor Magenta
