package com.jingfang.asset_stocktaking.module.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 盘点任务查询请求对象
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
public class TaskSearchRequest implements Serializable {

    /**
     * 盘点计划ID
     */
    private String planId;

    /**
     * 任务名称（模糊查询）
     */
    private String taskName;

    /**
     * 分配给的用户ID
     */
    private Long assignedUserId;

    /**
     * 状态：1-待执行，2-执行中，3-已完成
     */
    private Integer status;

    /**
     * 状态列表（多选查询）
     */
    private List<Integer> statusList;

    /**
     * 开始时间范围-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTimeBegin;

    /**
     * 开始时间范围-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTimeEnd;

    /**
     * 结束时间范围-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTimeBegin;

    /**
     * 结束时间范围-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTimeEnd;

    /**
     * 创建时间范围-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimeBegin;

    /**
     * 创建时间范围-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimeEnd;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 预期数量范围-最小值
     */
    private Integer expectedCountMin;

    /**
     * 预期数量范围-最大值
     */
    private Integer expectedCountMax;

    /**
     * 完成进度范围-最小值（百分比）
     */
    private Double progressMin;

    /**
     * 完成进度范围-最大值（百分比）
     */
    private Double progressMax;

    /**
     * 是否只查询我的任务
     */
    private Boolean onlyMyTasks;

    /**
     * 是否只查询逾期任务
     */
    private Boolean onlyOverdueTasks;

    /**
     * 部门ID（数据权限过滤）
     */
    private Long deptId;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序方向：asc-升序，desc-降序
     */
    private String orderDirection;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 是否查询进度信息
     */
    private Boolean includeProgress;

    /**
     * 是否查询统计信息
     */
    private Boolean includeStatistics;

    /**
     * 关键词搜索（任务名称、分配用户）
     */
    private String keyword;

    private static final long serialVersionUID = 1L;
}
