package com.jingfang.asset_stocktaking.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.asset_stocktaking.module.entity.AssetStocktakingTask;
import com.jingfang.asset_stocktaking.module.request.TaskSearchRequest;
import com.jingfang.asset_stocktaking.module.vo.StocktakingTaskVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 盘点任务数据访问层
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Mapper
public interface StocktakingTaskMapper extends BaseMapper<AssetStocktakingTask> {

    /**
     * 分页查询盘点任务列表
     * 
     * @param page 分页对象
     * @param request 查询条件
     * @return 盘点任务列表
     */
    IPage<StocktakingTaskVo> selectTaskList(IPage<StocktakingTaskVo> page, @Param("request") TaskSearchRequest request);

    /**
     * 根据ID查询盘点任务详情
     * 
     * @param taskId 任务ID
     * @return 盘点任务详情
     */
    StocktakingTaskVo selectTaskById(@Param("taskId") String taskId);

    /**
     * 查询任务进度信息
     * 
     * @param taskId 任务ID
     * @return 进度信息
     */
    StocktakingTaskVo.TaskProgress selectTaskProgress(@Param("taskId") String taskId);

    /**
     * 查询任务统计信息
     * 
     * @param taskId 任务ID
     * @return 统计信息
     */
    StocktakingTaskVo.TaskStatistics selectTaskStatistics(@Param("taskId") String taskId);

    /**
     * 根据计划ID查询任务列表
     * 
     * @param planId 计划ID
     * @return 任务列表
     */
    List<AssetStocktakingTask> selectTaskByPlanId(@Param("planId") String planId);

    /**
     * 根据分配用户查询任务列表
     * 
     * @param assignedUserId 分配用户ID
     * @return 任务列表
     */
    List<AssetStocktakingTask> selectTaskByAssignedUser(@Param("assignedUserId") Long assignedUserId);

    /**
     * 根据状态查询任务列表
     * 
     * @param status 状态
     * @return 任务列表
     */
    List<AssetStocktakingTask> selectTaskByStatus(@Param("status") Integer status);

    /**
     * 统计计划下各状态的任务数量
     * 
     * @param planId 计划ID
     * @return 状态统计
     */
    List<java.util.Map<String, Object>> countTaskByStatusInPlan(@Param("planId") String planId);

    /**
     * 查询用户的待执行任务
     * 
     * @param assignedUserId 分配用户ID
     * @return 待执行任务列表
     */
    List<AssetStocktakingTask> selectPendingTasksByUser(@Param("assignedUserId") Long assignedUserId);

    /**
     * 查询用户的进行中任务
     * 
     * @param assignedUserId 分配用户ID
     * @return 进行中任务列表
     */
    List<AssetStocktakingTask> selectInProgressTasksByUser(@Param("assignedUserId") Long assignedUserId);

    /**
     * 查询逾期任务
     * 
     * @return 逾期任务列表
     */
    List<AssetStocktakingTask> selectOverdueTasks();

    /**
     * 更新任务状态
     * 
     * @param taskId 任务ID
     * @param status 新状态
     * @return 影响行数
     */
    int updateTaskStatus(@Param("taskId") String taskId, @Param("status") Integer status);

    /**
     * 更新任务进度
     * 
     * @param taskId 任务ID
     * @param actualCount 实际盘点数量
     * @return 影响行数
     */
    int updateTaskProgress(@Param("taskId") String taskId, @Param("actualCount") Integer actualCount);

    /**
     * 批量更新任务状态
     * 
     * @param taskIds 任务ID列表
     * @param status 新状态
     * @return 影响行数
     */
    int batchUpdateTaskStatus(@Param("taskIds") List<String> taskIds, @Param("status") Integer status);

    /**
     * 开始任务（更新开始时间和状态）
     * 
     * @param taskId 任务ID
     * @return 影响行数
     */
    int startTask(@Param("taskId") String taskId);

    /**
     * 完成任务（更新结束时间和状态）
     * 
     * @param taskId 任务ID
     * @return 影响行数
     */
    int completeTask(@Param("taskId") String taskId);

    /**
     * 查询计划的任务完成情况
     * 
     * @param planId 计划ID
     * @return 完成情况统计
     */
    java.util.Map<String, Object> selectPlanTaskCompletion(@Param("planId") String planId);

    /**
     * 重新分配任务
     * 
     * @param taskId 任务ID
     * @param newAssignedUserId 新分配用户ID
     * @return 影响行数
     */
    int reassignTask(@Param("taskId") String taskId, @Param("newAssignedUserId") Long newAssignedUserId);
}
