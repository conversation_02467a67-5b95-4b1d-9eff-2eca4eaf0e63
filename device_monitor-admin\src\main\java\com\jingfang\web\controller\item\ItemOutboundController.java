package com.jingfang.web.controller.item;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.common.core.domain.model.LoginUser;
import com.jingfang.common.utils.BusinessCodeGenerator;
import com.jingfang.wh_item.module.dto.ItemOutboundDto;
import com.jingfang.wh_item.module.entity.ItemOutbound;
import com.jingfang.wh_item.module.request.ItemOutboundSearchRequest;
import com.jingfang.wh_item.module.vo.ItemOutboundVo;
import com.jingfang.wh_item.service.ItemOutboundService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/item/outbound")
public class ItemOutboundController extends BaseController {

    @Resource
    private ItemOutboundService itemOutboundService;

    @Resource
    private BusinessCodeGenerator codeGenerator;

    /**
     * 新增物品出库单
     */
    @PreAuthorize("@ss.hasPermi('item:outbound:add')")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody @Validated ItemOutboundDto outboundDto) {
        try {
            LoginUser user = getLoginUser();
            log.info("新增出库单请求参数：{}", outboundDto);
            String outboundId = codeGenerator.generateCode("WPCK");
            ItemOutbound itemOutbound = outboundDto.getMain();
            itemOutbound.setOutboundId(outboundId);
            itemOutbound.setCreatorId(user.getUserId());

            outboundDto.setMain(itemOutbound);
            itemOutboundService.addItemOutbound(outboundDto, user.getUser().getNickName());
            return AjaxResult.success("新增出库单成功", outboundId);
        } catch (Exception e) {
            log.error("新增出库单异常：", e);
            return AjaxResult.error("新增出库单失败：" + e.getMessage());
        }
    }

    /**
     * 编辑物品出库单
     */
    @PreAuthorize("@ss.hasPermi('item:outbound:edit')")
    @PutMapping("/{outboundId}")
    public AjaxResult edit(
            @PathVariable("outboundId") String outboundId,
            @RequestBody @Validated ItemOutboundDto outboundDto) {
        try {
            log.info("编辑出库单请求参数：outboundId={}, data={}", outboundId, outboundDto);
            itemOutboundService.updateItemOutbound(outboundId, outboundDto, getNickName());
            return AjaxResult.success("编辑出库单成功");
        } catch (Exception e) {
            log.error("编辑出库单异常：", e);
            return AjaxResult.error("编辑出库单失败：" + e.getMessage());
        }
    }

    /**
     * 获取出库单详情
     */
    @PreAuthorize("@ss.hasPermi('item:outbound:query')")
    @GetMapping("/{outboundId}")
    public AjaxResult getDetail(@PathVariable("outboundId") String outboundId) {
        try {
            log.info("获取出库单详情，ID: {}", outboundId);
            ItemOutboundVo detail = itemOutboundService.getOutboundDetail(outboundId);
            log.info("出库单详情获取成功，ID: {}, 状态: {}, 制单人: {}, 经手人: {}, 审核人: {}",
                    outboundId, detail.getStatus(),
                    detail.getCreatorName(), detail.getHandlerName(), detail.getAuditorName());
            return AjaxResult.success(detail);
        } catch (Exception e) {
            log.error("获取出库单详情异常：", e);
            return AjaxResult.error("获取出库单详情失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询出库单列表
     */
    @PreAuthorize("@ss.hasPermi('item:outbound:list')")
    @PostMapping("/list")
    public AjaxResult list(@RequestBody ItemOutboundSearchRequest request) {
        IPage<ItemOutboundVo> vos = itemOutboundService.selectOutboundList(request);
        return AjaxResult.success(vos);
    }

    /**
     * 提交出库单待确认
     */
    @PreAuthorize("@ss.hasPermi('item:outbound:submit')")
    @PutMapping("/submit/{outboundId}")
    public AjaxResult submit(@PathVariable("outboundId") String outboundId) {
        try {
            itemOutboundService.submitOutbound(outboundId, getNickName());
            return AjaxResult.success("提交成功，等待经手人确认");
        } catch (Exception e) {
            log.error("提交出库单异常：", e);
            return AjaxResult.error("提交失败：" + e.getMessage());
        }
    }

    /**
     * 经手人确认出库单
     */
    @PreAuthorize("@ss.hasPermi('item:outbound:handle')")
    @PutMapping("/handle/{outboundId}")
    public AjaxResult handle(
            @PathVariable("outboundId") String outboundId,
            @RequestParam(value = "remark", required = false) String remark) {
        try {
            itemOutboundService.handleOutbound(outboundId, remark, getNickName());
            return AjaxResult.success("确认操作成功");
        } catch (Exception e) {
            log.error("经手人确认出库单异常：", e);
            return AjaxResult.error("确认操作失败：" + e.getMessage());
        }
    }

    /**
     * 审核出库单
     */
    @PreAuthorize("@ss.hasPermi('item:outbound:audit')")
    @PutMapping("/audit/{outboundId}/{status}")
    public AjaxResult audit(
            @PathVariable("outboundId") String outboundId,
            @PathVariable("status") Integer status,
            @RequestParam(value = "remark", required = false) String remark) {
        try {
            itemOutboundService.auditOutbound(outboundId, status, remark, getNickName());
            return AjaxResult.success("审核操作成功");
        } catch (Exception e) {
            log.error("审核出库单异常：", e);
            return AjaxResult.error("审核操作失败：" + e.getMessage());
        }
    }

    /**
     * 删除出库单
     */
    @PreAuthorize("@ss.hasPermi('item:outbound:remove')")
    @DeleteMapping("/{outboundIds}")
    public AjaxResult remove(@PathVariable String[] outboundIds) {
        try {
            itemOutboundService.deleteOutbound(outboundIds, getNickName());
            return AjaxResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除出库单异常：", e);
            return AjaxResult.error("删除失败：" + e.getMessage());
        }
    }

} 