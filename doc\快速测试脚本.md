# 微信小程序库存盘点接口快速测试脚本

## 测试环境配置

```bash
# 基础URL
BASE_URL="http://localhost:8080"

# 认证Token（请替换为实际的JWT Token）
TOKEN="your_jwt_token_here"

# 测试数据ID（请替换为实际的ID）
STOCKTAKING_ID="test_stocktaking_id"
ITEM_ID="test_item_id"
ITEM_CODE="TEST001"
DETAIL_ID="test_detail_id"
WAREHOUSE_ID="1"
```

## P0级别核心功能测试

### 1. 获取我的盘点任务列表
```bash
curl -X GET \
  "${BASE_URL}/item/stocktaking/my-tasks" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json"
```

### 2. 根据物品条码查询物品信息
```bash
curl -X GET \
  "${BASE_URL}/item/by-code/${ITEM_CODE}" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json"
```

### 3. 根据物品信息查找盘点明细
```bash
curl -X GET \
  "${BASE_URL}/item/stocktaking/detail/by-item?stocktakingId=${STOCKTAKING_ID}&itemId=${ITEM_ID}&warehouseId=${WAREHOUSE_ID}" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json"
```

### 4. 更新盘点明细
```bash
curl -X PUT \
  "${BASE_URL}/item/stocktaking/detail/${DETAIL_ID}" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "actualQuantity": 95.00,
    "differenceReason": "部分物品损耗",
    "photos": [
      "http://example.com/upload/2025/01/11/photo1.jpg",
      "http://example.com/upload/2025/01/11/photo2.jpg"
    ]
  }'
```

## P1级别重要功能测试

### 5. 获取盘点进度
```bash
curl -X GET \
  "${BASE_URL}/item/stocktaking/${STOCKTAKING_ID}/progress" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json"
```

### 6. 获取个人盘点进度
```bash
curl -X GET \
  "${BASE_URL}/item/stocktaking/my-progress" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json"
```

## P2级别优化功能测试

### 7. 获取个人盘点记录

#### 7.1 今日记录
```bash
curl -X GET \
  "${BASE_URL}/item/stocktaking/my-records?timeRange=today" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json"
```

#### 7.2 本周记录
```bash
curl -X GET \
  "${BASE_URL}/item/stocktaking/my-records?timeRange=week" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json"
```

#### 7.3 本月记录
```bash
curl -X GET \
  "${BASE_URL}/item/stocktaking/my-records?timeRange=month" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json"
```

#### 7.4 自定义时间范围
```bash
curl -X GET \
  "${BASE_URL}/item/stocktaking/my-records?timeRange=custom&startDate=2025-01-01&endDate=2025-01-11" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json"
```

### 8. 获取物品盘点历史
```bash
curl -X GET \
  "${BASE_URL}/item/stocktaking/item-history/${ITEM_ID}" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json"
```

## 错误场景测试

### 物品条码不存在
```bash
curl -X GET \
  "${BASE_URL}/item/by-code/NOTEXIST" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json"
```

### 盘点明细不存在
```bash
curl -X GET \
  "${BASE_URL}/item/stocktaking/detail/by-item?stocktakingId=not_exist&itemId=not_exist&warehouseId=999" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json"
```

## 测试前准备工作

### 1. 获取认证Token
```bash
# 登录获取Token
curl -X POST \
  "${BASE_URL}/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "your_username",
    "password": "your_password"
  }'
```

### 2. 创建测试数据

#### 2.1 创建测试物品
```bash
curl -X POST \
  "${BASE_URL}/item/add" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "baseInfo": {
      "itemName": "测试物品A",
      "itemCode": "TEST001",
      "specModel": "规格A",
      "unit": "个",
      "itemType": 1,
      "safetyStock": 10.00
    },
    "inventory": {
      "currentQuantity": 100.00,
      "warehouseId": 1,
      "shelfLocation": "A-01-01"
    }
  }'
```

#### 2.2 创建盘点计划
```bash
curl -X POST \
  "${BASE_URL}/item/stocktaking" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "stocktakingName": "测试盘点计划",
    "stocktakingType": 1,
    "warehouseId": 1,
    "planStartTime": "2025-01-11 09:00:00",
    "planEndTime": "2025-01-11 18:00:00",
    "remark": "API测试用盘点计划"
  }'
```

#### 2.3 生成盘点明细
```bash
curl -X POST \
  "${BASE_URL}/item/stocktaking/${STOCKTAKING_ID}/generate" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json"
```

#### 2.4 开始盘点
```bash
curl -X POST \
  "${BASE_URL}/item/stocktaking/${STOCKTAKING_ID}/start" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json"
```

## 测试结果验证

### 成功响应格式
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": { ... }
}
```

### 错误响应格式
```json
{
  "code": 500,
  "msg": "错误信息",
  "data": null
}
```

## 注意事项

1. **替换变量**: 请将脚本中的变量替换为实际值
2. **权限验证**: 确保用户具有相应的权限
3. **数据准备**: 测试前需要准备相应的测试数据
4. **环境检查**: 确保后端服务正常运行
5. **数据库更新**: 确保已执行数据库修改脚本

## 批量测试脚本

创建一个shell脚本文件 `test_all.sh`：

```bash
#!/bin/bash

# 设置变量
BASE_URL="http://localhost:8080"
TOKEN="your_jwt_token_here"

echo "开始测试微信小程序库存盘点接口..."

# 测试P0级别接口
echo "=== P0级别测试 ==="
echo "1. 测试获取我的盘点任务列表..."
curl -s -X GET "${BASE_URL}/item/stocktaking/my-tasks" -H "Authorization: Bearer ${TOKEN}" | jq .

echo "2. 测试根据条码查询物品..."
curl -s -X GET "${BASE_URL}/item/by-code/TEST001" -H "Authorization: Bearer ${TOKEN}" | jq .

# 继续其他测试...
echo "测试完成！"
```

使用方法：
```bash
chmod +x test_all.sh
./test_all.sh
```
