package com.jingfang.asset_stocktaking.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jingfang.asset_stocktaking.module.dto.StocktakingPlanDto;
import com.jingfang.asset_stocktaking.module.entity.AssetStocktakingPlan;
import com.jingfang.asset_stocktaking.module.request.PlanSearchRequest;
import com.jingfang.asset_stocktaking.module.vo.StocktakingPlanVo;

import java.util.List;

/**
 * 盘点计划服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface StocktakingPlanService extends IService<AssetStocktakingPlan> {

    /**
     * 创建盘点计划
     * 
     * @param planDto 盘点计划数据
     * @return 是否成功
     */
    boolean createPlan(StocktakingPlanDto planDto);

    /**
     * 编辑盘点计划
     * 
     * @param planDto 盘点计划数据
     * @return 是否成功
     */
    boolean editPlan(StocktakingPlanDto planDto);

    /**
     * 删除盘点计划
     * 
     * @param planId 计划ID
     * @return 是否成功
     */
    boolean deletePlan(String planId);

    /**
     * 批量删除盘点计划
     * 
     * @param planIds 计划ID列表
     * @return 是否成功
     */
    boolean batchDeletePlans(List<String> planIds);

    /**
     * 分页查询盘点计划列表
     * 
     * @param request 查询条件
     * @return 分页结果
     */
    IPage<StocktakingPlanVo> selectPlanList(PlanSearchRequest request);

    /**
     * 根据ID查询盘点计划详情
     * 
     * @param planId 计划ID
     * @return 计划详情
     */
    StocktakingPlanVo selectPlanById(String planId);

    /**
     * 提交审批
     * 
     * @param planId 计划ID
     * @return 是否成功
     */
    boolean submitForApproval(String planId);

    /**
     * 审批通过
     * 
     * @param planId 计划ID
     * @param approvalComment 审批意见
     * @return 是否成功
     */
    boolean approvePlan(String planId, String approvalComment);

    /**
     * 审批拒绝
     * 
     * @param planId 计划ID
     * @param rejectReason 拒绝原因
     * @return 是否成功
     */
    boolean rejectPlan(String planId, String rejectReason);

    /**
     * 启动盘点计划
     * 
     * @param planId 计划ID
     * @return 是否成功
     */
    boolean startPlan(String planId);

    /**
     * 完成盘点计划
     * 
     * @param planId 计划ID
     * @return 是否成功
     */
    boolean completePlan(String planId);

    /**
     * 取消盘点计划
     * 
     * @param planId 计划ID
     * @param cancelReason 取消原因
     * @return 是否成功
     */
    boolean cancelPlan(String planId, String cancelReason);

    /**
     * 检查计划名称是否重复
     * 
     * @param planName 计划名称
     * @param excludePlanId 排除的计划ID
     * @return 是否重复
     */
    boolean checkPlanNameExists(String planName, String excludePlanId);

    /**
     * 查询用户有权限的盘点计划
     * 
     * @param userId 用户ID
     * @return 计划列表
     */
    List<AssetStocktakingPlan> selectPlanByUserPermission(Long userId);

    /**
     * 查询即将到期的盘点计划
     * 
     * @param days 提前天数
     * @return 即将到期的计划列表
     */
    List<AssetStocktakingPlan> selectExpiringPlans(Integer days);

    /**
     * 查询逾期的盘点计划
     * 
     * @return 逾期计划列表
     */
    List<AssetStocktakingPlan> selectOverduePlans();

    /**
     * 统计各状态的盘点计划数量
     * 
     * @return 状态统计
     */
    List<java.util.Map<String, Object>> countPlanByStatus();

    /**
     * 根据盘点范围生成资产列表
     * 
     * @param planDto 盘点计划数据
     * @return 资产ID列表
     */
    List<String> generateAssetListByScope(StocktakingPlanDto planDto);

    /**
     * 验证盘点计划数据
     * 
     * @param planDto 盘点计划数据
     * @return 验证结果
     */
    boolean validatePlanData(StocktakingPlanDto planDto);

    /**
     * 复制盘点计划
     * 
     * @param sourcePlanId 源计划ID
     * @param newPlanName 新计划名称
     * @return 新计划ID
     */
    String copyPlan(String sourcePlanId, String newPlanName);
}
