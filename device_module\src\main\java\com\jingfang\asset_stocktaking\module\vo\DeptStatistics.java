package com.jingfang.asset_stocktaking.module.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 部门统计信息视图对象
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
public class DeptStatistics implements Serializable {
    
    /**
     * 部门ID
     */
    private Long deptId;
    
    /**
     * 部门名称
     */
    private String deptName;
    
    /**
     * 应盘资产数
     */
    private Integer totalAssets;
    
    /**
     * 实盘资产数
     */
    private Integer inventoriedAssets;
    
    /**
     * 差异资产数
     */
    private Integer differenceAssets;
    
    /**
     * 完成率
     */
    private Double completionRate;
    
    /**
     * 差异率
     */
    private Double differenceRate;
    
    /**
     * 盘盈数量
     */
    private Integer surplusCount;
    
    /**
     * 盘亏数量
     */
    private Integer deficitCount;
    
    /**
     * 资产总值
     */
    private BigDecimal totalValue;

    private static final long serialVersionUID = 1L;
}
