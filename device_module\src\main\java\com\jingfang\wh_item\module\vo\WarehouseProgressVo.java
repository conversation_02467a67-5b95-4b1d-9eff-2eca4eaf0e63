package com.jingfang.wh_item.module.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 仓库进度VO
 * 用于显示仓库盘点进度信息
 */
@Data
public class WarehouseProgressVo implements Serializable {
    
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    
    /**
     * 仓库名称
     */
    private String warehouseName;
    
    /**
     * 总物品数
     */
    private Integer totalItems;
    
    /**
     * 已完成数
     */
    private Integer completedItems;
    
    /**
     * 完成率
     */
    private BigDecimal completionRate;
    
    private static final long serialVersionUID = 1L;
}
