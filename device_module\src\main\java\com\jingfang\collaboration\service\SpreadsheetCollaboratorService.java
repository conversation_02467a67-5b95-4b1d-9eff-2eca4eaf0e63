package com.jingfang.collaboration.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jingfang.collaboration.domain.SpreadsheetCollaborator;
import com.jingfang.collaboration.vo.CollaboratorVo;
import com.jingfang.collaboration.vo.OnlineUserVo;

import java.util.List;

/**
 * 表格协作者服务接口
 */
public interface SpreadsheetCollaboratorService extends IService<SpreadsheetCollaborator> {
    
    /**
     * 查询表格的协作者列表
     */
    List<CollaboratorVo> getCollaboratorsBySpreadsheetId(String spreadsheetId);
    
    /**
     * 查询用户在指定表格的权限
     */
    String getUserPermission(String spreadsheetId, Long userId);
    
    /**
     * 查询表格的在线用户列表
     */
    List<OnlineUserVo> getOnlineUsers(String spreadsheetId);
    
    /**
     * 更新用户在线状态
     */
    boolean updateUserOnlineStatus(String spreadsheetId, Long userId, String isOnline);
    
    /**
     * 批量更新用户在线状态
     */
    boolean batchUpdateUserOnlineStatus(List<Long> userIds, String spreadsheetId, String isOnline);
}
