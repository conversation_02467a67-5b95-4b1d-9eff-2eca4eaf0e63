package com.jingfang.maintenance_plan.module.dto;

import com.jingfang.maintenance_plan.module.entity.MaintenancePlan;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


import java.util.Date;
import java.util.List;

/**
 * 维护计划DTO
 */
@Data
public class MaintenancePlanDto {
    
    /**
     * 维护计划ID
     */
    private String planId;
    
    /**
     * 计划名称
     */
    @NotBlank(message = "计划名称不能为空")
    private String planName;
    
    /**
     * 资产ID
     */
    @NotBlank(message = "资产ID不能为空")
    private String assetId;
    
    /**
     * 资产名称（用于显示）
     */
    private String assetName;
    
    /**
     * 维护事项描述
     */
    @NotBlank(message = "维护事项不能为空")
    private String maintenanceItems;
    
    /**
     * 维护周期类型(1-按天, 2-按周, 3-按月, 4-按年)
     */
    @NotNull(message = "维护周期类型不能为空")
    private Integer cycleType;
    
    /**
     * 维护周期值
     */
    @NotNull(message = "维护周期值不能为空")
    private Integer cycleValue;
    
    /**
     * 负责人类型(1-个人, 2-部门)
     */
    @NotNull(message = "负责人类型不能为空")
    private Integer responsibleType;
    
    /**
     * 负责人ID（用户ID或部门ID）
     */
    @NotNull(message = "负责人不能为空")
    private Long responsibleId;
    
    /**
     * 负责人名称（用于显示）
     */
    private String responsibleName;
    
    /**
     * 计划状态(1-启用, 0-停用)
     */
    private Integer status;
    
    /**
     * 下次维护时间
     */
    private Date nextMaintenanceTime;
    
    /**
     * 备注说明
     */
    private String remark;
    
    /**
     * 关联的备品备件列表
     */
    private List<MaintenancePlanPartDto> partList;
    

    
    /**
     * 转换为实体类
     */
    public MaintenancePlan toEntity() {
        MaintenancePlan plan = new MaintenancePlan();
        plan.setPlanId(this.planId);
        plan.setPlanName(this.planName);
        plan.setAssetId(this.assetId);
        plan.setMaintenanceItems(this.maintenanceItems);
        plan.setCycleType(this.cycleType);
        plan.setCycleValue(this.cycleValue);
        plan.setResponsibleType(this.responsibleType);
        plan.setResponsibleId(this.responsibleId);
        plan.setStatus(this.status);
        plan.setNextMaintenanceTime(this.nextMaintenanceTime);
        plan.setRemark(this.remark);
        return plan;
    }
} 