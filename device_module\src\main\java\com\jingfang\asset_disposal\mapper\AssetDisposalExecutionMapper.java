package com.jingfang.asset_disposal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jingfang.asset_disposal.module.entity.AssetDisposalExecution;
import com.jingfang.asset_disposal.module.vo.AssetDisposalExecutionVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 资产处置执行Mapper接口
 */
@Mapper
public interface AssetDisposalExecutionMapper extends BaseMapper<AssetDisposalExecution> {
    
    /**
     * 根据处置单ID查询执行记录
     */
    AssetDisposalExecutionVo selectExecutionByDisposalId(@Param("disposalId") String disposalId);
} 