<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.asset_ledger.mapper.AssetMaintainInfoMapper">

    <resultMap id="BaseResultMap" type="com.jingfang.asset_ledger.module.entity.AssetMaintainInfo">
            <id property="maintainId" column="maintain_id" />
            <result property="assetId" column="asset_id" />
            <result property="maintainVendor" column="maintain_vendor" />
            <result property="startTime" column="start_time" />
            <result property="endTime" column="end_time" />
            <result property="maintainStatus" column="maintain_status" />
            <result property="contactNumber" column="contact_number" />
            <result property="managerId" column="manager_id" />
            <result property="maintainMethod" column="maintain_method" />
            <result property="remark" column="remark" />
    </resultMap>

    <sql id="Base_Column_List">
        maintain_id,asset_id,maintain_vendor,start_time,end_time,maintain_status,
        contact_number,manager_id,maintain_method,remark
    </sql>
</mapper>
