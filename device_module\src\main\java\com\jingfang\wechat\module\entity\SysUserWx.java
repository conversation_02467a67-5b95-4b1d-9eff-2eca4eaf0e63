package com.jingfang.wechat.module.entity;

import com.jingfang.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 用户微信绑定关系对象 sys_user_wx
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SysUserWx extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 系统用户ID */
    private Long userId;

    /** 微信用户ID */
    private Long wxUserId;

    /** 绑定时间 */
    private Date bindTime;

    /** 绑定类型（0账号绑定 1手机号绑定） */
    private String bindType;

    /** 状态（0正常 1解绑） */
    private String status;

}
