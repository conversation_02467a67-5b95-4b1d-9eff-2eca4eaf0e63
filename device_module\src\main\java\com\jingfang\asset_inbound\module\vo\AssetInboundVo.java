package com.jingfang.asset_inbound.module.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jingfang.asset_inbound.module.entity.AssetInboundOperationLog;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 资产入库单视图对象
 * 用于返回入库单信息
 */
@Data
public class AssetInboundVo {

    /**
     * 入库单ID
     */
    private String inboundId;

    /**
     * 业务日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date businessDate;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 存放位置编码
     */
    private Integer storageLocation;
    
    /**
     * 存放位置名称
     */
    private String storageLocationName;

    /**
     * 状态(0:草稿,1:待审核,2:已审核,3:已完成,9:已取消)
     */
    private Integer status;
    
    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 制单人ID
     */
    private Long creatorId;
    
    /**
     * 制单人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 经手人ID
     */
    private Long handlerId;
    
    /**
     * 经手人姓名
     */
    private String handlerName;
    
    /**
     * 经手确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date handleTime;
    
    /**
     * 经手人备注
     */
    private String handleRemark;
    
    /**
     * 审核人ID
     */
    private Long auditorId;
    
    /**
     * 审核人姓名
     */
    private String auditorName;
    
    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditTime;
    
    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 入库说明
     */
    private String inboundDescription;
    
    /**
     * 明细列表
     */
    private List<AssetInboundDetailVo> details;
    
    /**
     * 操作日志列表
     */
    private List<AssetInboundOperationLog> logs;
} 