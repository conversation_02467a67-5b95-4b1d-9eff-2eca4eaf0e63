package com.jingfang.maintenance_plan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingfang.asset_part_relation.service.AssetPartRelationService;
import com.jingfang.common.utils.BusinessCodeGenerator;
import com.jingfang.maintenance_plan.mapper.MaintenancePlanMapper;
import com.jingfang.maintenance_plan.mapper.MaintenancePlanPartMapper;
import com.jingfang.maintenance_plan.module.dto.MaintenancePlanDto;
import com.jingfang.maintenance_plan.module.dto.MaintenancePlanPartDto;
import com.jingfang.maintenance_plan.module.entity.MaintenancePlan;
import com.jingfang.maintenance_plan.module.entity.MaintenancePlanPart;
import com.jingfang.maintenance_plan.module.request.MaintenancePlanSearchRequest;
import com.jingfang.maintenance_plan.module.vo.MaintenancePlanVo;
import com.jingfang.maintenance_plan.service.MaintenancePlanService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 维护计划Service实现类
 */
@Slf4j
@Service
public class MaintenancePlanServiceImpl extends ServiceImpl<MaintenancePlanMapper, MaintenancePlan> 
        implements MaintenancePlanService {
    
    @Resource
    private MaintenancePlanMapper maintenancePlanMapper;
    
    @Resource
    private MaintenancePlanPartMapper maintenancePlanPartMapper;
    
    @Resource
    private AssetPartRelationService assetPartRelationService;
    
    @Resource
    private BusinessCodeGenerator codeGenerator;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addMaintenancePlan(MaintenancePlanDto planDto, String createBy) {
        try {
            // 生成维护计划ID
            String planId = codeGenerator.generateCode("WHJH");
            
            // 转换为实体类
            MaintenancePlan plan = planDto.toEntity();
            plan.setPlanId(planId);
            plan.setCreateBy(createBy);
            plan.setCreateTime(new Date());
            plan.setDeleted(0);
            
            // 计算下次维护时间
            Date nextMaintenanceTime = calculateNextMaintenanceTime(
                    plan.getCycleType(), plan.getCycleValue(), new Date());
            plan.setNextMaintenanceTime(nextMaintenanceTime);
            
            // 保存维护计划
            boolean success = this.save(plan);
            if (!success) {
                return false;
            }
            
            // 保存备品备件关联
            if (!CollectionUtils.isEmpty(planDto.getPartList())) {
                for (MaintenancePlanPartDto partDto : planDto.getPartList()) {
                    // 验证备品备件是否与资产关联
                    boolean isRelated = assetPartRelationService.checkRelationExists(
                            planDto.getAssetId(), partDto.getPartId());
                    if (!isRelated) {
                        throw new RuntimeException("备品备件[" + partDto.getPartName() + "]与所选资产无关联关系");
                    }
                    
                    MaintenancePlanPart planPart = new MaintenancePlanPart();
                    planPart.setRelationId(codeGenerator.generateCode("WHJHBJ"));
                    planPart.setPlanId(planId);
                    planPart.setPartId(partDto.getPartId());
                    planPart.setRequiredQuantity(partDto.getRequiredQuantity());
                    planPart.setRemark(partDto.getRemark());
                    planPart.setCreateBy(createBy);
                    planPart.setCreateTime(new Date());
                    planPart.setDeleted(0);
                    
                    maintenancePlanPartMapper.insert(planPart);
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("新增维护计划失败：", e);
            throw new RuntimeException("新增维护计划失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMaintenancePlan(MaintenancePlanDto planDto, String updateBy) {
        try {
            // 转换为实体类
            MaintenancePlan plan = planDto.toEntity();
            plan.setUpdateBy(updateBy);
            plan.setUpdateTime(new Date());
            
            // 重新计算下次维护时间
            Date nextMaintenanceTime = calculateNextMaintenanceTime(
                    plan.getCycleType(), plan.getCycleValue(), new Date());
            plan.setNextMaintenanceTime(nextMaintenanceTime);
            
            // 更新维护计划
            boolean success = this.updateById(plan);
            if (!success) {
                return false;
            }
            
            // 删除原有的备品备件关联
            maintenancePlanPartMapper.deleteByPlanId(plan.getPlanId(), updateBy);
            
            // 重新保存备品备件关联
            if (!CollectionUtils.isEmpty(planDto.getPartList())) {
                for (MaintenancePlanPartDto partDto : planDto.getPartList()) {
                    // 验证备品备件是否与资产关联
                    boolean isRelated = assetPartRelationService.checkRelationExists(
                            planDto.getAssetId(), partDto.getPartId());
                    if (!isRelated) {
                        throw new RuntimeException("备品备件[" + partDto.getPartName() + "]与所选资产无关联关系");
                    }
                    
                    MaintenancePlanPart planPart = new MaintenancePlanPart();
                    planPart.setRelationId(codeGenerator.generateCode("WHJHBJ"));
                    planPart.setPlanId(plan.getPlanId());
                    planPart.setPartId(partDto.getPartId());
                    planPart.setRequiredQuantity(partDto.getRequiredQuantity());
                    planPart.setRemark(partDto.getRemark());
                    planPart.setCreateBy(updateBy);
                    planPart.setCreateTime(new Date());
                    planPart.setDeleted(0);
                    
                    maintenancePlanPartMapper.insert(planPart);
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("修改维护计划失败：", e);
            throw new RuntimeException("修改维护计划失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMaintenancePlans(String[] planIds, String updateBy) {
        try {
            for (String planId : planIds) {
                // 逻辑删除维护计划
                MaintenancePlan plan = new MaintenancePlan();
                plan.setPlanId(planId);
                plan.setDeleted(1);
                plan.setUpdateBy(updateBy);
                plan.setUpdateTime(new Date());
                this.updateById(plan);
                
                // 删除备品备件关联
                maintenancePlanPartMapper.deleteByPlanId(planId, updateBy);
            }
            return true;
        } catch (Exception e) {
            log.error("删除维护计划失败：", e);
            throw new RuntimeException("删除维护计划失败：" + e.getMessage());
        }
    }
    
    @Override
    public IPage<MaintenancePlanVo> selectMaintenancePlanList(MaintenancePlanSearchRequest request) {
        Page<MaintenancePlanVo> page = new Page<>(request.getPageNum(), request.getPageSize());
        return maintenancePlanMapper.selectMaintenancePlanList(page, request);
    }
    
    @Override
    public MaintenancePlanVo selectMaintenancePlanById(String planId) {
        MaintenancePlanVo planVo = maintenancePlanMapper.selectMaintenancePlanById(planId);
        if (planVo != null) {
            // 查询关联的备品备件
            List<MaintenancePlanVo.MaintenancePlanPartVo> partList = 
                    maintenancePlanPartMapper.selectPartsByPlanId(planId);
            planVo.setPartList(partList);
        }
        return planVo;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMaintenancePlanStatus(String planId, Integer status, String updateBy) {
        try {
            MaintenancePlan plan = new MaintenancePlan();
            plan.setPlanId(planId);
            plan.setStatus(status);
            plan.setUpdateBy(updateBy);
            plan.setUpdateTime(new Date());
            return this.updateById(plan);
        } catch (Exception e) {
            log.error("更新维护计划状态失败：", e);
            return false;
        }
    }
    
    @Override
    public List<MaintenancePlanVo> selectUpcomingMaintenancePlans(Integer days) {
        return maintenancePlanMapper.selectUpcomingMaintenancePlans(days);
    }
    
    @Override
    public List<MaintenancePlanVo> selectOverdueMaintenancePlans() {
        return maintenancePlanMapper.selectOverdueMaintenancePlans();
    }
    
    @Override
    public List<MaintenancePlanVo> selectMaintenancePlansByAssetId(String assetId) {
        return maintenancePlanMapper.selectMaintenancePlansByAssetId(assetId);
    }
    
    @Override
    public List<MaintenancePlanVo> selectMaintenancePlansByResponsible(Integer responsibleType, Long responsibleId) {
        return maintenancePlanMapper.selectMaintenancePlansByResponsible(responsibleType, responsibleId);
    }
    
    @Override
    public Date calculateNextMaintenanceTime(Integer cycleType, Integer cycleValue, Date baseTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(baseTime);
        
        switch (cycleType) {
            case 1: // 按天
                calendar.add(Calendar.DAY_OF_MONTH, cycleValue);
                break;
            case 2: // 按周
                calendar.add(Calendar.WEEK_OF_YEAR, cycleValue);
                break;
            case 3: // 按月
                calendar.add(Calendar.MONTH, cycleValue);
                break;
            case 4: // 按年
                calendar.add(Calendar.YEAR, cycleValue);
                break;
            default:
                throw new IllegalArgumentException("不支持的周期类型：" + cycleType);
        }
        
        return calendar.getTime();
    }
} 