package com.jingfang.asset_part_relation.module.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 资产备品备件关联查询请求
 */
@Data
public class AssetPartRelationSearchRequest implements Serializable {
    
    /**
     * 当前页码
     */
    private Integer pageNum = 1;
    
    /**
     * 每页大小
     */
    private Integer pageSize = 10;
    
    /**
     * 资产ID
     */
    private String assetId;
    
    /**
     * 资产名称
     */
    private String assetName;
    
    /**
     * 备品备件ID
     */
    private String partId;
    
    /**
     * 备品备件名称
     */
    private String partName;
    
    /**
     * 备品备件编码
     */
    private String partCode;
    
    /**
     * 关联类型(1-必需, 2-推荐, 3-可选)
     */
    private Integer relationType;
    
    /**
     * 备件分类(1-关键, 2-常用, 3-次要)
     */
    private Integer partCategory;
    
    /**
     * 库存状态(1-正常, 2-不足, 3-过剩)
     */
    private Integer stockStatus;
    
    private static final long serialVersionUID = 1L;
} 