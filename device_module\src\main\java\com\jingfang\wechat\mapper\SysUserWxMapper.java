package com.jingfang.wechat.mapper;


import com.jingfang.wechat.module.entity.SysUserWx;

/**
 * 用户微信关联 数据层
 */
public interface SysUserWxMapper {
    /**
     * 通过微信用户ID查询关联信息
     *
     * @param wxUserId 微信用户ID
     * @return 关联信息
     */
    public SysUserWx selectUserWxByWxUserId(Long wxUserId);

    /**
     * 通过用户ID和微信用户ID查询关联信息
     *
     * @param userId 用户ID
     * @param wxUserId 微信用户ID
     * @return 关联信息
     */
    public SysUserWx selectUserWxByUserIdAndWxUserId(Long userId, Long wxUserId);

    /**
     * 新增用户微信关联
     *
     * @param userWx 用户微信关联信息
     * @return 结果
     */
    public int insertUserWx(SysUserWx userWx);

    /**
     * 修改用户微信关联
     *
     * @param userWx 用户微信关联信息
     * @return 结果
     */
    public int updateUserWx(SysUserWx userWx);
}
