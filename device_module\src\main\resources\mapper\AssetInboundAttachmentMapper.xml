<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.asset_inbound.mapper.AssetInboundAttachmentMapper">

    <resultMap id="BaseResultMap" type="com.jingfang.asset_inbound.module.entity.AssetInboundAttachment">
            <result property="inboundId" column="inbound_id" />
            <result property="fileName" column="file_name" />
            <result property="fileType" column="file_type" />
            <result property="storagePath" column="storage_path" />
    </resultMap>

    <sql id="Base_Column_List">
        inbound_id,file_name,file_type,storage_path
    </sql>
</mapper>
