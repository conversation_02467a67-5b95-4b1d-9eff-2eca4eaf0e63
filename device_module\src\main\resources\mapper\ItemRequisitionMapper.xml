<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.wh_item_requisition.mapper.ItemRequisitionMapper">

    <!-- 分页查询领用单列表 -->
    <select id="selectRequisitionList" resultType="com.jingfang.wh_item_requisition.module.vo.ItemRequisitionVo">
        SELECT 
            t1.requisition_id,
            t1.business_date,
            t1.applicant_id,
            u_applicant.nick_name as applicant_name,
            t1.dept_id,
            d1.dept_name,
            t1.requisition_purpose,
            t1.status,
            CASE t1.status 
                WHEN 1 THEN '草稿' 
                WHEN 2 THEN '待确认' 
                WHEN 3 THEN '待审核' 
                WHEN 4 THEN '已通过' 
                WHEN 5 THEN '已退回' 
                WHEN 6 THEN '已完成' 
                ELSE '未知' 
            END as status_name,
            t1.creator_id,
            u_creator.nick_name as creator_name,
            t1.create_time,
            t1.handler_id,
            u_handler.nick_name as handler_name,
            t1.handle_time,
            t1.auditor_id,
            u_auditor.nick_name as auditor_name,
            t1.audit_time,
            COALESCE(t2.detail_count, 0) as detail_count,
            COALESCE(t2.total_requisition_quantity, 0) as total_requisition_quantity,
            COALESCE(t2.total_approved_quantity, 0) as total_approved_quantity,
            COALESCE(t2.total_actual_quantity, 0) as total_actual_quantity
        FROM 
            item_requisition t1
            LEFT JOIN sys_user u_applicant ON t1.applicant_id = u_applicant.user_id
            LEFT JOIN sys_dept d1 ON t1.dept_id = d1.dept_id
            LEFT JOIN sys_user u_creator ON t1.creator_id = u_creator.user_id
            LEFT JOIN sys_user u_handler ON t1.handler_id = u_handler.user_id
            LEFT JOIN sys_user u_auditor ON t1.auditor_id = u_auditor.user_id
            LEFT JOIN (
                SELECT 
                    requisition_id,
                    COUNT(*) as detail_count,
                    SUM(requisition_quantity) as total_requisition_quantity,
                    SUM(COALESCE(approved_quantity, 0)) as total_approved_quantity,
                    SUM(COALESCE(actual_quantity, 0)) as total_actual_quantity
                FROM item_requisition_detail 
                GROUP BY requisition_id
            ) t2 ON t1.requisition_id = t2.requisition_id
        WHERE 
            t1.del_flag = '0'
        <if test="request.requisitionId != null and request.requisitionId != ''">
            AND t1.requisition_id = #{request.requisitionId}
        </if>
        <if test="request.applicantId != null">
            AND t1.applicant_id = #{request.applicantId}
        </if>
        <if test="request.applicantName != null and request.applicantName != ''">
            AND u_applicant.nick_name LIKE CONCAT('%', #{request.applicantName}, '%')
        </if>
        <if test="request.deptId != null">
            AND t1.dept_id = #{request.deptId}
        </if>
        <if test="request.deptName != null and request.deptName != ''">
            AND d1.dept_name LIKE CONCAT('%', #{request.deptName}, '%')
        </if>
        <if test="request.status != null">
            AND t1.status = #{request.status}
        </if>
        <if test="request.creatorId != null">
            AND t1.creator_id = #{request.creatorId}
        </if>
        <if test="request.handlerId != null">
            AND t1.handler_id = #{request.handlerId}
        </if>
        <if test="request.auditorId != null">
            AND t1.auditor_id = #{request.auditorId}
        </if>
        <if test="request.businessDateStart != null">
            AND t1.business_date >= #{request.businessDateStart}
        </if>
        <if test="request.businessDateEnd != null">
            AND t1.business_date &lt;= #{request.businessDateEnd}
        </if>
        <if test="request.createTimeStart != null">
            AND t1.create_time >= #{request.createTimeStart}
        </if>
        <if test="request.createTimeEnd != null">
            AND t1.create_time &lt;= #{request.createTimeEnd}
        </if>
        <if test="request.itemId != null and request.itemId != ''">
            AND EXISTS (
                SELECT 1 FROM item_requisition_detail d 
                WHERE d.requisition_id = t1.requisition_id 
                AND d.item_id = #{request.itemId}
            )
        </if>
        <if test="request.itemName != null and request.itemName != ''">
            AND EXISTS (
                SELECT 1 FROM item_requisition_detail d 
                INNER JOIN item_base_info i ON d.item_id = i.item_id
                WHERE d.requisition_id = t1.requisition_id 
                AND i.item_name LIKE CONCAT('%', #{request.itemName}, '%')
            )
        </if>
        <if test="request.warehouseId != null">
            AND EXISTS (
                SELECT 1 FROM item_requisition_detail d 
                WHERE d.requisition_id = t1.requisition_id 
                AND d.warehouse_id = #{request.warehouseId}
            )
        </if>
        ORDER BY t1.create_time DESC
    </select>

    <!-- 查询领用单详情 -->
    <select id="selectRequisitionDetail" resultType="com.jingfang.wh_item_requisition.module.vo.ItemRequisitionDetailVo">
        SELECT 
            t1.requisition_id,
            t1.business_date,
            t1.applicant_id,
            u_applicant.nick_name as applicant_name,
            t1.dept_id,
            d1.dept_name,
            t1.requisition_purpose,
            t1.status,
            CASE t1.status 
                WHEN 1 THEN '草稿' 
                WHEN 2 THEN '待确认' 
                WHEN 3 THEN '待审核' 
                WHEN 4 THEN '已通过' 
                WHEN 5 THEN '已退回' 
                WHEN 6 THEN '已完成' 
                ELSE '未知' 
            END as status_name,
            t1.creator_id,
            u_creator.nick_name as creator_name,
            t1.create_time,
            t1.handler_id,
            u_handler.nick_name as handler_name,
            t1.handle_time,
            t1.handle_remark,
            t1.auditor_id,
            u_auditor.nick_name as auditor_name,
            t1.audit_time,
            t1.audit_remark,
            COALESCE(t2.detail_count, 0) as detail_count,
            COALESCE(t2.total_requisition_quantity, 0) as total_requisition_quantity,
            COALESCE(t2.total_approved_quantity, 0) as total_approved_quantity,
            COALESCE(t2.total_actual_quantity, 0) as total_actual_quantity
        FROM 
            item_requisition t1
            LEFT JOIN sys_user u_applicant ON t1.applicant_id = u_applicant.user_id
            LEFT JOIN sys_dept d1 ON t1.dept_id = d1.dept_id
            LEFT JOIN sys_user u_creator ON t1.creator_id = u_creator.user_id
            LEFT JOIN sys_user u_handler ON t1.handler_id = u_handler.user_id
            LEFT JOIN sys_user u_auditor ON t1.auditor_id = u_auditor.user_id
            LEFT JOIN (
                SELECT 
                    requisition_id,
                    COUNT(*) as detail_count,
                    SUM(requisition_quantity) as total_requisition_quantity,
                    SUM(COALESCE(approved_quantity, 0)) as total_approved_quantity,
                    SUM(COALESCE(actual_quantity, 0)) as total_actual_quantity
                FROM item_requisition_detail 
                WHERE requisition_id = #{requisitionId}
                GROUP BY requisition_id
            ) t2 ON t1.requisition_id = t2.requisition_id
        WHERE 
            t1.requisition_id = #{requisitionId}
            AND t1.del_flag = '0'
    </select>

</mapper> 