package com.jingfang.device_module.controller;

import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.device_module.mqtt.service.MqttDevicePropertyService;
import com.jingfang.device_module.mqtt.dto.DevicePropertyResponse;
import com.jingfang.device_module.mqtt.dto.DevicePropertyWriteResponse;
import com.jingfang.device_module.service.DeviceParamService;
import com.jingfang.device_module.module.vo.DeviceParamVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 设备属性查询测试控制器
 */
@Slf4j
@RestController
@RequestMapping("/device/property/test")
public class DevicePropertyTestController extends BaseController {

    @Resource
    private MqttDevicePropertyService mqttDevicePropertyService;

    @Resource
    private DeviceParamService deviceParamService;

    /**
     * 测试MQTT设备属性查询
     */
    @GetMapping("/mqtt")
    public AjaxResult testMqttDeviceProperty(@RequestParam String deviceIp, 
                                           @RequestParam String properties) {
        try {
            log.info("开始测试MQTT设备属性查询，设备IP: {}, 属性: {}", deviceIp, properties);
            
            List<String> propertyNames = Arrays.asList(properties.split(","));
            
            CompletableFuture<DevicePropertyResponse> future = mqttDevicePropertyService
                    .queryDeviceProperties(deviceIp, propertyNames);
            
            DevicePropertyResponse response = future.get(20, java.util.concurrent.TimeUnit.SECONDS);
            
            return AjaxResult.success("MQTT设备属性查询成功", response);
        } catch (Exception e) {
            log.error("MQTT设备属性查询测试失败", e);
            return AjaxResult.error("MQTT设备属性查询失败: " + e.getMessage());
        }
    }

    /**
     * 测试设备运行参数获取（新的MQTT方式）
     */
    @GetMapping("/normal-params")
    public AjaxResult testDeviceNormalParams(@RequestParam Long deviceId) {
        try {
            log.info("开始测试设备运行参数获取，设备ID: {}", deviceId);
            
            List<DeviceParamVo> params = deviceParamService.showDeviceParamTypeOne(deviceId);
            
            return AjaxResult.success("设备运行参数获取成功", params);
        } catch (Exception e) {
            log.error("设备运行参数获取测试失败", e);
            return AjaxResult.error("设备运行参数获取失败: " + e.getMessage());
        }
    }

    /**
     * 测试MQTT连接状态
     */
    @GetMapping("/mqtt-connection")
    public AjaxResult testMqttConnection() {
        try {
            boolean isConnected = mqttDevicePropertyService.testMqttConnection();
            if (isConnected) {
                return AjaxResult.success("MQTT属性查询连接状态正常");
            } else {
                return AjaxResult.error("MQTT属性查询连接状态异常");
            }
        } catch (Exception e) {
            log.error("测试MQTT属性查询连接状态失败", e);
            return AjaxResult.error("测试MQTT属性查询连接状态失败: " + e.getMessage());
        }
    }

    /**
     * 模拟设备属性查询（用于测试JSON格式）
     */
    @PostMapping("/simulate")
    public AjaxResult simulateDevicePropertyQuery(@RequestBody SimulateRequest request) {
        try {
            log.info("模拟设备属性查询，设备IP: {}, 属性数量: {}", 
                    request.getDeviceIp(), request.getPropertyNames().size());
            
            CompletableFuture<DevicePropertyResponse> future = mqttDevicePropertyService
                    .queryDeviceProperties(request.getDeviceIp(), request.getPropertyNames());
            
            DevicePropertyResponse response = future.get(20, java.util.concurrent.TimeUnit.SECONDS);
            
            return AjaxResult.success("模拟设备属性查询成功", response);
        } catch (Exception e) {
            log.error("模拟设备属性查询失败", e);
            return AjaxResult.error("模拟设备属性查询失败: " + e.getMessage());
        }
    }

    /**
     * 测试MQTT设备属性写入
     */
    @PostMapping("/write")
    public AjaxResult testMqttDevicePropertyWrite(@RequestParam String deviceIp,
                                                @RequestParam String propertyName,
                                                @RequestParam String propertyValue) {
        try {
            log.info("开始测试MQTT设备属性写入，设备IP: {}, 属性: {}={}", deviceIp, propertyName, propertyValue);

            CompletableFuture<DevicePropertyWriteResponse> future = mqttDevicePropertyService
                    .writeDeviceProperty(deviceIp, propertyName, propertyValue);

            DevicePropertyWriteResponse response = future.get(20, java.util.concurrent.TimeUnit.SECONDS);

            return AjaxResult.success("MQTT设备属性写入成功", response);
        } catch (Exception e) {
            log.error("MQTT设备属性写入测试失败", e);
            return AjaxResult.error("MQTT设备属性写入失败: " + e.getMessage());
        }
    }

    /**
     * 测试设备参数写入（通过Service）
     */
    @PostMapping("/write-param")
    public AjaxResult testDeviceParamWrite(@RequestParam Long deviceId,
                                         @RequestParam String paramName,
                                         @RequestParam String paramValue) {
        try {
            log.info("开始测试设备参数写入，设备ID: {}, 参数: {}={}", deviceId, paramName, paramValue);

            boolean success = deviceParamService.writeDeviceParam(deviceId, paramName, paramValue);

            if (success) {
                return AjaxResult.success("设备参数写入成功");
            } else {
                return AjaxResult.error("设备参数写入失败");
            }
        } catch (Exception e) {
            log.error("设备参数写入测试失败", e);
            return AjaxResult.error("设备参数写入失败: " + e.getMessage());
        }
    }

    /**
     * 模拟请求参数
     */
    public static class SimulateRequest {
        private String deviceIp;
        private List<String> propertyNames;

        public String getDeviceIp() {
            return deviceIp;
        }

        public void setDeviceIp(String deviceIp) {
            this.deviceIp = deviceIp;
        }

        public List<String> getPropertyNames() {
            return propertyNames;
        }

        public void setPropertyNames(List<String> propertyNames) {
            this.propertyNames = propertyNames;
        }
    }
}
