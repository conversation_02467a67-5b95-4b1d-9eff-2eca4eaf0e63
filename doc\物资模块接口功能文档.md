# 物资模块接口功能文档

## 概述
物资模块提供了完整的物资管理功能，包括物品信息管理、库存管理、入库管理、出库管理、领用管理和库存盘点等核心功能。本文档详细描述了各个Controller的接口功能，便于前端开发者进行对接。

## 1. 物品信息管理 (ItemController)

**基础路径**: `/item`

### 1.1 物品基础操作

#### 新增物品
- **接口**: `POST /item/add`
- **功能**: 新增物品（包含库存信息，实际为物品单体入库）
- **权限**: 无特殊权限要求
- **参数**: ItemDto（物品信息）
- **返回**:
```json
{
  "code": 200,
  "msg": "新增物品成功",
  "data": null
}
```

#### 物品登记
- **接口**: `POST /item/register`
- **功能**: 物品登记（只登记基本信息和属性，不包含库存信息）
- **权限**: 无特殊权限要求
- **参数**: ItemRegistrationDto（物品登记信息）
- **返回**:
```json
{
  "code": 200,
  "msg": "物品登记成功",
  "data": null
}
```

#### 修改物品
- **接口**: `PUT /item`
- **功能**: 修改物品信息
- **权限**: `item:edit`
- **参数**: ItemDto（物品信息）
- **返回**:
```json
{
  "code": 200,
  "msg": "修改物品成功",
  "data": null
}
```

#### 删除物品
- **接口**: `DELETE /item/{itemIds}`
- **功能**: 删除物品（支持批量删除）
- **权限**: `item:remove`
- **参数**: itemIds（物品ID字符串，多个以逗号分隔）
- **返回**:
```json
{
  "code": 200,
  "msg": "删除物品成功",
  "data": null
}
```

#### 获取物品详情
- **接口**: `GET /item/{itemId}`
- **功能**: 获取物品详细信息
- **权限**: `item:query`
- **参数**: itemId（物品ID）
- **返回**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "itemId": "ITEM001",
    "itemName": "螺丝刀",
    "itemCode": "SD001",
    "itemType": 2,
    "itemTypeName": "备品备件",
    "specModel": "十字头 6mm",
    "unit": "把",
    "imageUrl": "http://example.com/image.jpg",
    "totalQuantity": 100.00,
    "safetyStock": 10.00,
    "hasExpiry": 0,
    "expiryPeriod": null,
    "productionDate": null,
    "storageCondition": null,
    "applicableDevice": "设备A型号",
    "partCategory": 1,
    "partCategoryName": "关键",
    "replacementCycle": 30,
    "maintenanceHistory": "维修记录001",
    "remark": "重要备件",
    "createTime": "2024-01-01 10:00:00",
    "createBy": "admin",
    "inventoryList": [
      {
        "inventoryId": "INV001",
        "itemId": "ITEM001",
        "warehouseId": 1,
        "warehouseName": "主仓库",
        "currentQuantity": 50.00,
        "safetyStock": 5.00,
        "stockStatus": 1,
        "stockStatusName": "正常",
        "shelfLocation": "A-01-01"
      },
      {
        "inventoryId": "INV002",
        "itemId": "ITEM001",
        "warehouseId": 2,
        "warehouseName": "备用仓库",
        "currentQuantity": 50.00,
        "safetyStock": 5.00,
        "stockStatus": 1,
        "stockStatusName": "正常",
        "shelfLocation": "B-02-03"
      }
    ]
  }
}
```

### 1.2 物品查询

#### 物品列表
- **接口**: `POST /item/list`
- **功能**: 分页查询物品列表
- **权限**: `item:list`
- **参数**: ItemSearchRequest（查询条件）
- **返回**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "records": [
      {
        "itemId": "ITEM001",
        "itemName": "螺丝刀",
        "itemCode": "SD001",
        "itemType": 2,
        "specModel": "十字头 6mm",
        "unit": "把",
        "totalQuantity": 100.00,
        "safetyStock": 10.00,
        "minStockStatus": 1,
        "stockStatusName": "正常",
        "warehouseCount": 2,
        "warehouseIds": [1, 2],
        "warehouseId": 1,
        "warehouseName": "主仓库",
        "shelfLocation": "A-01-01",
        "imageUrl": "http://example.com/image.jpg",
        "remark": "重要备件"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

#### 物品选择列表
- **接口**: `POST /item/select/list`
- **功能**: 物品选择列表（用于领用等业务场景，包含仓库和货架位置信息）
- **权限**: `item:list`
- **参数**: ItemSearchRequest（查询条件）
- **返回**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "records": [
      {
        "itemId": "ITEM001",
        "itemName": "螺丝刀",
        "itemCode": "SD001",
        "itemType": 2,
        "specModel": "十字头 6mm",
        "unit": "把",
        "totalQuantity": 100.00,
        "safetyStock": 10.00,
        "minStockStatus": 1,
        "stockStatusName": "正常",
        "warehouseCount": 2,
        "warehouseIds": [1, 2],
        "warehouseId": 1,
        "warehouseName": "主仓库",
        "shelfLocation": "A-01-01",
        "imageUrl": "http://example.com/image.jpg",
        "remark": "重要备件"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 1.3 库存相关操作

#### 更新物品库存
- **接口**: `PUT /item/stock`
- **功能**: 更新物品库存数量
- **权限**: `item:stock:update`
- **参数**:
  - itemId（物品ID）
  - warehouseId（仓库ID）
  - quantity（变动数量）
  - operationType（操作类型）
  - operationId（关联单据ID，可选）
  - remark（备注，可选）
  - shelfLocation（货架位置，可选）
- **返回**:
```json
{
  "code": 200,
  "msg": "更新库存成功",
  "data": null
}
```

#### 物品库存统计
- **接口**: `POST /item/stock/statistics`
- **功能**: 查询物品库存统计信息
- **权限**: `item:stock:statistics`
- **参数**: ItemStockStatisticsRequest（查询条件）
- **返回**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "records": [
      {
        "itemId": "ITEM001",
        "itemName": "螺丝刀",
        "itemCode": "SD001",
        "itemType": 2,
        "specModel": "十字头 6mm",
        "unit": "把",
        "warehouseId": 1,
        "warehouseName": "主仓库",
        "currentQuantity": 50.00,
        "safetyStock": 5.00,
        "stockStatus": 1,
        "stockStatusName": "正常",
        "totalInbound": 100.00,
        "totalOutbound": 50.00,
        "lastInboundDate": "2024-01-01",
        "lastOutboundDate": "2024-01-05"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

#### 更新企业级安全库存
- **接口**: `PUT /item/safety-stock`
- **功能**: 更新物品企业级安全库存
- **权限**: `item:safety:stock:update`
- **参数**:
  - itemId（物品ID）
  - safetyStock（安全库存数量）
- **返回**:
```json
{
  "code": 200,
  "msg": "更新企业级安全库存成功",
  "data": null
}
```

#### 同步物品总库存
- **接口**: `POST /item/stock/sync`
- **功能**: 同步物品总库存（可指定单个物品或同步所有物品）
- **权限**: `item:stock:sync`
- **参数**: itemId（物品ID，可选）
- **返回**:
```json
// 同步单个物品时
{
  "code": 200,
  "msg": "同步物品总库存成功，总库存：100.00",
  "data": null
}

// 同步所有物品时
{
  "code": 200,
  "msg": "同步所有物品总库存完成，成功同步：50 个物品",
  "data": null
}
```

#### 库存下限告警
- **接口**: `GET /item/stock/alert`
- **功能**: 查询库存不足的物品（总库存低于企业级安全库存）
- **权限**: `item:stock:alert`
- **参数**: 无
- **返回**:
```json
{
  "code": 200,
  "msg": "查询库存告警成功",
  "data": {
    "total": 2,
    "items": [
      {
        "itemId": "ITEM002",
        "itemName": "扳手",
        "itemCode": "BS001",
        "itemType": 2,
        "specModel": "活动扳手 200mm",
        "unit": "把",
        "totalQuantity": 3.00,
        "safetyStock": 10.00,
        "minStockStatus": 2,
        "stockStatusName": "不足",
        "warehouseCount": 1,
        "warehouseIds": [1],
        "warehouseId": 1,
        "warehouseName": "主仓库",
        "shelfLocation": "A-02-01"
      }
    ]
  }
}
```

## 2. 库存管理 (ItemStockController)

**基础路径**: `/item/stock`

#### 库存查看
- **接口**: `GET /item/stock/view`
- **功能**: 库存查看（支持按仓库和按物品两种展示模式）
- **权限**: `item:stock:view`
- **参数**: ItemStockViewRequest（查询条件）
- **返回**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "records": [
      {
        "itemId": "ITEM001",
        "itemName": "螺丝刀",
        "itemCode": "SD001",
        "itemType": 2,
        "specModel": "十字头 6mm",
        "unit": "把",
        "warehouseId": 1,
        "warehouseName": "主仓库",
        "currentQuantity": 50.00,
        "safetyStock": 5.00,
        "stockStatus": 1,
        "stockStatusName": "正常",
        "shelfLocation": "A-01-01",
        "stockDetails": [
          {
            "warehouseId": 1,
            "warehouseName": "主仓库",
            "currentQuantity": 30.00,
            "safetyStock": 3.00,
            "stockStatus": 1,
            "stockStatusName": "正常",
            "shelfLocation": "A-01-01"
          },
          {
            "warehouseId": 2,
            "warehouseName": "备用仓库",
            "currentQuantity": 20.00,
            "safetyStock": 2.00,
            "stockStatus": 1,
            "stockStatusName": "正常",
            "shelfLocation": "B-02-03"
          }
        ]
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

#### 调整物品库存
- **接口**: `PUT /item/stock/adjust`
- **功能**: 调整物品库存数量
- **权限**: `item:stock:adjust`
- **参数**: ItemStockAdjustmentDto（库存调整信息）
- **返回**:
```json
{
  "code": 200,
  "msg": "库存调整成功",
  "data": null
}
```
- **日志**: 记录库存调整操作日志

#### 获取当前库存
- **接口**: `GET /item/stock/current`
- **功能**: 获取物品在指定仓库的当前库存信息
- **权限**: `item:stock:view`
- **参数**:
  - itemId（物品ID）
  - warehouseId（仓库ID）
- **返回**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "itemId": "ITEM001",
    "itemName": "螺丝刀",
    "itemCode": "SD001",
    "itemType": 2,
    "specModel": "十字头 6mm",
    "unit": "把",
    "warehouseId": 1,
    "warehouseName": "主仓库",
    "currentQuantity": 50.00,
    "safetyStock": 5.00,
    "stockStatus": 1,
    "stockStatusName": "正常",
    "shelfLocation": "A-01-01"
  }
}
```

## 3. 入库管理 (ItemInboundController)

**基础路径**: `/item/inbound`

### 3.1 入库单基础操作

#### 新增入库单
- **接口**: `POST /item/inbound/add`
- **功能**: 新增物品入库单
- **权限**: `item:inbound:add`
- **参数**: ItemInboundDto（入库单信息）
- **返回**:
```json
{
  "code": 200,
  "msg": "新增入库单成功",
  "data": "WPRK202401010001"
}
```
- **说明**: 自动生成入库单号（WPRK前缀）

#### 编辑入库单
- **接口**: `PUT /item/inbound/{inboundId}`
- **功能**: 编辑物品入库单
- **权限**: `item:inbound:edit`
- **参数**:
  - inboundId（入库单ID）
  - ItemInboundDto（入库单信息）
- **返回**:
```json
{
  "code": 200,
  "msg": "编辑入库单成功",
  "data": null
}
```

#### 获取入库单详情
- **接口**: `GET /item/inbound/{inboundId}`
- **功能**: 获取入库单详细信息
- **权限**: `item:inbound:query`
- **参数**: inboundId（入库单ID）
- **返回**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "inboundId": "WPRK202401010001",
    "businessDate": "2024-01-01",
    "supplierName": "供应商A",
    "inboundType": 1,
    "inboundTypeName": "采购入库",
    "status": 1,
    "statusName": "草稿",
    "creatorId": 1,
    "creatorName": "张三",
    "createTime": "2024-01-01 10:00:00",
    "handlerId": 2,
    "handlerName": "李四",
    "handleTime": null,
    "handleRemark": null,
    "auditorId": null,
    "auditorName": null,
    "auditTime": null,
    "auditRemark": null,
    "updateTime": "2024-01-01 10:00:00",
    "inboundDescription": "采购入库说明",
    "details": [
      {
        "detailId": 1,
        "inboundId": "WPRK202401010001",
        "itemId": "ITEM001",
        "itemName": "螺丝刀",
        "itemCode": "SD001",
        "specModel": "十字头 6mm",
        "unit": "把",
        "batchNo": "BATCH001",
        "productionDate": "2024-01-01",
        "expiryDate": "2024-12-31",
        "quantity": 10.00,
        "unitPrice": 15.00,
        "amount": 150.00,
        "warehouseId": 1,
        "warehouseName": "主仓库",
        "shelfLocation": "A-01-01",
        "remark": "新采购"
      }
    ],
    "logs": [
      {
        "logId": 1,
        "inboundId": "WPRK202401010001",
        "operationType": "CREATE",
        "operationDesc": "创建入库单",
        "operatorId": 1,
        "operatorName": "张三",
        "operationTime": "2024-01-01 10:00:00",
        "remark": "初始创建"
      }
    ]
  }
}
```

#### 分页查询入库单列表
- **接口**: `POST /item/inbound/list`
- **功能**: 分页查询入库单列表
- **权限**: `item:inbound:list`
- **参数**: ItemInboundSearchRequest（查询条件）
- **返回**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "records": [
      {
        "inboundId": "WPRK202401010001",
        "businessDate": "2024-01-01",
        "supplierName": "供应商A",
        "inboundType": 1,
        "inboundTypeName": "采购入库",
        "status": 1,
        "statusName": "草稿",
        "creatorId": 1,
        "creatorName": "张三",
        "createTime": "2024-01-01 10:00:00",
        "handlerId": 2,
        "handlerName": "李四",
        "handleTime": null,
        "handleRemark": null,
        "auditorId": null,
        "auditorName": null,
        "auditTime": null,
        "auditRemark": null,
        "updateTime": "2024-01-01 10:00:00",
        "inboundDescription": "采购入库说明"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

#### 删除入库单
- **接口**: `DELETE /item/inbound/{inboundIds}`
- **功能**: 删除入库单（支持批量删除）
- **权限**: `item:inbound:remove`
- **参数**: inboundIds（入库单ID数组）
- **返回**:
```json
{
  "code": 200,
  "msg": "删除成功",
  "data": null
}
```

### 3.2 入库单流程操作

#### 提交入库单
- **接口**: `PUT /item/inbound/submit/{inboundId}`
- **功能**: 提交入库单待确认
- **权限**: `item:inbound:submit`
- **参数**: inboundId（入库单ID）
- **返回**:
```json
{
  "code": 200,
  "msg": "提交成功，等待经手人确认",
  "data": null
}
```

#### 经手人确认入库单
- **接口**: `PUT /item/inbound/handle/{inboundId}`
- **功能**: 经手人确认入库单
- **权限**: `item:inbound:handle`
- **参数**:
  - inboundId（入库单ID）
  - remark（备注，可选）
- **返回**:
```json
{
  "code": 200,
  "msg": "确认操作成功",
  "data": null
}
```

#### 审核入库单
- **接口**: `PUT /item/inbound/audit/{inboundId}/{status}`
- **功能**: 审核入库单
- **权限**: `item:inbound:audit`
- **参数**:
  - inboundId（入库单ID）
  - status（审核状态）
  - remark（审核备注，可选）
- **返回**:
```json
{
  "code": 200,
  "msg": "审核操作成功",
  "data": null
}
```

## 4. 出库管理 (ItemOutboundController)

**基础路径**: `/item/outbound`

### 4.1 出库单基础操作

#### 新增出库单
- **接口**: `POST /item/outbound/add`
- **功能**: 新增物品出库单
- **权限**: `item:outbound:add`
- **参数**: ItemOutboundDto（出库单信息）
- **返回**:
```json
{
  "code": 200,
  "msg": "新增出库单成功",
  "data": "WPCK202401010001"
}
```
- **说明**: 自动生成出库单号（WPCK前缀）

#### 编辑出库单
- **接口**: `PUT /item/outbound/{outboundId}`
- **功能**: 编辑物品出库单
- **权限**: `item:outbound:edit`
- **参数**:
  - outboundId（出库单ID）
  - ItemOutboundDto（出库单信息）
- **返回**:
```json
{
  "code": 200,
  "msg": "编辑出库单成功",
  "data": null
}
```

#### 获取出库单详情
- **接口**: `GET /item/outbound/{outboundId}`
- **功能**: 获取出库单详细信息
- **权限**: `item:outbound:query`
- **参数**: outboundId（出库单ID）
- **返回**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "outboundId": "WPCK202401010001",
    "businessDate": "2024-01-01",
    "recipientName": "王五",
    "recipientDept": "生产部",
    "outboundType": 1,
    "outboundTypeName": "正常出库",
    "status": 1,
    "statusName": "草稿",
    "creatorId": 1,
    "creatorName": "张三",
    "createTime": "2024-01-01 14:00:00",
    "handlerId": 2,
    "handlerName": "李四",
    "handleTime": null,
    "handleRemark": null,
    "auditorId": null,
    "auditorName": null,
    "auditTime": null,
    "auditRemark": null,
    "updateTime": "2024-01-01 14:00:00",
    "outboundDescription": "生产领用",
    "details": [
      {
        "detailId": 1,
        "outboundId": "WPCK202401010001",
        "itemId": "ITEM001",
        "itemName": "螺丝刀",
        "itemCode": "SD001",
        "specModel": "十字头 6mm",
        "unit": "把",
        "batchNo": "BATCH001",
        "productionDate": "2024-01-01",
        "expiryDate": "2024-12-31",
        "quantity": 2.00,
        "unitPrice": 15.00,
        "amount": 30.00,
        "warehouseId": 1,
        "warehouseName": "主仓库",
        "shelfLocation": "A-01-01",
        "remark": "生产使用"
      }
    ],
    "logs": [
      {
        "logId": 1,
        "outboundId": "WPCK202401010001",
        "operationType": "CREATE",
        "operationDesc": "创建出库单",
        "operatorId": 1,
        "operatorName": "张三",
        "operationTime": "2024-01-01 14:00:00",
        "remark": "初始创建"
      }
    ]
  }
}
```

#### 分页查询出库单列表
- **接口**: `POST /item/outbound/list`
- **功能**: 分页查询出库单列表
- **权限**: `item:outbound:list`
- **参数**: ItemOutboundSearchRequest（查询条件）
- **返回**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "records": [
      {
        "outboundId": "WPCK202401010001",
        "businessDate": "2024-01-01",
        "recipientName": "王五",
        "recipientDept": "生产部",
        "outboundType": 1,
        "outboundTypeName": "正常出库",
        "status": 1,
        "statusName": "草稿",
        "creatorId": 1,
        "creatorName": "张三",
        "createTime": "2024-01-01 14:00:00",
        "handlerId": 2,
        "handlerName": "李四",
        "handleTime": null,
        "handleRemark": null,
        "auditorId": null,
        "auditorName": null,
        "auditTime": null,
        "auditRemark": null,
        "updateTime": "2024-01-01 14:00:00",
        "outboundDescription": "生产领用"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

#### 删除出库单
- **接口**: `DELETE /item/outbound/{outboundIds}`
- **功能**: 删除出库单（支持批量删除）
- **权限**: `item:outbound:remove`
- **参数**: outboundIds（出库单ID数组）
- **返回**:
```json
{
  "code": 200,
  "msg": "删除成功",
  "data": null
}
```

### 4.2 出库单流程操作

#### 提交出库单
- **接口**: `PUT /item/outbound/submit/{outboundId}`
- **功能**: 提交出库单待确认
- **权限**: `item:outbound:submit`
- **参数**: outboundId（出库单ID）
- **返回**:
```json
{
  "code": 200,
  "msg": "提交成功，等待经手人确认",
  "data": null
}
```

#### 经手人确认出库单
- **接口**: `PUT /item/outbound/handle/{outboundId}`
- **功能**: 经手人确认出库单
- **权限**: `item:outbound:handle`
- **参数**:
  - outboundId（出库单ID）
  - remark（备注，可选）
- **返回**:
```json
{
  "code": 200,
  "msg": "确认操作成功",
  "data": null
}
```

#### 审核出库单
- **接口**: `PUT /item/outbound/audit/{outboundId}/{status}`
- **功能**: 审核出库单
- **权限**: `item:outbound:audit`
- **参数**:
  - outboundId（出库单ID）
  - status（审核状态）
  - remark（审核备注，可选）
- **返回**:
```json
{
  "code": 200,
  "msg": "审核操作成功",
  "data": null
}
```

## 5. 领用管理 (ItemRequisitionController)

**基础路径**: `/item/requisition`

### 5.1 领用单基础操作

#### 新增领用单
- **接口**: `POST /item/requisition/add`
- **功能**: 新增领用单
- **权限**: `item:requisition:add`
- **参数**: ItemRequisitionDto（领用单信息）
- **返回**:
```json
{
  "code": 200,
  "msg": "新增领用单成功",
  "data": "REQ202401010001"
}
```

#### 修改领用单
- **接口**: `PUT /item/requisition/{requisitionId}`
- **功能**: 修改领用单信息
- **权限**: `item:requisition:edit`
- **参数**:
  - requisitionId（领用单ID）
  - ItemRequisitionDto（领用单信息）
- **返回**:
```json
{
  "code": 200,
  "msg": "修改领用单成功",
  "data": null
}
```

#### 删除领用单
- **接口**: `DELETE /item/requisition/{requisitionIds}`
- **功能**: 删除领用单（支持批量删除）
- **权限**: `item:requisition:remove`
- **参数**: requisitionIds（领用单ID字符串，多个以逗号分隔）
- **返回**:
```json
{
  "code": 200,
  "msg": "删除领用单成功",
  "data": null
}
```

#### 查询领用单列表
- **接口**: `POST /item/requisition/list`
- **功能**: 分页查询领用单列表
- **权限**: `item:requisition:list`
- **参数**: ItemRequisitionSearchRequest（查询条件）
- **返回**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "records": [
      {
        "requisitionId": "REQ202401010001",
        "businessDate": "2024-01-01",
        "applicantId": 3,
        "applicantName": "赵六",
        "deptId": 10,
        "deptName": "生产部",
        "requisitionPurpose": "设备维修用",
        "status": 1,
        "statusName": "草稿",
        "creatorId": 1,
        "creatorName": "张三",
        "createTime": "2024-01-01 16:00:00",
        "handlerId": 2,
        "handlerName": "李四",
        "handleTime": null,
        "auditorId": null,
        "auditorName": null,
        "auditTime": null,
        "detailCount": 2,
        "totalRequisitionQuantity": 5.00,
        "totalApprovedQuantity": 0.00,
        "totalActualQuantity": 0.00
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

#### 查询领用单详情
- **接口**: `GET /item/requisition/{requisitionId}`
- **功能**: 查询领用单详细信息
- **权限**: `item:requisition:query`
- **参数**: requisitionId（领用单ID）
- **返回**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "requisitionId": "REQ202401010001",
    "businessDate": "2024-01-01",
    "applicantId": 3,
    "applicantName": "赵六",
    "deptId": 10,
    "deptName": "生产部",
    "requisitionPurpose": "设备维修用",
    "status": 1,
    "statusName": "草稿",
    "creatorId": 1,
    "creatorName": "张三",
    "createTime": "2024-01-01 16:00:00",
    "handlerId": 2,
    "handlerName": "李四",
    "handleTime": null,
    "auditorId": null,
    "auditorName": null,
    "auditTime": null,
    "detailCount": 2,
    "totalRequisitionQuantity": 5.00,
    "totalApprovedQuantity": 0.00,
    "totalActualQuantity": 0.00,
    "details": [
      {
        "detailId": 1,
        "requisitionId": "REQ202401010001",
        "itemId": "ITEM001",
        "itemName": "螺丝刀",
        "itemCode": "SD001",
        "specModel": "十字头 6mm",
        "unit": "把",
        "warehouseId": 1,
        "warehouseName": "主仓库",
        "shelfLocation": "A-01-01",
        "requisitionQuantity": 3.00,
        "approvedQuantity": 0.00,
        "actualQuantity": 0.00,
        "remark": "维修使用"
      },
      {
        "detailId": 2,
        "requisitionId": "REQ202401010001",
        "itemId": "ITEM002",
        "itemName": "扳手",
        "itemCode": "BS001",
        "specModel": "活动扳手 200mm",
        "unit": "把",
        "warehouseId": 1,
        "warehouseName": "主仓库",
        "shelfLocation": "A-02-01",
        "requisitionQuantity": 2.00,
        "approvedQuantity": 0.00,
        "actualQuantity": 0.00,
        "remark": "维修使用"
      }
    ],
    "operationLogs": [
      {
        "logId": 1,
        "requisitionId": "REQ202401010001",
        "operationType": "CREATE",
        "operationDesc": "创建领用单",
        "operatorId": 1,
        "operatorName": "张三",
        "operationTime": "2024-01-01 16:00:00",
        "remark": "初始创建"
      }
    ]
  }
}
```

### 5.2 领用单流程操作

#### 提交领用单
- **接口**: `PUT /item/requisition/{requisitionId}/submit`
- **功能**: 提交领用单
- **权限**: `item:requisition:submit`
- **参数**: requisitionId（领用单ID）
- **返回**:
```json
{
  "code": 200,
  "msg": "提交领用单成功",
  "data": null
}
```

#### 确认领用单
- **接口**: `PUT /item/requisition/{requisitionId}/confirm`
- **功能**: 确认领用单
- **权限**: `item:requisition:confirm`
- **参数**:
  - requisitionId（领用单ID）
  - remark（确认备注，可选）
- **返回**:
```json
{
  "code": 200,
  "msg": "确认领用单成功",
  "data": null
}
```

#### 审核领用单
- **接口**: `PUT /item/requisition/{requisitionId}/audit`
- **功能**: 审核领用单
- **权限**: `item:requisition:audit`
- **参数**:
  - requisitionId（领用单ID）
  - status（审核结果：4-通过, 5-退回）
  - remark（审核备注，可选）
- **返回**:
```json
{
  "code": 200,
  "msg": "审核领用单成功",
  "data": null
}
```

#### 完成领用
- **接口**: `PUT /item/requisition/{requisitionId}/complete`
- **功能**: 完成领用操作
- **权限**: `item:requisition:complete`
- **参数**: requisitionId（领用单ID）
- **返回**:
```json
{
  "code": 200,
  "msg": "完成领用成功",
  "data": null
}
```

## 6. 库存盘点管理 (ItemStocktakingController)

**基础路径**: `/item/stocktaking`

### 6.1 盘点单基础操作

#### 查询库存盘点列表
- **接口**: `GET /item/stocktaking/list`
- **功能**: 分页查询库存盘点列表
- **权限**: `item:stocktaking:list`
- **参数**: ItemStocktakingSearchRequest（查询条件）
- **返回**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "records": [
      {
        "stocktakingId": "ST202401010001",
        "stocktakingCode": "ST202401010001",
        "stocktakingName": "2024年第一季度盘点",
        "stocktakingType": 1,
        "stocktakingTypeName": "全盘",
        "warehouseId": null,
        "warehouseName": null,
        "status": 0,
        "statusName": "草稿",
        "planStartTime": "2024-01-01 09:00:00",
        "planEndTime": "2024-01-01 18:00:00",
        "actualStartTime": null,
        "actualEndTime": null,
        "creatorId": 1,
        "creatorName": "张三",
        "createTime": "2024-01-01 08:00:00",
        "auditorId": null,
        "auditorName": null,
        "auditTime": null,
        "auditRemark": null,
        "totalItems": 0,
        "completedItems": 0,
        "differenceItems": 0,
        "remark": "季度例行盘点"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

#### 获取库存盘点详情
- **接口**: `GET /item/stocktaking/{stocktakingId}`
- **功能**: 获取库存盘点详细信息
- **权限**: `item:stocktaking:query`
- **参数**: stocktakingId（盘点单ID）
- **返回**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "stocktakingId": "ST202401010001",
    "stocktakingCode": "ST202401010001",
    "stocktakingName": "2024年第一季度盘点",
    "stocktakingType": 1,
    "stocktakingTypeName": "全盘",
    "warehouseId": null,
    "warehouseName": null,
    "status": 1,
    "statusName": "进行中",
    "planStartTime": "2024-01-01 09:00:00",
    "planEndTime": "2024-01-01 18:00:00",
    "actualStartTime": "2024-01-01 09:30:00",
    "actualEndTime": null,
    "creatorId": 1,
    "creatorName": "张三",
    "createTime": "2024-01-01 08:00:00",
    "auditorId": null,
    "auditorName": null,
    "auditTime": null,
    "auditRemark": null,
    "totalItems": 50,
    "completedItems": 30,
    "differenceItems": 5,
    "remark": "季度例行盘点"
  }
}
```

#### 查询盘点明细列表
- **接口**: `GET /item/stocktaking/{stocktakingId}/details`
- **功能**: 查询盘点明细列表
- **权限**: `item:stocktaking:query`
- **参数**: stocktakingId（盘点单ID）
- **返回**:
```json
{
  "code": 200,
  "msg": "success",
  "data": [
    {
      "detailId": "STD001",
      "stocktakingId": "ST202401010001",
      "itemId": "ITEM001",
      "itemName": "螺丝刀",
      "itemCode": "SD001",
      "specModel": "十字头 6mm",
      "unit": "把",
      "warehouseId": 1,
      "warehouseName": "主仓库",
      "shelfLocation": "A-01-01",
      "bookQuantity": 50.00,
      "actualQuantity": 48.00,
      "differenceQuantity": -2.00,
      "differenceReason": "损耗",
      "status": 1,
      "statusName": "已盘点",
      "operatorId": 2,
      "operatorName": "李四",
      "operationTime": "2024-01-01 15:00:00",
      "remark": "发现2把损坏"
    }
  ]
}
```

#### 新增库存盘点
- **接口**: `POST /item/stocktaking`
- **功能**: 新增库存盘点单
- **权限**: `item:stocktaking:add`
- **参数**: ItemStocktakingDto（盘点单信息）
- **返回**:
```json
{
  "code": 200,
  "msg": "创建成功",
  "data": "ST202401010001"
}
```
- **日志**: 记录新增操作日志

#### 修改库存盘点
- **接口**: `PUT /item/stocktaking`
- **功能**: 修改库存盘点单
- **权限**: `item:stocktaking:edit`
- **参数**: ItemStocktakingDto（盘点单信息）
- **返回**:
```json
{
  "code": 200,
  "msg": "修改成功",
  "data": null
}
```
- **日志**: 记录修改操作日志

#### 删除库存盘点
- **接口**: `DELETE /item/stocktaking/{stocktakingId}`
- **功能**: 删除库存盘点单
- **权限**: `item:stocktaking:remove`
- **参数**: stocktakingId（盘点单ID）
- **返回**:
```json
{
  "code": 200,
  "msg": "删除成功",
  "data": null
}
```
- **日志**: 记录删除操作日志

### 6.2 盘点流程操作

#### 生成盘点明细
- **接口**: `POST /item/stocktaking/{stocktakingId}/generate`
- **功能**: 生成盘点明细
- **权限**: `item:stocktaking:generate`
- **参数**: stocktakingId（盘点单ID）
- **返回**:
```json
{
  "code": 200,
  "msg": "生成盘点明细成功",
  "data": null
}
```
- **日志**: 记录生成盘点明细操作日志

#### 开始盘点
- **接口**: `POST /item/stocktaking/{stocktakingId}/start`
- **功能**: 开始盘点操作
- **权限**: `item:stocktaking:start`
- **参数**: stocktakingId（盘点单ID）
- **返回**:
```json
{
  "code": 200,
  "msg": "开始盘点成功",
  "data": null
}
```
- **日志**: 记录开始盘点操作日志

#### 录入盘点结果
- **接口**: `POST /item/stocktaking/record`
- **功能**: 录入盘点结果
- **权限**: `item:stocktaking:record`
- **参数**:
  - detailId（明细ID）
  - actualQuantity（实盘数量）
  - differenceReason（差异原因，可选）
- **返回**:
```json
{
  "code": 200,
  "msg": "录入盘点结果成功",
  "data": null
}
```
- **日志**: 记录录入盘点结果操作日志

#### 完成盘点
- **接口**: `POST /item/stocktaking/{stocktakingId}/complete`
- **功能**: 完成盘点操作
- **权限**: `item:stocktaking:complete`
- **参数**: stocktakingId（盘点单ID）
- **返回**:
```json
{
  "code": 200,
  "msg": "完成盘点成功",
  "data": null
}
```
- **日志**: 记录完成盘点操作日志

#### 审核盘点
- **接口**: `POST /item/stocktaking/{stocktakingId}/audit`
- **功能**: 审核盘点结果
- **权限**: `item:stocktaking:audit`
- **参数**:
  - stocktakingId（盘点单ID）
  - approved（是否通过）
  - auditRemark（审核备注，可选）
- **返回**:
```json
{
  "code": 200,
  "msg": "审核盘点成功",
  "data": null
}
```
- **日志**: 记录审核盘点操作日志

#### 应用盘点差异
- **接口**: `POST /item/stocktaking/{stocktakingId}/apply`
- **功能**: 应用盘点差异到库存
- **权限**: `item:stocktaking:apply`
- **参数**: stocktakingId（盘点单ID）
- **返回**:
```json
{
  "code": 200,
  "msg": "应用盘点差异成功",
  "data": null
}
```
- **日志**: 记录应用盘点差异操作日志

## 7. 权限说明

### 7.1 物品管理权限
- `item:edit` - 修改物品权限
- `item:remove` - 删除物品权限
- `item:query` - 查询物品详情权限
- `item:list` - 查询物品列表权限

### 7.2 库存管理权限
- `item:stock:view` - 查看库存权限
- `item:stock:adjust` - 调整库存权限
- `item:stock:update` - 更新库存权限
- `item:stock:statistics` - 库存统计权限
- `item:safety:stock:update` - 更新安全库存权限
- `item:stock:sync` - 同步库存权限
- `item:stock:alert` - 库存告警权限

### 7.3 入库管理权限
- `item:inbound:add` - 新增入库单权限
- `item:inbound:edit` - 编辑入库单权限
- `item:inbound:query` - 查询入库单权限
- `item:inbound:list` - 查询入库单列表权限
- `item:inbound:remove` - 删除入库单权限
- `item:inbound:submit` - 提交入库单权限
- `item:inbound:handle` - 确认入库单权限
- `item:inbound:audit` - 审核入库单权限

### 7.4 出库管理权限
- `item:outbound:add` - 新增出库单权限
- `item:outbound:edit` - 编辑出库单权限
- `item:outbound:query` - 查询出库单权限
- `item:outbound:list` - 查询出库单列表权限
- `item:outbound:remove` - 删除出库单权限
- `item:outbound:submit` - 提交出库单权限
- `item:outbound:handle` - 确认出库单权限
- `item:outbound:audit` - 审核出库单权限

### 7.5 领用管理权限
- `item:requisition:add` - 新增领用单权限
- `item:requisition:edit` - 修改领用单权限
- `item:requisition:remove` - 删除领用单权限
- `item:requisition:list` - 查询领用单列表权限
- `item:requisition:query` - 查询领用单详情权限
- `item:requisition:submit` - 提交领用单权限
- `item:requisition:confirm` - 确认领用单权限
- `item:requisition:audit` - 审核领用单权限
- `item:requisition:complete` - 完成领用权限

### 7.6 库存盘点权限
- `item:stocktaking:list` - 查询盘点列表权限
- `item:stocktaking:query` - 查询盘点详情权限
- `item:stocktaking:add` - 新增盘点单权限
- `item:stocktaking:edit` - 修改盘点单权限
- `item:stocktaking:remove` - 删除盘点单权限
- `item:stocktaking:generate` - 生成盘点明细权限
- `item:stocktaking:start` - 开始盘点权限
- `item:stocktaking:record` - 录入盘点结果权限
- `item:stocktaking:complete` - 完成盘点权限
- `item:stocktaking:audit` - 审核盘点权限
- `item:stocktaking:apply` - 应用盘点差异权限

## 8. 数据传输对象详细说明

### 8.1 请求对象 (Request/Dto)

#### ItemDto - 物品信息传输对象
```json
{
  "baseInfo": {
    "itemId": "string",           // 物品ID（新增时可为空，系统自动生成）
    "itemName": "string",         // 物品名称（必填）
    "itemCode": "string",         // 物品编码/SKU（必填）
    "itemType": 1,                // 物品类别：1-消耗品, 2-备品备件（必填）
    "specModel": "string",        // 规格型号
    "unit": "string",             // 单位（个/盒/箱等）
    "safetyStock": 0.00,          // 企业级安全库存
    "imageUrl": "string",         // 图片URL
    "remark": "string"            // 备注
  },
  "inventory": {
    "inventoryId": "string",      // 库存ID（新增时可为空）
    "itemId": "string",           // 物品ID
    "currentQuantity": 0.00,      // 当前库存数量（必填）
    "safetyStock": 0.00,          // 安全库存
    "stockStatus": 1,             // 库存状态：1-正常, 2-不足, 3-过剩
    "warehouseId": 1,             // 仓库ID（必填）
    "shelfLocation": "string"     // 货架/库位编号
  },
  "consumableAttr": {             // 消耗品属性（itemType=1时有效）
    "hasExpiry": 1,               // 是否有有效期：0-无, 1-有
    "expiryPeriod": 365,          // 有效期/保质期（天）
    "productionDate": "2024-01-01", // 生产日期
    "storageCondition": "string"  // 存储条件
  },
  "partAttr": {                   // 备品备件属性（itemType=2时有效）
    "applicableDevice": "string", // 适用设备型号
    "partCategory": 1,            // 备件分类：1-关键, 2-常用, 3-次要
    "replacementCycle": 30,       // 建议更换周期（天）
    "maintenanceHistory": "string" // 维修历史关联
  }
}
```

#### ItemRegistrationDto - 物品登记信息传输对象
```json
{
  "baseInfo": {
    // 同ItemDto.baseInfo，但不包含库存相关字段
    "itemName": "string",         // 物品名称（必填）
    "itemCode": "string",         // 物品编码（必填）
    "itemType": 1,                // 物品类别（必填）
    "specModel": "string",        // 规格型号
    "unit": "string",             // 单位
    "imageUrl": "string",         // 图片URL
    "remark": "string"            // 备注
  },
  "safetyStock": 0.00,            // 企业级安全库存
  "consumableAttr": {             // 消耗品属性（可选）
    // 同ItemDto.consumableAttr
  },
  "partAttr": {                   // 备品备件属性（可选）
    // 同ItemDto.partAttr
  }
}
```

#### ItemSearchRequest - 物品查询请求对象
```json
{
  "pageNum": 1,                   // 当前页码（必填）
  "pageSize": 10,                 // 每页数量（必填）
  "itemId": "string",             // 物品ID（可选）
  "itemName": "string",           // 物品名称（模糊查询）
  "itemCode": "string",           // 物品编码（模糊查询）
  "itemType": 1,                  // 物品类型：1-消耗品, 2-备品备件
  "specModel": "string",          // 规格型号（模糊查询）
  "stockStatus": 1,               // 库存状态：1-正常, 2-不足, 3-过剩
  "warehouseId": 1,               // 仓库ID
  "hasExpiry": 1                  // 是否有有效期：0-无, 1-有
}
```

#### ItemStockStatisticsRequest - 库存统计查询请求
```json
{
  "pageNum": 1,                   // 当前页码
  "pageSize": 10,                 // 每页数量
  "statisticsType": 1,            // 统计类型：1-按仓库统计, 2-按物品统计
  "itemId": "string",             // 物品ID
  "itemName": "string",           // 物品名称（模糊查询）
  "itemCode": "string",           // 物品编码（模糊查询）
  "itemType": 1,                  // 物品类型
  "specModel": "string",          // 规格型号（模糊查询）
  "warehouseId": 1,               // 仓库ID
  "stockStatus": 1,               // 库存状态
  "onlyWithStock": true           // 是否只显示有库存的记录
}
```

#### ItemStockViewRequest - 库存视图查询请求
```json
{
  "pageNum": 1,                   // 页码（默认1）
  "pageSize": 10,                 // 每页大小（默认10）
  "viewMode": 1,                  // 展示模式：1-按仓库展示, 2-按物品展示（必填）
  "itemId": "string",             // 物品ID
  "itemName": "string",           // 物品名称（模糊查询）
  "itemCode": "string",           // 物品编码（模糊查询）
  "itemType": 1,                  // 物品类型
  "specModel": "string",          // 规格型号（模糊查询）
  "warehouseId": 1,               // 仓库ID
  "stockStatus": 1,               // 库存状态
  "onlyWithStock": true           // 是否只显示有库存的记录（默认true）
}
```

#### ItemInboundDto - 入库单传输对象
```json
{
  "main": {
    "inboundId": "string",        // 入库单ID（新增时可为空，系统自动生成）
    "businessDate": "2024-01-01", // 业务日期（必填）
    "supplierName": "string",     // 供应商名称
    "inboundType": 1,             // 入库类型（根据数据字典配置）
    "handlerId": 1,               // 经手人ID（系统自动设置）
    "inboundDescription": "string" // 入库说明
  },
  "details": [                    // 入库明细列表（必填，至少一条）
    {
      "itemId": "string",         // 物品ID（必填）
      "batchNo": "string",        // 批次号
      "productionDate": "2024-01-01", // 生产日期
      "expiryDate": "2024-12-31", // 过期日期
      "quantity": 10.00,          // 入库数量（必填）
      "unitPrice": 100.00,        // 单价
      "amount": 1000.00,          // 金额
      "warehouseId": 1,           // 仓库ID（必填）
      "shelfLocation": "string",  // 货架位置
      "remark": "string"          // 备注
    }
  ]
}
```

#### ItemOutboundDto - 出库单传输对象
```json
{
  "main": {
    "outboundId": "string",       // 出库单ID（新增时可为空，系统自动生成）
    "businessDate": "2024-01-01", // 业务日期（必填）
    "recipientName": "string",    // 领用人姓名
    "recipientDept": "string",    // 领用部门
    "outboundType": 1,            // 出库类型（根据数据字典配置）
    "creatorId": 1,               // 制单人ID（系统自动设置）
    "outboundDescription": "string" // 出库说明
  },
  "details": [                    // 出库明细列表（必填，至少一条）
    {
      "itemId": "string",         // 物品ID（必填）
      "batchNo": "string",        // 批次号
      "productionDate": "2024-01-01", // 生产日期
      "expiryDate": "2024-12-31", // 过期日期
      "quantity": 5.00,           // 出库数量（必填）
      "unitPrice": 100.00,        // 单价
      "amount": 500.00,           // 金额
      "warehouseId": 1,           // 仓库ID（必填）
      "shelfLocation": "string",  // 货架位置
      "remark": "string"          // 备注
    }
  ]
}
```

#### ItemRequisitionDto - 领用单传输对象
```json
{
  "main": {
    "requisitionId": "string",    // 领用单ID（新增时可为空，系统自动生成）
    "businessDate": "2024-01-01", // 业务日期（必填）
    "applicantId": 1,             // 申请人ID（必填）
    "deptId": 1,                  // 申请部门ID（必填）
    "requisitionPurpose": "string", // 领用用途/说明
    "creatorId": 1,               // 制单人ID（系统自动设置）
    "handlerId": 1,               // 经手人ID
    "auditorId": 1                // 审核人ID
  },
  "details": [                    // 领用明细列表（必填，至少一条）
    {
      "itemId": "string",         // 物品ID（必填）
      "warehouseId": 1,           // 仓库ID（必填）
      "shelfLocation": "string",  // 货架位置
      "requisitionQuantity": 3.00, // 申请数量（必填）
      "approvedQuantity": 3.00,   // 批准数量
      "actualQuantity": 3.00,     // 实际领用数量
      "remark": "string"          // 备注
    }
  ]
}
```

#### ItemStocktakingDto - 盘点单传输对象
```json
{
  "stocktakingId": "string",      // 盘点单ID（新增时可为空，系统自动生成）
  "stocktakingName": "string",    // 盘点名称（必填）
  "stocktakingType": 1,           // 盘点类型：1-全盘，2-抽盘，3-循环盘点（必填）
  "warehouseId": 1,               // 仓库ID（全盘时为空）
  "planStartTime": "2024-01-01 09:00:00", // 计划开始时间
  "planEndTime": "2024-01-01 18:00:00",   // 计划结束时间
  "remark": "string"              // 备注
}
```

#### 查询请求对象
```json
// ItemInboundSearchRequest - 入库单查询请求
{
  "pageNum": 1,                   // 当前页码
  "pageSize": 10,                 // 每页数量
  "inboundId": "string",          // 入库单ID
  "supplierName": "string",       // 供应商名称（模糊查询）
  "inboundType": 1,               // 入库类型
  "status": 1,                    // 状态
  "creatorId": 1,                 // 制单人ID
  "handlerId": 1,                 // 经手人ID
  "auditorId": 1,                 // 审核人ID
  "businessDateStart": "2024-01-01", // 业务日期开始
  "businessDateEnd": "2024-01-31",   // 业务日期结束
  "createTimeStart": "2024-01-01 00:00:00", // 创建时间开始
  "createTimeEnd": "2024-01-31 23:59:59"    // 创建时间结束
}

// ItemOutboundSearchRequest - 出库单查询请求
{
  "pageNum": 1,                   // 当前页码
  "pageSize": 10,                 // 每页数量
  "outboundId": "string",         // 出库单ID
  "recipientName": "string",      // 领用人姓名（模糊查询）
  "recipientDept": "string",      // 领用部门（模糊查询）
  "outboundType": 1,              // 出库类型
  "status": 1,                    // 状态
  "creatorId": 1,                 // 制单人ID
  "handlerId": 1,                 // 经手人ID
  "auditorId": 1,                 // 审核人ID
  "businessDateStart": "2024-01-01", // 业务日期开始
  "businessDateEnd": "2024-01-31",   // 业务日期结束
  "createTimeStart": "2024-01-01 00:00:00", // 创建时间开始
  "createTimeEnd": "2024-01-31 23:59:59"    // 创建时间结束
}

// ItemRequisitionSearchRequest - 领用单查询请求
{
  "pageNum": 1,                   // 当前页码
  "pageSize": 10,                 // 每页数量
  "requisitionId": "string",      // 领用单ID
  "applicantId": 1,               // 申请人ID
  "applicantName": "string",      // 申请人姓名（模糊查询）
  "deptId": 1,                    // 申请部门ID
  "deptName": "string",           // 申请部门名称（模糊查询）
  "status": 1,                    // 状态：1-草稿, 2-待确认, 3-待审核, 4-已通过, 5-已退回, 6-已完成
  "creatorId": 1,                 // 制单人ID
  "handlerId": 1,                 // 经手人ID
  "auditorId": 1,                 // 审核人ID
  "businessDateStart": "2024-01-01", // 业务日期开始
  "businessDateEnd": "2024-01-31",   // 业务日期结束
  "createTimeStart": "2024-01-01 00:00:00", // 创建时间开始
  "createTimeEnd": "2024-01-31 23:59:59",   // 创建时间结束
  "itemId": "string",             // 物品ID（查询包含指定物品的领用单）
  "itemName": "string",           // 物品名称（查询包含指定物品的领用单）
  "warehouseId": 1                // 仓库ID（查询包含指定仓库物品的领用单）
}

// ItemStocktakingSearchRequest - 盘点单查询请求
{
  "pageNum": 1,                   // 页码（默认1）
  "pageSize": 10,                 // 每页大小（默认10）
  "stocktakingCode": "string",    // 盘点单号
  "stocktakingName": "string",    // 盘点名称（模糊查询）
  "stocktakingType": 1,           // 盘点类型：1-全盘，2-抽盘，3-循环盘点
  "warehouseId": 1,               // 仓库ID
  "status": 1,                    // 状态：0-草稿，1-进行中，2-已完成，3-已审核
  "creatorName": "string",        // 创建人姓名（模糊查询）
  "createTimeStart": "2024-01-01 00:00:00", // 创建时间开始
  "createTimeEnd": "2024-01-31 23:59:59"    // 创建时间结束
}
```

### 8.2 响应对象 (Vo)

#### ItemVo - 物品信息视图对象
```json
{
  "itemId": "string",             // 物品ID
  "itemName": "string",           // 物品名称
  "itemCode": "string",           // 物品编码
  "itemType": 1,                  // 物品类别：1-消耗品, 2-备品备件
  "specModel": "string",          // 规格型号
  "unit": "string",               // 单位
  "totalQuantity": 100.00,        // 总库存数量（所有仓库库存之和）
  "safetyStock": 10.00,           // 企业级安全库存
  "minStockStatus": 1,            // 最低库存状态：1-正常, 2-不足, 3-过剩
  "stockStatusName": "正常",       // 库存状态名称
  "warehouseCount": 2,            // 仓库数量（该物品分布在多少个仓库）
  "warehouseIds": [1, 2],         // 仓库ID列表
  "warehouseId": 1,               // 主要仓库ID（库存最多的仓库）
  "warehouseName": "string",      // 主要仓库名称
  "shelfLocation": "string",      // 主要货架位置
  "imageUrl": "string",           // 图片URL
  "remark": "string"              // 备注
}
```

#### ItemDetailVo - 物品详情视图对象
```json
{
  "itemId": "string",             // 物品ID
  "itemName": "string",           // 物品名称
  "itemCode": "string",           // 物品编码
  "itemType": 1,                  // 物品类别
  "itemTypeName": "消耗品",        // 物品类别名称
  "specModel": "string",          // 规格型号
  "unit": "string",               // 单位
  "imageUrl": "string",           // 图片URL
  "totalQuantity": 100.00,        // 总库存数量
  "safetyStock": 10.00,           // 企业级安全库存
  "hasExpiry": 1,                 // 是否有有效期：0-无, 1-有
  "expiryPeriod": 365,            // 有效期/保质期（天）
  "productionDate": "2024-01-01", // 生产日期
  "storageCondition": "string",   // 存储条件
  "applicableDevice": "string",   // 适用设备型号
  "partCategory": 1,              // 备件分类：1-关键, 2-常用, 3-次要
  "partCategoryName": "关键",      // 备件分类名称
  "replacementCycle": 30,         // 建议更换周期（天）
  "maintenanceHistory": "string", // 维修历史关联
  "remark": "string",             // 备注
  "createTime": "2024-01-01 10:00:00", // 创建时间
  "createBy": "string",           // 创建人
  "inventoryList": [              // 库存信息列表（支持多仓库）
    {
      "inventoryId": "string",    // 库存ID
      "itemId": "string",         // 物品ID
      "warehouseId": 1,           // 仓库ID
      "warehouseName": "string",  // 仓库名称
      "currentQuantity": 50.00,   // 当前库存
      "safetyStock": 5.00,        // 安全库存
      "stockStatus": 1,           // 库存状态
      "stockStatusName": "正常",   // 库存状态名称
      "shelfLocation": "string"   // 货架/库位
    }
  ]
}
```

#### ItemStockViewVo - 库存视图对象
```json
{
  "itemId": "string",             // 物品ID
  "itemName": "string",           // 物品名称
  "itemCode": "string",           // 物品编码
  "itemType": 1,                  // 物品类别
  "specModel": "string",          // 规格型号
  "unit": "string",               // 单位
  "warehouseId": 1,               // 仓库ID
  "warehouseName": "string",      // 仓库名称
  "currentQuantity": 50.00,       // 当前库存数量
  "safetyStock": 5.00,            // 安全库存
  "stockStatus": 1,               // 库存状态：1-正常, 2-不足, 3-过剩
  "stockStatusName": "正常",       // 库存状态名称
  "shelfLocation": "string",      // 货架位置
  "stockDetails": [               // 库存详情列表（按物品展示模式时使用）
    {
      "warehouseId": 1,           // 仓库ID
      "warehouseName": "string",  // 仓库名称
      "currentQuantity": 30.00,   // 当前库存
      "safetyStock": 3.00,        // 安全库存
      "stockStatus": 1,           // 库存状态
      "stockStatusName": "正常",   // 库存状态名称
      "shelfLocation": "string"   // 货架位置
    }
  ]
}
```

#### ItemInboundVo - 入库单视图对象
```json
{
  "inboundId": "WPRK202401010001", // 入库单ID
  "businessDate": "2024-01-01",   // 业务日期
  "supplierName": "string",       // 供应商名称
  "inboundType": 1,               // 入库类型
  "inboundTypeName": "采购入库",   // 入库类型名称
  "status": 1,                    // 状态：1-草稿, 2-待确认, 3-待审核, 4-已通过, 5-已退回
  "statusName": "草稿",            // 状态名称
  "creatorId": 1,                 // 制单人ID
  "creatorName": "string",        // 制单人姓名
  "createTime": "2024-01-01 10:00:00", // 创建时间
  "handlerId": 1,                 // 经手人ID
  "handlerName": "string",        // 经手人姓名
  "handleTime": "2024-01-01 11:00:00", // 经手确认时间
  "handleRemark": "string",       // 经手备注
  "auditorId": 1,                 // 审核人ID
  "auditorName": "string",        // 审核人姓名
  "auditTime": "2024-01-01 12:00:00",  // 审核时间
  "auditRemark": "string",        // 审核备注
  "updateTime": "2024-01-01 12:00:00", // 更新时间
  "inboundDescription": "string", // 入库说明
  "details": [                    // 明细列表
    {
      "detailId": 1,              // 明细ID
      "inboundId": "string",      // 入库单ID
      "itemId": "string",         // 物品ID
      "itemName": "string",       // 物品名称
      "itemCode": "string",       // 物品编码
      "specModel": "string",      // 规格型号
      "unit": "string",           // 单位
      "batchNo": "string",        // 批次号
      "productionDate": "2024-01-01", // 生产日期
      "expiryDate": "2024-12-31", // 过期日期
      "quantity": 10.00,          // 入库数量
      "unitPrice": 100.00,        // 单价
      "amount": 1000.00,          // 金额
      "warehouseId": 1,           // 仓库ID
      "warehouseName": "string",  // 仓库名称
      "shelfLocation": "string",  // 货架位置
      "remark": "string"          // 备注
    }
  ],
  "logs": [                       // 操作日志列表
    {
      "logId": 1,                 // 日志ID
      "inboundId": "string",      // 入库单ID
      "operationType": "CREATE",  // 操作类型
      "operationDesc": "创建入库单", // 操作描述
      "operatorId": 1,            // 操作人ID
      "operatorName": "string",   // 操作人姓名
      "operationTime": "2024-01-01 10:00:00", // 操作时间
      "remark": "string"          // 备注
    }
  ]
}
```

#### ItemOutboundVo - 出库单视图对象
```json
{
  "outboundId": "WPCK202401010001", // 出库单ID
  "businessDate": "2024-01-01",   // 业务日期
  "recipientName": "string",      // 领用人姓名
  "recipientDept": "string",      // 领用部门
  "outboundType": 1,              // 出库类型
  "outboundTypeName": "正常出库",  // 出库类型名称
  "status": 1,                    // 状态：1-草稿, 2-待确认, 3-待审核, 4-已通过, 5-已退回
  "statusName": "草稿",            // 状态名称
  "creatorId": 1,                 // 制单人ID
  "creatorName": "string",        // 制单人姓名
  "createTime": "2024-01-01 10:00:00", // 创建时间
  "handlerId": 1,                 // 经手人ID
  "handlerName": "string",        // 经手人姓名
  "handleTime": "2024-01-01 11:00:00", // 经手确认时间
  "handleRemark": "string",       // 经手备注
  "auditorId": 1,                 // 审核人ID
  "auditorName": "string",        // 审核人姓名
  "auditTime": "2024-01-01 12:00:00",  // 审核时间
  "auditRemark": "string",        // 审核备注
  "updateTime": "2024-01-01 12:00:00", // 更新时间
  "outboundDescription": "string", // 出库说明
  "details": [                    // 明细列表（结构同入库单明细）
    {
      "detailId": 1,              // 明细ID
      "outboundId": "string",     // 出库单ID
      "itemId": "string",         // 物品ID
      "itemName": "string",       // 物品名称
      "itemCode": "string",       // 物品编码
      "specModel": "string",      // 规格型号
      "unit": "string",           // 单位
      "batchNo": "string",        // 批次号
      "productionDate": "2024-01-01", // 生产日期
      "expiryDate": "2024-12-31", // 过期日期
      "quantity": 5.00,           // 出库数量
      "unitPrice": 100.00,        // 单价
      "amount": 500.00,           // 金额
      "warehouseId": 1,           // 仓库ID
      "warehouseName": "string",  // 仓库名称
      "shelfLocation": "string",  // 货架位置
      "remark": "string"          // 备注
    }
  ],
  "logs": [                       // 操作日志列表（结构同入库单日志）
    {
      "logId": 1,                 // 日志ID
      "outboundId": "string",     // 出库单ID
      "operationType": "CREATE",  // 操作类型
      "operationDesc": "创建出库单", // 操作描述
      "operatorId": 1,            // 操作人ID
      "operatorName": "string",   // 操作人姓名
      "operationTime": "2024-01-01 10:00:00", // 操作时间
      "remark": "string"          // 备注
    }
  ]
}
```

#### ItemRequisitionVo - 领用单视图对象
```json
{
  "requisitionId": "string",      // 领用单ID
  "businessDate": "2024-01-01",  // 业务日期
  "applicantId": 1,               // 申请人ID
  "applicantName": "string",      // 申请人姓名
  "deptId": 1,                    // 申请部门ID
  "deptName": "string",           // 申请部门名称
  "requisitionPurpose": "string", // 领用用途/说明
  "status": 1,                    // 状态：1-草稿, 2-待确认, 3-待审核, 4-已通过, 5-已退回, 6-已完成
  "statusName": "草稿",            // 状态名称
  "creatorId": 1,                 // 制单人ID
  "creatorName": "string",        // 制单人姓名
  "createTime": "2024-01-01 10:00:00", // 创建时间
  "handlerId": 1,                 // 经手人ID
  "handlerName": "string",        // 经手人姓名
  "handleTime": "2024-01-01 11:00:00", // 经手确认时间
  "auditorId": 1,                 // 审核人ID
  "auditorName": "string",        // 审核人姓名
  "auditTime": "2024-01-01 12:00:00",  // 审核时间
  "detailCount": 3,               // 明细数量（物品种类数）
  "totalRequisitionQuantity": 10.00, // 申请总数量
  "totalApprovedQuantity": 10.00, // 批准总数量
  "totalActualQuantity": 10.00    // 实际领用总数量
}
```

#### ItemRequisitionDetailVo - 领用单详情视图对象
```json
{
  // 继承ItemRequisitionVo的所有字段
  "requisitionId": "string",      // 领用单ID
  "businessDate": "2024-01-01",  // 业务日期
  "applicantId": 1,               // 申请人ID
  "applicantName": "string",      // 申请人姓名
  "deptId": 1,                    // 申请部门ID
  "deptName": "string",           // 申请部门名称
  "requisitionPurpose": "string", // 领用用途/说明
  "status": 1,                    // 状态
  "statusName": "草稿",            // 状态名称
  "creatorId": 1,                 // 制单人ID
  "creatorName": "string",        // 制单人姓名
  "createTime": "2024-01-01 10:00:00", // 创建时间
  "handlerId": 1,                 // 经手人ID
  "handlerName": "string",        // 经手人姓名
  "handleTime": "2024-01-01 11:00:00", // 经手确认时间
  "auditorId": 1,                 // 审核人ID
  "auditorName": "string",        // 审核人姓名
  "auditTime": "2024-01-01 12:00:00",  // 审核时间
  "detailCount": 3,               // 明细数量
  "totalRequisitionQuantity": 10.00, // 申请总数量
  "totalApprovedQuantity": 10.00, // 批准总数量
  "totalActualQuantity": 10.00,   // 实际领用总数量
  "details": [                    // 领用单明细列表（包含物品详细信息）
    {
      "detailId": 1,              // 明细ID
      "requisitionId": "string",  // 领用单ID
      "itemId": "string",         // 物品ID
      "itemName": "string",       // 物品名称
      "itemCode": "string",       // 物品编码
      "specModel": "string",      // 规格型号
      "unit": "string",           // 单位
      "warehouseId": 1,           // 仓库ID
      "warehouseName": "string",  // 仓库名称
      "shelfLocation": "string",  // 货架位置
      "requisitionQuantity": 3.00, // 申请数量
      "approvedQuantity": 3.00,   // 批准数量
      "actualQuantity": 3.00,     // 实际领用数量
      "remark": "string"          // 备注
    }
  ],
  "operationLogs": [              // 操作日志列表
    {
      "logId": 1,                 // 日志ID
      "requisitionId": "string",  // 领用单ID
      "operationType": "CREATE",  // 操作类型
      "operationDesc": "创建领用单", // 操作描述
      "operatorId": 1,            // 操作人ID
      "operatorName": "string",   // 操作人姓名
      "operationTime": "2024-01-01 10:00:00", // 操作时间
      "remark": "string"          // 备注
    }
  ]
}
```

#### ItemStocktakingVo - 盘点单视图对象
```json
{
  "stocktakingId": "string",      // 盘点单ID
  "stocktakingCode": "string",    // 盘点单号
  "stocktakingName": "string",    // 盘点名称
  "stocktakingType": 1,           // 盘点类型：1-全盘，2-抽盘，3-循环盘点
  "stocktakingTypeName": "全盘",   // 盘点类型名称
  "warehouseId": 1,               // 仓库ID
  "warehouseName": "string",      // 仓库名称
  "status": 1,                    // 状态：0-草稿，1-进行中，2-已完成，3-已审核
  "statusName": "草稿",            // 状态名称
  "planStartTime": "2024-01-01 09:00:00", // 计划开始时间
  "planEndTime": "2024-01-01 18:00:00",   // 计划结束时间
  "actualStartTime": "2024-01-01 09:30:00", // 实际开始时间
  "actualEndTime": "2024-01-01 17:30:00",   // 实际结束时间
  "creatorId": 1,                 // 创建人ID
  "creatorName": "string",        // 创建人姓名
  "createTime": "2024-01-01 08:00:00", // 创建时间
  "auditorId": 1,                 // 审核人ID
  "auditorName": "string",        // 审核人姓名
  "auditTime": "2024-01-01 19:00:00",  // 审核时间
  "auditRemark": "string",        // 审核备注
  "totalItems": 50,               // 盘点物品总数
  "completedItems": 45,           // 已完成盘点物品数
  "differenceItems": 5,           // 有差异物品数
  "remark": "string"              // 备注
}
```

#### ItemStocktakingDetailVo - 盘点明细视图对象
```json
{
  "detailId": "string",           // 明细ID
  "stocktakingId": "string",      // 盘点单ID
  "itemId": "string",             // 物品ID
  "itemName": "string",           // 物品名称
  "itemCode": "string",           // 物品编码
  "specModel": "string",          // 规格型号
  "unit": "string",               // 单位
  "warehouseId": 1,               // 仓库ID
  "warehouseName": "string",      // 仓库名称
  "shelfLocation": "string",      // 货架位置
  "bookQuantity": 50.00,          // 账面数量
  "actualQuantity": 48.00,        // 实盘数量
  "differenceQuantity": -2.00,    // 差异数量（实盘-账面）
  "differenceReason": "string",   // 差异原因
  "status": 1,                    // 明细状态：0-未盘点，1-已盘点
  "statusName": "已盘点",          // 明细状态名称
  "operatorId": 1,                // 盘点人ID
  "operatorName": "string",       // 盘点人姓名
  "operationTime": "2024-01-01 15:00:00", // 盘点时间
  "remark": "string"              // 备注
}
```

### 8.3 通用响应格式

所有接口都遵循统一的响应格式：

#### 成功响应
```json
{
  "code": 200,                    // 响应码：200-成功
  "msg": "操作成功",               // 响应消息
  "data": {                       // 响应数据（具体结构见各接口说明）
    // 具体数据内容
  }
}
```

#### 分页响应
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "records": [                  // 数据列表
      // 具体数据项
    ],
    "total": 100,                 // 总记录数
    "size": 10,                   // 每页大小
    "current": 1,                 // 当前页码
    "pages": 10                   // 总页数
  }
}
```

#### 错误响应
```json
{
  "code": 500,                    // 响应码：500-服务器错误，400-客户端错误等
  "msg": "操作失败：具体错误信息",  // 错误消息
  "data": null                    // 错误时数据为null
}
```

## 9. 业务流程说明

### 9.1 入库流程
1. 新增入库单 → 2. 提交入库单 → 3. 经手人确认 → 4. 审核入库单 → 5. 完成入库

### 9.2 出库流程
1. 新增出库单 → 2. 提交出库单 → 3. 经手人确认 → 4. 审核出库单 → 5. 完成出库

### 9.3 领用流程
1. 新增领用单 → 2. 提交领用单 → 3. 确认领用单 → 4. 审核领用单 → 5. 完成领用

### 9.4 盘点流程
1. 新增盘点单 → 2. 生成盘点明细 → 3. 开始盘点 → 4. 录入盘点结果 → 5. 完成盘点 → 6. 审核盘点 → 7. 应用盘点差异

## 10. 重要说明和注意事项

### 10.1 数据类型说明
- **日期格式**:
  - 日期：`yyyy-MM-dd`（如：2024-01-01）
  - 日期时间：`yyyy-MM-dd HH:mm:ss`（如：2024-01-01 10:30:00）
- **数值类型**:
  - 数量、金额等使用BigDecimal类型，支持小数
  - ID类型：字符串类型的使用String，数值类型的使用Long或Integer
- **枚举值说明**:
  - 物品类别：1-消耗品, 2-备品备件
  - 库存状态：1-正常, 2-不足, 3-过剩
  - 单据状态：1-草稿, 2-待确认, 3-待审核, 4-已通过, 5-已退回, 6-已完成
  - 盘点类型：1-全盘，2-抽盘，3-循环盘点
  - 备件分类：1-关键, 2-常用, 3-次要

### 10.2 业务规则
1. **物品管理**:
   - 物品编码（itemCode）必须唯一
   - 物品类别决定了可用的属性（消耗品属性或备品备件属性）
   - 企业级安全库存用于全局库存告警

2. **库存管理**:
   - 支持多仓库库存管理
   - 库存状态根据当前库存与安全库存的比较自动计算
   - 总库存 = 所有仓库该物品库存之和

3. **单据流程**:
   - 入库流程：草稿 → 待确认 → 待审核 → 已通过 → 完成入库
   - 出库流程：草稿 → 待确认 → 待审核 → 已通过 → 完成出库
   - 领用流程：草稿 → 待确认 → 待审核 → 已通过 → 已完成
   - 盘点流程：草稿 → 进行中 → 已完成 → 已审核 → 应用差异

4. **权限控制**:
   - 制单人可以编辑草稿状态的单据
   - 经手人负责确认单据
   - 审核人负责审核单据
   - 不同角色有不同的操作权限

### 10.3 技术要点
1. **权限控制**: 所有接口都有相应的权限控制，前端需要根据用户权限显示/隐藏相应功能
2. **单据编号**:
   - 入库单号以"WPRK"开头
   - 出库单号以"WPCK"开头
   - 系统自动生成，格式：前缀+年月日+流水号
3. **操作日志**: 重要操作都会记录操作日志，便于追溯
4. **异常处理**: 所有接口都有完善的异常处理机制，会返回详细的错误信息
5. **数据验证**: 使用@Validated注解进行数据验证，确保数据完整性
6. **分页查询**: 列表查询接口都支持分页，返回IPage对象
7. **批量操作**: 删除操作支持批量处理，提高操作效率
8. **事务处理**: 涉及库存变动的操作都使用事务保证数据一致性

### 10.4 前端开发建议
1. **表单验证**: 前端应该实现必要的表单验证，与后端验证形成双重保障
2. **状态管理**: 根据单据状态控制按钮的显示和可操作性
3. **权限控制**: 根据用户权限动态显示菜单和功能按钮
4. **错误处理**: 妥善处理接口返回的错误信息，给用户友好的提示
5. **数据格式**: 严格按照接口文档的数据格式进行数据传输
6. **分页处理**: 合理设置分页大小，避免一次性加载过多数据
7. **缓存策略**: 对于字典数据（如仓库列表、用户列表）可以适当缓存

### 10.5 常见问题
1. **Q: 新增物品时库存信息是必填的吗？**
   A: 使用`/item/add`接口时库存信息是必填的，这相当于物品入库。如果只想登记物品基本信息，使用`/item/register`接口。

2. **Q: 如何处理多仓库库存？**
   A: 系统支持一个物品在多个仓库有库存，每个仓库的库存是独立管理的，总库存是所有仓库库存的总和。

3. **Q: 单据状态可以回退吗？**
   A: 一般情况下单据状态不能回退，但审核人可以将单据退回到前一状态。

4. **Q: 库存不足时还能出库吗？**
   A: 系统会检查库存是否充足，库存不足时会阻止出库操作。

5. **Q: 盘点差异如何处理？**
   A: 盘点完成并审核通过后，可以应用盘点差异，系统会自动调整库存。

---

**文档版本**: v2.0
**更新日期**: 2025-01-10
**维护人员**: 系统开发团队
**更新内容**: 补充详细的参数和返回结果说明
