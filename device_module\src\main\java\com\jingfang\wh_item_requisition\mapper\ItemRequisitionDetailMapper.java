package com.jingfang.wh_item_requisition.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jingfang.wh_item_requisition.module.entity.ItemRequisitionDetail;
import com.jingfang.wh_item_requisition.module.vo.ItemRequisitionDetailVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物品领用单明细Mapper接口
 */
@Mapper
public interface ItemRequisitionDetailMapper extends BaseMapper<ItemRequisitionDetail> {
    
    /**
     * 根据领用单ID删除明细
     *
     * @param requisitionId 领用单ID
     * @return 删除数量
     */
    int deleteByRequisitionId(@Param("requisitionId") String requisitionId);
    
    /**
     * 根据领用单ID查询明细列表
     *
     * @param requisitionId 领用单ID
     * @return 明细列表
     */
    List<ItemRequisitionDetail> selectByRequisitionId(@Param("requisitionId") String requisitionId);
    
    /**
     * 根据领用单ID查询明细列表（包含物品详细信息）
     *
     * @param requisitionId 领用单ID
     * @return 明细列表（包含物品详细信息）
     */
    List<ItemRequisitionDetailVo.ItemRequisitionDetailItemVo> selectDetailWithItemInfo(@Param("requisitionId") String requisitionId);
} 