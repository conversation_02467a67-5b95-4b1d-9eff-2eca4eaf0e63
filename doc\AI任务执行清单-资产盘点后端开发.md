# AI任务执行清单 - 资产盘点后端开发

## 执行环境信息
- 项目根目录: device_monitor
- 后端模块: device_module
- 控制器目录: device_monitor-admin/src/main/java/com/jingfang/web/controller
- 现有资产模块: com.jingfang.web.controller.asset
- 数据库: MySQL
- 框架: RuoYi + Spring Boot + MyBatis Plus

## 任务执行顺序

### 阶段1: 数据库设计 (优先级: 最高)

#### 任务1.1: 创建数据表SQL脚本
**执行动作**: 创建SQL文件
**文件路径**: `doc/sql/asset_stocktaking_tables.sql`
**内容要求**:
```sql
-- 1. 盘点计划表
CREATE TABLE asset_stocktaking_plan (
    plan_id VARCHAR(32) PRIMARY KEY,
    plan_name VARCHAR(100) NOT NULL,
    plan_type TINYINT DEFAULT 1,
    plan_scope TEXT,
    start_date DATE,
    end_date DATE,
    responsible_user_id BIGINT,
    status TINYINT DEFAULT 1,
    create_by VARCHAR(64),
    create_time DATETIME,
    update_by VARCHAR(64),
    update_time DATETIME,
    remark TEXT
);

-- 2. 盘点任务表
CREATE TABLE asset_stocktaking_task (
    task_id VARCHAR(32) PRIMARY KEY,
    plan_id VARCHAR(32) NOT NULL,
    task_name VARCHAR(100),
    assigned_user_id BIGINT,
    asset_scope TEXT,
    expected_count INT DEFAULT 0,
    actual_count INT DEFAULT 0,
    status TINYINT DEFAULT 1,
    start_time DATETIME,
    end_time DATETIME,
    create_by VARCHAR(64),
    create_time DATETIME
);

-- 3. 盘点记录表
CREATE TABLE asset_stocktaking_record (
    record_id VARCHAR(32) PRIMARY KEY,
    task_id VARCHAR(32) NOT NULL,
    asset_id VARCHAR(32),
    asset_code VARCHAR(50),
    found_status TINYINT,
    actual_location VARCHAR(200),
    actual_status TINYINT,
    inventory_user_id BIGINT,
    inventory_time DATETIME,
    remark TEXT
);

-- 4. 盘点差异表
CREATE TABLE asset_stocktaking_difference (
    diff_id VARCHAR(32) PRIMARY KEY,
    plan_id VARCHAR(32) NOT NULL,
    asset_id VARCHAR(32),
    diff_type TINYINT,
    book_value TEXT,
    actual_value TEXT,
    diff_reason VARCHAR(500),
    handle_status TINYINT DEFAULT 1,
    handle_suggestion TEXT,
    create_time DATETIME
);
```

#### 任务1.2: 创建索引优化脚本
**执行动作**: 创建SQL文件
**文件路径**: `doc/sql/asset_stocktaking_indexes.sql`
**内容要求**: 为主要查询字段添加索引

### 阶段2: 实体类设计 (优先级: 最高)

#### 任务2.1: 创建盘点计划实体类
**执行动作**: 创建Java文件
**文件路径**: `device_module/src/main/java/com/jingfang/asset_stocktaking/module/entity/AssetStocktakingPlan.java`
**技术要求**:
- 使用@TableName注解映射表名
- 使用@TableId注解标识主键
- 添加@JsonFormat注解处理日期格式
- 包含完整的getter/setter和toString方法

#### 任务2.2: 创建盘点任务实体类
**执行动作**: 创建Java文件
**文件路径**: `device_module/src/main/java/com/jingfang/asset_stocktaking/module/entity/AssetStocktakingTask.java`

#### 任务2.3: 创建盘点记录实体类
**执行动作**: 创建Java文件
**文件路径**: `device_module/src/main/java/com/jingfang/asset_stocktaking/module/entity/AssetStocktakingRecord.java`

#### 任务2.4: 创建盘点差异实体类
**执行动作**: 创建Java文件
**文件路径**: `device_module/src/main/java/com/jingfang/asset_stocktaking/module/entity/AssetStocktakingDifference.java`

#### 任务2.5: 创建DTO类
**执行动作**: 创建多个Java文件
**文件路径**: `device_module/src/main/java/com/jingfang/asset_stocktaking/module/dto/`
**文件列表**:
- StocktakingPlanDto.java
- StocktakingTaskDto.java
- StocktakingRecordDto.java
- StocktakingDifferenceDto.java

#### 任务2.6: 创建VO类
**执行动作**: 创建多个Java文件
**文件路径**: `device_module/src/main/java/com/jingfang/asset_stocktaking/module/vo/`
**文件列表**:
- StocktakingPlanVo.java
- StocktakingTaskVo.java
- StocktakingRecordVo.java
- StocktakingReportVo.java

#### 任务2.7: 创建请求对象类
**执行动作**: 创建多个Java文件
**文件路径**: `device_module/src/main/java/com/jingfang/asset_stocktaking/module/request/`
**文件列表**:
- PlanSearchRequest.java
- TaskSearchRequest.java
- RecordSearchRequest.java

### 阶段3: 数据访问层 (优先级: 高)

#### 任务3.1: 创建Mapper接口
**执行动作**: 创建多个Java文件
**文件路径**: `device_module/src/main/java/com/jingfang/asset_stocktaking/mapper/`
**文件列表**:
- StocktakingPlanMapper.java (继承BaseMapper<AssetStocktakingPlan>)
- StocktakingTaskMapper.java (继承BaseMapper<AssetStocktakingTask>)
- StocktakingRecordMapper.java (继承BaseMapper<AssetStocktakingRecord>)
- StocktakingDifferenceMapper.java (继承BaseMapper<AssetStocktakingDifference>)

#### 任务3.2: 创建Mapper XML文件
**执行动作**: 创建多个XML文件
**文件路径**: `device_module/src/main/resources/mapper/`
**文件列表**:
- StocktakingPlanMapper.xml
- StocktakingTaskMapper.xml
- StocktakingRecordMapper.xml
- StocktakingDifferenceMapper.xml

### 阶段4: 服务层 (优先级: 高)

#### 任务4.1: 创建Service接口
**执行动作**: 创建多个Java文件
**文件路径**: `device_module/src/main/java/com/jingfang/asset_stocktaking/service/`
**文件列表**:
- StocktakingPlanService.java (继承IService<AssetStocktakingPlan>)
- StocktakingTaskService.java (继承IService<AssetStocktakingTask>)
- StocktakingRecordService.java (继承IService<AssetStocktakingRecord>)
- StocktakingDifferenceService.java (继承IService<AssetStocktakingDifference>)
- StocktakingReportService.java

#### 任务4.2: 创建Service实现类
**执行动作**: 创建多个Java文件
**文件路径**: `device_module/src/main/java/com/jingfang/asset_stocktaking/service/impl/`
**文件列表**:
- StocktakingPlanServiceImpl.java
- StocktakingTaskServiceImpl.java
- StocktakingRecordServiceImpl.java
- StocktakingDifferenceServiceImpl.java
- StocktakingReportServiceImpl.java

### 阶段5: 控制器层 (优先级: 高)

#### 任务5.1: 创建Controller类
**执行动作**: 创建多个Java文件
**文件路径**: `device_monitor-admin/src/main/java/com/jingfang/web/controller/asset/stocktaking/`
**文件列表**:
- StocktakingPlanController.java
- StocktakingTaskController.java
- StocktakingRecordController.java
- StocktakingDifferenceController.java
- StocktakingReportController.java

**技术要求**:
- 继承BaseController
- 使用@RestController和@RequestMapping注解
- 添加@PreAuthorize权限控制注解
- 添加@Log操作日志注解
- 使用AjaxResult统一返回格式

### 阶段6: 核心业务逻辑 (优先级: 中)

#### 任务6.1: 实现任务分发算法
**执行动作**: 在StocktakingTaskServiceImpl中实现
**方法名**: distributeTasksByPlan(String planId)
**算法要求**:
- 根据盘点计划范围生成任务
- 按部门/位置分组资产
- 均衡分配给指定人员
- 生成任务记录

#### 任务6.2: 实现差异分析算法
**执行动作**: 在StocktakingDifferenceServiceImpl中实现
**方法名**: analyzeDifferences(String planId)
**算法要求**:
- 对比盘点记录与资产台账
- 识别盘盈、盘亏、位置差异、状态差异
- 计算差异影响程度
- 生成处理建议

#### 任务6.3: 实现进度跟踪功能
**执行动作**: 在StocktakingTaskServiceImpl中实现
**方法名**: calculateProgress(String planId)
**功能要求**:
- 实时计算完成百分比
- 统计各状态任务数量
- 预估完成时间

### 阶段7: 权限和安全配置 (优先级: 中)

#### 任务7.1: 配置权限字符串
**执行动作**: 在Controller方法上添加@PreAuthorize注解
**权限列表**:
- stocktaking:plan:view
- stocktaking:plan:add
- stocktaking:plan:edit
- stocktaking:plan:remove
- stocktaking:task:view
- stocktaking:task:execute
- stocktaking:record:view
- stocktaking:difference:view
- stocktaking:report:export

#### 任务7.2: 添加操作日志
**执行动作**: 在Controller方法上添加@Log注解
**日志配置**:
- title = "资产盘点"
- businessType = BusinessType.INSERT/UPDATE/DELETE/EXPORT

### 阶段8: 集成现有模块 (优先级: 中)

#### 任务8.1: 集成资产台账模块
**执行动作**: 在StocktakingDifferenceServiceImpl中调用AssetService
**集成点**:
- 获取资产基础信息
- 更新资产状态和位置
- 记录变更历史

#### 任务8.2: 集成资产处置模块
**执行动作**: 在盘点后处理中调用AssetDisposalService
**集成点**:
- 盘亏资产自动创建处置申请
- 盘盈资产入库处理

## 执行注意事项

### 代码规范
1. 不使用Swagger注解
2. 每个实体类独立文件
3. 使用中文注释
4. 遵循RuoYi命名规范

### 依赖关系
1. 实体类 → Mapper → Service → Controller
2. 先完成基础CRUD，再实现复杂业务逻辑
3. 最后进行模块集成

### 测试验证
1. 每完成一个类，立即编译检查
2. 每完成一个模块，进行单元测试
3. 最后进行集成测试

### 错误处理
1. 使用RuoYi统一异常处理
2. 关键操作添加事务注解
3. 输入参数验证
