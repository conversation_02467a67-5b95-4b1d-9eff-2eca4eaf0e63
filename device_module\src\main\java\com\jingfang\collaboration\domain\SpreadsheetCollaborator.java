package com.jingfang.collaboration.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 表格协作者实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("collaboration_spreadsheet_collaborator")
public class SpreadsheetCollaborator {
    
    /**
     * 协作记录ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    
    /**
     * 表格ID
     */
    private String spreadsheetId;
    
    /**
     * 协作者用户ID
     */
    private Long userId;
    
    /**
     * 协作者用户名
     */
    private String userName;
    
    /**
     * 协作者昵称
     */
    private String nickName;
    
    /**
     * 协作者部门ID
     */
    private Long deptId;
    
    /**
     * 协作者部门名称
     */
    private String deptName;
    
    /**
     * 权限类型（owner:所有者 editor:编辑者 commenter:评论者 viewer:查看者）
     */
    private String permission;
    
    /**
     * 邀请者ID
     */
    private Long inviteBy;
    
    /**
     * 邀请者姓名
     */
    private String inviteByName;
    
    /**
     * 邀请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date inviteTime;
    
    /**
     * 接受邀请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date acceptTime;
    
    /**
     * 状态（0待接受 1已接受 2已拒绝 3已移除）
     */
    private String status;
    
    /**
     * 最后访问时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastAccessTime;
    
    /**
     * 是否在线（0离线 1在线）
     */
    private String isOnline;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    
    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private Integer delFlag;
    
    /**
     * 备注
     */
    private String remark;
}
