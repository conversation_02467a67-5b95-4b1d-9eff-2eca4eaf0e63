package com.jingfang.collaboration.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;



/**
 * 协作者数据传输对象
 */
@Data
public class CollaboratorDto {
    
    /**
     * 协作记录ID
     */
    private String id;
    
    /**
     * 表格ID
     */
    private String spreadsheetId;
    
    /**
     * 协作者用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 协作者用户名
     */
    private String userName;
    
    /**
     * 协作者昵称
     */
    private String nickName;
    
    /**
     * 协作者部门ID
     */
    private Long deptId;
    
    /**
     * 协作者部门名称
     */
    private String deptName;
    
    /**
     * 权限类型（owner:所有者 editor:编辑者 commenter:评论者 viewer:查看者）
     */
    @NotBlank(message = "权限类型不能为空")
    private String permission;
    
    /**
     * 备注
     */
    private String remark;
}
