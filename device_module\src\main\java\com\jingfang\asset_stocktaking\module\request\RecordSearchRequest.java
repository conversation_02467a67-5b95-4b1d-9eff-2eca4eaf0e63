package com.jingfang.asset_stocktaking.module.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 盘点记录查询请求对象
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
public class RecordSearchRequest implements Serializable {

    /**
     * 盘点任务ID
     */
    private String taskId;

    /**
     * 盘点计划ID
     */
    private String planId;

    /**
     * 资产ID
     */
    private String assetId;

    /**
     * 资产编码（模糊查询）
     */
    private String assetCode;

    /**
     * 资产名称（模糊查询）
     */
    private String assetName;

    /**
     * 发现状态：1-找到，0-未找到
     */
    private Integer foundStatus;

    /**
     * 实际状态
     */
    private Integer actualStatus;

    /**
     * 盘点人ID
     */
    private Long inventoryUserId;

    /**
     * 盘点时间范围-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inventoryTimeBegin;

    /**
     * 盘点时间范围-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inventoryTimeEnd;

    /**
     * 资产分类ID列表
     */
    private List<Integer> categoryIds;

    /**
     * 部门ID列表
     */
    private List<Long> deptIds;

    /**
     * 存放位置ID列表
     */
    private List<Integer> locationIds;

    /**
     * 是否只查询异常记录
     */
    private Boolean onlyAbnormal;

    /**
     * 是否只查询差异记录
     */
    private Boolean onlyDifference;

    /**
     * 是否只查询我的记录
     */
    private Boolean onlyMyRecords;

    /**
     * 资产价值范围-最小值
     */
    private Double assetValueMin;

    /**
     * 资产价值范围-最大值
     */
    private Double assetValueMax;

    /**
     * 部门ID（数据权限过滤）
     */
    private Long deptId;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序方向：asc-升序，desc-降序
     */
    private String orderDirection;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 是否查询资产详细信息
     */
    private Boolean includeAssetInfo;

    /**
     * 是否查询差异信息
     */
    private Boolean includeDifferenceInfo;

    /**
     * 关键词搜索（资产编码、资产名称、位置）
     */
    private String keyword;

    private static final long serialVersionUID = 1L;
}
