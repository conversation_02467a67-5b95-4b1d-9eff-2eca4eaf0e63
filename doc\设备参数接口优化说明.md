# 设备参数接口优化说明

## 修改概述

根据您的需求，对设备参数相关接口进行了优化，确保：
1. 获取设备运行参数的接口只返回 `param_type = 0` 的一般参数
2. 创建了新的获取设备告警信息的接口，只返回 `param_type = 1` 的告警参数
3. 创建了新的获取设备控制参数的接口，只返回 `param_type = 3` 的控制参数

## 接口变更详情

### 1. 现有接口优化

#### 获取设备运行参数接口

**接口路径**: `GET /device/list/detail/normal`

**修改内容**:
- 查询条件增加：`param_type = 0`（只查询一般参数）
- 响应数据增加：`rwType` 和 `paramType` 字段
- 使用MQTT方式实时查询参数值

**查询逻辑**:
```java
LambdaQueryWrapper<DeviceParam> queryWrapper = new LambdaQueryWrapper<>();
queryWrapper.eq(DeviceParam::getDeviceId, deviceId);
queryWrapper.eq(DeviceParam::getDelFlag, 0);
queryWrapper.eq(DeviceParam::getParamType, 0); // 只查询一般参数
```

### 2. 新增接口

#### 获取设备告警信息接口

**接口路径**: `GET /device/alert/info`

**功能说明**:
- 专门用于获取设备告警信息
- 只查询 `param_type = 1` 的告警参数
- 支持MQTT实时查询告警状态
- 自动判断告警是否触发

**查询逻辑**:
```java
LambdaQueryWrapper<DeviceParam> queryWrapper = new LambdaQueryWrapper<>();
queryWrapper.eq(DeviceParam::getDeviceId, deviceId);
queryWrapper.eq(DeviceParam::getDelFlag, 0);
queryWrapper.eq(DeviceParam::getParamType, 1); // 只查询告警参数
```

#### 获取设备控制参数接口

**接口路径**: `GET /device/control/params`

**功能说明**:
- 专门用于获取设备控制参数
- 只查询 `param_type = 3` 的控制参数
- 根据 `rw_type` 决定是否查询实时值
- 支持多种控制参数类型

**查询逻辑**:
```java
LambdaQueryWrapper<DeviceParam> queryWrapper = new LambdaQueryWrapper<>();
queryWrapper.eq(DeviceParam::getDeviceId, deviceId);
queryWrapper.eq(DeviceParam::getDelFlag, 0);
queryWrapper.eq(DeviceParam::getParamType, 3); // 只查询控制参数
```

## 代码实现

### 1. Service层新增方法

在 `DeviceParamService` 接口中新增：
```java
/**
 * 获取设备告警信息（新接口）
 * @param deviceId 设备ID
 * @return 设备告警信息列表
 */
List<DeviceParamVo> getDeviceAlertInfo(Long deviceId);

/**
 * 获取设备控制参数
 * @param deviceId 设备ID
 * @return 设备控制参数列表
 */
List<DeviceParamVo> getDeviceControlParams(Long deviceId);
```

在 `DeviceParamServiceImpl` 实现类中：
- 实现了 `getDeviceAlertInfo` 方法
- 实现了 `getDeviceControlParams` 方法
- 支持MQTT实时查询告警参数和控制参数
- 添加了 `isAlertTriggered` 方法用于判断告警状态
- 控制参数根据 `rw_type` 决定是否查询实时值

### 2. Controller层新增接口

在 `DeviceController` 中新增：
```java
/**
 * 获取设备告警信息（新接口）
 */
@GetMapping("/alert/info")
public TableDataInfo getDeviceAlertInfo(Long deviceId){
    try {
        List<DeviceParamVo> vos = paramService.getDeviceAlertInfo(deviceId);
        return getDataTable(vos);
    } catch (Exception e) {
        logger.error("获取设备告警信息失败", e);
        return getDataTable(new ArrayList<>());
    }
}

/**
 * 获取设备控制参数
 */
@GetMapping("/control/params")
public TableDataInfo getDeviceControlParams(Long deviceId){
    try {
        List<DeviceParamVo> vos = paramService.getDeviceControlParams(deviceId);
        return getDataTable(vos);
    } catch (Exception e) {
        logger.error("获取设备控制参数失败", e);
        return getDataTable(new ArrayList<>());
    }
}
```

## 告警判断逻辑

新增的 `isAlertTriggered` 方法支持多种数据类型的告警判断：

### 1. 布尔值类型
- `true` → 告警
- `false` → 正常

### 2. 数值类型
- 非零值 → 告警
- 零值 → 正常

### 3. 字符串类型
支持的告警状态值：
- `"true"`, `"1"`, `"on"`, `"active"` → 告警
- `"告警"`, `"报警"`, `"故障"`, `"异常"` → 告警
- 其他值 → 正常

## 接口响应格式

### 运行参数接口响应
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "paramId": 1,
      "paramName": "冷却水进水流量",
      "paramValue": "25.6L/min",
      "rangeStart": 0.0,
      "rangeEnd": 100.0,
      "rwType": 0,
      "paramType": 0,
      "alertValue": false
    }
  ],
  "total": 1
}
```

### 告警信息接口响应
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "paramId": 2,
      "paramName": "高温告警",
      "paramValue": "否",
      "alertValue": false,
      "rwType": 0,
      "paramType": 1,
      "rangeStart": null,
      "rangeEnd": null
    }
  ],
  "total": 1
}
```

### 控制参数接口响应
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "paramId": 3,
      "paramName": "温度设定值",
      "paramValue": "25.0°C",
      "rangeStart": 10.0,
      "rangeEnd": 40.0,
      "rwType": 2,
      "paramType": 3,
      "alertValue": false
    }
  ],
  "total": 1
}
```

## 使用示例

### 1. 获取设备运行参数
```bash
curl "http://localhost:8080/device/list/detail/normal?deviceId=1"
```

### 2. 获取设备告警信息
```bash
curl "http://localhost:8080/device/alert/info?deviceId=1"
```

### 3. 获取设备控制参数
```bash
curl "http://localhost:8080/device/control/params?deviceId=1"
```

## 错误处理

### 1. 设备离线
- 返回空列表
- 记录警告日志

### 2. MQTT查询失败
- 返回配置的参数但值显示为 "--"
- 告警状态设为 `false`
- 记录错误日志

### 3. 参数配置为空
- 返回空列表
- 记录信息日志

## 性能优化

### 1. 精确查询
- 通过 `param_type` 字段精确区分参数类型
- 避免不必要的数据处理

### 2. 异步处理
- MQTT查询使用异步方式
- 支持超时控制（15秒）

### 3. 错误恢复
- 查询失败时返回基础配置信息
- 保证接口可用性

## 注意事项

1. **参数配置**: 确保 `param_type` 字段正确配置
2. **MQTT连接**: 确保MQTT服务正常运行
3. **设备在线**: 只有在线设备才能查询实时数据
4. **告警逻辑**: 根据实际业务需求调整告警判断逻辑
5. **超时处理**: 可根据网络情况调整超时时间

## 后续扩展

1. **告警阈值**: 可以基于参数的正常范围判断告警
2. **告警级别**: 可以扩展支持不同级别的告警
3. **历史告警**: 可以记录告警历史数据
4. **告警通知**: 可以集成告警通知功能

通过这些优化，设备参数查询功能更加精确和高效，为后续的功能扩展奠定了良好的基础！
