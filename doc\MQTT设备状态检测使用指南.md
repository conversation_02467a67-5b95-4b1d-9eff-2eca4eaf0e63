# MQTT设备状态检测使用指南

## 1. 功能概述

本指南介绍如何使用新的MQTT协议来检测设备在线状态，替代原有的Socket连接检测方式。MQTT方式提供了更高的可靠性、实时性和扩展性。

## 2. 系统要求

### 2.1 MQTT服务器配置
- MQTT Broker地址: `tcp://192.168.110.135:10883`
- 用户名: `admin`
- 密码: `429498517`
- 客户端ID: `device_monitor_client`

### 2.2 主题配置
- **请求主题**: `/sys/thing/node/status/get/device_monitor_client`
- **响应主题**: `/sys/thing/node/status/get_reply/device_monitor_client`

## 3. 配置步骤

### 3.1 应用配置
在 `application.yml` 中确认MQTT配置：

```yaml
mqtt:
  server-uri: tcp://192.168.110.135:10883
  client-id: device_monitor_client
  username: admin
  password: 429498517
  device:
    status:
      request:
        topic: /sys/thing/node/status/get/device_monitor_client
      response:
        topic: /sys/thing/node/status/get_reply/device_monitor_client
      timeout: 10
```

### 3.2 定时任务配置
执行SQL脚本 `sql/device_mqtt_status_job.sql` 来添加定时任务：

```sql
-- 执行脚本添加定时任务
source sql/device_mqtt_status_job.sql;
```

## 4. 使用方法

### 4.1 自动检测
系统启动后会自动执行以下定时任务：
- **设备基础信息缓存刷新**: 每小时执行一次
- **设备在线状态检测**: 每5分钟执行一次
- **设备缓存和状态综合刷新**: 每30分钟执行一次

### 4.2 手动测试
可以通过以下API接口进行手动测试：

#### 4.2.1 测试MQTT设备状态查询
```http
GET /device/status/test/mqtt
```

#### 4.2.2 测试设备连接状态检测
```http
GET /device/status/test/connection
```

#### 4.2.3 刷新设备缓存和状态
```http
GET /device/status/test/refresh
```

### 4.3 管理界面操作
1. 登录系统管理界面
2. 进入 **系统监控** -> **定时任务**
3. 查看和管理设备状态检测相关的定时任务

## 5. MQTT通信协议

### 5.1 请求格式
```json
{
    "id": "唯一请求ID",
    "version": "V1.0",
    "ack": 1,
    "params": []
}
```

### 5.2 响应格式
```json
{
    "id": "对应的请求ID",
    "version": "V1.0",
    "code": 0,
    "params": [
        {
            "clientID": "设备IP地址",
            "commStatus": "onLine|offLine"
        }
    ]
}
```

## 6. 故障排除

### 6.1 MQTT连接问题
**问题**: 无法连接到MQTT服务器
**解决方案**:
1. 检查MQTT服务器是否正常运行
2. 验证网络连接和防火墙设置
3. 确认用户名和密码是否正确

### 6.2 设备状态检测失败
**问题**: 设备状态检测返回错误
**解决方案**:
1. 查看应用日志中的错误信息
2. 检查MQTT主题配置是否正确
3. 验证设备是否支持MQTT状态查询

### 6.3 回退机制
当MQTT检测失败时，系统会自动回退到Socket检测方式：
- 日志中会显示 "MQTT设备状态检测失败，回退到Socket检测"
- 检测结果会标注为 "Socket检测"

## 7. 监控和日志

### 7.1 日志查看
关键日志信息：
- `开始测试MQTT设备状态查询`
- `MQTT设备状态查询完成，在线设备IP: {}`
- `MQTT设备状态检测失败，回退到Socket检测`

### 7.2 性能监控
- 检测响应时间: 通常在1-3秒内完成
- 超时设置: 默认10秒
- 并发处理: 支持多个设备同时检测

## 8. 最佳实践

### 8.1 定时任务频率
- 建议设备在线状态检测频率: 每5分钟
- 避免过于频繁的检测影响系统性能
- 根据实际需求调整检测间隔

### 8.2 异常处理
- 始终保持Socket检测作为备选方案
- 设置合理的超时时间
- 记录详细的错误日志便于问题排查

### 8.3 扩展建议
- 可以根据设备类型配置不同的检测策略
- 支持设备状态变化的实时通知
- 考虑添加设备状态历史记录功能

## 9. 版本更新说明

### 9.1 新增功能
- 基于MQTT协议的设备状态检测
- 自动回退到Socket检测的容错机制
- 完善的定时任务配置
- 详细的测试接口和日志记录

### 9.2 兼容性
- 保持与原有Socket检测方式的兼容
- 不影响现有的设备管理功能
- 平滑升级，无需修改现有配置
