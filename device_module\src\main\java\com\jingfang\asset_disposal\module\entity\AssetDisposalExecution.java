package com.jingfang.asset_disposal.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 资产处置执行记录表
 * @TableName asset_disposal_execution
 */
@TableName(value = "asset_disposal_execution")
@Data
public class AssetDisposalExecution implements Serializable {
    
    /**
     * 执行记录ID
     */
    @TableId(type = IdType.INPUT)
    private String executionId;
    
    /**
     * 处置单ID
     */
    private String disposalId;
    
    /**
     * 执行人ID
     */
    private Long executorId;
    
    /**
     * 执行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date executionTime;
    
    /**
     * 实际处置价值
     */
    private BigDecimal actualDisposalValue;
    
    /**
     * 处置凭证（发票、收据等）
     */
    private String disposalCertificate;
    
    /**
     * 执行说明
     */
    private String executionDescription;
    
    /**
     * 执行状态(1-执行中, 2-已完成, 3-异常)
     */
    private Integer executionStatus;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
} 