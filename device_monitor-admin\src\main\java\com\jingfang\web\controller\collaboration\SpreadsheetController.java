package com.jingfang.web.controller.collaboration;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.collaboration.dto.InviteDto;
import com.jingfang.collaboration.dto.SpreadsheetDto;
import com.jingfang.collaboration.service.SpreadsheetService;
import com.jingfang.collaboration.vo.CollaboratorVo;
import com.jingfang.collaboration.vo.OnlineUserVo;
import com.jingfang.collaboration.vo.SpreadsheetVo;
import com.jingfang.common.annotation.Log;
import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.common.enums.BusinessType;
import com.jingfang.common.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 在线表格Controller
 */
@Slf4j
@Api(tags = "在线表格管理")
@RestController
@RequestMapping("/collaboration/spreadsheet")
public class SpreadsheetController extends BaseController {
    
    @Autowired
    private SpreadsheetService spreadsheetService;
    
    /**
     * 查询表格列表
     */
    @ApiOperation("查询表格列表")
    @PreAuthorize("@ss.hasPermi('collaboration:spreadsheet:list')")
    @GetMapping("/list")
    public AjaxResult list(@RequestParam(defaultValue = "1") int pageNum,
                          @RequestParam(defaultValue = "10") int pageSize,
                          @RequestParam(required = false) String title,
                          @RequestParam(required = false) String status) {
        Long userId = SecurityUtils.getUserId();
        IPage<SpreadsheetVo> page = spreadsheetService.getSpreadsheetList(pageNum, pageSize, title, status, userId);
        return AjaxResult.success(page);
    }
    
    /**
     * 查询用户有权限访问的表格列表
     */
    @ApiOperation("查询用户有权限访问的表格列表")
    @PreAuthorize("@ss.hasPermi('collaboration:spreadsheet:list')")
    @GetMapping("/accessible")
    public AjaxResult getAccessibleSpreadsheets(@RequestParam(defaultValue = "1") int pageNum,
                                               @RequestParam(defaultValue = "10") int pageSize,
                                               @RequestParam(required = false) String title,
                                               @RequestParam(required = false) String status) {
        Long userId = SecurityUtils.getUserId();
        IPage<SpreadsheetVo> page = spreadsheetService.getUserAccessibleSpreadsheets(pageNum, pageSize, title, status, userId);
        return AjaxResult.success(page);
    }
    
    /**
     * 获取表格详细信息
     */
    @ApiOperation("获取表格详细信息")
    @PreAuthorize("@ss.hasPermi('collaboration:spreadsheet:query')")
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable String id) {
        Long userId = SecurityUtils.getUserId();
        SpreadsheetVo vo = spreadsheetService.getSpreadsheetDetail(id, userId);
        return AjaxResult.success(vo);
    }
    
    /**
     * 新增表格
     */
    @ApiOperation("新增表格")
    @PreAuthorize("@ss.hasPermi('collaboration:spreadsheet:add')")
    @Log(title = "在线表格", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SpreadsheetDto dto) {
        Long userId = SecurityUtils.getUserId();
        String id = spreadsheetService.createSpreadsheet(dto, userId);
        return AjaxResult.success("创建成功", id);
    }
    
    /**
     * 修改表格
     */
    @ApiOperation("修改表格")
    @PreAuthorize("@ss.hasPermi('collaboration:spreadsheet:edit')")
    @Log(title = "在线表格", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SpreadsheetDto dto) {
        Long userId = SecurityUtils.getUserId();
        boolean result = spreadsheetService.updateSpreadsheet(dto, userId);
        return result ? AjaxResult.success("修改成功") : AjaxResult.error("修改失败");
    }
    
    /**
     * 删除表格
     */
    @ApiOperation("删除表格")
    @PreAuthorize("@ss.hasPermi('collaboration:spreadsheet:remove')")
    @Log(title = "在线表格", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        Long userId = SecurityUtils.getUserId();
        boolean result = spreadsheetService.deleteSpreadsheets(ids, userId);
        return result ? AjaxResult.success("删除成功") : AjaxResult.error("删除失败");
    }
    
    /**
     * 保存表格数据
     */
    @ApiOperation("保存表格数据")
    @PreAuthorize("@ss.hasPermi('collaboration:spreadsheet:edit')")
    @Log(title = "保存表格数据", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/save")
    public AjaxResult saveData(@PathVariable String id, @RequestBody String data) {
        Long userId = SecurityUtils.getUserId();
        boolean result = spreadsheetService.saveSpreadsheetData(id, data, userId);
        return result ? AjaxResult.success("保存成功") : AjaxResult.error("保存失败");
    }
    
    /**
     * 邀请协作者
     */
    @ApiOperation("邀请协作者")
    @PreAuthorize("@ss.hasPermi('collaboration:spreadsheet:invite')")
    @Log(title = "邀请协作者", businessType = BusinessType.INSERT)
    @PostMapping("/invite")
    public AjaxResult invite(@Validated @RequestBody InviteDto dto) {
        Long userId = SecurityUtils.getUserId();
        boolean result = spreadsheetService.inviteCollaborators(dto, userId);
        return result ? AjaxResult.success("邀请成功") : AjaxResult.error("邀请失败");
    }
    
    /**
     * 接受协作邀请
     */
    @ApiOperation("接受协作邀请")
    @PostMapping("/accept/{collaboratorId}")
    public AjaxResult acceptInvitation(@PathVariable String collaboratorId) {
        Long userId = SecurityUtils.getUserId();
        boolean result = spreadsheetService.acceptInvitation(collaboratorId, userId);
        return result ? AjaxResult.success("接受邀请成功") : AjaxResult.error("接受邀请失败");
    }
    
    /**
     * 拒绝协作邀请
     */
    @ApiOperation("拒绝协作邀请")
    @PostMapping("/reject/{collaboratorId}")
    public AjaxResult rejectInvitation(@PathVariable String collaboratorId) {
        Long userId = SecurityUtils.getUserId();
        boolean result = spreadsheetService.rejectInvitation(collaboratorId, userId);
        return result ? AjaxResult.success("拒绝邀请成功") : AjaxResult.error("拒绝邀请失败");
    }
    
    /**
     * 移除协作者
     */
    @ApiOperation("移除协作者")
    @PreAuthorize("@ss.hasPermi('collaboration:spreadsheet:manage')")
    @Log(title = "移除协作者", businessType = BusinessType.DELETE)
    @DeleteMapping("/collaborator/{collaboratorId}")
    public AjaxResult removeCollaborator(@PathVariable String collaboratorId) {
        Long userId = SecurityUtils.getUserId();
        boolean result = spreadsheetService.removeCollaborator(collaboratorId, userId);
        return result ? AjaxResult.success("移除成功") : AjaxResult.error("移除失败");
    }
    
    /**
     * 修改协作者权限
     */
    @ApiOperation("修改协作者权限")
    @PreAuthorize("@ss.hasPermi('collaboration:spreadsheet:manage')")
    @Log(title = "修改协作者权限", businessType = BusinessType.UPDATE)
    @PutMapping("/collaborator/{collaboratorId}/permission")
    public AjaxResult updatePermission(@PathVariable String collaboratorId, @RequestParam String permission) {
        Long userId = SecurityUtils.getUserId();
        boolean result = spreadsheetService.updateCollaboratorPermission(collaboratorId, permission, userId);
        return result ? AjaxResult.success("权限修改成功") : AjaxResult.error("权限修改失败");
    }
    
    /**
     * 查询表格协作者列表
     */
    @ApiOperation("查询表格协作者列表")
    @PreAuthorize("@ss.hasPermi('collaboration:spreadsheet:query')")
    @GetMapping("/{id}/collaborators")
    public AjaxResult getCollaborators(@PathVariable String id) {
        Long userId = SecurityUtils.getUserId();
        List<CollaboratorVo> collaborators = spreadsheetService.getCollaborators(id, userId);
        return AjaxResult.success(collaborators);
    }
    
    /**
     * 查询在线用户列表
     */
    @ApiOperation("查询在线用户列表")
    @PreAuthorize("@ss.hasPermi('collaboration:spreadsheet:query')")
    @GetMapping("/{id}/users")
    public AjaxResult getOnlineUsers(@PathVariable String id) {
        Long userId = SecurityUtils.getUserId();
        List<OnlineUserVo> onlineUsers = spreadsheetService.getOnlineUsers(id, userId);
        return AjaxResult.success(onlineUsers);
    }
    
    /**
     * 生成分享链接
     */
    @ApiOperation("生成分享链接")
    @PreAuthorize("@ss.hasPermi('collaboration:spreadsheet:share')")
    @Log(title = "生成分享链接", businessType = BusinessType.UPDATE)
    @PostMapping("/share")
    public AjaxResult generateShareUrl(@RequestParam String spreadsheetId,
                                      @RequestParam(required = false) String password,
                                      @RequestParam(required = false) Long expireDays) {
        Long userId = SecurityUtils.getUserId();
        String token = spreadsheetService.generateShareUrl(spreadsheetId, password, expireDays, userId);
        return AjaxResult.success("分享链接生成成功", token);
    }
    
    /**
     * 验证分享链接
     */
    @ApiOperation("验证分享链接")
    @PostMapping("/share/validate")
    public AjaxResult validateShareUrl(@RequestParam String token, @RequestParam(required = false) String password) {
        SpreadsheetVo vo = spreadsheetService.validateShareUrl(token, password);
        return AjaxResult.success(vo);
    }

    /**
     * 查询用户的协作邀请列表
     */
    @ApiOperation("查询用户的协作邀请列表")
    @PreAuthorize("@ss.hasPermi('collaboration:spreadsheet:query')")
    @GetMapping("/invitations")
    public AjaxResult getUserInvitations(@RequestParam(defaultValue = "1") int pageNum,
                                        @RequestParam(defaultValue = "10") int pageSize,
                                        @RequestParam(required = false) String spreadsheetTitle,
                                        @RequestParam(required = false) String status) {
        Long userId = SecurityUtils.getUserId();
        IPage<CollaboratorVo> page = spreadsheetService.getUserInvitations(pageNum, pageSize, spreadsheetTitle, status, userId);
        return AjaxResult.success(page);
    }

    /**
     * 退出协作
     */
    @ApiOperation("退出协作")
    @PreAuthorize("@ss.hasPermi('collaboration:spreadsheet:leave')")
    @Log(title = "退出协作", businessType = BusinessType.DELETE)
    @PostMapping("/{id}/leave")
    public AjaxResult leaveCollaboration(@PathVariable String id) {
        Long userId = SecurityUtils.getUserId();
        boolean result = spreadsheetService.leaveCollaboration(id, userId);
        return result ? AjaxResult.success("退出协作成功") : AjaxResult.error("退出协作失败");
    }
}
