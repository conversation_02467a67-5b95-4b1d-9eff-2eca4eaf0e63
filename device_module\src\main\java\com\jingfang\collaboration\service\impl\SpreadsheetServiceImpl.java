package com.jingfang.collaboration.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingfang.collaboration.domain.Spreadsheet;
import com.jingfang.collaboration.domain.SpreadsheetCollaborator;
import com.jingfang.collaboration.dto.InviteDto;
import com.jingfang.collaboration.dto.SpreadsheetDto;
import com.jingfang.collaboration.mapper.SpreadsheetMapper;
import com.jingfang.collaboration.mapper.SpreadsheetCollaboratorMapper;
import com.jingfang.collaboration.service.SpreadsheetService;
import com.jingfang.collaboration.service.SpreadsheetCollaboratorService;
import com.jingfang.collaboration.vo.CollaboratorVo;
import com.jingfang.collaboration.vo.OnlineUserVo;
import com.jingfang.collaboration.vo.SpreadsheetVo;
import com.jingfang.common.exception.ServiceException;
import com.jingfang.common.utils.SecurityUtils;
import com.jingfang.common.utils.StringUtils;
import com.jingfang.common.utils.uuid.IdUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 表格服务实现类
 */
@Slf4j
@Service
public class SpreadsheetServiceImpl extends ServiceImpl<SpreadsheetMapper, Spreadsheet> implements SpreadsheetService {
    
    @Autowired
    private SpreadsheetMapper spreadsheetMapper;

    @Autowired
    private SpreadsheetCollaboratorMapper collaboratorMapper;

    @Autowired
    private SpreadsheetCollaboratorService collaboratorService;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createSpreadsheet(SpreadsheetDto dto, Long userId) {
        try {
            Spreadsheet spreadsheet = new Spreadsheet();
            BeanUtils.copyProperties(dto, spreadsheet);
            
            // 设置基本信息
            spreadsheet.setId(IdUtils.fastSimpleUUID());
            spreadsheet.setCreateBy(userId);
            spreadsheet.setCreateByName(SecurityUtils.getUsername());
            spreadsheet.setCreateTime(new Date());
            spreadsheet.setUpdateBy(userId);
            spreadsheet.setUpdateByName(SecurityUtils.getUsername());
            spreadsheet.setUpdateTime(new Date());
            spreadsheet.setVersion(1);
            spreadsheet.setDelFlag(0);
            
            // 默认状态
            if (StringUtils.isEmpty(spreadsheet.getStatus())) {
                spreadsheet.setStatus("0");
            }
            if (StringUtils.isEmpty(spreadsheet.getIsPublic())) {
                spreadsheet.setIsPublic("0");
            }
            
            // 保存表格
            this.save(spreadsheet);
            
            // 创建者自动成为所有者
            SpreadsheetCollaborator owner = new SpreadsheetCollaborator();
            owner.setId(IdUtils.fastSimpleUUID());
            owner.setSpreadsheetId(spreadsheet.getId());
            owner.setUserId(userId);
            owner.setUserName(SecurityUtils.getUsername());
            owner.setNickName(SecurityUtils.getUsername()); // 暂时使用用户名
            owner.setDeptId(SecurityUtils.getDeptId());
            owner.setPermission("owner");
            owner.setInviteBy(userId);
            owner.setInviteByName(SecurityUtils.getUsername());
            owner.setInviteTime(new Date());
            owner.setAcceptTime(new Date());
            owner.setStatus("1"); // 已接受
            owner.setIsOnline("0");
            owner.setCreateTime(new Date());
            owner.setUpdateTime(new Date());
            owner.setDelFlag(0);
            
            collaboratorService.save(owner);
            
            log.info("用户{}创建表格成功，表格ID：{}", userId, spreadsheet.getId());
            return spreadsheet.getId();
            
        } catch (Exception e) {
            log.error("创建表格失败", e);
            throw new ServiceException("创建表格失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSpreadsheet(SpreadsheetDto dto, Long userId) {
        try {
            // 检查权限
            String permission = checkUserPermission(dto.getId(), userId);
            if (!"owner".equals(permission) && !"editor".equals(permission)) {
                throw new ServiceException("您没有权限编辑此表格");
            }
            
            Spreadsheet spreadsheet = this.getById(dto.getId());
            if (spreadsheet == null) {
                throw new ServiceException("表格不存在");
            }
            
            // 更新基本信息
            BeanUtils.copyProperties(dto, spreadsheet);
            spreadsheet.setUpdateBy(userId);
            spreadsheet.setUpdateByName(SecurityUtils.getUsername());
            spreadsheet.setUpdateTime(new Date());
            spreadsheet.setVersion(spreadsheet.getVersion() + 1);
            
            boolean result = this.updateById(spreadsheet);
            
            if (result) {
                log.info("用户{}更新表格成功，表格ID：{}", userId, dto.getId());
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("更新表格失败", e);
            throw new ServiceException("更新表格失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSpreadsheet(String id, Long userId) {
        try {
            // 检查权限 - 只有所有者可以删除
            String permission = checkUserPermission(id, userId);
            if (!"owner".equals(permission)) {
                throw new ServiceException("只有表格所有者可以删除表格");
            }
            
            // 删除表格（逻辑删除）
            boolean result = this.removeById(id);
            
            if (result) {
                // 删除所有协作者记录
                collaboratorService.remove(new LambdaQueryWrapper<SpreadsheetCollaborator>()
                    .eq(SpreadsheetCollaborator::getSpreadsheetId, id));
                
                log.info("用户{}删除表格成功，表格ID：{}", userId, id);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("删除表格失败", e);
            throw new ServiceException("删除表格失败：" + e.getMessage());
        }
    }
    
    @Override
    public boolean deleteSpreadsheets(String[] ids, Long userId) {
        for (String id : ids) {
            deleteSpreadsheet(id, userId);
        }
        return true;
    }
    
    @Override
    public SpreadsheetVo getSpreadsheetDetail(String id, Long userId) {
        try {
            // 检查权限
            String permission = checkUserPermission(id, userId);
            if (StringUtils.isEmpty(permission)) {
                throw new ServiceException("您没有权限访问此表格");
            }
            
            SpreadsheetVo vo = spreadsheetMapper.selectSpreadsheetDetail(id, userId);
            if (vo == null) {
                throw new ServiceException("表格不存在");
            }
            
            vo.setUserPermission(permission);
            
            return vo;
            
        } catch (Exception e) {
            log.error("查询表格详情失败", e);
            throw new ServiceException("查询表格详情失败：" + e.getMessage());
        }
    }
    
    @Override
    public IPage<SpreadsheetVo> getSpreadsheetList(int pageNum, int pageSize, String title, String status, Long userId) {
        Page<SpreadsheetVo> page = new Page<>(pageNum, pageSize);
        return spreadsheetMapper.selectSpreadsheetList(page, userId, title, status);
    }
    
    @Override
    public IPage<SpreadsheetVo> getUserAccessibleSpreadsheets(int pageNum, int pageSize, String title, String status, Long userId) {
        Page<SpreadsheetVo> page = new Page<>(pageNum, pageSize);
        return spreadsheetMapper.selectUserAccessibleSpreadsheets(page, userId, title, status);
    }
    
    @Override
    public String checkUserPermission(String spreadsheetId, Long userId) {
        // 查询用户在该表格的权限
        LambdaQueryWrapper<SpreadsheetCollaborator> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SpreadsheetCollaborator::getSpreadsheetId, spreadsheetId)
               .eq(SpreadsheetCollaborator::getUserId, userId)
               .eq(SpreadsheetCollaborator::getStatus, "1"); // 已接受
        
        SpreadsheetCollaborator collaborator = collaboratorService.getOne(wrapper);
        return collaborator != null ? collaborator.getPermission() : null;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean inviteCollaborators(InviteDto dto, Long inviteBy) {
        try {
            // 检查权限 - 只有所有者和编辑者可以邀请
            String permission = checkUserPermission(dto.getSpreadsheetId(), inviteBy);
            if (!"owner".equals(permission) && !"editor".equals(permission)) {
                throw new ServiceException("您没有权限邀请协作者");
            }

            for (Long userId : dto.getUserIds()) {
                // 检查是否已经是协作者
                LambdaQueryWrapper<SpreadsheetCollaborator> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(SpreadsheetCollaborator::getSpreadsheetId, dto.getSpreadsheetId())
                       .eq(SpreadsheetCollaborator::getUserId, userId);

                SpreadsheetCollaborator existing = collaboratorService.getOne(wrapper);
                if (existing != null) {
                    log.warn("用户{}已经是表格{}的协作者", userId, dto.getSpreadsheetId());
                    continue;
                }

                // 创建协作者记录
                SpreadsheetCollaborator collaborator = new SpreadsheetCollaborator();
                collaborator.setId(IdUtils.fastSimpleUUID());
                collaborator.setSpreadsheetId(dto.getSpreadsheetId());
                collaborator.setUserId(userId);
                // 这里需要查询用户信息，暂时使用用户ID
                collaborator.setUserName("user_" + userId);
                collaborator.setNickName("用户" + userId);
                collaborator.setPermission(dto.getPermission());
                collaborator.setInviteBy(inviteBy);
                collaborator.setInviteByName(SecurityUtils.getUsername());
                collaborator.setInviteTime(new Date());
                collaborator.setStatus("0"); // 待接受
                collaborator.setRemark(dto.getMessage()); // 邀请消息暂存在remark字段
                collaborator.setIsOnline("0");
                collaborator.setCreateTime(new Date());
                collaborator.setUpdateTime(new Date());
                collaborator.setDelFlag(0);

                collaboratorService.save(collaborator);

                // TODO: 发送邀请通知
                if (dto.getSendMessage()) {
                    // 发送站内消息
                }
                if (dto.getSendEmail()) {
                    // 发送邮件通知
                }
            }

            log.info("用户{}邀请协作者成功，表格ID：{}", inviteBy, dto.getSpreadsheetId());
            return true;

        } catch (Exception e) {
            log.error("邀请协作者失败", e);
            throw new ServiceException("邀请协作者失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean acceptInvitation(String collaboratorId, Long userId) {
        try {
            SpreadsheetCollaborator collaborator = collaboratorService.getById(collaboratorId);
            if (collaborator == null) {
                throw new ServiceException("邀请记录不存在");
            }

            if (!collaborator.getUserId().equals(userId)) {
                throw new ServiceException("您没有权限操作此邀请");
            }

            if (!"0".equals(collaborator.getStatus())) {
                throw new ServiceException("邀请已处理");
            }

            collaborator.setStatus("1"); // 已接受
            collaborator.setAcceptTime(new Date());
            collaborator.setUpdateTime(new Date());

            boolean result = collaboratorService.updateById(collaborator);

            if (result) {
                log.info("用户{}接受协作邀请成功，协作记录ID：{}", userId, collaboratorId);
            }

            return result;

        } catch (Exception e) {
            log.error("接受协作邀请失败", e);
            throw new ServiceException("接受协作邀请失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean rejectInvitation(String collaboratorId, Long userId) {
        try {
            SpreadsheetCollaborator collaborator = collaboratorService.getById(collaboratorId);
            if (collaborator == null) {
                throw new ServiceException("邀请记录不存在");
            }

            if (!collaborator.getUserId().equals(userId)) {
                throw new ServiceException("您没有权限操作此邀请");
            }

            if (!"0".equals(collaborator.getStatus())) {
                throw new ServiceException("邀请已处理");
            }

            collaborator.setStatus("2"); // 已拒绝
            collaborator.setUpdateTime(new Date());

            boolean result = collaboratorService.updateById(collaborator);

            if (result) {
                log.info("用户{}拒绝协作邀请成功，协作记录ID：{}", userId, collaboratorId);
            }

            return result;

        } catch (Exception e) {
            log.error("拒绝协作邀请失败", e);
            throw new ServiceException("拒绝协作邀请失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeCollaborator(String collaboratorId, Long userId) {
        try {
            SpreadsheetCollaborator collaborator = collaboratorService.getById(collaboratorId);
            if (collaborator == null) {
                throw new ServiceException("协作者记录不存在");
            }

            // 检查权限 - 只有所有者可以移除协作者
            String permission = checkUserPermission(collaborator.getSpreadsheetId(), userId);
            if (!"owner".equals(permission)) {
                throw new ServiceException("只有表格所有者可以移除协作者");
            }

            // 不能移除所有者
            if ("owner".equals(collaborator.getPermission())) {
                throw new ServiceException("不能移除表格所有者");
            }

            collaborator.setStatus("3"); // 已移除
            collaborator.setUpdateTime(new Date());

            boolean result = collaboratorService.updateById(collaborator);

            if (result) {
                log.info("用户{}移除协作者成功，协作记录ID：{}", userId, collaboratorId);
            }

            return result;

        } catch (Exception e) {
            log.error("移除协作者失败", e);
            throw new ServiceException("移除协作者失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCollaboratorPermission(String collaboratorId, String permission, Long userId) {
        try {
            SpreadsheetCollaborator collaborator = collaboratorService.getById(collaboratorId);
            if (collaborator == null) {
                throw new ServiceException("协作者记录不存在");
            }

            // 检查权限 - 只有所有者可以修改权限
            String userPermission = checkUserPermission(collaborator.getSpreadsheetId(), userId);
            if (!"owner".equals(userPermission)) {
                throw new ServiceException("只有表格所有者可以修改协作者权限");
            }

            // 不能修改所有者权限
            if ("owner".equals(collaborator.getPermission())) {
                throw new ServiceException("不能修改表格所有者权限");
            }

            // 不能将权限设置为所有者
            if ("owner".equals(permission)) {
                throw new ServiceException("不能将协作者权限设置为所有者");
            }

            collaborator.setPermission(permission);
            collaborator.setUpdateTime(new Date());

            boolean result = collaboratorService.updateById(collaborator);

            if (result) {
                log.info("用户{}修改协作者权限成功，协作记录ID：{}，新权限：{}", userId, collaboratorId, permission);
            }

            return result;

        } catch (Exception e) {
            log.error("修改协作者权限失败", e);
            throw new ServiceException("修改协作者权限失败：" + e.getMessage());
        }
    }

    @Override
    public List<CollaboratorVo> getCollaborators(String spreadsheetId, Long userId) {
        try {
            // 检查权限
            String permission = checkUserPermission(spreadsheetId, userId);
            if (StringUtils.isEmpty(permission)) {
                throw new ServiceException("您没有权限查看协作者列表");
            }

            return collaboratorService.getCollaboratorsBySpreadsheetId(spreadsheetId);

        } catch (Exception e) {
            log.error("查询协作者列表失败", e);
            throw new ServiceException("查询协作者列表失败：" + e.getMessage());
        }
    }

    @Override
    public List<OnlineUserVo> getOnlineUsers(String spreadsheetId, Long userId) {
        try {
            // 检查权限
            String permission = checkUserPermission(spreadsheetId, userId);
            if (StringUtils.isEmpty(permission)) {
                throw new ServiceException("您没有权限查看在线用户列表");
            }

            return collaboratorService.getOnlineUsers(spreadsheetId);

        } catch (Exception e) {
            log.error("查询在线用户列表失败", e);
            throw new ServiceException("查询在线用户列表失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String generateShareUrl(String spreadsheetId, String password, Long expireDays, Long userId) {
        try {
            // 检查权限 - 只有所有者和编辑者可以生成分享链接
            String permission = checkUserPermission(spreadsheetId, userId);
            if (!"owner".equals(permission) && !"editor".equals(permission)) {
                throw new ServiceException("您没有权限生成分享链接");
            }

            Spreadsheet spreadsheet = this.getById(spreadsheetId);
            if (spreadsheet == null) {
                throw new ServiceException("表格不存在");
            }

            // 生成分享token
            String shareToken = IdUtils.fastSimpleUUID();

            // 设置过期时间
            Date expireTime = null;
            if (expireDays != null && expireDays > 0) {
                expireTime = new Date(System.currentTimeMillis() + expireDays * 24 * 60 * 60 * 1000);
            }

            spreadsheet.setShareToken(shareToken);
            spreadsheet.setSharePassword(password);
            spreadsheet.setShareExpireTime(expireTime);
            spreadsheet.setUpdateBy(userId);
            spreadsheet.setUpdateByName(SecurityUtils.getUsername());
            spreadsheet.setUpdateTime(new Date());

            this.updateById(spreadsheet);

            log.info("用户{}生成分享链接成功，表格ID：{}", userId, spreadsheetId);
            return shareToken;

        } catch (Exception e) {
            log.error("生成分享链接失败", e);
            throw new ServiceException("生成分享链接失败：" + e.getMessage());
        }
    }

    @Override
    public SpreadsheetVo validateShareUrl(String token, String password) {
        try {
            LambdaQueryWrapper<Spreadsheet> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Spreadsheet::getShareToken, token)
                   .eq(Spreadsheet::getStatus, "0"); // 正常状态

            Spreadsheet spreadsheet = this.getOne(wrapper);
            if (spreadsheet == null) {
                throw new ServiceException("分享链接不存在或已失效");
            }

            // 检查是否过期
            if (spreadsheet.getShareExpireTime() != null &&
                spreadsheet.getShareExpireTime().before(new Date())) {
                throw new ServiceException("分享链接已过期");
            }

            // 检查密码
            if (StringUtils.isNotEmpty(spreadsheet.getSharePassword()) &&
                !spreadsheet.getSharePassword().equals(password)) {
                throw new ServiceException("分享密码错误");
            }

            // 转换为VO
            SpreadsheetVo vo = new SpreadsheetVo();
            BeanUtils.copyProperties(spreadsheet, vo);
            vo.setUserPermission("viewer"); // 通过分享链接访问的用户默认为查看者

            return vo;

        } catch (Exception e) {
            log.error("验证分享链接失败", e);
            throw new ServiceException("验证分享链接失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveSpreadsheetData(String spreadsheetId, String data, Long userId) {
        try {
            // 检查权限
            String permission = checkUserPermission(spreadsheetId, userId);
            if (!"owner".equals(permission) && !"editor".equals(permission)) {
                throw new ServiceException("您没有权限编辑此表格");
            }

            Spreadsheet spreadsheet = this.getById(spreadsheetId);
            if (spreadsheet == null) {
                throw new ServiceException("表格不存在");
            }

            spreadsheet.setData(data);
            spreadsheet.setUpdateBy(userId);
            spreadsheet.setUpdateByName(SecurityUtils.getUsername());
            spreadsheet.setUpdateTime(new Date());
            spreadsheet.setVersion(spreadsheet.getVersion() + 1);

            boolean result = this.updateById(spreadsheet);

            if (result) {
                log.info("用户{}保存表格数据成功，表格ID：{}", userId, spreadsheetId);
            }

            return result;

        } catch (Exception e) {
            log.error("保存表格数据失败", e);
            throw new ServiceException("保存表格数据失败：" + e.getMessage());
        }
    }

    @Override
    public IPage<CollaboratorVo> getUserInvitations(int pageNum, int pageSize, String spreadsheetTitle, String status, Long userId) {
        try {
            IPage<CollaboratorVo> page = new Page<>(pageNum, pageSize);
            return collaboratorMapper.selectUserInvitations(page, userId, spreadsheetTitle, status);
        } catch (Exception e) {
            log.error("查询用户邀请列表失败", e);
            throw new ServiceException("查询用户邀请列表失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean leaveCollaboration(String spreadsheetId, Long userId) {
        try {
            // 查找用户在该表格的协作记录
            LambdaQueryWrapper<SpreadsheetCollaborator> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SpreadsheetCollaborator::getSpreadsheetId, spreadsheetId)
                   .eq(SpreadsheetCollaborator::getUserId, userId)
                   .eq(SpreadsheetCollaborator::getStatus, "1"); // 已接受的协作

            SpreadsheetCollaborator collaborator = collaboratorService.getOne(wrapper);
            if (collaborator == null) {
                throw new ServiceException("您不是该表格的协作者");
            }

            // 不能退出自己创建的表格
            if ("owner".equals(collaborator.getPermission())) {
                throw new ServiceException("表格所有者不能退出协作");
            }

            // 删除协作记录
            boolean result = collaboratorService.removeById(collaborator.getId());

            if (result) {
                log.info("用户{}退出表格{}协作成功", userId, spreadsheetId);
            }

            return result;

        } catch (Exception e) {
            log.error("退出协作失败", e);
            throw new ServiceException("退出协作失败：" + e.getMessage());
        }
    }
}
