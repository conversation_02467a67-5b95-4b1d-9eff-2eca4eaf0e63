package com.jingfang.wh_item.module.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 物品库存信息VO
 */
@Data
public class ItemInventoryVo implements Serializable {
    
    /**
     * 库存ID
     */
    private String inventoryId;
    
    /**
     * 物品ID
     */
    private String itemId;
    
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    
    /**
     * 当前库存
     */
    private BigDecimal currentQuantity;
    
    /**
     * 安全库存
     */
    private BigDecimal safetyStock;
    
    /**
     * 库存状态(1-正常, 2-不足, 3-过剩)
     */
    private Integer stockStatus;
    
    /**
     * 库存状态名称
     */
    private String stockStatusName;
    
    /**
     * 货架/库位
     */
    private String shelfLocation;
    
    private static final long serialVersionUID = 1L;
} 