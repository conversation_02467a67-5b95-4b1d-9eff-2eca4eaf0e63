package com.jingfang.framework.manager;

import com.ghgande.j2mod.modbus.facade.ModbusTCPMaster;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;

public class PooledModbusTCPMaster {


    private final ModbusTCPMaster master;

    public PooledModbusTCPMaster(ModbusTCPMaster master) {
        this.master = master;
    }

    // 通过 DefaultPooledObject 进行包装
    public PooledObject<ModbusTCPMaster> wrap() {
        return new DefaultPooledObject<>(master);
    }

    // 销毁对象时关闭连接
    public void destroy() {
        try {
            master.disconnect();
        } catch (Exception e) {
            System.err.println("Error disconnecting ModbusTCPMaster: " + e.getMessage());
        }
    }

    // 验证对象是否有效
    public boolean isValid() {
        try {
            return master != null && master.isConnected();
        } catch (Exception e) {
            return false;
        }
    }

    // 返回空闲时间，确保返回 long 类型
    public long getIdleTime() {
        return 0;  // 返回空闲时间，可以根据需要定制
    }

    // 自定义关闭操作
    public void close() {
        // 关闭时执行的操作
    }
}
