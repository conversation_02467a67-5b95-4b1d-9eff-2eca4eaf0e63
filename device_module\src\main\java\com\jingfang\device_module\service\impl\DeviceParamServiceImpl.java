package com.jingfang.device_module.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ghgande.j2mod.modbus.facade.ModbusTCPMaster;
import com.ghgande.j2mod.modbus.procimg.InputRegister;
import com.ghgande.j2mod.modbus.procimg.Register;
import com.ghgande.j2mod.modbus.util.BitVector;
import com.jingfang.common.core.redis.RedisCache;
import com.jingfang.common.utils.bean.BeanUtils;
import com.jingfang.device_module.module.dto.DeviceAddDto;
import com.jingfang.device_module.module.dto.NormalParamAddDto;
import com.jingfang.device_module.module.entity.DeviceInfo;
import com.jingfang.device_module.module.entity.DeviceParam;
import com.jingfang.device_module.module.vo.DeviceParamVo;
import com.jingfang.device_module.mqtt.dto.DevicePropertyResponse;
import com.jingfang.device_module.mqtt.dto.DevicePropertyWriteRequest;
import com.jingfang.device_module.mqtt.dto.DevicePropertyWriteResponse;
import com.jingfang.device_module.mqtt.service.MqttDevicePropertyService;
import com.jingfang.device_module.service.DeviceInfoService;
import com.jingfang.device_module.service.DeviceParamService;
import com.jingfang.device_module.mapper.DeviceParamMapper;
import com.jingfang.device_module.utils.ModbusUtils;
import com.jingfang.framework.manager.ModbusConnectionManager;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【device_param】的数据库操作Service实现
* @createDate 2025-03-25 13:58:50
*/
@Slf4j
@Service
public class DeviceParamServiceImpl extends ServiceImpl<DeviceParamMapper, DeviceParam>
    implements DeviceParamService{

    @Resource
    private RedisCache redisCache;

    @Resource
    private ModbusConnectionManager connectionManager;

    @Resource
    private MqttDevicePropertyService mqttDevicePropertyService;

    @Override
    public void modbusConnectTest() {
        ModbusTCPMaster master = null;
        try {
            // 获取连接（如果连接池不存在会自动创建）
            master = connectionManager.getConnection("1", "***************",502 );
            try {
                assert master != null;
                master.connect();
            }catch (Exception e) {
                throw new RuntimeException(e);
            }
            // 使用连接进行通信
            BitVector bitVector = master.readCoils(0, 1);
            master.readInputRegisters(0,0);
            System.out.println(bitVector.getBit(0));


            // ... 进行Modbus通信操作 ...

        } catch (Exception e) {
            log.error("Error communicating with device {}: {}", 1, e.getMessage());
        } finally {
            // 归还连接
            if (master != null) {
                connectionManager.returnConnection("1", master);
            }
        }
    }

    @Override
    public boolean addDeviceParamTypeOne(NormalParamAddDto dto) {
        DeviceParam deviceParam = new DeviceParam();
        BeanUtils.copyProperties(dto,deviceParam);
        return this.saveOrUpdate(deviceParam);
    }

    @Override
    public boolean addDeviceParamTypeTwo(DeviceAddDto dto) {
        DeviceParam deviceParam = new DeviceParam();
        BeanUtils.copyProperties(dto,deviceParam);
        return this.saveOrUpdate(deviceParam);
    }

    @Override
    public List<DeviceParamVo> showDeviceParamTypeOne(Long deviceId) {
        log.info("开始获取设备运行参数，设备ID: {}", deviceId);

        // 检查设备是否在线
        Set<Long> deviceIds = redisCache.getCacheSet("online device");
        if (!deviceIds.contains(deviceId)) {
            log.warn("设备不在线，设备ID: {}", deviceId);
            return new ArrayList<>();
        }

        // 获取设备信息
        Map<String, DeviceInfo> maps = redisCache.getCacheMap("smart device");
        DeviceInfo deviceInfo = maps.get(deviceId.toString());
        if (deviceInfo == null) {
            log.warn("未找到设备信息，设备ID: {}", deviceId);
            return new ArrayList<>();
        }

        // 查询设备的运行参数配置（一般参数）
        LambdaQueryWrapper<DeviceParam> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceParam::getDeviceId, deviceId);
        queryWrapper.eq(DeviceParam::getDelFlag, 0);
        queryWrapper.eq(DeviceParam::getParamType, 0); // 0-一般参数
        List<DeviceParam> params = this.baseMapper.selectList(queryWrapper);

        if (params.isEmpty()) {
            log.info("设备没有配置运行参数，设备ID: {}", deviceId);
            return new ArrayList<>();
        }

        try {
            // 提取参数名称列表
            List<String> propertyNames = params.stream()
                    .map(DeviceParam::getParamName)
                    .collect(Collectors.toList());

            log.info("准备查询MQTT属性，设备IP: {}, 属性列表: {}", deviceInfo.getIpAddress(), propertyNames);

            // 通过MQTT查询设备属性
            CompletableFuture<DevicePropertyResponse> future = mqttDevicePropertyService
                    .queryDeviceProperties(deviceInfo.getIpAddress(), propertyNames);

            DevicePropertyResponse response = future.get(15, java.util.concurrent.TimeUnit.SECONDS);

            if (response == null || response.getCode() != 0) {
                log.error("MQTT查询设备属性失败，设备IP: {}, 响应码: {}",
                        deviceInfo.getIpAddress(), response != null ? response.getCode() : "null");
                return new ArrayList<>();
            }

            // 处理响应数据
            List<DeviceParamVo> vos = new ArrayList<>();
            if (response.getParams() != null && !response.getParams().isEmpty()) {
                DevicePropertyResponse.DevicePropertyParam deviceParam = response.getParams().get(0);
                if (deviceParam.getProperties() != null) {
                    // 创建属性值映射
                    Map<String, Object> propertyValueMap = deviceParam.getProperties().stream()
                            .collect(Collectors.toMap(
                                    DevicePropertyResponse.PropertyValue::getName,
                                    DevicePropertyResponse.PropertyValue::getValue,
                                    (existing, replacement) -> existing
                            ));

                    // 构建返回结果
                    for (DeviceParam param : params) {
                        DeviceParamVo vo = new DeviceParamVo();
                        BeanUtils.copyProperties(param, vo);

                        Object value = propertyValueMap.get(param.getParamName());
                        if (value != null) {
                            // 格式化数值
                            String formattedValue = formatParamValue(value, param);
                            vo.setParamValue(formattedValue);
                        } else {
                            vo.setParamValue("--");
                            log.warn("未获取到参数值，参数名: {}", param.getParamName());
                        }

                        vos.add(vo);
                    }
                }
            }

            log.info("成功获取设备运行参数，设备ID: {}, 参数数量: {}", deviceId, vos.size());
            return vos;

        } catch (Exception e) {
            log.error("获取设备运行参数失败，设备ID: {}, 错误: {}", deviceId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 格式化参数值
     */
    private String formatParamValue(Object value, DeviceParam param) {
        if (value == null) {
            return "--";
        }

        String unit = param.getParamUnit() != null ? param.getParamUnit() : "";

        // 根据值的类型自动格式化
        if (value instanceof Number) {
            Number numValue = (Number) value;
            // 如果是整数，显示为整数
            if (numValue.doubleValue() == numValue.intValue()) {
                return String.valueOf(numValue.intValue()) + unit;
            } else {
                // 如果是小数，保留2位小数
                DecimalFormat decimalFormat = new DecimalFormat("#.##");
                return decimalFormat.format(numValue.doubleValue()) + unit;
            }
        } else if (value instanceof Boolean) {
            return ((Boolean) value) ? "是" + unit : "否" + unit;
        } else {
            return value.toString() + unit;
        }
    }

    @Override
    public List<DeviceParamVo> showDeviceParamTypeTwo(Long deviceId) {
        log.info("开始获取设备告警参数，设备ID: {}", deviceId);

        // 检查设备是否在线
        Set<Long> deviceIds = redisCache.getCacheSet("online device");
        if (!deviceIds.contains(deviceId)) {
            log.warn("设备不在线，设备ID: {}", deviceId);
            return new ArrayList<>();
        }

        // 查询设备的告警参数配置
        LambdaQueryWrapper<DeviceParam> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceParam::getDeviceId, deviceId);
        queryWrapper.eq(DeviceParam::getDelFlag, 0);
        queryWrapper.eq(DeviceParam::getParamType, 1); // 1-告警参数
        List<DeviceParam> params = this.baseMapper.selectList(queryWrapper);

        if (params.isEmpty()) {
            log.info("设备没有配置告警参数，设备ID: {}", deviceId);
            return new ArrayList<>();
        }

        // 获取设备信息
        Map<String, DeviceInfo> maps = redisCache.getCacheMap("smart device");
        DeviceInfo deviceInfo = maps.get(deviceId.toString());
        if (deviceInfo == null) {
            log.warn("未找到设备信息，设备ID: {}", deviceId);
            return new ArrayList<>();
        }

        List<DeviceParamVo> vos = new ArrayList<>();

        try {
            // 提取告警参数名称列表
            List<String> propertyNames = params.stream()
                    .map(DeviceParam::getParamName)
                    .collect(Collectors.toList());

            log.info("准备查询MQTT告警属性，设备IP: {}, 属性列表: {}", deviceInfo.getIpAddress(), propertyNames);

            // 通过MQTT查询设备告警属性
            CompletableFuture<DevicePropertyResponse> future = mqttDevicePropertyService
                    .queryDeviceProperties(deviceInfo.getIpAddress(), propertyNames);

            DevicePropertyResponse response = future.get(15, java.util.concurrent.TimeUnit.SECONDS);

            if (response == null || response.getCode() != 0) {
                log.error("MQTT查询设备告警属性失败，设备IP: {}, 响应码: {}",
                        deviceInfo.getIpAddress(), response != null ? response.getCode() : "null");
                // 如果MQTT查询失败，返回配置的告警参数但不显示值
                for (DeviceParam param : params) {
                    DeviceParamVo vo = new DeviceParamVo();
                    BeanUtils.copyProperties(param, vo);
                    vo.setParamValue("--");
                    vo.setAlertValue(false);
                    vos.add(vo);
                }
                return vos;
            }

            // 处理MQTT响应
            if (response.getParams() != null && !response.getParams().isEmpty()) {
                DevicePropertyResponse.DevicePropertyParam deviceParam = response.getParams().get(0);
                if (deviceParam.getProperties() != null) {
                    // 构建属性值映射
                    Map<String, Object> propertyValueMap = deviceParam.getProperties().stream()
                            .collect(Collectors.toMap(
                                    DevicePropertyResponse.PropertyValue::getName,
                                    DevicePropertyResponse.PropertyValue::getValue,
                                    (existing, replacement) -> existing
                            ));

                    // 构建返回结果
                    for (DeviceParam param : params) {
                        DeviceParamVo vo = new DeviceParamVo();
                        BeanUtils.copyProperties(param, vo);

                        Object value = propertyValueMap.get(param.getParamName());
                        if (value != null) {
                            // 格式化数值
                            String formattedValue = formatParamValue(value, param);
                            vo.setParamValue(formattedValue);

                            // 判断是否告警（告警参数通常为布尔值或状态值）
                            vo.setAlertValue(isAlertTriggered(value));
                        } else {
                            vo.setParamValue("--");
                            vo.setAlertValue(false);
                            log.warn("未获取到告警参数值，参数名: {}", param.getParamName());
                        }

                        vos.add(vo);
                    }
                }
            }

            log.info("成功获取设备告警参数，设备ID: {}, 参数数量: {}", deviceId, vos.size());
            return vos;

        } catch (Exception e) {
            log.error("获取设备告警参数失败，设备ID: {}, 错误: {}", deviceId, e.getMessage(), e);
            // 异常情况下返回配置的告警参数但不显示值
            for (DeviceParam param : params) {
                DeviceParamVo vo = new DeviceParamVo();
                BeanUtils.copyProperties(param, vo);
                vo.setParamValue("--");
                vo.setAlertValue(false);
                vos.add(vo);
            }
            return vos;
        }
    }

    @Override
    public List<DeviceParamVo> getDeviceAlertInfo(Long deviceId) {
        log.info("开始获取设备告警信息，设备ID: {}", deviceId);

        // 检查设备是否在线
        Set<Long> deviceIds = redisCache.getCacheSet("online device");
        if (!deviceIds.contains(deviceId)) {
            log.warn("设备不在线，设备ID: {}", deviceId);
            return new ArrayList<>();
        }

        // 获取设备信息
        Map<String, DeviceInfo> maps = redisCache.getCacheMap("smart device");
        DeviceInfo deviceInfo = maps.get(deviceId.toString());
        if (deviceInfo == null) {
            log.warn("未找到设备信息，设备ID: {}", deviceId);
            return new ArrayList<>();
        }

        // 查询设备的告警参数配置
        LambdaQueryWrapper<DeviceParam> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceParam::getDeviceId, deviceId);
        queryWrapper.eq(DeviceParam::getDelFlag, 0);
        queryWrapper.eq(DeviceParam::getParamType, 1); // 1-告警参数
        List<DeviceParam> params = this.baseMapper.selectList(queryWrapper);

        if (params.isEmpty()) {
            log.info("设备没有配置告警参数，设备ID: {}", deviceId);
            return new ArrayList<>();
        }

        List<DeviceParamVo> vos = new ArrayList<>();

        try {
            // 提取告警参数名称列表
            List<String> propertyNames = params.stream()
                    .map(DeviceParam::getParamName)
                    .collect(Collectors.toList());

            log.info("准备查询MQTT告警属性，设备IP: {}, 属性列表: {}", deviceInfo.getIpAddress(), propertyNames);

            // 通过MQTT查询设备告警属性
            CompletableFuture<DevicePropertyResponse> future = mqttDevicePropertyService
                    .queryDeviceProperties(deviceInfo.getIpAddress(), propertyNames);

            DevicePropertyResponse response = future.get(15, java.util.concurrent.TimeUnit.SECONDS);

            if (response == null || response.getCode() != 0) {
                log.error("MQTT查询设备告警属性失败，设备IP: {}, 响应码: {}",
                        deviceInfo.getIpAddress(), response != null ? response.getCode() : "null");
                // 如果MQTT查询失败，返回配置的告警参数但不显示值
                for (DeviceParam param : params) {
                    DeviceParamVo vo = new DeviceParamVo();
                    BeanUtils.copyProperties(param, vo);
                    vo.setParamValue("--");
                    vo.setAlertValue(false);
                    vos.add(vo);
                }
                return vos;
            }

            // 处理MQTT响应
            if (response.getParams() != null && !response.getParams().isEmpty()) {
                DevicePropertyResponse.DevicePropertyParam deviceParam = response.getParams().get(0);
                if (deviceParam.getProperties() != null) {
                    // 构建属性值映射
                    Map<String, Object> propertyValueMap = deviceParam.getProperties().stream()
                            .collect(Collectors.toMap(
                                    DevicePropertyResponse.PropertyValue::getName,
                                    DevicePropertyResponse.PropertyValue::getValue,
                                    (existing, replacement) -> existing
                            ));

                    // 构建返回结果
                    for (DeviceParam param : params) {
                        DeviceParamVo vo = new DeviceParamVo();
                        BeanUtils.copyProperties(param, vo);

                        Object value = propertyValueMap.get(param.getParamName());
                        if (value != null) {
                            // 格式化数值
                            String formattedValue = formatParamValue(value, param);
                            vo.setParamValue(formattedValue);

                            // 判断是否告警
                            vo.setAlertValue(isAlertTriggered(value));
                        } else {
                            vo.setParamValue("--");
                            vo.setAlertValue(false);
                            log.warn("未获取到告警参数值，参数名: {}", param.getParamName());
                        }

                        vos.add(vo);
                    }
                }
            }

            log.info("成功获取设备告警信息，设备ID: {}, 参数数量: {}", deviceId, vos.size());
            return vos;

        } catch (Exception e) {
            log.error("获取设备告警信息失败，设备ID: {}, 错误: {}", deviceId, e.getMessage(), e);
            // 异常情况下返回配置的告警参数但不显示值
            for (DeviceParam param : params) {
                DeviceParamVo vo = new DeviceParamVo();
                BeanUtils.copyProperties(param, vo);
                vo.setParamValue("--");
                vo.setAlertValue(false);
                vos.add(vo);
            }
            return vos;
        }
    }

    @Override
    public List<DeviceParamVo> getDeviceControlParams(Long deviceId) {
        log.info("开始获取设备控制参数，设备ID: {}", deviceId);

        // 检查设备是否在线
        Set<Long> deviceIds = redisCache.getCacheSet("online device");
        if (!deviceIds.contains(deviceId)) {
            log.warn("设备不在线，设备ID: {}", deviceId);
            return new ArrayList<>();
        }

        // 获取设备信息
        Map<String, DeviceInfo> maps = redisCache.getCacheMap("smart device");
        DeviceInfo deviceInfo = maps.get(deviceId.toString());
        if (deviceInfo == null) {
            log.warn("未找到设备信息，设备ID: {}", deviceId);
            return new ArrayList<>();
        }

        // 查询设备的控制参数配置
        LambdaQueryWrapper<DeviceParam> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceParam::getDeviceId, deviceId);
        queryWrapper.eq(DeviceParam::getDelFlag, 0);
        queryWrapper.eq(DeviceParam::getParamType, 2); // 3-控制参数
        List<DeviceParam> params = this.baseMapper.selectList(queryWrapper);

        if (params.isEmpty()) {
            log.info("设备没有配置控制参数，设备ID: {}", deviceId);
            return new ArrayList<>();
        }

        List<DeviceParamVo> vos = new ArrayList<>();

        try {
            // 提取控制参数名称列表（只查询可读的控制参数）
            List<String> propertyNames = params.stream()
                    .filter(param -> param.getRwType() == null || param.getRwType() == 0 || param.getRwType() == 2) // 只读或读写
                    .map(DeviceParam::getParamName)
                    .collect(Collectors.toList());

            if (!propertyNames.isEmpty()) {
                log.info("准备查询MQTT控制参数，设备IP: {}, 属性列表: {}", deviceInfo.getIpAddress(), propertyNames);

                // 通过MQTT查询设备控制参数
                CompletableFuture<DevicePropertyResponse> future = mqttDevicePropertyService
                        .queryDeviceProperties(deviceInfo.getIpAddress(), propertyNames);

                DevicePropertyResponse response = future.get(15, java.util.concurrent.TimeUnit.SECONDS);

                if (response != null && response.getCode() == 0) {
                    // 处理MQTT响应
                    if (response.getParams() != null && !response.getParams().isEmpty()) {
                        DevicePropertyResponse.DevicePropertyParam deviceParam = response.getParams().get(0);
                        if (deviceParam.getProperties() != null) {
                            // 构建属性值映射
                            Map<String, Object> propertyValueMap = deviceParam.getProperties().stream()
                                    .collect(Collectors.toMap(
                                            DevicePropertyResponse.PropertyValue::getName,
                                            DevicePropertyResponse.PropertyValue::getValue,
                                            (existing, replacement) -> existing
                                    ));

                            // 构建返回结果
                            for (DeviceParam param : params) {
                                DeviceParamVo vo = new DeviceParamVo();
                                BeanUtils.copyProperties(param, vo);

                                // 只有可读的参数才查询值
                                if (param.getRwType() == null || param.getRwType() == 0 || param.getRwType() == 2) {
                                    Object value = propertyValueMap.get(param.getParamName());
                                    if (value != null) {
                                        // 格式化数值
                                        String formattedValue = formatParamValue(value, param);
                                        vo.setParamValue(formattedValue);
                                    } else {
                                        vo.setParamValue("--");
                                        log.warn("未获取到控制参数值，参数名: {}", param.getParamName());
                                    }
                                } else {
                                    // 只写参数不显示值
                                    vo.setParamValue("--");
                                }

                                vo.setAlertValue(false); // 控制参数不涉及告警
                                vos.add(vo);
                            }
                        }
                    }
                } else {
                    log.error("MQTT查询设备控制参数失败，设备IP: {}, 响应码: {}",
                            deviceInfo.getIpAddress(), response != null ? response.getCode() : "null");
                    // 如果MQTT查询失败，返回配置的控制参数但不显示值
                    for (DeviceParam param : params) {
                        DeviceParamVo vo = new DeviceParamVo();
                        BeanUtils.copyProperties(param, vo);
                        vo.setParamValue("--");
                        vo.setAlertValue(false);
                        vos.add(vo);
                    }
                }
            } else {
                // 如果没有可读的控制参数，直接返回配置信息
                for (DeviceParam param : params) {
                    DeviceParamVo vo = new DeviceParamVo();
                    BeanUtils.copyProperties(param, vo);
                    vo.setParamValue("--"); // 只写参数不显示值
                    vo.setAlertValue(false);
                    vos.add(vo);
                }
            }

            log.info("成功获取设备控制参数，设备ID: {}, 参数数量: {}", deviceId, vos.size());
            return vos;

        } catch (Exception e) {
            log.error("获取设备控制参数失败，设备ID: {}, 错误: {}", deviceId, e.getMessage(), e);
            // 异常情况下返回配置的控制参数但不显示值
            for (DeviceParam param : params) {
                DeviceParamVo vo = new DeviceParamVo();
                BeanUtils.copyProperties(param, vo);
                vo.setParamValue("--");
                vo.setAlertValue(false);
                vos.add(vo);
            }
            return vos;
        }
    }

    /**
     * 判断告警参数是否触发告警
     * @param value 参数值
     * @return 是否告警
     */
    private boolean isAlertTriggered(Object value) {
        if (value == null) {
            return false;
        }

        // 如果是布尔值，直接返回
        if (value instanceof Boolean) {
            return (Boolean) value;
        }

        // 如果是数值类型，非零表示告警
        if (value instanceof Number) {
            return ((Number) value).doubleValue() != 0.0;
        }

        // 如果是字符串，检查常见的告警状态值
        if (value instanceof String) {
            String strValue = value.toString().toLowerCase().trim();
            return "true".equals(strValue) ||
                   "1".equals(strValue) ||
                   "on".equals(strValue) ||
                   "active".equals(strValue) ||
                   "告警".equals(strValue) ||
                   "报警".equals(strValue) ||
                   "故障".equals(strValue) ||
                   "异常".equals(strValue);
        }

        return false;
    }

    @Override
    public boolean writeDeviceParam(Long deviceId, String paramName, String paramValue) {
        log.info("开始写入设备参数，设备ID: {}, 参数名: {}, 参数值: {}", deviceId, paramName, paramValue);

        try {
            // 检查设备是否在线
            Set<Long> deviceIds = redisCache.getCacheSet("online device");
            if (!deviceIds.contains(deviceId)) {
                log.warn("设备不在线，设备ID: {}", deviceId);
                return false;
            }

            // 获取设备信息
            Map<String, DeviceInfo> maps = redisCache.getCacheMap("smart device");
            DeviceInfo deviceInfo = maps.get(deviceId.toString());
            if (deviceInfo == null) {
                log.warn("未找到设备信息，设备ID: {}", deviceId);
                return false;
            }

            // 验证参数是否存在且可写
            LambdaQueryWrapper<DeviceParam> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DeviceParam::getDeviceId, deviceId);
            queryWrapper.eq(DeviceParam::getParamName, paramName);
            queryWrapper.eq(DeviceParam::getDelFlag, 0);
            DeviceParam param = this.baseMapper.selectOne(queryWrapper);

            if (param == null) {
                log.error("参数不存在，设备ID: {}, 参数名: {}", deviceId, paramName);
                return false;
            }

            // 检查参数是否可写
            if (param.getRwType() == null || param.getRwType() == 0) {
                log.error("参数为只读，无法写入，设备ID: {}, 参数名: {}", deviceId, paramName);
                return false;
            }

            // 验证参数值范围（如果有设置）
            if (!validateParamValue(param, paramValue)) {
                log.error("参数值超出范围，设备ID: {}, 参数名: {}, 参数值: {}, 范围: [{}, {}]",
                        deviceId, paramName, paramValue, param.getRangeStart(), param.getRangeEnd());
                return false;
            }

            // 通过MQTT写入参数
            CompletableFuture<DevicePropertyWriteResponse> future = mqttDevicePropertyService
                    .writeDeviceProperty(deviceInfo.getIpAddress(), paramName, paramValue);

            DevicePropertyWriteResponse response = future.get(15, java.util.concurrent.TimeUnit.SECONDS);

            if (response != null && response.getCode() == 0) {
                log.info("设备参数写入成功，设备ID: {}, 参数名: {}, 参数值: {}", deviceId, paramName, paramValue);
                return true;
            } else {
                log.error("设备参数写入失败，设备ID: {}, 参数名: {}, 响应码: {}",
                        deviceId, paramName, response != null ? response.getCode() : "null");
                return false;
            }

        } catch (Exception e) {
            log.error("写入设备参数异常，设备ID: {}, 参数名: {}, 错误: {}", deviceId, paramName, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean writeDeviceParams(Long deviceId, Map<String, String> paramMap) {
        log.info("开始批量写入设备参数，设备ID: {}, 参数数量: {}", deviceId, paramMap.size());

        try {
            // 检查设备是否在线
            Set<Long> deviceIds = redisCache.getCacheSet("online device");
            if (!deviceIds.contains(deviceId)) {
                log.warn("设备不在线，设备ID: {}", deviceId);
                return false;
            }

            // 获取设备信息
            Map<String, DeviceInfo> maps = redisCache.getCacheMap("smart device");
            DeviceInfo deviceInfo = maps.get(deviceId.toString());
            if (deviceInfo == null) {
                log.warn("未找到设备信息，设备ID: {}", deviceId);
                return false;
            }

            // 验证所有参数
            List<DevicePropertyWriteRequest.PropertyWriteInfo> properties = new ArrayList<>();
            for (Map.Entry<String, String> entry : paramMap.entrySet()) {
                String paramName = entry.getKey();
                String paramValue = entry.getValue();

                // 验证参数是否存在且可写
                LambdaQueryWrapper<DeviceParam> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(DeviceParam::getDeviceId, deviceId);
                queryWrapper.eq(DeviceParam::getParamName, paramName);
                queryWrapper.eq(DeviceParam::getDelFlag, 0);
                DeviceParam param = this.baseMapper.selectOne(queryWrapper);

                if (param == null) {
                    log.error("参数不存在，设备ID: {}, 参数名: {}", deviceId, paramName);
                    return false;
                }

                // 检查参数是否可写
                if (param.getRwType() == null || param.getRwType() == 0) {
                    log.error("参数为只读，无法写入，设备ID: {}, 参数名: {}", deviceId, paramName);
                    return false;
                }

                // 验证参数值范围
                if (!validateParamValue(param, paramValue)) {
                    log.error("参数值超出范围，设备ID: {}, 参数名: {}, 参数值: {}, 范围: [{}, {}]",
                            deviceId, paramName, paramValue, param.getRangeStart(), param.getRangeEnd());
                    return false;
                }

                DevicePropertyWriteRequest.PropertyWriteInfo property = new DevicePropertyWriteRequest.PropertyWriteInfo();
                property.setName(paramName);
                property.setValue(paramValue);
                properties.add(property);
            }

            // 通过MQTT批量写入参数
            CompletableFuture<DevicePropertyWriteResponse> future = mqttDevicePropertyService
                    .writeDeviceProperties(deviceInfo.getIpAddress(), properties);

            DevicePropertyWriteResponse response = future.get(15, java.util.concurrent.TimeUnit.SECONDS);

            if (response != null && response.getCode() == 0) {
                log.info("设备参数批量写入成功，设备ID: {}, 参数数量: {}", deviceId, paramMap.size());
                return true;
            } else {
                log.error("设备参数批量写入失败，设备ID: {}, 响应码: {}",
                        deviceId, response != null ? response.getCode() : "null");
                return false;
            }

        } catch (Exception e) {
            log.error("批量写入设备参数异常，设备ID: {}, 错误: {}", deviceId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 验证参数值是否在有效范围内
     * @param param 参数配置
     * @param value 参数值
     * @return 是否有效
     */
    private boolean validateParamValue(DeviceParam param, String value) {
        // 如果没有设置范围，则不验证
        if (param.getRangeStart() == null || param.getRangeEnd() == null) {
            return true;
        }

        try {
            double numValue = Double.parseDouble(value);
            return numValue >= param.getRangeStart() && numValue <= param.getRangeEnd();
        } catch (NumberFormatException e) {
            // 如果不是数值类型，则不验证范围
            return true;
        }
    }
}




