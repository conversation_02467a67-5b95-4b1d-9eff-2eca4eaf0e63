package com.jingfang.wh_item_requisition.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 物品领用操作日志表
 * @TableName item_requisition_operation_log
 */
@TableName(value = "item_requisition_operation_log")
@Data
public class ItemRequisitionOperationLog implements Serializable {
    
    /**
     * 日志ID
     */
    @TableId(type = IdType.AUTO)
    private Long logId;
    
    /**
     * 领用单ID
     */
    private String requisitionId;
    
    /**
     * 操作类型(1-创建, 2-提交, 3-确认, 4-审核通过, 5-审核退回, 6-完成, 7-删除)
     */
    private Integer operationType;
    
    /**
     * 操作内容
     */
    private String operationContent;
    
    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operationTime;
    
    /**
     * 操作人ID
     */
    private Long operatorId;
    
    /**
     * 操作人姓名
     */
    private String operatorName;
    
    private static final long serialVersionUID = 1L;
} 