package com.jingfang.wh_item.module.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 物品出库单查询请求
 */
@Data
public class ItemOutboundSearchRequest {

    /**
     * 当前页码
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

    /**
     * 出库单ID
     */
    private String outboundId;

    /**
     * 收货人姓名
     */
    private String recipientName;

    /**
     * 收货部门
     */
    private String recipientDept;

    /**
     * 出库类型
     */
    private Integer outboundType;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 制单人ID
     */
    private Long creatorId;

    /**
     * 业务日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date businessDateStart;

    /**
     * 业务日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date businessDateEnd;

    /**
     * 创建时间开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTimeStart;

    /**
     * 创建时间结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTimeEnd;

    /**
     * 物品名称（明细查询）
     */
    private String itemName;

    /**
     * 物品编码（明细查询）
     */
    private String itemCode;

    /**
     * 物品类型（明细查询）1-消耗品，2-备品备件
     */
    private Integer itemType;
} 