package com.jingfang.wh_item.module.dto;

import com.jingfang.wh_item.module.entity.ItemOutbound;
import com.jingfang.wh_item.module.entity.ItemOutboundDetail;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

@Data
public class ItemOutboundDto {
    /**
     * 出库单主信息
     */
    private ItemOutbound main;

    /**
     * 出库明细列表
     */
    @NotNull(message = "出库明细不能为空")
    @Size(min = 1, message = "至少需要一条出库明细")
    private List<ItemOutboundDetail> details;
} 