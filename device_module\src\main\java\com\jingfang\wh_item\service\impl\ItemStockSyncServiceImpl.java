package com.jingfang.wh_item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jingfang.common.utils.SecurityUtils;
import com.jingfang.wh_item.mapper.ItemBaseInfoMapper;
import com.jingfang.wh_item.mapper.ItemInventoryMapper;
import com.jingfang.wh_item.module.entity.ItemBaseInfo;
import com.jingfang.wh_item.module.entity.ItemInventory;
import com.jingfang.wh_item.service.ItemStockSyncService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 物品库存同步服务实现类
 */
@Slf4j
@Service
public class ItemStockSyncServiceImpl implements ItemStockSyncService {

    @Resource
    private ItemBaseInfoMapper itemBaseInfoMapper;

    @Resource
    private ItemInventoryMapper itemInventoryMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BigDecimal syncTotalStock(String itemId) {
        try {
            // 1. 计算物品在所有仓库的库存总和
            LambdaQueryWrapper<ItemInventory> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ItemInventory::getItemId, itemId);
            List<ItemInventory> inventoryList = itemInventoryMapper.selectList(wrapper);
            
            BigDecimal totalStock = BigDecimal.ZERO;
            if (inventoryList != null && !inventoryList.isEmpty()) {
                for (ItemInventory inventory : inventoryList) {
                    if (inventory.getCurrentQuantity() != null) {
                        totalStock = totalStock.add(inventory.getCurrentQuantity());
                    }
                }
            }
            
            // 2. 更新item_base_info表中的总库存
            ItemBaseInfo baseInfo = new ItemBaseInfo();
            baseInfo.setItemId(itemId);
            baseInfo.setTotalStock(totalStock);
            baseInfo.setUpdateTime(new Date());
            try {
                baseInfo.setUpdateBy(SecurityUtils.getUsername());
            } catch (Exception e) {
                baseInfo.setUpdateBy("system");
            }
            
            int updateCount = itemBaseInfoMapper.updateById(baseInfo);
            if (updateCount > 0) {
                log.info("同步物品总库存成功，itemId={}，总库存={}", itemId, totalStock);
                return totalStock;
            } else {
                log.warn("同步物品总库存失败，物品不存在，itemId={}", itemId);
                return BigDecimal.ZERO;
            }
        } catch (Exception e) {
            log.error("同步物品总库存异常，itemId={}", itemId, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchSyncTotalStock(String... itemIds) {
        if (itemIds == null || itemIds.length == 0) {
            return 0;
        }
        
        int successCount = 0;
        for (String itemId : itemIds) {
            try {
                syncTotalStock(itemId);
                successCount++;
            } catch (Exception e) {
                log.error("批量同步物品总库存失败，itemId={}", itemId, e);
            }
        }
        
        log.info("批量同步物品总库存完成，总数={}，成功={}，失败={}", 
                itemIds.length, successCount, itemIds.length - successCount);
        return successCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int syncAllTotalStock() {
        try {
            // 查询所有未删除的物品
            LambdaQueryWrapper<ItemBaseInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ItemBaseInfo::getDeleted, 0);
            wrapper.select(ItemBaseInfo::getItemId);
            List<ItemBaseInfo> itemList = itemBaseInfoMapper.selectList(wrapper);
            
            if (itemList == null || itemList.isEmpty()) {
                log.info("没有需要同步的物品");
                return 0;
            }
            
            int successCount = 0;
            for (ItemBaseInfo item : itemList) {
                try {
                    syncTotalStock(item.getItemId());
                    successCount++;
                } catch (Exception e) {
                    log.error("同步物品总库存失败，itemId={}", item.getItemId(), e);
                }
            }
            
            log.info("同步所有物品总库存完成，总数={}，成功={}，失败={}", 
                    itemList.size(), successCount, itemList.size() - successCount);
            return successCount;
        } catch (Exception e) {
            log.error("同步所有物品总库存异常", e);
            throw e;
        }
    }

    @Override
    public Integer calculateStockStatus(String itemId) {
        try {
            // 查询物品基本信息
            ItemBaseInfo baseInfo = itemBaseInfoMapper.selectById(itemId);
            if (baseInfo == null) {
                log.warn("物品不存在，无法计算库存状态，itemId={}", itemId);
                return 1; // 默认正常
            }
            
            BigDecimal totalStock = baseInfo.getTotalStock();
            BigDecimal safetyStock = baseInfo.getSafetyStock();
            
            if (totalStock == null) {
                totalStock = BigDecimal.ZERO;
            }
            if (safetyStock == null) {
                safetyStock = BigDecimal.ZERO;
            }
            
            // 计算库存状态
            if (safetyStock.compareTo(BigDecimal.ZERO) <= 0) {
                return 1; // 未设置安全库存，默认正常
            }
            
            if (totalStock.compareTo(safetyStock) < 0) {
                return 2; // 库存不足
            } else if (totalStock.compareTo(safetyStock.multiply(new BigDecimal("2"))) > 0) {
                return 3; // 库存过剩（超过安全库存的2倍）
            } else {
                return 1; // 正常
            }
        } catch (Exception e) {
            log.error("计算物品库存状态异常，itemId={}", itemId, e);
            return 1; // 异常时默认正常
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSafetyStock(String itemId, BigDecimal safetyStock) {
        try {
            ItemBaseInfo baseInfo = new ItemBaseInfo();
            baseInfo.setItemId(itemId);
            baseInfo.setSafetyStock(safetyStock);
            baseInfo.setUpdateTime(new Date());
            try {
                baseInfo.setUpdateBy(SecurityUtils.getUsername());
            } catch (Exception e) {
                baseInfo.setUpdateBy("system");
            }
            
            int updateCount = itemBaseInfoMapper.updateById(baseInfo);
            if (updateCount > 0) {
                log.info("更新物品企业级安全库存成功，itemId={}，安全库存={}", itemId, safetyStock);
                return true;
            } else {
                log.warn("更新物品企业级安全库存失败，物品不存在，itemId={}", itemId);
                return false;
            }
        } catch (Exception e) {
            log.error("更新物品企业级安全库存异常，itemId={}", itemId, e);
            throw e;
        }
    }
} 