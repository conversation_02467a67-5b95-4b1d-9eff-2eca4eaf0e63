package com.jingfang.asset_inbound.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.asset_inbound.module.dto.AssetInboundDto;
import com.jingfang.asset_inbound.module.request.AssetInboundSearchRequest;
import com.jingfang.asset_inbound.module.vo.AssetInboundVo;

import java.util.List;

/**
 * 资产入库业务接口
 */
public interface AssetInboundService {
    
    /**
     * 新增资产入库单
     *
     * @param inboundDto 入库单信息
     * @param username 操作用户
     * @return 入库单ID
     */
    String addAssetInbound(AssetInboundDto inboundDto, String username);
    
    /**
     * 编辑资产入库单
     *
     * @param inboundId 入库单ID
     * @param inboundDto 入库单信息
     * @param username 操作用户
     */
    void updateAssetInbound(String inboundId, AssetInboundDto inboundDto, String username);
    
    /**
     * 获取入库单详情
     *
     * @param inboundId 入库单ID
     * @return 入库单详情
     */
    AssetInboundVo getInboundDetail(String inboundId);
    
    /**
     * 查询入库单列表
     *
     * @param request 查询条件
     * @return 入库单列表
     */
    IPage<AssetInboundVo> selectInboundList(AssetInboundSearchRequest request);
    
    /**
     * 提交入库单待确认
     *
     * @param inboundId 入库单ID
     * @param username 操作用户
     */
    void submitInbound(String inboundId, String username);
    
    /**
     * 经手人确认入库单
     *
     * @param inboundId 入库单ID
     * @param remark 确认意见
     * @param username 操作用户
     */
    void handleInbound(String inboundId, String remark, String username);
    
    /**
     * 审核入库单
     *
     * @param inboundId 入库单ID
     * @param status 审核结果（4-通过，5-退回）
     * @param remark 审核意见
     * @param username 操作用户
     */
    void auditInbound(String inboundId, Integer status, String remark, String username);
    
    /**
     * 删除入库单
     *
     * @param inboundIds 入库单ID数组
     * @param username 操作用户
     */
    void deleteInbound(String[] inboundIds, String username);
}
