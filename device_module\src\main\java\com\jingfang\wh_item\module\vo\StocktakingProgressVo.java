package com.jingfang.wh_item.module.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 盘点进度VO
 * 用于显示整体盘点进度信息
 */
@Data
public class StocktakingProgressVo implements Serializable {
    
    /**
     * 总物品数
     */
    private Integer totalItems;

    /**
     * 已完成数
     */
    private Integer completedItems;

    /**
     * 有差异数
     */
    private Integer differenceItems;

    /**
     * 完成率
     */
    private BigDecimal completionRate;

    /**
     * 各仓库进度
     */
    private List<WarehouseProgressVo> warehouseProgress;
    
    private static final long serialVersionUID = 1L;
}
