package com.jingfang.web.controller.item;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.common.annotation.Log;
import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.common.enums.BusinessType;
import com.jingfang.common.utils.SecurityUtils;
import com.jingfang.wh_item.module.dto.ItemStocktakingDto;
import com.jingfang.wh_item.module.dto.ItemStocktakingDetailUpdateDto;
import com.jingfang.wh_item.module.request.ItemStocktakingSearchRequest;
import com.jingfang.wh_item.module.vo.ItemStocktakingDetailVo;
import com.jingfang.wh_item.module.vo.ItemStocktakingVo;
import com.jingfang.wh_item.module.vo.StocktakingProgressVo;
import com.jingfang.wh_item.module.vo.PersonalProgressVo;
import com.jingfang.wh_item.service.ItemStocktakingService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 库存盘点Controller
 */
@RestController
@RequestMapping("/item/stocktaking")
public class ItemStocktakingController extends BaseController {
    
    @Resource
    private ItemStocktakingService stocktakingService;
    
    /**
     * 查询库存盘点列表
     */
    @PreAuthorize("@ss.hasPermi('item:stocktaking:list')")
    @GetMapping("/list")
    public AjaxResult list(ItemStocktakingSearchRequest request) {
        IPage<ItemStocktakingVo> page = stocktakingService.selectStocktakingList(request);
        return AjaxResult.success(page);
    }
    
    /**
     * 获取库存盘点详细信息
     */
    @PreAuthorize("@ss.hasPermi('item:stocktaking:query')")
    @GetMapping("/{stocktakingId}")
    public AjaxResult getInfo(@PathVariable String stocktakingId) {
        ItemStocktakingVo detail = stocktakingService.getStocktakingDetail(stocktakingId);
        return success(detail);
    }
    
    /**
     * 查询盘点明细列表
     */
    @PreAuthorize("@ss.hasPermi('item:stocktaking:query')")
    @GetMapping("/{stocktakingId}/details")
    public AjaxResult getDetails(@PathVariable String stocktakingId) {
        List<ItemStocktakingDetailVo> details = stocktakingService.selectStocktakingDetailList(stocktakingId);
        return success(details);
    }
    
    /**
     * 新增库存盘点
     */
    @PreAuthorize("@ss.hasPermi('item:stocktaking:add')")
    @Log(title = "库存盘点", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody ItemStocktakingDto dto) {
        String username = SecurityUtils.getUsername();
        String stocktakingId = stocktakingService.createStocktaking(dto, username);
        return AjaxResult.success("创建成功", stocktakingId);
    }
    
    /**
     * 修改库存盘点
     */
    @PreAuthorize("@ss.hasPermi('item:stocktaking:edit')")
    @Log(title = "库存盘点", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody ItemStocktakingDto dto) {
        String username = SecurityUtils.getUsername();
        boolean success = stocktakingService.updateStocktaking(dto, username);
        return success ? AjaxResult.success() : AjaxResult.error("修改失败");
    }
    
    /**
     * 删除库存盘点
     */
    @PreAuthorize("@ss.hasPermi('item:stocktaking:remove')")
    @Log(title = "库存盘点", businessType = BusinessType.DELETE)
    @DeleteMapping("/{stocktakingId}")
    public AjaxResult remove(@PathVariable String stocktakingId) {
        boolean success = stocktakingService.deleteStocktaking(stocktakingId);
        return success ? AjaxResult.success() : AjaxResult.error("删除失败");
    }
    
    /**
     * 生成盘点明细
     */
    @PreAuthorize("@ss.hasPermi('item:stocktaking:generate')")
    @Log(title = "生成盘点明细", businessType = BusinessType.UPDATE)
    @PostMapping("/{stocktakingId}/generate")
    public AjaxResult generateDetails(@PathVariable String stocktakingId) {
        boolean success = stocktakingService.generateStocktakingDetails(stocktakingId);
        return success ? AjaxResult.success("生成盘点明细成功") : AjaxResult.error("生成盘点明细失败");
    }
    
    /**
     * 开始盘点
     */
    @PreAuthorize("@ss.hasPermi('item:stocktaking:start')")
    @Log(title = "开始盘点", businessType = BusinessType.UPDATE)
    @PostMapping("/{stocktakingId}/start")
    public AjaxResult start(@PathVariable String stocktakingId) {
        String username = SecurityUtils.getUsername();
        boolean success = stocktakingService.startStocktaking(stocktakingId, username);
        return success ? AjaxResult.success("开始盘点成功") : AjaxResult.error("开始盘点失败");
    }
    
    /**
     * 录入盘点结果
     */
    @PreAuthorize("@ss.hasPermi('item:stocktaking:record')")
    @Log(title = "录入盘点结果", businessType = BusinessType.UPDATE)
    @PostMapping("/record")
    public AjaxResult recordResult(
            @RequestParam String detailId,
            @RequestParam BigDecimal actualQuantity,
            @RequestParam(required = false) String differenceReason) {
        String username = SecurityUtils.getUsername();
        boolean success = stocktakingService.recordStocktakingResult(detailId, actualQuantity, differenceReason, username);
        return success ? AjaxResult.success("录入盘点结果成功") : AjaxResult.error("录入盘点结果失败");
    }
    
    /**
     * 完成盘点
     */
    @PreAuthorize("@ss.hasPermi('item:stocktaking:complete')")
    @Log(title = "完成盘点", businessType = BusinessType.UPDATE)
    @PostMapping("/{stocktakingId}/complete")
    public AjaxResult complete(@PathVariable String stocktakingId) {
        String username = SecurityUtils.getUsername();
        boolean success = stocktakingService.completeStocktaking(stocktakingId, username);
        return success ? AjaxResult.success("完成盘点成功") : AjaxResult.error("完成盘点失败");
    }
    
    /**
     * 审核盘点
     */
    @PreAuthorize("@ss.hasPermi('item:stocktaking:audit')")
    @Log(title = "审核盘点", businessType = BusinessType.UPDATE)
    @PostMapping("/{stocktakingId}/audit")
    public AjaxResult audit(
            @PathVariable String stocktakingId,
            @RequestParam boolean approved,
            @RequestParam(required = false) String auditRemark) {
        String username = SecurityUtils.getUsername();
        boolean success = stocktakingService.auditStocktaking(stocktakingId, approved, auditRemark, username);
        return success ? AjaxResult.success("审核盘点成功") : AjaxResult.error("审核盘点失败");
    }
    
    /**
     * 应用盘点差异
     */
    @PreAuthorize("@ss.hasPermi('item:stocktaking:apply')")
    @Log(title = "应用盘点差异", businessType = BusinessType.UPDATE)
    @PostMapping("/{stocktakingId}/apply")
    public AjaxResult applyDifferences(@PathVariable String stocktakingId) {
        String username = SecurityUtils.getUsername();
        boolean success = stocktakingService.applyStocktakingDifferences(stocktakingId, username);
        return success ? AjaxResult.success("应用盘点差异成功") : AjaxResult.error("应用盘点差异失败");
    }
    
    // ==================== 微信小程序专用接口 ====================
    
    /**
     * 获取我的盘点任务列表
     */
    @PreAuthorize("@ss.hasPermi('item:stocktaking:list')")
    @GetMapping("/my-tasks")
    public AjaxResult getMyTasks() {
        String username = SecurityUtils.getUsername();
        List<ItemStocktakingVo> tasks = stocktakingService.getMyTasks(username);
        return AjaxResult.success(tasks);
    }
    
    /**
     * 获取盘点进度
     */
    @PreAuthorize("@ss.hasPermi('item:stocktaking:query')")
    @GetMapping("/{stocktakingId}/progress")
    public AjaxResult getProgress(@PathVariable String stocktakingId) {
        StocktakingProgressVo progress = stocktakingService.getStocktakingProgress(stocktakingId);
        return AjaxResult.success(progress);
    }
    
    /**
     * 获取个人盘点进度
     */
    @PreAuthorize("@ss.hasPermi('item:stocktaking:query')")
    @GetMapping("/my-progress")
    public AjaxResult getMyProgress() {
        String username = SecurityUtils.getUsername();
        PersonalProgressVo progress = stocktakingService.getMyProgress(username);
        return AjaxResult.success(progress);
    }
    
    /**
     * 获取个人盘点记录
     */
    @PreAuthorize("@ss.hasPermi('item:stocktaking:query')")
    @GetMapping("/my-records")
    public AjaxResult getMyRecords(
            @RequestParam(defaultValue = "today") String timeRange,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        String username = SecurityUtils.getUsername();
        List<ItemStocktakingDetailVo> records = stocktakingService.getMyRecords(username, timeRange, startDate, endDate);
        return AjaxResult.success(records);
    }
    
    /**
     * 获取物品盘点历史
     */
    @PreAuthorize("@ss.hasPermi('item:stocktaking:query')")
    @GetMapping("/item-history/{itemId}")
    public AjaxResult getItemHistory(@PathVariable String itemId) {
        List<ItemStocktakingDetailVo> history = stocktakingService.getItemHistory(itemId);
        return AjaxResult.success(history);
    }
    
    /**
     * 更新盘点明细
     */
    @PreAuthorize("@ss.hasPermi('item:stocktaking:record')")
    @Log(title = "更新盘点明细", businessType = BusinessType.UPDATE)
    @PutMapping("/detail/{detailId}")
    public AjaxResult updateDetail(
            @PathVariable String detailId,
            @Validated @RequestBody ItemStocktakingDetailUpdateDto dto) {
        String username = SecurityUtils.getUsername();
        boolean success = stocktakingService.updateStocktakingDetail(detailId, dto, username);
        return success ? AjaxResult.success("更新盘点明细成功") : AjaxResult.error("更新盘点明细失败");
    }
    
    /**
     * 根据物品信息查找盘点明细
     */
    @PreAuthorize("@ss.hasPermi('item:stocktaking:query')")
    @GetMapping("/detail/by-item")
    public AjaxResult getDetailByItem(
            @RequestParam String stocktakingId,
            @RequestParam String itemId,
            @RequestParam Integer warehouseId) {
        ItemStocktakingDetailVo detail = stocktakingService.getStocktakingDetailByItem(stocktakingId, itemId, warehouseId);
        return detail != null ? AjaxResult.success(detail) : AjaxResult.error("未找到对应的盘点明细");
    }
}
