# 物品入库功能实现文档

本文档详细描述了物品入库功能的实现方式，以便于在微信小程序上复现该功能。

## 一、功能概述

物品入库功能是物资管理系统中的核心功能之一，用于记录和管理物品入库的完整流程。该功能包含完整的业务流程：从创建入库单、确认、审核到最终完成入库，并自动更新库存数据。

### 1.1 业务流程

物品入库的完整业务流程如下：

1. **创建入库单**：用户创建入库单，填写供应商、入库类型、业务日期和入库物品明细
2. **提交入库单**：用户提交入库单，状态从"草稿"变为"待确认"
3. **经手人确认**：经手人确认入库单信息，状态从"待确认"变为"待审核"
4. **审核入库单**：审核人审核入库单，可以通过或退回
   - 审核通过：状态从"待审核"变为"已通过"，并自动增加库存
   - 审核退回：状态从"待审核"变为"已退回"，用户可以修改后重新提交
5. **完成入库**：审核通过后，系统自动完成入库流程，增加相应库存

### 1.2 状态流转

入库单状态流转图：

```
[草稿(1)] ----提交----> [待确认(2)] ----确认----> [待审核(3)]
    ^                                             |
    |                                             v
    +-------------退回---------------- [已退回(5)] / [已通过(4)]
```

**状态说明**：
- 1：草稿 - 新创建的入库单，可以编辑和删除
- 2：待确认 - 已提交，等待经手人确认
- 3：待审核 - 经手人已确认，等待审核人审核
- 4：已通过 - 审核通过，库存已自动更新
- 5：已退回 - 审核退回，可以重新修改和提交

## 二、数据结构

### 2.1 数据库表设计

物品入库功能涉及以下数据库表：

1. **item_inbound**：入库单主表
2. **item_inbound_detail**：入库单明细表
3. **item_inbound_operation_log**：入库单操作日志表

#### 2.1.1 入库单主表 (item_inbound)

| 字段名 | 类型 | 说明 |
|-------|------|------|
| inbound_id | varchar | 入库单ID（主键） |
| business_date | date | 业务日期 |
| supplier_name | varchar | 供应商名称 |
| inbound_type | int | 入库类型(1-采购入库, 2-退货入库, 3-调拨入库, 4-其他入库) |
| status | int | 状态(1-草稿, 2-待确认, 3-待审核, 4-已通过, 5-已退回) |
| creator_id | bigint | 制单人ID |
| create_time | datetime | 创建时间 |
| handler_id | bigint | 经手人ID |
| handle_time | datetime | 经手确认时间 |
| handle_remark | varchar | 经手人备注 |
| auditor_id | bigint | 审核人ID |
| audit_time | datetime | 审核时间 |
| audit_remark | varchar | 审核备注 |
| update_time | datetime | 更新时间 |
| del_flag | char | 删除标志(0-正常, 1-删除) |

#### 2.1.2 入库单明细表 (item_inbound_detail)

| 字段名 | 类型 | 说明 |
|-------|------|------|
| detail_id | bigint | 明细ID（主键） |
| inbound_id | varchar | 入库单ID（外键） |
| item_id | varchar | 物品ID |
| batch_no | varchar | 批次号 |
| production_date | date | 生产日期 |
| expiry_date | date | 过期日期 |
| quantity | decimal | 入库数量 |
| unit_price | decimal | 单价 |
| amount | decimal | 金额 |
| warehouse_id | int | 仓库ID |
| shelf_location | varchar | 货架位置 |
| remark | varchar | 备注 |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |

#### 2.1.3 入库单操作日志表 (item_inbound_operation_log)

| 字段名 | 类型 | 说明 |
|-------|------|------|
| log_id | bigint | 日志ID（主键） |
| inbound_id | varchar | 入库单ID（外键） |
| operation_type | int | 操作类型(1-创建, 2-修改, 3-提交, 4-确认, 5-审核通过, 6-审核退回, 7-库存更新, 9-删除) |
| operation_content | varchar | 操作内容 |
| operation_time | datetime | 操作时间 |
| operator_id | bigint | 操作人ID |
| operator_name | varchar | 操作人姓名 |

### 2.2 数据传输对象

#### 2.2.1 入库单DTO (ItemInboundDto)

用于前后端交互的数据传输对象：

```json
{
  "main": {
    "inboundId": "string",        // 入库单ID（新增时可为空，系统自动生成）
    "businessDate": "2024-01-01", // 业务日期（必填）
    "supplierName": "string",     // 供应商名称
    "inboundType": 1,             // 入库类型（必填）
    "handlerId": 1                // 经手人ID（系统自动设置）
  },
  "details": [                    // 入库明细列表（必填，至少一条）
    {
      "itemId": "string",         // 物品ID（必填）
      "batchNo": "string",        // 批次号
      "productionDate": "2024-01-01", // 生产日期
      "expiryDate": "2024-12-31", // 过期日期
      "quantity": 10.00,          // 入库数量（必填）
      "unitPrice": 100.00,        // 单价
      "amount": 1000.00,          // 金额
      "warehouseId": 1,           // 仓库ID（必填）
      "shelfLocation": "string",  // 货架位置
      "remark": "string"          // 备注
    }
  ]
}
```

## 三、接口设计

### 3.1 后端接口

物品入库功能的后端接口路径为 `/item/inbound`，主要包括以下接口：

#### 3.1.1 新增入库单

- **接口**: `POST /item/inbound/add`
- **功能**: 创建新的入库单
- **权限**: `item:inbound:add`
- **参数**: ItemInboundDto（入库单信息）
- **返回**: 操作结果和入库单ID

#### 3.1.2 修改入库单

- **接口**: `PUT /item/inbound/{inboundId}`
- **功能**: 修改入库单信息
- **权限**: `item:inbound:edit`
- **参数**: 
  - inboundId: 入库单ID
  - ItemInboundDto: 入库单信息
- **返回**: 操作结果

#### 3.1.3 提交入库单

- **接口**: `PUT /item/inbound/submit/{inboundId}`
- **功能**: 提交入库单，状态变为待确认
- **权限**: `item:inbound:submit`
- **参数**: inboundId（入库单ID）
- **返回**: 操作结果

#### 3.1.4 确认入库单

- **接口**: `PUT /item/inbound/handle/{inboundId}`
- **功能**: 经手人确认入库单，状态变为待审核
- **权限**: `item:inbound:handle`
- **参数**: 
  - inboundId: 入库单ID
  - remark: 确认备注（可选）
- **返回**: 操作结果

#### 3.1.5 审核入库单

- **接口**: `PUT /item/inbound/audit/{inboundId}`
- **功能**: 审核入库单，可通过或退回
- **权限**: `item:inbound:audit`
- **参数**: 
  - inboundId: 入库单ID
  - status: 审核结果(4-通过, 5-退回)
  - remark: 审核备注（可选）
- **返回**: 操作结果

#### 3.1.6 查询入库单列表

- **接口**: `POST /item/inbound/list`
- **功能**: 分页查询入库单列表
- **权限**: `item:inbound:list`
- **参数**: ItemInboundSearchRequest（查询条件）
- **返回**: 分页数据

#### 3.1.7 查询入库单详情

- **接口**: `GET /item/inbound/{inboundId}`
- **功能**: 查询入库单详情
- **权限**: `item:inbound:query`
- **参数**: inboundId（入库单ID）
- **返回**: 入库单详情

#### 3.1.8 删除入库单

- **接口**: `DELETE /item/inbound/{inboundIds}`
- **功能**: 删除入库单
- **权限**: `item:inbound:remove`
- **参数**: inboundIds（入库单ID，多个用逗号分隔）
- **返回**: 操作结果

### 3.2 接口返回格式

所有接口都遵循统一的返回格式：

```json
{
  "code": 200,           // 状态码：200-成功，其他-失败
  "msg": "success",      // 消息
  "data": {}            // 数据（可选）
}
```

## 四、业务逻辑实现

### 4.1 核心业务流程

#### 4.1.1 创建入库单

1. **数据校验**：
   - 验证业务日期是否有效
   - 验证入库明细中的物品ID是否存在
   - 验证入库数量是否大于0
   - 验证仓库ID是否有效

2. **生成入库单ID**：
   - 使用业务代码生成器生成入库单ID
   - 格式：WPRK + 时间戳 + 序号
   - 示例：WPRK202401010001

3. **保存数据**：
   - 保存入库单主表信息，状态设为草稿(1)
   - 保存入库单明细信息
   - 记录操作日志

#### 4.1.2 状态流转逻辑

1. **提交入库单**：
   - 只有草稿(1)和已退回(5)状态可以提交
   - 状态更新为待确认(2)
   - 记录操作日志

2. **确认入库单**：
   - 只有待确认(2)状态可以确认
   - 状态更新为待审核(3)
   - 记录经手人信息和确认时间
   - 记录操作日志

3. **审核入库单**：
   - 只有待审核(3)状态可以审核
   - 审核通过：状态更新为已通过(4)，自动增加库存
   - 审核退回：状态更新为已退回(5)
   - 记录审核人信息和审核时间
   - 记录操作日志

#### 4.1.3 库存增加逻辑

当入库单审核通过时，系统会自动增加库存：

1. **遍历入库明细**：
   - 获取每个明细的物品ID、仓库ID和入库数量
   - 验证仓库ID是否有效

2. **增加库存**：
   - 调用物品服务的库存更新接口
   - 操作类型为入库(1)
   - 关联单据ID为入库单ID
   - 传递货架位置信息

3. **库存状态更新**：
   - 根据安全库存设置更新库存状态
   - 同步更新物品总库存

4. **记录日志**：
   - 库存增加成功后，记录库存更新操作日志

### 4.2 权限控制

物品入库功能基于RuoYi框架的权限体系，主要权限包括：

- `item:inbound:add`：新增入库单
- `item:inbound:edit`：修改入库单
- `item:inbound:submit`：提交入库单
- `item:inbound:handle`：确认入库单
- `item:inbound:audit`：审核入库单
- `item:inbound:list`：查询入库单列表
- `item:inbound:query`：查询入库单详情
- `item:inbound:remove`：删除入库单

### 4.3 数据校验规则

1. **必填字段校验**：
   - 业务日期、入库类型为必填
   - 入库明细至少包含一条记录
   - 物品ID、仓库ID、入库数量为必填

2. **业务规则校验**：
   - 入库数量必须大于0
   - 物品必须存在且未删除
   - 仓库必须存在且有效
   - 单价和金额必须为非负数

3. **状态校验**：
   - 只有特定状态的入库单才能进行相应操作
   - 已通过或已删除的入库单不能修改

### 4.4 库存更新机制

#### 4.4.1 库存更新流程

1. **查找或创建库存记录**：
   - 根据物品ID和仓库ID查找库存记录
   - 如果不存在，则创建新的库存记录

2. **更新库存数量**：
   - 当前库存 = 原库存 + 入库数量
   - 更新库存状态（正常/不足/过剩）
   - 更新货架位置（如果提供）

3. **同步总库存**：
   - 调用库存同步服务更新物品总库存
   - 总库存 = 所有仓库库存之和

#### 4.4.2 库存状态判断

```java
// 库存状态判断逻辑
int stockStatus = 1; // 默认正常
if (safetyStock != null && safetyStock.compareTo(BigDecimal.ZERO) > 0) {
    if (currentQuantity.compareTo(safetyStock) < 0) {
        stockStatus = 2; // 库存不足
    }
}
```

## 五、前端实现

### 5.1 页面结构

前端页面主要包括以下部分：

1. **查询条件区域**：
   - 基础查询：入库单号、业务日期、入库状态
   - 高级查询：供应商、入库类型、制单人

2. **操作按钮区域**：
   - 新增、删除、导出等操作按钮

3. **数据表格区域**：
   - 显示入库单列表
   - 支持分页、排序、筛选

4. **详情弹窗**：
   - 显示入库单详细信息
   - 包含明细列表和操作日志

### 5.2 关键功能实现

#### 5.2.1 状态显示

根据入库单状态显示不同的标签颜色：

```javascript
const statusMap = {
  1: { label: '草稿', type: 'info' },
  2: { label: '待确认', type: 'warning' },
  3: { label: '待审核', type: 'primary' },
  4: { label: '已通过', type: 'success' },
  5: { label: '已退回', type: 'danger' }
}
```

#### 5.2.2 操作按钮控制

根据入库单状态和用户权限控制操作按钮的显示：

```javascript
// 可编辑状态：草稿、已退回
const canEdit = [1, 5].includes(status)

// 可提交状态：草稿、已退回
const canSubmit = [1, 5].includes(status)

// 可确认状态：待确认
const canHandle = status === 2

// 可审核状态：待审核
const canAudit = status === 3

// 可删除状态：草稿、已退回
const canDelete = [1, 5].includes(status)
```

#### 5.2.3 入库类型管理

入库类型通过数据字典配置：

```javascript
const inboundTypeMap = {
  1: '采购入库',
  2: '退货入库',
  3: '调拨入库',
  4: '其他入库'
}
```

## 六、微信小程序适配建议

### 6.1 界面设计建议

1. **简化操作流程**：
   - 合并相似功能，减少页面跳转
   - 使用底部弹窗代替新页面
   - 提供快速入库功能

2. **优化交互体验**：
   - 使用下拉刷新和上拉加载
   - 支持扫码录入物品信息
   - 提供拍照上传功能

3. **适配小屏幕**：
   - 精简表格列，只显示关键信息
   - 使用卡片式布局代替表格
   - 优化表单布局

### 6.2 功能简化建议

1. **状态流转简化**：
   - 可以考虑简化审核流程
   - 提供快速审核功能
   - 支持批量操作

2. **权限简化**：
   - 基于用户角色自动判断权限
   - 减少复杂的权限配置
   - 提供角色模板

3. **数据录入优化**：
   - 支持扫码录入物品信息
   - 提供常用物品快速选择
   - 支持批量导入功能

### 6.3 技术实现建议

1. **接口调用**：
   - 使用wx.request进行接口调用
   - 统一封装请求和响应处理
   - 实现请求重试机制

2. **数据管理**：
   - 使用全局状态管理
   - 实现数据缓存和同步机制
   - 支持离线操作

3. **组件化开发**：
   - 封装通用组件（状态标签、操作按钮等）
   - 提高代码复用性
   - 统一UI风格

### 6.4 特殊功能建议

1. **扫码功能**：
   - 支持扫描物品条码自动填充信息
   - 支持扫描入库单二维码查看详情

2. **拍照功能**：
   - 支持拍照记录入库现场
   - 上传图片作为入库凭证

3. **定位功能**：
   - 记录入库操作的地理位置
   - 用于审计和追溯

## 七、总结

物品入库功能是一个完整的业务流程管理系统，包含了状态流转、权限控制、库存管理等多个方面。在微信小程序上实现时，需要根据移动端的特点进行适当的简化和优化，确保用户体验的同时保持业务逻辑的完整性。

关键实现要点：
1. 完整的状态流转机制
2. 严格的权限控制
3. 自动的库存增加
4. 详细的操作日志
5. 灵活的查询和筛选功能
6. 支持批次管理和有效期管理
7. 多仓库支持
8. 实时库存同步
