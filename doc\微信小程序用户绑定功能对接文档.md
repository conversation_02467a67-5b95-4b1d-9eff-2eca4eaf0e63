# 微信小程序用户绑定功能对接文档

## 1. 功能概述

本文档描述了微信小程序用户与系统用户的绑定功能，包括微信登录、账号绑定和解绑等操作。通过此功能，用户可以使用微信小程序直接登录系统，无需重复输入用户名和密码。

## 2. 接口说明

### 2.1 微信登录接口

**接口描述**：微信小程序用户登录，获取openid并检查是否已绑定系统账号

**请求路径**：`/wx/login`  
**请求方式**：POST  
**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|-----|------|
| code | String | 是 | 微信临时登录凭证 |
| userInfo | Object | 否 | 微信用户信息 |

userInfo对象结构：

```json
{
  "nickName": "用户昵称",
  "avatarUrl": "头像地址",
  "gender": 1,
  "country": "国家",
  "province": "省份",
  "city": "城市"
}
```

**返回结果**：

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "openid": "微信openid",
    "sessionKey": "微信会话密钥",
    "isBind": true,
    "token": "系统登录token（已绑定时返回）",
    "user": {
      // 系统用户信息（已绑定时返回）
    }
  }
}
```

### 2.2 绑定系统账号接口

**接口描述**：将微信小程序用户与系统账号进行绑定

**请求路径**：`/wx/bind`  
**请求方式**：POST  
**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|-----|------|
| openid | String | 是 | 微信openid |
| username | String | 是 | 系统用户名 |
| password | String | 是 | 系统用户密码 |
| bindType | String | 否 | 绑定类型（0账号绑定 1手机号绑定），默认为0 |

**返回结果**：

```json
{
  "code": 200,
  "msg": "绑定成功",
  "data": {
    "token": "系统登录token",
    "user": {
      // 系统用户信息
    }
  }
}
```

### 2.3 解绑系统账号接口

**接口描述**：解除微信小程序用户与系统账号的绑定关系

**请求路径**：`/wx/unbind`  
**请求方式**：POST  
**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|-----|------|
| openid | String | 是 | 微信openid |
| userId | Long | 是 | 系统用户ID |

**返回结果**：

```json
{
  "code": 200,
  "msg": "解绑成功"
}
```

## 3. 前端实现流程

### 3.1 微信登录流程

1. 调用微信API获取登录凭证code
2. 调用后端登录接口，传入code和用户信息
3. 根据返回结果判断是否已绑定系统账号
   - 已绑定：保存token，跳转到首页
   - 未绑定：跳转到绑定页面

```javascript
// 微信登录示例代码
wx.login({
  success: (res) => {
    if (res.code) {
      // 获取用户信息（可选）
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (userRes) => {
          // 调用后端登录接口
          wx.request({
            url: 'https://your-domain.com/wx/login',
            method: 'POST',
            data: {
              code: res.code,
              userInfo: userRes.userInfo
            },
            success: (result) => {
              const data = result.data.data;
              // 保存openid和sessionKey
              wx.setStorageSync('openid', data.openid);
              
              if (data.isBind) {
                // 已绑定，保存token并跳转
                wx.setStorageSync('token', data.token);
                wx.switchTab({ url: '/pages/index/index' });
              } else {
                // 未绑定，跳转到绑定页面
                wx.navigateTo({ url: '/pages/bind/bind' });
              }
            }
          });
        }
      });
    }
  }
});
```

### 3.2 账号绑定流程

1. 用户输入系统账号和密码
2. 调用后端绑定接口，传入openid、用户名和密码
3. 绑定成功后保存token，跳转到首页

```javascript
// 绑定账号示例代码
bindAccount() {
  const { username, password } = this.data;
  const openid = wx.getStorageSync('openid');
  
  wx.request({
    url: 'https://your-domain.com/wx/bind',
    method: 'POST',
    data: {
      openid,
      username,
      password,
      bindType: '0'
    },
    success: (res) => {
      if (res.data.code === 200) {
        // 绑定成功，保存token
        wx.setStorageSync('token', res.data.data.token);
        wx.switchTab({ url: '/pages/index/index' });
      } else {
        wx.showToast({ title: res.data.msg, icon: 'none' });
      }
    }
  });
}
```

### 3.3 账号解绑流程

1. 用户点击解绑按钮，弹出确认对话框
2. 确认后调用后端解绑接口，传入openid和userId
3. 解绑成功后清除token，跳转到登录页面

```javascript
// 解绑账号示例代码
unbindAccount() {
  wx.showModal({
    title: '提示',
    content: '确定要解绑当前账号吗？',
    success: (res) => {
      if (res.confirm) {
        const openid = wx.getStorageSync('openid');
        const userInfo = wx.getStorageSync('userInfo');
        
        wx.request({
          url: 'https://your-domain.com/wx/unbind',
          method: 'POST',
          data: {
            openid,
            userId: userInfo.userId
          },
          success: (res) => {
            if (res.data.code === 200) {
              // 解绑成功，清除token
              wx.removeStorageSync('token');
              wx.removeStorageSync('userInfo');
              wx.redirectTo({ url: '/pages/login/login' });
            } else {
              wx.showToast({ title: res.data.msg, icon: 'none' });
            }
          }
        });
      }
    }
  });
}
```

## 4. 页面设计建议

### 4.1 登录页面

- 微信一键登录按钮
- 系统账号登录入口（可选）
- 简洁的页面设计，突出微信登录按钮

### 4.2 绑定页面

- 系统账号输入框
- 密码输入框
- 绑定按钮
- 返回按钮（可选）
- 绑定说明文字

### 4.3 个人中心页面

- 显示用户基本信息
- 账号解绑按钮
- 其他个人设置选项

## 5. 安全建议

1. 所有接口通信使用HTTPS加密传输
2. 密码不要明文存储，前端传输时也应进行加密
3. 敏感操作（如解绑）需要二次确认
4. 存储在本地的token应设置过期时间
5. 定期检查异常的绑定和解绑操作

## 6. 测试要点

1. 微信登录成功率
2. 绑定成功率和失败处理
3. 解绑操作的正确性
4. 多设备登录的token处理
5. 网络异常情况下的用户体验
6. 用户信息变更后的同步机制

## 7. 常见问题

### Q1: 用户拒绝授权获取用户信息怎么处理？
A1: 可以只使用openid进行登录，不强制要求用户授权个人信息。

### Q2: 一个微信账号可以绑定多个系统账号吗？
A2: 当前设计是一对一关系，一个微信账号只能绑定一个系统账号。

### Q3: 系统账号被禁用后，微信登录会怎样？
A3: 系统会检查账号状态，如果被禁用，微信登录也会失败。

### Q4: token过期后如何处理？
A4: 前端检测到token过期，应重新调用登录接口获取新token。

## 8. 后续优化方向

1. 支持一个微信账号绑定多个系统账号
2. 增加手机号快速绑定功能
3. 添加微信消息推送功能
4. 支持微信扫码登录Web端
5. 增加登录安全策略，如异地登录提醒

---

如有任何疑问，请与后端开发团队联系。本文档将根据实际开发情况持续更新。