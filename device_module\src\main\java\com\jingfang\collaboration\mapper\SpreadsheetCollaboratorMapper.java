package com.jingfang.collaboration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.collaboration.domain.SpreadsheetCollaborator;
import com.jingfang.collaboration.vo.CollaboratorVo;
import com.jingfang.collaboration.vo.OnlineUserVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 表格协作者Mapper接口
 */
@Mapper
public interface SpreadsheetCollaboratorMapper extends BaseMapper<SpreadsheetCollaborator> {
    
    /**
     * 查询表格的协作者列表
     */
    List<CollaboratorVo> selectCollaboratorsBySpreadsheetId(@Param("spreadsheetId") String spreadsheetId);
    
    /**
     * 查询用户在指定表格的权限
     */
    String selectUserPermission(@Param("spreadsheetId") String spreadsheetId, @Param("userId") Long userId);
    
    /**
     * 查询表格的在线用户列表
     */
    List<OnlineUserVo> selectOnlineUsers(@Param("spreadsheetId") String spreadsheetId);
    
    /**
     * 批量更新用户在线状态
     */
    int updateUserOnlineStatus(@Param("userIds") List<Long> userIds, @Param("spreadsheetId") String spreadsheetId,
                               @Param("isOnline") String isOnline);

    /**
     * 查询用户的协作邀请列表
     */
    IPage<CollaboratorVo> selectUserInvitations(IPage<CollaboratorVo> page, @Param("userId") Long userId,
                                               @Param("spreadsheetTitle") String spreadsheetTitle,
                                               @Param("status") String status);
}
