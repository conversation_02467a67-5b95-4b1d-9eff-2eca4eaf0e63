package com.jingfang.asset_ledger.module.vo;

import lombok.Data;

import java.util.Date;

@Data
public class AssetDetailMaintainInfoVo {

    private Long maintainId;

    /**
     * 维保厂商
     */
    private String maintainVendor;

    /**
     *
     */
    private Date startTime;

    /**
     *
     */
    private Date endTime;

    /**
     * 维保状态
     */
    private Integer maintainStatus;

    /**
     * 联系方式
     */
    private String contactNumber;

    /**
     * 负责人
     */
    private Long managerId;

    /**
     * 维保方式
     */
    private Integer maintainMethod;

    /**
     * 备注
     */
    private String remark;



}
