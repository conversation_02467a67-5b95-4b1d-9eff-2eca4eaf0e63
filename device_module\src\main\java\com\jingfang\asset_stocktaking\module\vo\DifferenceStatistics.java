package com.jingfang.asset_stocktaking.module.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 差异统计信息视图对象
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
public class DifferenceStatistics implements Serializable {
    
    /**
     * 差异资产总数
     */
    private Integer totalDifferences;
    
    /**
     * 盘盈数量
     */
    private Integer surplusCount;
    
    /**
     * 盘亏数量
     */
    private Integer deficitCount;
    
    /**
     * 状态差异数量
     */
    private Integer statusDiffCount;
    
    /**
     * 位置差异数量
     */
    private Integer locationDiffCount;
    
    /**
     * 盘盈价值
     */
    private BigDecimal surplusValue;
    
    /**
     * 盘亏价值
     */
    private BigDecimal deficitValue;
    
    /**
     * 差异率
     */
    private Double differenceRate;
    
    /**
     * 已处理差异数
     */
    private Integer processedDifferences;
    
    /**
     * 待处理差异数
     */
    private Integer pendingDifferences;

    private static final long serialVersionUID = 1L;
}
