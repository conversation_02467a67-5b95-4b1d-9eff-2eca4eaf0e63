{"微信小程序库存盘点接口测试用例": {"说明": "本文档包含所有新增接口的测试用例，请根据实际环境修改baseUrl、token等参数", "baseUrl": "http://localhost:8080", "headers": {"Content-Type": "application/json", "Authorization": "Bearer your_jwt_token_here"}, "P0级别接口测试": {"1. 获取我的盘点任务列表": {"method": "GET", "url": "{{baseUrl}}/item/stocktaking/my-tasks", "description": "获取当前用户分配的盘点任务", "expectedResponse": {"code": 200, "msg": "操作成功", "data": [{"stocktakingId": "string", "stocktakingCode": "PD20250111001", "stocktakingName": "2025年1月盘点", "stocktakingType": 1, "stocktakingTypeName": "全盘", "status": 1, "statusName": "进行中", "totalItems": 100, "checkedItems": 50, "progress": 50.0}]}}, "2. 根据物品条码查询物品信息": {"method": "GET", "url": "{{baseUrl}}/item/by-code/TEST001", "description": "扫码查询物品详情", "pathParams": {"itemCode": "TEST001"}, "expectedResponse": {"code": 200, "msg": "查询成功", "data": {"itemId": "string", "itemName": "测试物品", "itemCode": "TEST001", "specModel": "规格型号", "unit": "个", "itemType": 1, "safetyStock": 10.0, "totalStock": 100.0}}}, "3. 根据物品信息查找盘点明细": {"method": "GET", "url": "{{baseUrl}}/item/stocktaking/detail/by-item", "description": "扫码后定位盘点明细", "queryParams": {"stocktakingId": "test_stocktaking_id", "itemId": "test_item_id", "warehouseId": 1}, "expectedResponse": {"code": 200, "msg": "操作成功", "data": {"detailId": "string", "stocktakingId": "string", "itemId": "string", "itemName": "测试物品", "itemCode": "TEST001", "warehouseId": 1, "warehouseName": "主仓库", "bookQuantity": 100.0, "actualQuantity": null, "status": 0, "statusName": "待盘点", "photos": []}}}, "4. 更新盘点明细": {"method": "PUT", "url": "{{baseUrl}}/item/stocktaking/detail/test_detail_id", "description": "录入盘点结果和照片", "pathParams": {"detailId": "test_detail_id"}, "requestBody": {"actualQuantity": 95.0, "differenceReason": "部分物品损耗", "photos": ["http://example.com/upload/2025/01/11/photo1.jpg", "http://example.com/upload/2025/01/11/photo2.jpg"]}, "expectedResponse": {"code": 200, "msg": "更新盘点明细成功"}}}, "P1级别接口测试": {"5. 获取盘点进度": {"method": "GET", "url": "{{baseUrl}}/item/stocktaking/test_stocktaking_id/progress", "description": "查看整体盘点进度", "pathParams": {"stocktakingId": "test_stocktaking_id"}, "expectedResponse": {"code": 200, "msg": "操作成功", "data": {"totalItems": 100, "completedItems": 50, "differenceItems": 5, "completionRate": 50.0, "warehouseProgress": []}}}, "6. 获取个人盘点进度": {"method": "GET", "url": "{{baseUrl}}/item/stocktaking/my-progress", "description": "查看个人盘点进度", "expectedResponse": {"code": 200, "msg": "操作成功", "data": {"totalAssigned": 50, "completed": 25, "withDifference": 3, "completionRate": 50.0, "activeTaskCount": 2}}}}, "P2级别接口测试": {"7. 获取个人盘点记录": {"method": "GET", "url": "{{baseUrl}}/item/stocktaking/my-records", "description": "查看个人盘点历史记录", "queryParams": {"timeRange": "today", "startDate": "2025-01-01", "endDate": "2025-01-11"}, "testCases": [{"name": "今日记录", "params": {"timeRange": "today"}}, {"name": "本周记录", "params": {"timeRange": "week"}}, {"name": "本月记录", "params": {"timeRange": "month"}}, {"name": "自定义时间范围", "params": {"timeRange": "custom", "startDate": "2025-01-01", "endDate": "2025-01-11"}}], "expectedResponse": {"code": 200, "msg": "操作成功", "data": [{"detailId": "string", "itemName": "测试物品", "itemCode": "TEST001", "warehouseName": "主仓库", "bookQuantity": 100.0, "actualQuantity": 95.0, "differenceQuantity": -5.0, "checkTime": "2025-01-11 10:30:00", "photos": ["url1", "url2"]}]}}, "8. 获取物品盘点历史": {"method": "GET", "url": "{{baseUrl}}/item/stocktaking/item-history/test_item_id", "description": "查看特定物品的盘点历史", "pathParams": {"itemId": "test_item_id"}, "expectedResponse": {"code": 200, "msg": "操作成功", "data": [{"detailId": "string", "stocktakingId": "string", "itemName": "测试物品", "itemCode": "TEST001", "checkTime": "2025-01-11 10:30:00", "bookQuantity": 100.0, "actualQuantity": 95.0, "differenceQuantity": -5.0, "checkerName": "张三", "photos": ["url1", "url2"]}]}}}, "测试数据准备": {"说明": "在测试前需要准备的测试数据", "步骤": ["1. 创建测试物品（物品条码：TEST001）", "2. 创建盘点计划", "3. 生成盘点明细", "4. 开始盘点任务"], "测试物品数据": {"itemCode": "TEST001", "itemName": "测试物品A", "specModel": "规格A", "unit": "个", "itemType": 1, "safetyStock": 10.0}, "盘点计划数据": {"stocktakingName": "测试盘点计划", "stocktakingType": 1, "warehouseId": 1, "planStartTime": "2025-01-11 09:00:00", "planEndTime": "2025-01-11 18:00:00", "remark": "API测试用盘点计划"}}, "错误场景测试": {"1. 物品条码不存在": {"method": "GET", "url": "{{baseUrl}}/item/by-code/NOTEXIST", "expectedResponse": {"code": 500, "msg": "未找到对应的物品信息"}}, "2. 盘点明细不存在": {"method": "GET", "url": "{{baseUrl}}/item/stocktaking/detail/by-item", "queryParams": {"stocktakingId": "not_exist", "itemId": "not_exist", "warehouseId": 999}, "expectedResponse": {"code": 500, "msg": "未找到对应的盘点明细"}}, "3. 更新不存在的明细": {"method": "PUT", "url": "{{baseUrl}}/item/stocktaking/detail/not_exist_detail", "requestBody": {"actualQuantity": 100.0}, "expectedResponse": {"code": 500, "msg": "更新盘点明细失败"}}}, "性能测试建议": {"说明": "建议的性能测试场景", "场景": [{"名称": "大量盘点明细查询", "描述": "测试包含1000+明细的盘点任务查询性能", "接口": "/item/stocktaking/my-tasks"}, {"名称": "历史记录分页查询", "描述": "测试大量历史记录的查询性能", "接口": "/item/stocktaking/my-records"}, {"名称": "并发盘点更新", "描述": "测试多用户同时更新盘点明细的并发性能", "接口": "/item/stocktaking/detail/{detailId}"}]}}}