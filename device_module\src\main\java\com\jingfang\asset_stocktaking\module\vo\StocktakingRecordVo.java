package com.jingfang.asset_stocktaking.module.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 盘点记录视图对象
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
public class StocktakingRecordVo implements Serializable {

    /**
     * 盘点记录ID
     */
    private String recordId;

    /**
     * 盘点任务ID
     */
    private String taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 资产ID
     */
    private String assetId;

    /**
     * 资产编码
     */
    private String assetCode;

    /**
     * 资产名称
     */
    private String assetName;

    /**
     * 资产分类
     */
    private String categoryName;

    /**
     * 发现状态：1-找到，0-未找到
     */
    private Integer foundStatus;

    /**
     * 发现状态描述
     */
    private String foundStatusDesc;

    /**
     * 实际位置
     */
    private String actualLocation;

    /**
     * 账面位置
     */
    private String bookLocation;

    /**
     * 实际状态
     */
    private Integer actualStatus;

    /**
     * 实际状态描述
     */
    private String actualStatusDesc;

    /**
     * 账面状态
     */
    private Integer bookStatus;

    /**
     * 账面状态描述
     */
    private String bookStatusDesc;

    /**
     * 盘点人ID
     */
    private Long inventoryUserId;

    /**
     * 盘点人姓名
     */
    private String inventoryUserName;

    /**
     * 盘点时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inventoryTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 资产基本信息
     */
    private AssetBasicInfo assetInfo;

    /**
     * 资产基本信息内部类
     */
    @Data
    public static class AssetBasicInfo implements Serializable {
        
        /**
         * 规格型号
         */
        private String specModel;
        
        /**
         * 品牌信息
         */
        private String assetBrand;
        
        /**
         * 资产价值
         */
        private Double assetValue;
        
        /**
         * 使用部门
         */
        private String deptName;
        
        /**
         * 管理人员
         */
        private String managerName;
        
        /**
         * 购置日期
         */
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private Date purchaseDate;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 差异信息
     */
    private DifferenceInfo differenceInfo;

    /**
     * 差异信息内部类
     */
    @Data
    public static class DifferenceInfo implements Serializable {
        
        /**
         * 是否存在差异
         */
        private Boolean hasDifference;
        
        /**
         * 差异类型
         */
        private Integer diffType;
        
        /**
         * 差异类型描述
         */
        private String diffTypeDesc;
        
        /**
         * 差异原因
         */
        private String diffReason;
        
        /**
         * 处理状态
         */
        private Integer handleStatus;
        
        /**
         * 处理建议
         */
        private String handleSuggestion;

        private static final long serialVersionUID = 1L;
    }

    private static final long serialVersionUID = 1L;
}
