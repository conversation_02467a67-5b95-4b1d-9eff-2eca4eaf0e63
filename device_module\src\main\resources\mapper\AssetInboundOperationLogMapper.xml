<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.asset_inbound.mapper.AssetInboundOperationLogMapper">

    <resultMap id="BaseResultMap" type="com.jingfang.asset_inbound.module.entity.AssetInboundOperationLog">
            <id property="logId" column="log_id" />
            <result property="inboundId" column="inbound_id" />
            <result property="operationType" column="operation_type" />
            <result property="operationContent" column="operation_content" />
            <result property="operationTime" column="operation_time" />
            <result property="operatorId" column="operator_id" />
    </resultMap>

    <sql id="Base_Column_List">
        log_id,inbound_id,operation_type,operation_content,operation_time,operator_id
    </sql>
</mapper>
