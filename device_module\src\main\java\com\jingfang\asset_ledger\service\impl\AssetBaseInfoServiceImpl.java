package com.jingfang.asset_ledger.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingfang.asset_ledger.module.entity.AssetBaseInfo;
import com.jingfang.asset_ledger.module.request.AssetSearchRequest;
import com.jingfang.asset_ledger.module.vo.AssetBaseInfoVo;
import com.jingfang.asset_ledger.module.vo.AssetDetailBaseInfoVo;
import com.jingfang.asset_ledger.service.AssetBaseInfoService;
import com.jingfang.asset_ledger.mapper.AssetBaseInfoMapper;
import com.jingfang.common.utils.BusinessCodeGenerator;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Optional;

/**
* <AUTHOR>
* @description 针对表【asset_ledger】的数据库操作Service实现
* @createDate 2025-04-16 16:41:33
*/
@Service
public class AssetBaseInfoServiceImpl extends ServiceImpl<AssetBaseInfoMapper, AssetBaseInfo>
    implements AssetBaseInfoService {

    @Resource
    private AssetBaseInfoMapper baseInfoMapper;
    
    @Resource
    private BusinessCodeGenerator codeGenerator;

    @Transactional
    @Override
    public void add(AssetBaseInfo baseInfo) {
        baseInfo.setCreateTime(new Date());
        // 使用BusinessCodeGenerator生成资产编号，前缀为ZC
        baseInfo.setAssetId(codeGenerator.generateCode("ZC"));
        this.save(baseInfo);
        baseInfoMapper.saveManagerIds(baseInfo.getAssetId(),baseInfo.getManagerIds());
        if (Optional.ofNullable(baseInfo.getAttachmentList()).isPresent()) {
            baseInfoMapper.saveAttachmentList(baseInfo.getAssetId(),baseInfo.getAttachmentList());
        }
        if (Optional.ofNullable(baseInfo.getPictureUrls()).isPresent()) {
            baseInfoMapper.savePictures(baseInfo.getAssetId(),baseInfo.getPictureUrls());
        }
    }

    @Override
    public void edit(AssetBaseInfo baseInfo) {
        baseInfo.setUpdateTime(new Date());
        this.saveOrUpdate(baseInfo);

        //删除旧管理人员信息 更新新信息
        baseInfoMapper.deleteManagerIds(baseInfo.getAssetId());
        baseInfoMapper.saveManagerIds(baseInfo.getAssetId(),baseInfo.getManagerIds());

        //删除旧附件关联信息 更新新信息
        baseInfoMapper.deleteAttachmentList(baseInfo.getAssetId());
        if (Optional.ofNullable(baseInfo.getAttachmentList()).isPresent()) {
            baseInfoMapper.saveAttachmentList(baseInfo.getAssetId(),baseInfo.getAttachmentList());
        }

        //删除旧图片关联信息 更新新信息
        baseInfoMapper.deletePictureList(baseInfo.getAssetId());
        if (Optional.ofNullable(baseInfo.getPictureUrls()).isPresent()) {
            baseInfoMapper.savePictures(baseInfo.getAssetId(),baseInfo.getPictureUrls());
        }

    }

    @Override
    public IPage<AssetBaseInfoVo> selectAssetList(AssetSearchRequest request) {
        IPage<AssetBaseInfoVo> page = new Page<>(request.getPageNum(), request.getPageSize());
        return baseInfoMapper.selectAssetList(page,request);
    }
    
    /**
     * 重载方法，支持传入自定义Page对象
     */
    public IPage<AssetBaseInfoVo> selectAssetList(AssetSearchRequest request, IPage<AssetBaseInfoVo> page) {
        return baseInfoMapper.selectAssetList(page, request);
    }

    @Override
    public AssetDetailBaseInfoVo selectBaseInfoById(String assetId) {
        AssetDetailBaseInfoVo vo = baseInfoMapper.selectBaseInfoById(assetId);
        vo.setManagers(baseInfoMapper.selectManagersByAssetId(assetId));
        vo.setPictureUrls(baseInfoMapper.selectPictureUrlsByAssetId(assetId));
        vo.setAttachmentList(baseInfoMapper.selectAttachmentByAssetId(assetId));

        return vo;
    }


    @Override
    public int deleteBaseInfo(String assetId) {
        return baseInfoMapper.deleteBaseInfo(assetId);
    }
}




