package com.jingfang.maintenance_task.module.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jingfang.maintenance_task.module.dto.MaintenanceTaskDto;
import com.jingfang.maintenance_task.module.entity.MaintenanceTask;
import com.jingfang.maintenance_task.module.request.MaintenanceTaskSearchRequest;
import com.jingfang.maintenance_task.module.vo.MaintenanceTaskVo;

import java.util.List;
import java.util.Map;

/**
 * 维护任务服务接口
 */
public interface MaintenanceTaskService extends IService<MaintenanceTask> {
    
    /**
     * 分页查询维护任务
     */
    IPage<MaintenanceTaskVo> getTaskPage(MaintenanceTaskSearchRequest request);
    
    /**
     * 根据ID查询维护任务详情
     */
    MaintenanceTaskVo getTaskById(String taskId);
    
    /**
     * 新增维护任务
     */
    String addTask(MaintenanceTaskDto taskDto);
    
    /**
     * 修改维护任务
     */
    boolean updateTask(MaintenanceTaskDto taskDto);
    
    /**
     * 删除维护任务
     */
    boolean deleteTask(String taskId);
    
    /**
     * 批量删除维护任务
     */
    boolean deleteTasks(List<String> taskIds);
    
    /**
     * 开始执行任务
     */
    boolean startTask(String taskId);
    
    /**
     * 保存为草稿
     */
    boolean saveDraft(MaintenanceTaskDto taskDto);
    
    /**
     * 提交任务结果
     */
    boolean submitTask(MaintenanceTaskDto taskDto);
    
    /**
     * 审核任务
     */
    boolean reviewTask(String taskId, Integer reviewResult, String reviewComment);
    
    /**
     * 委派任务
     */
    boolean delegateTask(String taskId, Integer responsibleType, Long responsibleId, String delegateReason);
    
    /**
     * 取消任务
     */
    boolean cancelTask(String taskId, String cancelReason);
    
    /**
     * 查询我的任务
     */
    List<MaintenanceTaskVo> getMyTasks(List<Integer> statusList);
    
    /**
     * 查询待审核任务
     */
    List<MaintenanceTaskVo> getPendingReviewTasks();
    
    /**
     * 查询即将到期的任务
     */
    List<MaintenanceTaskVo> getUpcomingTasks(Integer days);
    
    /**
     * 查询已逾期的任务
     */
    List<MaintenanceTaskVo> getOverdueTasks();
    
    /**
     * 根据维护计划ID查询任务列表
     */
    List<MaintenanceTaskVo> getTasksByPlanId(String planId);
    
    /**
     * 根据资产ID查询任务列表
     */
    List<MaintenanceTaskVo> getTasksByAssetId(String assetId);
    
    /**
     * 根据资产ID统计任务数量
     */
    Map<String, Integer> getTaskStatisticsByAssetId(String assetId);
    
    /**
     * 统计任务数量
     */
    Map<String, Integer> getTaskStatistics();
    
    /**
     * 生成维护任务（定时任务调用）
     */
    void generateMaintenanceTasks();
    
    /**
     * 根据维护计划生成任务
     */
    boolean generateTaskFromPlan(String planId);
} 