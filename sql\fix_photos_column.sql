-- 修复盘点明细表缺少photos字段的问题
-- 执行时间：2025年7月11日
-- 问题：item_stocktaking_detail表缺少photos字段，导致更新盘点明细接口失败

-- 检查表结构
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'item_stocktaking_detail'
ORDER BY ORDINAL_POSITION;

-- 添加photos字段
ALTER TABLE item_stocktaking_detail 
ADD COLUMN photos TEXT COMMENT '照片URL列表(JSON格式)' AFTER update_time;

-- 验证字段添加成功
DESCRIBE item_stocktaking_detail;

-- 可选：为现有记录设置默认值
UPDATE item_stocktaking_detail 
SET photos = '[]' 
WHERE photos IS NULL;

-- 验证修复结果
SELECT detail_id, photos 
FROM item_stocktaking_detail 
LIMIT 5;
