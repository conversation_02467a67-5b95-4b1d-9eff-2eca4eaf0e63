# 微信小程序库存盘点接口测试说明

## 概述

本文档说明如何使用PowerShell脚本对微信小程序库存盘点相关接口进行自动化测试。

## 测试文件说明

### 1. 测试用例文件
- `微信小程序库存盘点接口测试用例.json` - 包含所有接口的测试用例和预期响应

### 2. 测试脚本文件
- `test-stocktaking-api.ps1` - 完整的测试脚本，包含详细报告生成
- `quick-test-stocktaking.ps1` - 快速测试脚本，适合开发调试
- `advanced-test-stocktaking.ps1` - 高级测试脚本，支持配置文件和数据验证
- `test-config.json` - 测试配置文件

## 使用方法

### 1. 快速测试

```powershell
# 使用默认参数
.\quick-test-stocktaking.ps1

# 指定服务器地址和Token
.\quick-test-stocktaking.ps1 -BaseUrl "http://*************:8080" -Token "your_actual_token"
```

### 2. 完整测试

```powershell
# 使用默认参数
.\test-stocktaking-api.ps1

# 指定参数
.\test-stocktaking-api.ps1 -BaseUrl "http://*************:8080" -Token "your_actual_token"
```

### 3. 高级测试（推荐）

```powershell
# 使用默认配置文件
.\advanced-test-stocktaking.ps1

# 使用自定义配置文件
.\advanced-test-stocktaking.ps1 -ConfigFile "my-test-config.json"
```

## 配置文件说明

### test-config.json 配置项

```json
{
  "testConfig": {
    "baseUrl": "http://localhost:8080",           // 服务器地址
    "token": "your_jwt_token_here",               // JWT认证Token
    "timeout": 30,                                // 请求超时时间（秒）
    "retryCount": 3,                              // 重试次数
    "testData": {                                 // 测试数据
      "testItemCode": "TEST001",                  // 测试物品条码
      "testStocktakingId": "test_stocktaking_id", // 测试盘点单ID
      "testItemId": "test_item_id",               // 测试物品ID
      "testDetailId": "test_detail_id",           // 测试明细ID
      "testWarehouseId": 1                        // 测试仓库ID
    }
  }
}
```

## 测试接口列表

### P0级别接口（核心功能）
1. **获取我的盘点任务列表** - `GET /item/stocktaking/my-tasks`
2. **根据物品条码查询物品信息** - `GET /item/by-code/{itemCode}`
3. **根据物品信息查找盘点明细** - `GET /item/stocktaking/detail/by-item`
4. **更新盘点明细** - `PUT /item/stocktaking/detail/{detailId}`

### P1级别接口（重要功能）
5. **获取盘点进度** - `GET /item/stocktaking/{stocktakingId}/progress`
6. **获取个人盘点进度** - `GET /item/stocktaking/my-progress`

### P2级别接口（辅助功能）
7. **获取个人盘点记录** - `GET /item/stocktaking/my-records`
8. **获取物品盘点历史** - `GET /item/stocktaking/item-history/{itemId}`

### 错误场景测试
- 物品条码不存在
- 盘点明细不存在
- 更新不存在的明细

## 测试报告

### 报告文件类型
1. **JSON报告** - 包含详细的测试结果数据
2. **HTML报告** - 可视化的测试报告（仅完整测试脚本生成）

### 报告内容
- 测试摘要（总数、通过数、失败数、通过率）
- 详细测试结果
- 响应数据验证结果
- 错误信息和调试信息

## 准备工作

### 1. 环境要求
- Windows PowerShell 5.1 或 PowerShell Core 6.0+
- 网络连接到后端服务器

### 2. 认证Token获取
```powershell
# 登录获取Token的示例
$loginData = @{
    username = "your_username"
    password = "your_password"
}

$response = Invoke-RestMethod -Uri "http://localhost:8080/login" -Method POST -Body ($loginData | ConvertTo-Json) -ContentType "application/json"
$token = $response.token
```

### 3. 测试数据准备
在执行测试前，确保系统中存在以下测试数据：
- 物品条码：TEST001
- 至少一个进行中的盘点计划
- 分配给当前用户的盘点任务

## 常见问题

### 1. 认证失败
- 检查Token是否正确
- 确认Token未过期
- 验证用户权限

### 2. 连接失败
- 检查服务器地址是否正确
- 确认服务器是否启动
- 验证网络连接

### 3. 测试数据不存在
- 创建测试物品（条码：TEST001）
- 创建盘点计划并分配任务
- 确保数据库中有相关记录

## 扩展测试

### 1. 性能测试
```powershell
# 并发测试示例
1..10 | ForEach-Object -Parallel {
    .\quick-test-stocktaking.ps1 -BaseUrl $using:BaseUrl -Token $using:Token
} -ThrottleLimit 5
```

### 2. 压力测试
```powershell
# 循环测试
for ($i = 1; $i -le 100; $i++) {
    Write-Host "第 $i 轮测试"
    .\quick-test-stocktaking.ps1
    Start-Sleep -Seconds 1
}
```

## 注意事项

1. **测试环境隔离** - 建议在测试环境而非生产环境执行
2. **数据备份** - 测试前备份重要数据
3. **权限控制** - 确保测试用户具有相应的接口访问权限
4. **日志监控** - 测试期间监控服务器日志
5. **清理工作** - 测试完成后清理测试数据

## 联系支持

如有问题，请联系开发团队或查看相关文档：
- 后端接口文档：`微信小程序库存盘点后端接口开发需求文档.md`
- 功能实现总结：`微信小程序库存盘点后端功能实现总结.md`
