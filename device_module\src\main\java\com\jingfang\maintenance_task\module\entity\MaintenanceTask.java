package com.jingfang.maintenance_task.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 维护任务表
 * @TableName maintenance_task
 */
@TableName(value = "maintenance_task")
@Data
public class MaintenanceTask implements Serializable {
    
    /**
     * 维护任务ID
     */
    @TableId(type = IdType.INPUT)
    private String taskId;
    
    /**
     * 维护计划ID
     */
    private String planId;
    
    /**
     * 任务标题
     */
    private String taskTitle;
    
    /**
     * 资产ID
     */
    private String assetId;
    
    /**
     * 维护事项描述
     */
    private String maintenanceItems;
    
    /**
     * 计划执行时间
     */
    private Date scheduledTime;
    
    /**
     * 实际开始时间
     */
    private Date actualStartTime;
    
    /**
     * 实际完成时间
     */
    private Date actualEndTime;
    
    /**
     * 负责人类型(1-个人, 2-部门)
     */
    private Integer responsibleType;
    
    /**
     * 负责人ID（用户ID或部门ID）
     */
    private Long responsibleId;
    
    /**
     * 实际执行人ID
     */
    private Long executorId;
    
    /**
     * 任务状态(1-待执行, 2-执行中, 3-草稿, 4-待审核, 5-审核通过, 6-审核不通过, 7-已完成, 8-已取消)
     */
    private Integer status;
    
    /**
     * 任务优先级(1-低, 2-中, 3-高, 4-紧急)
     */
    private Integer priority;
    
    /**
     * 检查结果
     */
    private String checkResult;
    
    /**
     * 维护结果描述
     */
    private String resultDescription;
    
    /**
     * 问题描述
     */
    private String problemDescription;
    
    /**
     * 解决方案
     */
    private String solution;
    
    /**
     * 审核人ID
     */
    private Long reviewerId;
    
    /**
     * 审核时间
     */
    private Date reviewTime;
    
    /**
     * 审核意见
     */
    private String reviewComment;
    
    /**
     * 委派原因
     */
    private String delegateReason;
    
    /**
     * 委派人ID
     */
    private Long delegateBy;
    
    /**
     * 委派时间
     */
    private Date delegateTime;
    
    /**
     * 备注说明
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 是否删除(0-否, 1-是)
     */
    private Integer deleted;
    
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
} 