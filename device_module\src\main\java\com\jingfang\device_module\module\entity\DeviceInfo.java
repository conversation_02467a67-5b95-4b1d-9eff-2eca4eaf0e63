package com.jingfang.device_module.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.jingfang.common.enums.DeviceType;
import lombok.Data;

/**
 * 
 * @TableName device_info
 */
@TableName(value ="device_info")
@Data
public class DeviceInfo implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String deviceName;

    private String icon;

    private int deviceType;

    private String deviceModel;

    private String ipAddress;

    private int devicePort;

    private int deviceStatus;

    private String location;

    private String manufacturer;

    private String serialNumber;

    private String installationDate;

    private Date createdAt;

    private Date updatedAt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;


}