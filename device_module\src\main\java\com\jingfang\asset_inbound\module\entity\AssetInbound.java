package com.jingfang.asset_inbound.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 资产入库单主表
 * @TableName asset_inbound
 */
@TableName(value ="asset_inbound")
@Data
public class AssetInbound implements Serializable {
    /**
     * 入库单ID
     */
    @TableId(type = IdType.AUTO)
    private String inboundId;

    /**
     * 业务日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date businessDate;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 存放位置
     */
    private Integer storageLocation;

    /**
     * 状态(1:草稿,2:待确认,3:待审核,4:已通过,5:已退回)
     */
    private Integer status;
    
    /**
     * 制单人ID
     */
    private Long creatorId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 经手人ID
     */
    private Long handlerId;
    
    /**
     * 经手确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date handleTime;
    
    /**
     * 经手人备注
     */
    private String handleRemark;
    
    /**
     * 审核人ID
     */
    private Long auditorId;
    
    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditTime;
    
    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 入库说明
     */
    private String inboundDescription;

    /**
     * 删除标志(0代表存在,1代表删除)
     */
    private String delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}