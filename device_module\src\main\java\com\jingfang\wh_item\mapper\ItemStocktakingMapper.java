package com.jingfang.wh_item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.wh_item.module.entity.ItemStocktaking;
import com.jingfang.wh_item.module.request.ItemStocktakingSearchRequest;
import com.jingfang.wh_item.module.vo.ItemStocktakingVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 库存盘点Mapper接口
 */
@Mapper
public interface ItemStocktakingMapper extends BaseMapper<ItemStocktaking> {
    
    /**
     * 查询盘点列表
     */
    IPage<ItemStocktakingVo> selectStocktakingList(IPage<ItemStocktakingVo> page, @Param("request") ItemStocktakingSearchRequest request);
    
    /**
     * 获取盘点详情
     */
    ItemStocktakingVo selectStocktakingDetail(@Param("stocktakingId") String stocktakingId);
    
    /**
     * 获取下一个盘点单号
     */
    String getNextStocktakingCode();
} 