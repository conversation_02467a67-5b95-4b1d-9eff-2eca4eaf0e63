package com.jingfang.device_module.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 
 * @TableName device_param
 */
@TableName(value ="device_param")
@Data
public class DeviceParam implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long paramId;

    private Long deviceId;

    /**
     * 参数名
     */
    private String paramName;

    /**
     * 参数单位
     */
    private String paramUnit;

    /**
     * 正常运行范围下限
     */
    private Double rangeStart;

    /**
     * 正常运行范围上限
     */
    private Double rangeEnd;

    /**
     * 删除标志
     */
    private Integer delFlag;

    /**
     * 读写类型（0-只读，1-只写，2-读写）
     */
    private Integer rwType;

    /**
     * 参数类型（0-一般参数，1-告警参数）
     */
    private Integer paramType;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DeviceParam other = (DeviceParam) that;
        return (this.getParamId() == null ? other.getParamId() == null : this.getParamId().equals(other.getParamId()))
            && (this.getDeviceId() == null ? other.getDeviceId() == null : this.getDeviceId().equals(other.getDeviceId()))
            && (this.getParamName() == null ? other.getParamName() == null : this.getParamName().equals(other.getParamName()))
            && (this.getParamUnit() == null ? other.getParamUnit() == null : this.getParamUnit().equals(other.getParamUnit()))
            && (this.getRangeStart() == null ? other.getRangeStart() == null : this.getRangeStart().equals(other.getRangeStart()))
            && (this.getRangeEnd() == null ? other.getRangeEnd() == null : this.getRangeEnd().equals(other.getRangeEnd()))
            && (this.getDelFlag() == null ? other.getDelFlag() == null : this.getDelFlag().equals(other.getDelFlag()))
            && (this.getRwType() == null ? other.getRwType() == null : this.getRwType().equals(other.getRwType()))
            && (this.getParamType() == null ? other.getParamType() == null : this.getParamType().equals(other.getParamType()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getParamId() == null) ? 0 : getParamId().hashCode());
        result = prime * result + ((getDeviceId() == null) ? 0 : getDeviceId().hashCode());
        result = prime * result + ((getParamName() == null) ? 0 : getParamName().hashCode());
        result = prime * result + ((getParamUnit() == null) ? 0 : getParamUnit().hashCode());
        result = prime * result + ((getRangeStart() == null) ? 0 : getRangeStart().hashCode());
        result = prime * result + ((getRangeEnd() == null) ? 0 : getRangeEnd().hashCode());
        result = prime * result + ((getDelFlag() == null) ? 0 : getDelFlag().hashCode());
        result = prime * result + ((getRwType() == null) ? 0 : getRwType().hashCode());
        result = prime * result + ((getParamType() == null) ? 0 : getParamType().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", paramId=").append(paramId);
        sb.append(", deviceId=").append(deviceId);
        sb.append(", paramName=").append(paramName);
        sb.append(", paramUnit=").append(paramUnit);
        sb.append(", rangeStart=").append(rangeStart);
        sb.append(", rangeEnd=").append(rangeEnd);
        sb.append(", delFlag=").append(delFlag);
        sb.append(", rwType=").append(rwType);
        sb.append(", paramType=").append(paramType);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}