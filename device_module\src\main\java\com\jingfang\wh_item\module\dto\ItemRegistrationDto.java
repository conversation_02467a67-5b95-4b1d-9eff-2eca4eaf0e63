package com.jingfang.wh_item.module.dto;

import com.jingfang.wh_item.module.entity.ItemBaseInfo;
import com.jingfang.wh_item.module.entity.ItemConsumableAttr;
import com.jingfang.wh_item.module.entity.ItemPartAttr;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 物品登记DTO，用于物品基本信息登记（不包含库存信息）
 */
@Data
public class ItemRegistrationDto implements Serializable {
    
    /**
     * 物品基本信息
     */
    private ItemBaseInfo baseInfo;
    
    /**
     * 企业级安全库存
     */
    private BigDecimal safetyStock;
    
    /**
     * 消耗品属性（当itemType=1时有效）
     */
    private ItemConsumableAttr consumableAttr;
    
    /**
     * 备品备件属性（当itemType=2时有效）
     */
    private ItemPartAttr partAttr;
    
    private static final long serialVersionUID = 1L;
} 