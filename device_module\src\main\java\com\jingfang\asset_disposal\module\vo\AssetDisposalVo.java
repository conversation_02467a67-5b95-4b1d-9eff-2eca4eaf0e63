package com.jingfang.asset_disposal.module.vo;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 资产处置VO
 */
@Data
public class AssetDisposalVo {
    
    /**
     * 处置单ID
     */
    private String disposalId;
    
    /**
     * 处置申请标题
     */
    private String disposalTitle;
    
    /**
     * 资产ID
     */
    private String assetId;
    
    /**
     * 资产名称
     */
    private String assetName;
    
    /**
     * 处置类型(1-报废, 2-调拨, 3-出售, 4-其他)
     */
    private Integer disposalType;
    
    /**
     * 处置类型名称
     */
    private String disposalTypeName;
    
    /**
     * 处置原因
     */
    private String disposalReason;
    
    /**
     * 申请人ID
     */
    private Long applicantId;
    
    /**
     * 申请人姓名
     */
    private String applicantName;
    
    /**
     * 申请时间
     */
    private Date applyTime;
    
    /**
     * 预期处置时间
     */
    private Date expectedDisposalTime;
    
    /**
     * 当前资产价值
     */
    private BigDecimal currentValue;
    
    /**
     * 处置价值
     */
    private BigDecimal disposalValue;
    
    /**
     * 买方信息（出售时使用）
     */
    private String buyerInfo;
    
    /**
     * 调拨目标部门ID（调拨时使用）
     */
    private Long transferDeptId;
    
    /**
     * 调拨目标部门名称
     */
    private String transferDeptName;
    
    /**
     * 状态(1-草稿, 2-待审核, 3-审核中, 4-审核通过, 5-审核拒绝, 6-处置中, 7-已完成, 8-已取消)
     */
    private Integer status;
    
    /**
     * 状态名称
     */
    private String statusName;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 创建人
     */
    private String createBy;
} 