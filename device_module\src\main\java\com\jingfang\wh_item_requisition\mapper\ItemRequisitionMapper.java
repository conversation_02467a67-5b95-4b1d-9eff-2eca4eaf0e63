package com.jingfang.wh_item_requisition.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jingfang.wh_item_requisition.module.entity.ItemRequisition;
import com.jingfang.wh_item_requisition.module.request.ItemRequisitionSearchRequest;
import com.jingfang.wh_item_requisition.module.vo.ItemRequisitionDetailVo;
import com.jingfang.wh_item_requisition.module.vo.ItemRequisitionVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 物品领用单Mapper接口
 */
@Mapper
public interface ItemRequisitionMapper extends BaseMapper<ItemRequisition> {
    
    /**
     * 分页查询领用单列表
     *
     * @param page 分页参数
     * @param request 查询条件
     * @return 分页结果
     */
    IPage<ItemRequisitionVo> selectRequisitionList(Page<ItemRequisitionVo> page, @Param("request") ItemRequisitionSearchRequest request);
    
    /**
     * 查询领用单详情
     *
     * @param requisitionId 领用单ID
     * @return 领用单详情
     */
    ItemRequisitionDetailVo selectRequisitionDetail(@Param("requisitionId") String requisitionId);
} 