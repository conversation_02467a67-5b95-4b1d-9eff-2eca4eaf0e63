package com.jingfang.wh_item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jingfang.wh_item.module.entity.ItemOutboundDetail;
import com.jingfang.wh_item.module.vo.ItemOutboundDetailVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ItemOutboundDetailMapper extends BaseMapper<ItemOutboundDetail> {

    /**
     * 根据出库单ID查询明细列表（包含物品基本信息）
     */
    List<ItemOutboundDetailVo> selectDetailsByOutboundId(@Param("outboundId") String outboundId);
} 