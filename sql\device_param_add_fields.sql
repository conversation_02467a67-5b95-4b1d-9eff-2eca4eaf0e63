-- ========================================
-- 设备参数表新增字段脚本
-- 新增 rw_type 和 param_type 字段
-- ========================================

-- 添加读写类型字段
ALTER TABLE device_param 
ADD COLUMN IF NOT EXISTS `rw_type` tinyint(1) DEFAULT 0 COMMENT '读写类型 0-只读 1-只写 2-读写' AFTER `del_flag`;

-- 添加参数类型字段
ALTER TABLE device_param 
ADD COLUMN IF NOT EXISTS `param_type` tinyint(1) DEFAULT 0 COMMENT '参数类型 0-一般参数 1-告警参数' AFTER `rw_type`;

-- 为新字段添加索引以优化查询性能
ALTER TABLE device_param ADD INDEX IF NOT EXISTS `idx_param_type` (`param_type`);
ALTER TABLE device_param ADD INDEX IF NOT EXISTS `idx_rw_type` (`rw_type`);

-- 更新现有数据的默认值
UPDATE device_param SET 
  `rw_type` = 0,     -- 默认设为只读
  `param_type` = 0   -- 默认设为一般参数
WHERE `rw_type` IS NULL OR `param_type` IS NULL;

-- 验证字段添加结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'device_param' 
  AND COLUMN_NAME IN ('rw_type', 'param_type')
ORDER BY ORDINAL_POSITION;

-- 显示表结构
DESCRIBE device_param;
