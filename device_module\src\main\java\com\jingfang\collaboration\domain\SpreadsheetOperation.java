package com.jingfang.collaboration.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 表格操作记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("collaboration_spreadsheet_operation")
public class SpreadsheetOperation {
    
    /**
     * 操作记录ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    
    /**
     * 表格ID
     */
    private String spreadsheetId;
    
    /**
     * 操作用户ID
     */
    private Long userId;
    
    /**
     * 操作用户名
     */
    private String userName;
    
    /**
     * 操作用户昵称
     */
    private String nickName;
    
    /**
     * 操作类型（create:创建 edit:编辑 delete:删除 share:分享 invite:邀请 comment:评论）
     */
    private String operationType;
    
    /**
     * 操作描述
     */
    private String operationDesc;
    
    /**
     * 操作详情（JSON格式存储具体操作内容）
     */
    private String operationDetail;
    
    /**
     * 操作位置（如单元格位置）
     */
    private String operationPosition;
    
    /**
     * 操作前数据
     */
    private String beforeData;
    
    /**
     * 操作后数据
     */
    private String afterData;
    
    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operationTime;
    
    /**
     * 客户端IP
     */
    private String clientIp;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 备注
     */
    private String remark;
}
