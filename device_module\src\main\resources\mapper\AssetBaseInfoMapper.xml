<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.asset_ledger.mapper.AssetBaseInfoMapper">



    <insert id="saveManagerIds">
        insert into asset_manager (asset_id,manager_id)
        values 
            <foreach collection="managerIds" item="userId" separator=",">
                (#{assetId},#{userId})
            </foreach>
    </insert>

    <insert id="saveAttachmentList">
        insert into asset_attachment (asset_id, file_name, file_type, storage_path)
        values
            <foreach collection="attachments" item="attachment" separator=",">
                (#{assetId},#{attachment.fileName},#{attachment.fileType},#{attachment.storagePath})
            </foreach>
    </insert>

    <insert id="savePictures">
        insert into asset_picture (asset_id, storage_path)
        values
            <foreach collection="urls" item="url" separator=",">
                (#{assetId},#{url})
            </foreach>
    </insert>

    <delete id="deleteManagerIds">
        delete
        from asset_manager
        where asset_id = #{assetId}
    </delete>

    <delete id="deleteAttachmentList">
        delete
        from asset_attachment
        where asset_id = #{assetId}
    </delete>

    <delete id="deletePictureList">
        delete
        from asset_picture
        where asset_id = #{assetId}
    </delete>

    <update id="deleteBaseInfo">
        UPDATE asset_ledger
        set del_flag = 1
        WHERE asset_id = #{assetId}
    </update>


    <select id="selectAssetList" resultType="com.jingfang.asset_ledger.module.vo.AssetBaseInfoVo">
        SELECT t1.asset_id,t1.confirm_status,t1.asset_status,t1.category_id,t1.asset_name,t1.spec_model,t2.dept_name,t1.create_time,t1.update_time
        FROM asset_ledger as t1
                 LEFT JOIN sys_dept as t2 on t1.dept_id = t2.dept_id
        where t1.del_flag = 0
        <if test="request.confirmStatus != null">
            and t1.confirm_status = #{request.confirmStatus}
        </if>
        <if test="request.assetId != null and request.assetId != ''">
            and t1.asset_id = #{request.assetId}
        </if>
        <if test="request.specModel != null and request.specModel !=''">
            and t1.spec_model = #{request.specModel}
        </if>
        <if test="request.assetName != null and request.assetName !=''">
            and t1.asset_name like concat("%",#{request.assetName},"%")
        </if>
        <if test="request.deptId != null">
            and t1.dept_id = #{request.deptId}
        </if>
        <if test="request.storageLocation != null and request.storageLocation != ''">
            and t1.storage_location like concat("%",#{request.storageLocation},"%")
        </if>
        <if test="request.managerId != null">
            and EXISTS (
                SELECT 1 FROM asset_manager t3 
                WHERE t3.asset_id = t1.asset_id AND t3.manager_id = #{request.managerId}
            )
        </if>
        <if test="request.categoryId != null">
            and t1.category_id = #{request.categoryId}
        </if>
        order by t1.create_time desc, t1.update_time desc
    </select>

    <select id="selectBaseInfoById" resultType="com.jingfang.asset_ledger.module.vo.AssetDetailBaseInfoVo">
        SELECT t1.asset_id,t1.confirm_status,t1.asset_status,t1.category_id,t1.asset_name,t1.spec_model,t1.asset_brand,t1.asset_purpose,t1.storage_location,t1.detail_location,t1.asset_unit,t1.remark,t2.dept_name,t2.dept_id
        FROM asset_ledger as t1
                 LEFT JOIN sys_dept as t2 on t1.dept_id = t2.dept_id
        where t1.del_flag = 0 and t1.asset_id = #{assetId}
    </select>

    <select id="selectManagersByAssetId" resultType="com.jingfang.common.core.domain.entity.UserLite">
        SELECT  t2.user_id,t2.nick_name,t2.user_name
        FROM asset_manager as t1
                 LEFT JOIN sys_user as t2 on t1.manager_id = t2.user_id
        WHERE asset_id = #{assetId}
    </select>

    <select id="selectPictureUrlsByAssetId" resultType="java.lang.String">
        SELECT  storage_path
        FROM asset_picture
        WHERE asset_id = #{assetId}
    </select>

    <select id="selectAttachmentByAssetId" resultType="com.jingfang.asset_ledger.module.dto.AttachmentDto">
        SELECT  file_name,file_type,storage_path
        FROM asset_attachment
        WHERE asset_id = #{assetId}
    </select>

    <!-- 工作台统计查询 -->
    <select id="countTotalAssets" resultType="java.lang.Long">
        SELECT COUNT(1) FROM asset_ledger WHERE del_flag = 0
    </select>

    <select id="sumTotalAssetValue" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(am.purchase_price), 0)
        FROM asset_ledger al
        LEFT JOIN asset_maintenance am ON al.asset_id = am.asset_id
        WHERE al.del_flag = 0
    </select>

    <select id="countMonthlyNewAssets" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM asset_ledger
        WHERE del_flag = 0
        AND create_time >= #{startDate}
        AND create_time &lt; #{endDate}
    </select>

    <select id="sumMonthlyNewAssetValue" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(am.purchase_price), 0)
        FROM asset_ledger al
        LEFT JOIN asset_maintenance am ON al.asset_id = am.asset_id
        WHERE al.del_flag = 0
        AND al.create_time >= #{startDate}
        AND al.create_time &lt; #{endDate}
    </select>

    <select id="countAssetsByStatus" resultType="java.util.Map">
        SELECT
            asset_status as status,
            COUNT(1) as count
        FROM asset_ledger
        WHERE del_flag = 0
        GROUP BY asset_status
    </select>

    <select id="selectAssetTrends" resultType="java.util.Map">
        SELECT
            DATE_FORMAT(create_time, '%Y-%m-%d') as date,
            COUNT(1) as newCount,
            COALESCE(SUM(am.purchase_price), 0) as newValue
        FROM asset_ledger al
        LEFT JOIN asset_maintenance am ON al.asset_id = am.asset_id
        WHERE al.del_flag = 0
        AND al.create_time >= #{startDate}
        AND al.create_time &lt; #{endDate}
        GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d')
        ORDER BY date
    </select>
</mapper>
