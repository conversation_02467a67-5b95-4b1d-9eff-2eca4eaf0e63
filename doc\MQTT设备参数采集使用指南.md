# MQTT设备参数采集使用指南

## 概述

本指南介绍如何使用新的MQTT方式进行设备参数采集。新方式通过MQTT网关获取设备参数，相比原来的Modbus TCP方式更加简单和可靠。

## 系统架构

```
前端页面 → 后端API → MQTT服务 → MQTT网关 → 设备
```

## MQTT通信协议

### 请求格式

**主题**: `/sys/thing/node/property/get/device_monitor_client`

**JSON格式**:
```json
{
  "id": "123",
  "version": "1.0",
  "ack": 0,
  "params": [
    {
      "clientID": "************",
      "properties": [
        {
          "name": "冷却水进水流量"
        },
        {
          "name": "冷却水进水温度"
        }
      ]
    }
  ]
}
```

### 响应格式

**主题**: `/sys/thing/node/property/get_reply/device_monitor_client`

**JSON格式**:
```json
{
  "id": "123",
  "version": "1.0",
  "code": 0,
  "params": [
    {
      "clientID": "************",
      "properties": [
        {
          "name": "冷却水进水流量",
          "value": 0,
          "timestamp": 1751699660
        },
        {
          "name": "冷却水进水温度",
          "value": 27.14,
          "timestamp": 1751699660
        }
      ]
    }
  ]
}
```

## 配置说明

### 1. MQTT服务器配置

在 `application.yml` 中配置MQTT连接信息：

```yaml
mqtt:
  server-uri: tcp://192.168.110.135:10883
  client-id: device_monitor_client
  username: admin
  password: 429498517
  device:
    property:
      request:
        topic: /sys/thing/node/property/get/device_monitor_client
      response:
        topic: /sys/thing/node/property/get_reply/device_monitor_client
      timeout: 15
```

### 2. 设备参数配置

在 `device_param` 表中配置设备参数：

| 字段 | 说明 | 示例 |
|------|------|------|
| device_id | 设备ID | 1 |
| param_name | 参数名称（用于MQTT查询） | 冷却水进水流量 |
| param_unit | 参数单位 | L/min |
| range_start | 正常范围下限 | 0.0 |
| range_end | 正常范围上限 | 100.0 |
| rw_type | 读写类型（0-只读，1-只写，2-读写） | 0 |
| param_type | 参数类型（0-一般参数，1-告警参数，3-控制参数） | 0 |

## API接口

### 1. 获取设备运行参数

**接口**: `GET /device/list/detail/normal`

**参数**:
- `deviceId`: 设备ID

**说明**: 只获取 `param_type = 0` 的一般参数

**响应**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "paramId": 1,
      "paramName": "冷却水进水流量",
      "paramValue": "25.6L/min",
      "rangeStart": 0.0,
      "rangeEnd": 100.0,
      "rwType": 0,
      "paramType": 0
    }
  ],
  "total": 1
}
```

### 2. 获取设备告警信息

**接口**: `GET /device/alert/info`

**参数**:
- `deviceId`: 设备ID

**说明**: 只获取 `param_type = 1` 的告警参数，支持MQTT实时查询

**响应**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "paramId": 2,
      "paramName": "高温告警",
      "paramValue": "否",
      "alertValue": false,
      "rwType": 0,
      "paramType": 1
    }
  ],
  "total": 1
}
```

### 3. 获取设备控制参数

**接口**: `GET /device/control/params`

**参数**:
- `deviceId`: 设备ID

**说明**: 只获取 `param_type = 3` 的控制参数，根据 `rw_type` 决定是否查询实时值

**响应**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "paramId": 3,
      "paramName": "温度设定值",
      "paramValue": "25.0°C",
      "rangeStart": 10.0,
      "rangeEnd": 40.0,
      "rwType": 2,
      "paramType": 3,
      "alertValue": false
    }
  ],
  "total": 1
}
```

### 3. 测试接口

#### 测试MQTT连接
```http
GET /device/property/test/mqtt-connection
```

#### 测试设备属性查询
```http
GET /device/property/test/mqtt?deviceIp=************&properties=冷却水进水流量,冷却水进水温度
```

#### 测试设备运行参数
```http
GET /device/property/test/normal-params?deviceId=1
```

#### 测试设备告警信息
```http
GET /device/alert/info?deviceId=1
```

#### 测试设备控制参数
```http
GET /device/control/params?deviceId=1
```

#### 测试设备参数写入
```http
POST /device/param/write
参数: deviceId=1&paramName=温度设定值&paramValue=25.0
```

#### 测试批量参数写入
```http
POST /device/params/write?deviceId=1
请求体: {"温度设定值": "25.0", "工作模式": "1"}
```

#### 测试MQTT直接写入
```http
POST /device/property/test/write
参数: deviceIp=************&propertyName=阀自动模式下调节步长&propertyValue=6
```

## 使用流程

### 1. 配置设备参数

1. 在设备管理页面添加设备
2. 为设备配置参数，确保 `param_name` 字段与MQTT网关中的属性名称一致
3. 设置参数的数据类型、单位和正常范围

### 2. 查看实时数据

1. 进入设备详情页面
2. 切换到"运行数据"标签页
3. 系统会自动通过MQTT查询设备参数
4. 数据每5秒自动刷新

### 3. 数据处理流程

1. 前端请求设备运行参数
2. 后端查询设备配置的参数列表
3. 通过MQTT向网关发送属性查询请求
4. 网关返回设备属性值
5. 后端格式化数据并返回给前端
6. 前端展示实时参数数据

## 故障排查

### 1. MQTT连接问题

**症状**: 无法获取设备参数，日志显示MQTT连接失败

**解决方法**:
1. 检查MQTT服务器地址和端口
2. 验证用户名和密码
3. 确认网络连通性
4. 使用测试接口验证连接状态

### 2. 参数名称不匹配

**症状**: 设备在线但获取不到参数值

**解决方法**:
1. 检查 `device_param` 表中的 `param_name` 字段
2. 确保参数名称与MQTT网关配置一致
3. 使用MQTT客户端工具验证网关响应

### 3. 数据格式错误

**症状**: 获取到参数值但显示异常

**解决方法**:
1. 检查参数的 `data_type` 配置
2. 验证参数单位设置
3. 查看后端日志中的数据格式化过程

### 4. 超时问题

**症状**: 请求超时，无法获取参数

**解决方法**:
1. 增加超时时间配置
2. 检查网关响应速度
3. 减少单次查询的参数数量

## 性能优化

### 1. 批量查询

系统支持一次查询多个参数，减少MQTT通信次数：

```java
// 一次查询多个参数
List<String> propertyNames = Arrays.asList("温度", "湿度", "压力");
mqttDevicePropertyService.queryDeviceProperties(deviceIp, propertyNames);
```

### 2. 缓存机制

- 设备在线状态缓存在Redis中
- 设备基础信息缓存在Redis中
- 减少数据库查询次数

### 3. 异步处理

- MQTT查询使用异步方式
- 支持超时控制
- 避免阻塞主线程

## 字段说明

### 读写类型 (rw_type)

- **0 - 只读**: 参数只能读取，不能写入（如传感器数据）
- **1 - 只写**: 参数只能写入，不能读取（如控制指令）
- **2 - 读写**: 参数既可以读取也可以写入（如设定值）

### 参数类型 (param_type)

- **0 - 一般参数**: 正常的运行参数，用于监控设备状态
- **1 - 告警参数**: 告警相关的参数，用于故障诊断
- **3 - 控制参数**: 控制相关的参数，用于设备控制和配置

## 注意事项

1. **参数名称**: 必须与MQTT网关中的属性名称完全一致
2. **设备IP**: 使用设备的实际IP地址作为clientID
3. **读写类型**: 根据参数的实际用途正确配置读写类型
4. **参数类型**: 正确区分一般参数和告警参数，便于分类管理
5. **网络稳定**: 确保MQTT网络连接稳定
6. **并发控制**: 避免同时发起过多MQTT查询请求

## 参数写入功能

### 1. MQTT写入协议

**写入主题**: `/sys/thing/node/property/set/device_monitor_client`

**请求格式**:
```json
{
  "id": "0",
  "version": "1.0",
  "ack": 0,
  "params": [
    {
      "clientID": "************",
      "properties": [
        {
          "name": "阀自动模式下调节步长",
          "value": "6"
        }
      ]
    }
  ]
}
```

### 2. 写入接口

- **单个参数写入**: `POST /device/param/write`
- **批量参数写入**: `POST /device/params/write`

### 3. 参数验证

- 设备必须在线
- 参数必须可写（`rw_type = 1` 或 `rw_type = 2`）
- 参数值必须在有效范围内

## 扩展功能

### 1. 告警参数

已支持告警参数的MQTT查询，使用 `param_type = 1` 进行分类。

### 2. 参数写入

已实现通过MQTT向设备写入参数值的功能，支持单个和批量写入。

### 3. 历史数据

可以结合时间序列数据库存储历史参数数据。

通过以上配置和使用，您就可以成功使用MQTT方式进行设备参数采集和控制了！
