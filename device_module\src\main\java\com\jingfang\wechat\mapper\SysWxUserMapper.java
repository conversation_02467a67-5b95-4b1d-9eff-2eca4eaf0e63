package com.jingfang.wechat.mapper;


import com.jingfang.wechat.module.entity.SysWxUser;

/**
 * 微信用户 数据层
 */
public interface SysWxUserMapper {
    /**
     * 查询微信用户信息
     *
     * @param wxUserId 微信用户ID
     * @return 微信用户信息
     */
    public SysWxUser selectWxUserById(Long wxUserId);

    /**
     * 通过openid查询微信用户
     *
     * @param openid 微信openid
     * @return 微信用户对象信息
     */
    public SysWxUser selectWxUserByOpenid(String openid);

    /**
     * 新增微信用户信息
     *
     * @param wxUser 微信用户信息
     * @return 结果
     */
    public int insertWxUser(SysWxUser wxUser);

    /**
     * 修改微信用户信息
     *
     * @param wxUser 微信用户信息
     * @return 结果
     */
    public int updateWxUser(SysWxUser wxUser);
}
