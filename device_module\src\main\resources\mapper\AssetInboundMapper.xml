<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.asset_inbound.mapper.AssetInboundMapper">

    <resultMap id="BaseResultMap" type="com.jingfang.asset_inbound.module.entity.AssetInbound">
            <id property="inboundId" column="inbound_id" />
            <result property="businessDate" column="business_date" />
            <result property="supplierName" column="supplier_name" />
            <result property="storageLocation" column="storage_location" />
            <result property="status" column="status" />
            <result property="creatorId" column="creator_id" />
            <result property="createTime" column="create_time" />
            <result property="handlerId" column="handler_id" />
            <result property="handleTime" column="handler_time" />
            <result property="handleRemark" column="handler_remark" />
            <result property="auditorId" column="auditor_id" />
            <result property="auditTime" column="audit_time" />
            <result property="auditRemark" column="audit_remark" />
            <result property="updateTime" column="update_time" />
            <result property="inboundDescription" column="inbound_description" />
            <result property="delFlag" column="del_flag" />
    </resultMap>
    
    <resultMap id="InboundVoResultMap" type="com.jingfang.asset_inbound.module.vo.AssetInboundVo">
            <id property="inboundId" column="inbound_id" />
            <result property="businessDate" column="business_date" />
            <result property="supplierName" column="supplier_name" />
            <result property="storageLocation" column="storage_location" />
            <result property="status" column="status" />
            <result property="creatorId" column="creator_id" />
            <result property="creatorName" column="creator_name" />
            <result property="createTime" column="create_time" />
            <result property="handlerId" column="handler_id" />
            <result property="handlerName" column="handler_name" />
            <result property="handleTime" column="handler_time" />
            <result property="handleRemark" column="handler_remark" />
            <result property="auditorId" column="auditor_id" />
            <result property="auditorName" column="auditor_name" />
            <result property="auditTime" column="audit_time" />
            <result property="auditRemark" column="audit_remark" />
            <result property="updateTime" column="update_time" />
            <result property="inboundDescription" column="inbound_description" />
    </resultMap>

    <sql id="Base_Column_List">
        inbound_id,business_date,supplier_name,storage_location,status,
        creator_id,create_time,handler_id,handler_time,handler_remark,
        auditor_id,audit_time,audit_remark,update_time,inbound_description,del_flag
    </sql>
    
    <!-- 查询入库单列表带管理员名称 -->
    <select id="selectInboundListWithManager" resultMap="InboundVoResultMap">
        SELECT a.inbound_id, a.business_date, a.supplier_name,
               a.storage_location, a.status, a.create_time, a.update_time, 
               a.inbound_description,
               a.creator_id, c.nick_name as creator_name,
               a.handler_id, h.nick_name as handler_name
        FROM asset_inbound a
        LEFT JOIN sys_user c ON a.creator_id = c.user_id
        LEFT JOIN sys_user h ON a.handler_id = h.user_id
        <where>
            a.del_flag = '0'
            <if test="request.status != null">
                AND a.status = #{request.status}
            </if>
            <if test="request.businessDate != null">
                AND a.business_date >= #{request.businessDate}
            </if>
            <if test="request.storageLocation != null">
                AND a.storage_location = #{request.storageLocation}
            </if>
        </where>
        ORDER BY a.create_time DESC
    </select>
    
    <!-- 根据ID查询入库单详情 -->
    <select id="selectInboundDetailById" resultMap="InboundVoResultMap">
        SELECT a.inbound_id, a.business_date, a.supplier_name,
               a.storage_location, a.status, a.create_time, a.update_time, 
               a.inbound_description,
               a.creator_id, c.nick_name as creator_name,
               a.handler_id, h.nick_name as handler_name, a.handle_time, a.handle_remark,
               a.auditor_id, au.nick_name as auditor_name, a.audit_time, a.audit_remark
        FROM asset_inbound a
        LEFT JOIN sys_user c ON a.creator_id = c.user_id
        LEFT JOIN sys_user h ON a.handler_id = h.user_id
        LEFT JOIN sys_user au ON a.auditor_id = au.user_id
        WHERE a.inbound_id = #{inboundId}
          AND a.del_flag = '0'
    </select>

    <!-- 工作台统计查询 -->
    <select id="countMonthlyInbound" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM asset_inbound
        WHERE del_flag = '0'
        AND create_time >= #{startDate}
        AND create_time &lt; #{endDate}
    </select>

    <select id="countMonthlyInboundAssets" resultType="java.lang.Long">
        SELECT COALESCE(SUM(aid.quantity), 0)
        FROM asset_inbound ai
        LEFT JOIN asset_inbound_detail aid ON ai.inbound_id = aid.inbound_id
        WHERE ai.del_flag = '0'
        AND ai.status = 4
        AND ai.create_time >= #{startDate}
        AND ai.create_time &lt; #{endDate}
    </select>

    <select id="sumMonthlyInboundValue" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(aid.quantity * aid.unit_price), 0)
        FROM asset_inbound ai
        LEFT JOIN asset_inbound_detail aid ON ai.inbound_id = aid.inbound_id
        WHERE ai.del_flag = '0'
        AND ai.status = 4
        AND ai.create_time >= #{startDate}
        AND ai.create_time &lt; #{endDate}
    </select>

    <select id="countInboundByStatus" resultType="java.util.Map">
        SELECT
            status,
            COUNT(1) as count
        FROM asset_inbound
        WHERE del_flag = '0'
        GROUP BY status
    </select>

    <select id="selectInboundTrends" resultType="java.util.Map">
        SELECT
            DATE_FORMAT(ai.create_time, '%Y-%m-%d') as date,
            COUNT(1) as inboundCount,
            COALESCE(SUM(aid.quantity), 0) as assetCount,
            COALESCE(SUM(aid.quantity * aid.unit_price), 0) as assetValue
        FROM asset_inbound ai
        LEFT JOIN asset_inbound_detail aid ON ai.inbound_id = aid.inbound_id
        WHERE ai.del_flag = '0'
        AND ai.create_time >= #{startDate}
        AND ai.create_time &lt; #{endDate}
        GROUP BY DATE_FORMAT(ai.create_time, '%Y-%m-%d')
        ORDER BY date
    </select>
</mapper>
