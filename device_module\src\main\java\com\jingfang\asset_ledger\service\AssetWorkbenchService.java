package com.jingfang.asset_ledger.service;

import com.jingfang.asset_ledger.module.vo.AssetWorkbenchVo;

/**
 * 资产工作台服务接口
 */
public interface AssetWorkbenchService {
    
    /**
     * 获取资产工作台概览数据
     * @return 资产概览数据
     */
    AssetWorkbenchVo.AssetOverviewVo getAssetOverview();
    
    /**
     * 获取资产趋势数据
     * @param days 天数
     * @return 资产趋势数据
     */
    AssetWorkbenchVo.AssetTrendVo getAssetTrends(int days);
    
    /**
     * 获取入库出库统计数据
     * @return 入库出库统计数据
     */
    AssetWorkbenchVo.AssetInOutStatisticsVo getInOutStatistics();
    
    /**
     * 获取处置统计数据
     * @return 处置统计数据
     */
    AssetWorkbenchVo.AssetDisposalStatisticsVo getDisposalStatistics();
    
    /**
     * 获取完整的工作台数据
     * @return 工作台数据
     */
    AssetWorkbenchVo getWorkbenchData();
}
