package com.jingfang.asset_stocktaking.module.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 盘点汇总信息视图对象
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
public class SummaryInfo implements Serializable {
    
    /**
     * 计划开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planStartDate;
    
    /**
     * 计划结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planEndDate;
    
    /**
     * 实际开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualStartTime;
    
    /**
     * 实际结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualEndTime;
    
    /**
     * 负责人姓名
     */
    private String responsibleUserName;
    
    /**
     * 参与人数
     */
    private Integer participantCount;
    
    /**
     * 总任务数
     */
    private Integer totalTasks;
    
    /**
     * 已完成任务数
     */
    private Integer completedTasks;
    
    /**
     * 应盘资产总数
     */
    private Integer totalAssets;
    
    /**
     * 实盘资产总数
     */
    private Integer inventoriedAssets;
    
    /**
     * 盘点完成率
     */
    private Double completionRate;
    
    /**
     * 应盘资产总值
     */
    private BigDecimal totalAssetValue;
    
    /**
     * 实盘资产总值
     */
    private BigDecimal inventoriedAssetValue;

    private static final long serialVersionUID = 1L;
}
