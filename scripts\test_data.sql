-- 资产盘点功能测试数据脚本
-- 用于创建测试所需的基础数据

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ================================
-- 1. 清理测试数据
-- ================================
DELETE FROM asset_stocktaking_difference WHERE plan_id LIKE 'test-%';
DELETE FROM asset_stocktaking_record WHERE task_id IN (
    SELECT task_id FROM asset_stocktaking_task WHERE plan_id LIKE 'test-%'
);
DELETE FROM asset_stocktaking_task WHERE plan_id LIKE 'test-%';
DELETE FROM asset_stocktaking_plan WHERE plan_id LIKE 'test-%';

-- 清理测试资产数据
DELETE FROM asset_ledger WHERE asset_code LIKE 'TEST-%';

-- ================================
-- 2. 创建测试用户数据
-- ================================
-- 测试用户已存在，使用系统默认用户

-- ================================
-- 3. 创建测试部门数据
-- ================================
INSERT IGNORE INTO sys_dept (dept_id, parent_id, ancestors, dept_name, order_num, leader, phone, email, status, del_flag, create_by, create_time) VALUES
(200, 100, '0,100', '测试部门A', 1, 'admin', '15888888888', '<EMAIL>', '0', '0', 'admin', NOW()),
(201, 100, '0,100', '测试部门B', 2, 'admin', '15888888889', '<EMAIL>', '0', '0', 'admin', NOW()),
(202, 200, '0,100,200', '测试子部门A1', 1, 'admin', '15888888890', '<EMAIL>', '0', '0', 'admin', NOW());

-- ================================
-- 4. 创建测试资产分类数据
-- ================================
INSERT IGNORE INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, remark) VALUES
(100, '测试资产分类', 'test_asset_category', '0', 'admin', NOW(), '测试用资产分类');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1000, 1, '测试电子设备', '1', 'test_asset_category', '', 'primary', 'Y', '0', 'admin', NOW(), '测试用电子设备分类'),
(1001, 2, '测试办公家具', '2', 'test_asset_category', '', 'success', 'N', '0', 'admin', NOW(), '测试用办公家具分类'),
(1002, 3, '测试车辆设备', '3', 'test_asset_category', '', 'info', 'N', '0', 'admin', NOW(), '测试用车辆设备分类');

-- ================================
-- 5. 创建测试存放位置数据
-- ================================
INSERT IGNORE INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, remark) VALUES
(101, '测试存放位置', 'test_storage_location', '0', 'admin', NOW(), '测试用存放位置');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1010, 1, '测试办公室A', '1', 'test_storage_location', '', 'primary', 'Y', '0', 'admin', NOW(), '测试用办公室A'),
(1011, 2, '测试办公室B', '2', 'test_storage_location', '', 'success', 'N', '0', 'admin', NOW(), '测试用办公室B'),
(1012, 3, '测试仓库', '3', 'test_storage_location', '', 'info', 'N', '0', 'admin', NOW(), '测试用仓库');

-- ================================
-- 6. 创建测试资产台账数据
-- ================================
INSERT INTO asset_ledger (
    asset_id, asset_code, asset_name, category_id, asset_value, purchase_date, 
    dept_id, user_id, storage_location, detail_location, asset_status, 
    warranty_period, supplier, asset_model, serial_number, 
    create_by, create_time, remark
) VALUES
-- 电子设备
('test-asset-001', 'TEST-001', '测试笔记本电脑', 1, 5000.00, '2024-01-01', 200, 1, 1, '测试办公室A-001', 1, 36, '测试供应商A', 'ThinkPad X1', 'SN001', 'admin', NOW(), '测试用笔记本电脑'),
('test-asset-002', 'TEST-002', '测试台式电脑', 1, 3000.00, '2024-01-02', 200, 1, 1, '测试办公室A-002', 1, 36, '测试供应商A', 'Dell OptiPlex', 'SN002', 'admin', NOW(), '测试用台式电脑'),
('test-asset-003', 'TEST-003', '测试打印机', 1, 1500.00, '2024-01-03', 201, 1, 2, '测试办公室B-001', 1, 24, '测试供应商B', 'HP LaserJet', 'SN003', 'admin', NOW(), '测试用打印机'),
('test-asset-004', 'TEST-004', '测试投影仪', 1, 2500.00, '2024-01-04', 201, 1, 2, '测试办公室B-002', 1, 24, '测试供应商B', 'Epson EB', 'SN004', 'admin', NOW(), '测试用投影仪'),
('test-asset-005', 'TEST-005', '测试服务器', 1, 15000.00, '2024-01-05', 202, 1, 3, '测试仓库-001', 1, 60, '测试供应商C', 'Dell PowerEdge', 'SN005', 'admin', NOW(), '测试用服务器'),

-- 办公家具
('test-asset-006', 'TEST-006', '测试办公桌', 2, 800.00, '2024-01-06', 200, 1, 1, '测试办公室A-003', 1, 60, '测试供应商D', '实木办公桌', 'SN006', 'admin', NOW(), '测试用办公桌'),
('test-asset-007', 'TEST-007', '测试办公椅', 2, 500.00, '2024-01-07', 200, 1, 1, '测试办公室A-004', 1, 36, '测试供应商D', '人体工学椅', 'SN007', 'admin', NOW(), '测试用办公椅'),
('test-asset-008', 'TEST-008', '测试文件柜', 2, 600.00, '2024-01-08', 201, 1, 2, '测试办公室B-003', 1, 60, '测试供应商D', '四门文件柜', 'SN008', 'admin', NOW(), '测试用文件柜'),
('test-asset-009', 'TEST-009', '测试会议桌', 2, 2000.00, '2024-01-09', 201, 1, 2, '测试办公室B-004', 1, 60, '测试供应商D', '实木会议桌', 'SN009', 'admin', NOW(), '测试用会议桌'),
('test-asset-010', 'TEST-010', '测试书架', 2, 400.00, '2024-01-10', 202, 1, 3, '测试仓库-002', 1, 60, '测试供应商D', '五层书架', 'SN010', 'admin', NOW(), '测试用书架'),

-- 车辆设备
('test-asset-011', 'TEST-011', '测试公务车', 3, 150000.00, '2024-01-11', 200, 1, 3, '测试仓库-003', 1, 60, '测试供应商E', '丰田凯美瑞', 'SN011', 'admin', NOW(), '测试用公务车'),
('test-asset-012', 'TEST-012', '测试货车', 3, 80000.00, '2024-01-12', 201, 1, 3, '测试仓库-004', 1, 60, '测试供应商E', '五菱宏光', 'SN012', 'admin', NOW(), '测试用货车'),

-- 一些状态异常的资产
('test-asset-013', 'TEST-013', '测试维修设备', 1, 3000.00, '2024-01-13', 200, 1, 1, '测试办公室A-005', 2, 36, '测试供应商A', '维修中设备', 'SN013', 'admin', NOW(), '测试用维修设备'),
('test-asset-014', 'TEST-014', '测试报废设备', 1, 1000.00, '2024-01-14', 201, 1, 2, '测试办公室B-005', 3, 24, '测试供应商B', '报废设备', 'SN014', 'admin', NOW(), '测试用报废设备'),
('test-asset-015', 'TEST-015', '测试闲置设备', 1, 2000.00, '2024-01-15', 202, 1, 3, '测试仓库-005', 4, 36, '测试供应商C', '闲置设备', 'SN015', 'admin', NOW(), '测试用闲置设备');

-- ================================
-- 7. 创建测试盘点计划数据
-- ================================
INSERT INTO asset_stocktaking_plan (
    plan_id, plan_name, plan_type, plan_scope, start_date, end_date, 
    responsible_user_id, status, create_by, create_time, remark
) VALUES
('test-plan-001', '测试全盘计划', 1, '{"includeSubDept":true,"deptIds":[200,201,202],"categoryIds":[1,2,3],"locationIds":[1,2,3]}', '2025-01-01', '2025-01-31', 1, 1, 'admin', NOW(), '测试用全盘计划'),
('test-plan-002', '测试部分盘点计划', 2, '{"includeSubDept":false,"deptIds":[200],"categoryIds":[1],"locationIds":[1]}', '2025-01-15', '2025-01-25', 1, 2, 'admin', NOW(), '测试用部分盘点计划'),
('test-plan-003', '测试已完成计划', 1, '{"includeSubDept":true,"deptIds":[201],"categoryIds":[2],"locationIds":[2]}', '2024-12-01', '2024-12-31', 1, 4, 'admin', NOW(), '测试用已完成计划');

-- ================================
-- 8. 创建测试盘点任务数据
-- ================================
INSERT INTO asset_stocktaking_task (
    task_id, plan_id, task_name, assigned_user_id, asset_scope, 
    expected_count, actual_count, status, start_time, end_time, 
    create_by, create_time
) VALUES
('test-task-001', 'test-plan-001', '测试任务A-电子设备', 1, '["test-asset-001","test-asset-002","test-asset-003","test-asset-004","test-asset-005"]', 5, 0, 1, NULL, NULL, 'admin', NOW()),
('test-task-002', 'test-plan-001', '测试任务B-办公家具', 1, '["test-asset-006","test-asset-007","test-asset-008","test-asset-009","test-asset-010"]', 5, 0, 1, NULL, NULL, 'admin', NOW()),
('test-task-003', 'test-plan-002', '测试任务C-部分盘点', 1, '["test-asset-001","test-asset-002"]', 2, 0, 2, NOW(), NULL, 'admin', NOW()),
('test-task-004', 'test-plan-003', '测试任务D-已完成', 1, '["test-asset-006","test-asset-007","test-asset-008"]', 3, 3, 3, '2024-12-01 09:00:00', '2024-12-01 17:00:00', 'admin', NOW());

-- ================================
-- 9. 创建测试盘点记录数据
-- ================================
INSERT INTO asset_stocktaking_record (
    record_id, task_id, asset_id, asset_code, found_status, actual_location, 
    actual_status, inventory_user_id, inventory_time, remark
) VALUES
-- 已完成任务的记录
('test-record-001', 'test-task-004', 'test-asset-006', 'TEST-006', 1, '测试办公室A-003', 1, 1, '2024-12-01 10:00:00', '正常找到'),
('test-record-002', 'test-task-004', 'test-asset-007', 'TEST-007', 1, '测试办公室A-004', 1, 1, '2024-12-01 11:00:00', '正常找到'),
('test-record-003', 'test-task-004', 'test-asset-008', 'TEST-008', 0, NULL, NULL, 1, '2024-12-01 12:00:00', '未找到，可能已移动'),

-- 进行中任务的部分记录
('test-record-004', 'test-task-003', 'test-asset-001', 'TEST-001', 1, '测试办公室A-001', 1, 1, NOW(), '正常找到'),
('test-record-005', 'test-task-003', 'test-asset-002', 'TEST-002', 1, '测试办公室B-001', 1, 1, NOW(), '位置有变化');

-- ================================
-- 10. 创建测试差异数据
-- ================================
INSERT INTO asset_stocktaking_difference (
    diff_id, plan_id, asset_id, diff_type, book_value, actual_value, 
    diff_reason, handle_status, handle_suggestion, create_time
) VALUES
('test-diff-001', 'test-plan-003', 'test-asset-008', 2, 
 '{"assetCode":"TEST-008","assetName":"测试文件柜","location":"测试办公室B-003","assetStatus":1,"assetValue":600.00}',
 '{"foundStatus":0,"inventoryTime":"2024-12-01 12:00:00"}',
 '盘点时未找到该资产', 1, '建议：1.再次确认资产是否确实丢失；2.如确认丢失，请查明原因；3.按规定办理核销手续', NOW()),

('test-diff-002', 'test-plan-003', 'test-asset-002', 4,
 '{"assetCode":"TEST-002","assetName":"测试台式电脑","location":"测试办公室A-002","assetStatus":1,"assetValue":3000.00}',
 '{"foundStatus":1,"actualLocation":"测试办公室B-001","actualStatus":1,"inventoryTime":"' + DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') + '"}',
 '资产实际位置与账面位置不符', 1, '建议：1.确认资产实际存放位置；2.更新资产台账位置信息；3.如需调拨，按流程办理', NOW());

-- ================================
-- 11. 创建测试权限数据
-- ================================
-- 盘点相关菜单权限（如果不存在的话）
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
(2000, '资产盘点', 0, 6, 'stocktaking', NULL, '', 1, 0, 'M', '0', '0', '', 'tool', 'admin', NOW(), '资产盘点目录'),
(2001, '盘点计划', 2000, 1, 'plan', 'stocktaking/plan/index', '', 1, 0, 'C', '0', '0', 'stocktaking:plan:view', 'list', 'admin', NOW(), '盘点计划菜单'),
(2002, '盘点任务', 2000, 2, 'task', 'stocktaking/task/index', '', 1, 0, 'C', '0', '0', 'stocktaking:task:view', 'job', 'admin', NOW(), '盘点任务菜单'),
(2003, '盘点记录', 2000, 3, 'record', 'stocktaking/record/index', '', 1, 0, 'C', '0', '0', 'stocktaking:record:view', 'documentation', 'admin', NOW(), '盘点记录菜单'),
(2004, '差异处理', 2000, 4, 'difference', 'stocktaking/difference/index', '', 1, 0, 'C', '0', '0', 'stocktaking:difference:view', 'bug', 'admin', NOW(), '差异处理菜单'),
(2005, '盘点报告', 2000, 5, 'report', 'stocktaking/report/index', '', 1, 0, 'C', '0', '0', 'stocktaking:report:view', 'chart', 'admin', NOW(), '盘点报告菜单');

-- 为admin角色分配盘点权限
INSERT IGNORE INTO sys_role_menu (role_id, menu_id) VALUES
(1, 2000), (1, 2001), (1, 2002), (1, 2003), (1, 2004), (1, 2005);

-- ================================
-- 12. 提交事务
-- ================================
COMMIT;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 显示创建的测试数据统计
SELECT '测试数据创建完成' as message;
SELECT COUNT(*) as '测试资产数量' FROM asset_ledger WHERE asset_code LIKE 'TEST-%';
SELECT COUNT(*) as '测试计划数量' FROM asset_stocktaking_plan WHERE plan_id LIKE 'test-%';
SELECT COUNT(*) as '测试任务数量' FROM asset_stocktaking_task WHERE plan_id LIKE 'test-%';
SELECT COUNT(*) as '测试记录数量' FROM asset_stocktaking_record WHERE task_id LIKE 'test-%';
SELECT COUNT(*) as '测试差异数量' FROM asset_stocktaking_difference WHERE plan_id LIKE 'test-%';
