package com.jingfang.collaboration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jingfang.collaboration.domain.Spreadsheet;
import com.jingfang.collaboration.dto.InviteDto;
import com.jingfang.collaboration.dto.SpreadsheetDto;
import com.jingfang.collaboration.vo.CollaboratorVo;
import com.jingfang.collaboration.vo.OnlineUserVo;
import com.jingfang.collaboration.vo.SpreadsheetVo;

import java.util.List;

/**
 * 表格服务接口
 */
public interface SpreadsheetService extends IService<Spreadsheet> {
    
    /**
     * 创建表格
     */
    String createSpreadsheet(SpreadsheetDto dto, Long userId);
    
    /**
     * 更新表格
     */
    boolean updateSpreadsheet(SpreadsheetDto dto, Long userId);
    
    /**
     * 删除表格
     */
    boolean deleteSpreadsheet(String id, Long userId);
    
    /**
     * 批量删除表格
     */
    boolean deleteSpreadsheets(String[] ids, Long userId);
    
    /**
     * 查询表格详情
     */
    SpreadsheetVo getSpreadsheetDetail(String id, Long userId);
    
    /**
     * 查询表格列表
     */
    IPage<SpreadsheetVo> getSpreadsheetList(int pageNum, int pageSize, String title, String status, Long userId);
    
    /**
     * 查询用户有权限访问的表格列表
     */
    IPage<SpreadsheetVo> getUserAccessibleSpreadsheets(int pageNum, int pageSize, String title, String status, Long userId);
    
    /**
     * 邀请用户协作
     */
    boolean inviteCollaborators(InviteDto dto, Long inviteBy);
    
    /**
     * 接受协作邀请
     */
    boolean acceptInvitation(String collaboratorId, Long userId);
    
    /**
     * 拒绝协作邀请
     */
    boolean rejectInvitation(String collaboratorId, Long userId);
    
    /**
     * 移除协作者
     */
    boolean removeCollaborator(String collaboratorId, Long userId);
    
    /**
     * 更新协作者权限
     */
    boolean updateCollaboratorPermission(String collaboratorId, String permission, Long userId);
    
    /**
     * 查询表格协作者列表
     */
    List<CollaboratorVo> getCollaborators(String spreadsheetId, Long userId);
    
    /**
     * 查询在线用户列表
     */
    List<OnlineUserVo> getOnlineUsers(String spreadsheetId, Long userId);

    /**
     * 查询用户的协作邀请列表
     */
    IPage<CollaboratorVo> getUserInvitations(int pageNum, int pageSize, String spreadsheetTitle, String status, Long userId);

    /**
     * 退出协作
     */
    boolean leaveCollaboration(String spreadsheetId, Long userId);
    
    /**
     * 生成分享链接
     */
    String generateShareUrl(String spreadsheetId, String password, Long expireDays, Long userId);
    
    /**
     * 验证分享链接
     */
    SpreadsheetVo validateShareUrl(String token, String password);
    
    /**
     * 保存表格数据
     */
    boolean saveSpreadsheetData(String spreadsheetId, String data, Long userId);
    
    /**
     * 检查用户权限
     */
    String checkUserPermission(String spreadsheetId, Long userId);
}
