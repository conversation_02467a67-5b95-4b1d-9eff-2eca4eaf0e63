package com.jingfang.wh_item_requisition.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jingfang.wh_item_requisition.module.entity.ItemRequisitionOperationLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物品领用操作日志Mapper接口
 */
@Mapper
public interface ItemRequisitionOperationLogMapper extends BaseMapper<ItemRequisitionOperationLog> {
    
    /**
     * 根据领用单ID查询操作日志列表
     *
     * @param requisitionId 领用单ID
     * @return 操作日志列表
     */
    List<ItemRequisitionOperationLog> selectByRequisitionId(@Param("requisitionId") String requisitionId);
} 