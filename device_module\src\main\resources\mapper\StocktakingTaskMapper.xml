<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.asset_stocktaking.mapper.StocktakingTaskMapper">

    <!-- 结果映射 -->
    <resultMap id="StocktakingTaskVoResult" type="com.jingfang.asset_stocktaking.module.vo.StocktakingTaskVo">
        <id property="taskId" column="task_id"/>
        <result property="planId" column="plan_id"/>
        <result property="planName" column="plan_name"/>
        <result property="taskName" column="task_name"/>
        <result property="assignedUserId" column="assigned_user_id"/>
        <result property="assignedUserName" column="assigned_user_name"/>
        <result property="assetScope" column="asset_scope"/>
        <result property="expectedCount" column="expected_count"/>
        <result property="actualCount" column="actual_count"/>
        <result property="status" column="status"/>
        <result property="statusDesc" column="status_desc"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <!-- 分页查询盘点任务列表 -->
    <select id="selectTaskList" resultMap="StocktakingTaskVoResult">
        SELECT 
            t.task_id,
            t.plan_id,
            p.plan_name,
            t.task_name,
            t.assigned_user_id,
            u.nick_name as assigned_user_name,
            t.asset_scope,
            t.expected_count,
            t.actual_count,
            t.status,
            CASE t.status
                WHEN 1 THEN '待执行'
                WHEN 2 THEN '执行中'
                WHEN 3 THEN '已完成'
                ELSE '未知'
            END as status_desc,
            t.start_time,
            t.end_time,
            t.create_by,
            t.create_time
        FROM asset_stocktaking_task t
        LEFT JOIN asset_stocktaking_plan p ON t.plan_id = p.plan_id
        LEFT JOIN sys_user u ON t.assigned_user_id = u.user_id
        <where>
            <if test="request.planId != null and request.planId != ''">
                AND t.plan_id = #{request.planId}
            </if>
            <if test="request.taskName != null and request.taskName != ''">
                AND t.task_name LIKE CONCAT('%', #{request.taskName}, '%')
            </if>
            <if test="request.assignedUserId != null">
                AND t.assigned_user_id = #{request.assignedUserId}
            </if>
            <if test="request.status != null">
                AND t.status = #{request.status}
            </if>
            <if test="request.statusList != null and request.statusList.size() > 0">
                AND t.status IN
                <foreach collection="request.statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="request.startTimeBegin != null">
                AND t.start_time >= #{request.startTimeBegin}
            </if>
            <if test="request.startTimeEnd != null">
                AND t.start_time &lt;= #{request.startTimeEnd}
            </if>
            <if test="request.endTimeBegin != null">
                AND t.end_time >= #{request.endTimeBegin}
            </if>
            <if test="request.endTimeEnd != null">
                AND t.end_time &lt;= #{request.endTimeEnd}
            </if>
            <if test="request.createTimeBegin != null">
                AND t.create_time >= #{request.createTimeBegin}
            </if>
            <if test="request.createTimeEnd != null">
                AND t.create_time &lt;= #{request.createTimeEnd}
            </if>
            <if test="request.createBy != null and request.createBy != ''">
                AND t.create_by = #{request.createBy}
            </if>
            <if test="request.expectedCountMin != null">
                AND t.expected_count >= #{request.expectedCountMin}
            </if>
            <if test="request.expectedCountMax != null">
                AND t.expected_count &lt;= #{request.expectedCountMax}
            </if>
            <if test="request.onlyMyTasks != null and request.onlyMyTasks == true">
                AND t.assigned_user_id = #{request.assignedUserId}
            </if>
            <if test="request.onlyOverdueTasks != null and request.onlyOverdueTasks == true">
                AND t.status IN (1, 2) AND p.end_date &lt; CURDATE()
            </if>
            <if test="request.keyword != null and request.keyword != ''">
                AND (t.task_name LIKE CONCAT('%', #{request.keyword}, '%')
                     OR u.nick_name LIKE CONCAT('%', #{request.keyword}, '%'))
            </if>
        </where>
        <choose>
            <when test="request.orderBy != null and request.orderBy != ''">
                ORDER BY ${request.orderBy}
                <if test="request.orderDirection != null and request.orderDirection != ''">
                    ${request.orderDirection}
                </if>
            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 根据ID查询盘点任务详情 -->
    <select id="selectTaskById" resultMap="StocktakingTaskVoResult">
        SELECT 
            t.task_id,
            t.plan_id,
            p.plan_name,
            t.task_name,
            t.assigned_user_id,
            u.nick_name as assigned_user_name,
            t.asset_scope,
            t.expected_count,
            t.actual_count,
            t.status,
            CASE t.status
                WHEN 1 THEN '待执行'
                WHEN 2 THEN '执行中'
                WHEN 3 THEN '已完成'
                ELSE '未知'
            END as status_desc,
            t.start_time,
            t.end_time,
            t.create_by,
            t.create_time
        FROM asset_stocktaking_task t
        LEFT JOIN asset_stocktaking_plan p ON t.plan_id = p.plan_id
        LEFT JOIN sys_user u ON t.assigned_user_id = u.user_id
        WHERE t.task_id = #{taskId}
    </select>

    <!-- 查询任务进度信息 -->
    <select id="selectTaskProgress" resultType="com.jingfang.asset_stocktaking.module.vo.StocktakingTaskVo$TaskProgress">
        SELECT 
            CASE 
                WHEN t.expected_count = 0 THEN 0
                ELSE ROUND(COALESCE(t.actual_count, 0) * 100.0 / t.expected_count, 2)
            END as progressPercentage,
            COALESCE(record_stats.found_assets, 0) as foundAssets,
            COALESCE(record_stats.not_found_assets, 0) as notFoundAssets,
            COALESCE(record_stats.abnormal_assets, 0) as abnormalAssets,
            CASE 
                WHEN t.start_time IS NULL THEN 0
                ELSE TIMESTAMPDIFF(MINUTE, t.start_time, COALESCE(t.end_time, NOW()))
            END as durationMinutes
        FROM asset_stocktaking_task t
        LEFT JOIN (
            SELECT 
                task_id,
                SUM(CASE WHEN found_status = 1 THEN 1 ELSE 0 END) as found_assets,
                SUM(CASE WHEN found_status = 0 THEN 1 ELSE 0 END) as not_found_assets,
                SUM(CASE WHEN found_status = 0 OR actual_status = 3 THEN 1 ELSE 0 END) as abnormal_assets
            FROM asset_stocktaking_record 
            WHERE task_id = #{taskId}
            GROUP BY task_id
        ) record_stats ON t.task_id = record_stats.task_id
        WHERE t.task_id = #{taskId}
    </select>

    <!-- 查询任务统计信息 -->
    <select id="selectTaskStatistics" resultType="com.jingfang.asset_stocktaking.module.vo.StocktakingTaskVo$TaskStatistics">
        SELECT 
            COUNT(r.record_id) as totalRecords,
            SUM(CASE WHEN r.found_status = 1 AND r.actual_status != 3 THEN 1 ELSE 0 END) as normalRecords,
            SUM(CASE WHEN r.found_status = 0 OR r.actual_status = 3 THEN 1 ELSE 0 END) as abnormalRecords,
            COALESCE(diff_count.difference_records, 0) as differenceRecords,
            MAX(r.inventory_time) as lastInventoryTime
        FROM asset_stocktaking_task t
        LEFT JOIN asset_stocktaking_record r ON t.task_id = r.task_id
        LEFT JOIN (
            SELECT 
                COUNT(*) as difference_records
            FROM asset_stocktaking_difference d
            INNER JOIN asset_stocktaking_record r ON d.asset_id = r.asset_id
            WHERE r.task_id = #{taskId}
        ) diff_count ON 1=1
        WHERE t.task_id = #{taskId}
        GROUP BY t.task_id
    </select>

    <!-- 根据计划ID查询任务列表 -->
    <select id="selectTaskByPlanId" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingTask">
        SELECT * FROM asset_stocktaking_task WHERE plan_id = #{planId}
    </select>

    <!-- 根据分配用户查询任务列表 -->
    <select id="selectTaskByAssignedUser" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingTask">
        SELECT * FROM asset_stocktaking_task WHERE assigned_user_id = #{assignedUserId}
    </select>

    <!-- 根据状态查询任务列表 -->
    <select id="selectTaskByStatus" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingTask">
        SELECT * FROM asset_stocktaking_task WHERE status = #{status}
    </select>

    <!-- 统计计划下各状态的任务数量 -->
    <select id="countTaskByStatusInPlan" resultType="java.util.Map">
        SELECT 
            status,
            CASE status
                WHEN 1 THEN '待执行'
                WHEN 2 THEN '执行中'
                WHEN 3 THEN '已完成'
                ELSE '未知'
            END as statusDesc,
            COUNT(*) as count
        FROM asset_stocktaking_task 
        WHERE plan_id = #{planId}
        GROUP BY status
    </select>

    <!-- 查询用户的待执行任务 -->
    <select id="selectPendingTasksByUser" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingTask">
        SELECT * FROM asset_stocktaking_task 
        WHERE assigned_user_id = #{assignedUserId} AND status = 1
    </select>

    <!-- 查询用户的进行中任务 -->
    <select id="selectInProgressTasksByUser" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingTask">
        SELECT * FROM asset_stocktaking_task 
        WHERE assigned_user_id = #{assignedUserId} AND status = 2
    </select>

    <!-- 查询逾期任务 -->
    <select id="selectOverdueTasks" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingTask">
        SELECT t.* FROM asset_stocktaking_task t
        INNER JOIN asset_stocktaking_plan p ON t.plan_id = p.plan_id
        WHERE t.status IN (1, 2) AND p.end_date &lt; CURDATE()
    </select>

    <!-- 更新任务状态 -->
    <update id="updateTaskStatus">
        UPDATE asset_stocktaking_task 
        SET status = #{status}
        WHERE task_id = #{taskId}
    </update>

    <!-- 更新任务进度 -->
    <update id="updateTaskProgress">
        UPDATE asset_stocktaking_task 
        SET actual_count = #{actualCount}
        WHERE task_id = #{taskId}
    </update>

    <!-- 批量更新任务状态 -->
    <update id="batchUpdateTaskStatus">
        UPDATE asset_stocktaking_task 
        SET status = #{status}
        WHERE task_id IN
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </update>

    <!-- 开始任务 -->
    <update id="startTask">
        UPDATE asset_stocktaking_task 
        SET status = 2, start_time = NOW()
        WHERE task_id = #{taskId}
    </update>

    <!-- 完成任务 -->
    <update id="completeTask">
        UPDATE asset_stocktaking_task 
        SET status = 3, end_time = NOW()
        WHERE task_id = #{taskId}
    </update>

    <!-- 查询计划的任务完成情况 -->
    <select id="selectPlanTaskCompletion" resultType="java.util.Map">
        SELECT 
            COUNT(*) as totalTasks,
            SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completedTasks,
            SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as inProgressTasks,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as pendingTasks,
            CASE 
                WHEN COUNT(*) = 0 THEN 0
                ELSE ROUND(SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2)
            END as completionRate
        FROM asset_stocktaking_task 
        WHERE plan_id = #{planId}
    </select>

    <!-- 重新分配任务 -->
    <update id="reassignTask">
        UPDATE asset_stocktaking_task 
        SET assigned_user_id = #{newAssignedUserId}
        WHERE task_id = #{taskId}
    </update>

</mapper>
