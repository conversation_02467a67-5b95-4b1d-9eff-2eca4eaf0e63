package com.jingfang.asset_inbound.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingfang.asset_inbound.module.entity.AssetInboundAttachment;
import com.jingfang.asset_inbound.service.AssetInboundAttachmentService;
import com.jingfang.asset_inbound.mapper.AssetInboundAttachmentMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【asset_inbound_attachment(入库附件表)】的数据库操作Service实现
* @createDate 2025-05-07 14:08:37
*/
@Service
public class AssetInboundAttachmentServiceImpl extends ServiceImpl<AssetInboundAttachmentMapper, AssetInboundAttachment>
    implements AssetInboundAttachmentService{

}




