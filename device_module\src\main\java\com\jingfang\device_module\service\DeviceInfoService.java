package com.jingfang.device_module.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jingfang.device_module.module.dto.DeviceInfoDto;
import com.jingfang.device_module.module.entity.DeviceInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jingfang.device_module.module.vo.DeviceBaseInfoVo;
import com.jingfang.device_module.module.vo.DeviceInfoVo;
import com.jingfang.device_module.request.DeviceInfoSearchRequest;

/**
* <AUTHOR>
* @description 针对表【device_info】的数据库操作Service
* @createDate 2025-03-20 15:41:39
*/
public interface DeviceInfoService extends IService<DeviceInfo> {


    boolean adeNewDevice(DeviceInfoDto dto);

    Page<DeviceInfoVo> showDeviceInfoList(DeviceInfoSearchRequest request);

    String redisTest();

    String DeviceConnectStatusTest();

    String mqttDeviceStatusTest();

    String refreshCatch();


    DeviceBaseInfoVo showDeviceBaseInfo(Long deviceId);

    int addPictureUrl(Long deviceId, String pictureUrl);

    int deletePictureUrl(Long deviceId, String pictureUrl);
}
