package com.jingfang.web.controller.asset.ledger;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.asset_ledger.module.dto.AssetDto;
import com.jingfang.asset_ledger.module.dto.AssetExcelDto;
import com.jingfang.asset_ledger.module.request.AssetSearchRequest;
import com.jingfang.asset_ledger.module.vo.AssetBaseInfoVo;
import com.jingfang.asset_ledger.module.vo.AssetDetailVo;
import com.jingfang.asset_ledger.service.AssetBaseInfoService;
import com.jingfang.asset_ledger.service.impl.AssetService;
import com.jingfang.common.annotation.Anonymous;
import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.common.utils.poi.ExcelUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.stream.Collectors;




/**
 * 资产台账控制器
 */
@Slf4j
@RestController
@RequestMapping("/asset")
public class AssetLedgerController extends BaseController {

    @Resource
    private AssetService assetService;

    @Resource
    private AssetBaseInfoService baseInfoService;


    @PostMapping("/add")
    public AjaxResult add(@RequestBody AssetDto dto) {
        boolean flag = assetService.add(dto);
        if (flag) {
            return AjaxResult.success("add success");
        }else {
            return AjaxResult.error("add fail");
        }
    }


    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody AssetDto dto) {
        boolean flag = assetService.edit(dto);
        if (flag) {
            return AjaxResult.success("edit success");
        }else {
            return AjaxResult.error("edit fail");
        }
    }



    @PostMapping("/list")
    public AjaxResult assetList(@RequestBody AssetSearchRequest request) {
        IPage<AssetBaseInfoVo> vos = baseInfoService.selectAssetList(request);
        return AjaxResult.success(vos);
    }

    @GetMapping("/detail")
    public AjaxResult assetDetail(String assetId){
        AssetDetailVo vo = assetService.getDetail(assetId);
        return AjaxResult.success(vo);
    }
    
    /**
     * 获取资产详情（包含维护信息）
     */
    @GetMapping("/detailWithMaintenance")
    public AjaxResult assetDetailWithMaintenance(String assetId){
        try {
            AssetDetailVo vo = assetService.getDetail(assetId);
            
            // 记录日志以便调试
            log.info("资产{}详情查询成功，包含维护计划{}个，维护任务{}个", 
                    assetId, 
                    vo.getMaintenancePlans() != null ? vo.getMaintenancePlans().size() : 0,
                    vo.getMaintenanceTasks() != null ? vo.getMaintenanceTasks().size() : 0);
            
            return AjaxResult.success(vo);
        } catch (Exception e) {
            log.error("查询资产{}详情失败", assetId, e);
            return AjaxResult.error("查询资产详情失败：" + e.getMessage());
        }
    }


    @DeleteMapping("/delete")
    public AjaxResult deleteAsset(String assetId){
        boolean flag = assetService.deleteAsset(assetId);
        if (flag) {
            return AjaxResult.success("delete success");
        }else {
            return AjaxResult.error("delete fail");
        }
    }


    @PostMapping("/batch/json")
    public AjaxResult batchAddByExcel(@RequestBody String jsonBody){
        String message = assetService.batchAddByExcel(jsonBody);
        return AjaxResult.success(message);
    }

    /**
     * 下载资产导入模板
     */
    @Anonymous
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        try {
            ExcelUtil<AssetExcelDto> util = new ExcelUtil<>(AssetExcelDto.class);
            // 设置响应头，确保浏览器识别为下载文件
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment; filename=资产导入模板.xlsx");
            
            util.importTemplateExcel(response, "资产数据");
        } catch (Exception e) {
            log.error("下载Excel模板失败", e);
            try {
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().println("{\"msg\":\"下载模板失败，" + e.getMessage() + "\",\"code\":500}");
            } catch (Exception ex) {
                log.error("响应错误信息失败", ex);
            }
        }
    }

    /**
     * 导入资产数据
     */
    @Anonymous
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<AssetExcelDto> util = new ExcelUtil<>(AssetExcelDto.class);
        List<AssetExcelDto> excelDtoList = util.importExcel(file.getInputStream());
        
        // 将AssetExcelDto转换为AssetDto
        List<AssetDto> assetList = excelDtoList.stream()
                .map(AssetExcelDto::toAssetDto)
                .collect(Collectors.toList());
                
        String message = assetService.importData(assetList);
        return AjaxResult.success(message);
    }

    /**
     * 导出资产数据
     */
    @Anonymous
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody(required = false) AssetSearchRequest request) {
        try {
            if (request == null) {
                request = new AssetSearchRequest();
            }
            
            // 获取资产列表数据
            List<AssetDto> assetList = assetService.selectAssetListForExport(request);
            
            // 将AssetDto转换为AssetExcelDto用于导出
            List<AssetExcelDto> excelDtoList = assetList.stream()
                    .map(AssetExcelDto::fromAssetDto)
                    .collect(Collectors.toList());
            
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment; filename=asset_export.xlsx");
            
            // 导出Excel
            ExcelUtil<AssetExcelDto> util = new ExcelUtil<>(AssetExcelDto.class);
            util.exportExcel(response, excelDtoList, "资产数据");
            
        } catch (Exception e) {
            log.error("导出Excel失败", e);
            try {
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().println("{\"msg\":\"导出Excel失败，" + e.getMessage() + "\",\"code\":500}");
            } catch (Exception ex) {
                log.error("响应错误信息失败", ex);
            }
        }
    }
}
