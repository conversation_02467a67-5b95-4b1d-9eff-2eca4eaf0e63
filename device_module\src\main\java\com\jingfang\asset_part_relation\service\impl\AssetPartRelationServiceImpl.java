package com.jingfang.asset_part_relation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingfang.asset_part_relation.mapper.AssetPartRelationMapper;
import com.jingfang.asset_part_relation.module.dto.AssetPartRelationDto;
import com.jingfang.asset_part_relation.module.entity.AssetPartRelation;
import com.jingfang.asset_part_relation.module.request.AssetPartRelationSearchRequest;
import com.jingfang.asset_part_relation.module.vo.AssetPartRelationVo;
import com.jingfang.asset_part_relation.service.AssetPartRelationService;
import com.jingfang.common.utils.uuid.UUID;
import com.jingfang.wh_item.mapper.ItemBaseInfoMapper;
import com.jingfang.wh_item.module.entity.ItemBaseInfo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 资产备品备件关联Service实现类
 */
@Slf4j
@Service
public class AssetPartRelationServiceImpl extends ServiceImpl<AssetPartRelationMapper, AssetPartRelation> 
        implements AssetPartRelationService {
    
    @Resource
    private AssetPartRelationMapper relationMapper;
    
    @Resource
    private ItemBaseInfoMapper itemBaseInfoMapper;
    
    /**
     * 新增资产备品备件关联
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addRelation(AssetPartRelationDto relationDto, String username) {
        try {
            // 检查是否已存在关联
            if (checkRelationExists(relationDto.getAssetId(), relationDto.getPartId())) {
                log.warn("资产{}和备品备件{}已存在关联关系", relationDto.getAssetId(), relationDto.getPartId());
                throw new RuntimeException("该资产和备品备件已存在关联关系");
            }
            
            // 检查备品备件是否存在且类型正确
            if (!checkItemExists(relationDto.getPartId())) {
                log.warn("备品备件{}不存在或类型不正确", relationDto.getPartId());
                throw new RuntimeException("备品备件不存在或类型不正确");
            }
            
            AssetPartRelation relation = new AssetPartRelation();
            relation.setRelationId(UUID.fastUUID().toString(true));
            relation.setAssetId(relationDto.getAssetId());
            relation.setPartId(relationDto.getPartId());
            relation.setRelationType(relationDto.getRelationType());
            relation.setSuggestedQuantity(relationDto.getSuggestedQuantity());
            relation.setRemark(relationDto.getRemark());
            relation.setCreateTime(new Date());
            relation.setCreateBy(username);
            relation.setDeleted(0);
            
            return this.save(relation);
        } catch (Exception e) {
            log.error("新增资产备品备件关联失败", e);
            throw e;
        }
    }
    
    /**
     * 检查物品是否存在且为备品备件类型
     */
    private boolean checkItemExists(String itemId) {
        try {
            LambdaQueryWrapper<ItemBaseInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ItemBaseInfo::getItemId, itemId);
            wrapper.eq(ItemBaseInfo::getItemType, 2); // 备品备件类型
            wrapper.eq(ItemBaseInfo::getDeleted, 0); // 未删除
            Long count = itemBaseInfoMapper.selectCount(wrapper);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("检查物品是否存在失败，itemId={}", itemId, e);
            return false;
        }
    }
    
    /**
     * 批量新增资产备品备件关联
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchAddRelations(String assetId, List<AssetPartRelationDto> relationDtos, String username) {
        try {
            if (relationDtos == null || relationDtos.isEmpty()) {
                return true;
            }
            
            for (AssetPartRelationDto dto : relationDtos) {
                dto.setAssetId(assetId);
                addRelation(dto, username);
            }
            
            return true;
        } catch (Exception e) {
            log.error("批量新增资产备品备件关联失败", e);
            throw e;
        }
    }
    
    /**
     * 更新资产备品备件关联
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRelation(AssetPartRelationDto relationDto, String username) {
        try {
            AssetPartRelation relation = this.getById(relationDto.getRelationId());
            if (relation == null) {
                log.warn("关联记录{}不存在", relationDto.getRelationId());
                throw new RuntimeException("关联记录不存在");
            }
            
            // 如果修改了资产或备品备件，需要检查新的关联是否已存在
            if (!relation.getAssetId().equals(relationDto.getAssetId()) || 
                !relation.getPartId().equals(relationDto.getPartId())) {
                if (checkRelationExists(relationDto.getAssetId(), relationDto.getPartId())) {
                    log.warn("资产{}和备品备件{}已存在关联关系", relationDto.getAssetId(), relationDto.getPartId());
                    throw new RuntimeException("该资产和备品备件已存在关联关系");
                }
            }
            
            relation.setAssetId(relationDto.getAssetId());
            relation.setPartId(relationDto.getPartId());
            relation.setRelationType(relationDto.getRelationType());
            relation.setSuggestedQuantity(relationDto.getSuggestedQuantity());
            relation.setRemark(relationDto.getRemark());
            relation.setUpdateTime(new Date());
            relation.setUpdateBy(username);
            
            return this.updateById(relation);
        } catch (Exception e) {
            log.error("更新资产备品备件关联失败", e);
            throw e;
        }
    }
    
    /**
     * 删除资产备品备件关联
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRelation(String relationId, String username) {
        try {
            AssetPartRelation relation = this.getById(relationId);
            if (relation == null) {
                log.warn("关联记录{}不存在", relationId);
                return false;
            }
            
            relation.setDeleted(1);
            relation.setUpdateTime(new Date());
            relation.setUpdateBy(username);
            
            return this.updateById(relation);
        } catch (Exception e) {
            log.error("删除资产备品备件关联失败", e);
            throw e;
        }
    }
    
    /**
     * 批量删除资产备品备件关联
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRelations(String[] relationIds, String username) {
        try {
            for (String relationId : relationIds) {
                deleteRelation(relationId, username);
            }
            return true;
        } catch (Exception e) {
            log.error("批量删除资产备品备件关联失败", e);
            throw e;
        }
    }
    
    /**
     * 查询资产备品备件关联列表
     */
    @Override
    public IPage<AssetPartRelationVo> selectRelationList(AssetPartRelationSearchRequest request) {
        Page<AssetPartRelationVo> page = new Page<>(request.getPageNum(), request.getPageSize());
        return relationMapper.selectAssetPartRelationList(page, request);
    }
    
    /**
     * 根据资产ID查询关联的备品备件列表
     */
    @Override
    public List<AssetPartRelationVo> selectPartsByAssetId(String assetId) {
        return relationMapper.selectPartsByAssetId(assetId);
    }
    
    /**
     * 根据备品备件ID查询关联的资产列表
     */
    @Override
    public List<AssetPartRelationVo> selectAssetsByPartId(String partId) {
        return relationMapper.selectAssetsByPartId(partId);
    }
    
    /**
     * 检查资产和备品备件是否已关联
     */
    @Override
    public boolean checkRelationExists(String assetId, String partId) {
        int count = relationMapper.checkRelationExists(assetId, partId);
        return count > 0;
    }
    
    /**
     * 删除资产的所有备品备件关联
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByAssetId(String assetId, String username) {
        try {
            int count = relationMapper.deleteByAssetId(assetId, username);
            log.info("删除资产{}的所有备品备件关联，共{}条", assetId, count);
            return true;
        } catch (Exception e) {
            log.error("删除资产的所有备品备件关联失败", e);
            throw e;
        }
    }
    
    /**
     * 删除备品备件的所有资产关联
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByPartId(String partId, String username) {
        try {
            int count = relationMapper.deleteByPartId(partId, username);
            log.info("删除备品备件{}的所有资产关联，共{}条", partId, count);
            return true;
        } catch (Exception e) {
            log.error("删除备品备件的所有资产关联失败", e);
            throw e;
        }
    }
} 