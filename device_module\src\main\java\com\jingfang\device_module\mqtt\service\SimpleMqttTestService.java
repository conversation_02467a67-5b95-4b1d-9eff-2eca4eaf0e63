package com.jingfang.device_module.mqtt.service;

import com.alibaba.fastjson2.JSON;
import com.jingfang.device_module.mqtt.dto.DeviceStatusRequest;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 简单的MQTT测试服务
 */
@Slf4j
@Service
public class SimpleMqttTestService {

    @Value("${mqtt.server-uri:tcp://***************:10883}")
    private String serverUri;

    @Value("${mqtt.client-id:device_monitor_client}")
    private String clientId;

    @Value("${mqtt.username:admin}")
    private String username;

    @Value("${mqtt.password:429498517}")
    private String password;

    @Value("${mqtt.device.status.request.topic:/sys/thing/node/status/get/device_monitor_client}")
    private String requestTopic;

    @Value("${mqtt.device.status.response.topic:/sys/thing/node/status/get_reply/device_monitor_client}")
    private String responseTopic;

    private MqttClient mqttClient;
    private final ConcurrentHashMap<String, CompletableFuture<String>> pendingRequests = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        try {
            log.info("初始化简单MQTT测试服务...");
            log.info("MQTT配置 - 服务器: {}, 客户端ID: {}, 用户名: {}", serverUri, clientId, username);
            
            mqttClient = new MqttClient(serverUri, clientId + "_test");
            
            MqttConnectOptions options = new MqttConnectOptions();
            options.setUserName(username);
            options.setPassword(password.toCharArray());
            options.setCleanSession(true);
            options.setConnectionTimeout(30);
            options.setKeepAliveInterval(60);
            options.setAutomaticReconnect(true);
            
            mqttClient.connect(options);
            log.info("MQTT客户端连接成功");
            
            // 订阅响应主题
            mqttClient.subscribe(responseTopic, (topic, message) -> {
                String payload = new String(message.getPayload());
                log.info("收到MQTT响应 - 主题: {}, 内容: {}", topic, payload);
                
                try {
                    // 简单解析响应，提取ID
                    com.alibaba.fastjson2.JSONObject jsonObject = JSON.parseObject(payload);
                    String id = jsonObject.getString("id");
                    if (id != null) {
                        CompletableFuture<String> future = pendingRequests.remove(id);
                        if (future != null) {
                            future.complete(payload);
                        }
                    }
                } catch (Exception e) {
                    log.error("解析MQTT响应失败", e);
                }
            });
            
            log.info("已订阅响应主题: {}", responseTopic);
            
        } catch (Exception e) {
            log.error("初始化简单MQTT测试服务失败", e);
        }
    }

    @PreDestroy
    public void destroy() {
        try {
            if (mqttClient != null && mqttClient.isConnected()) {
                mqttClient.disconnect();
                mqttClient.close();
            }
        } catch (Exception e) {
            log.error("关闭MQTT客户端失败", e);
        }
    }

    /**
     * 测试发送设备状态查询
     */
    public CompletableFuture<String> testDeviceStatusQuery() {
        String requestId = UUID.randomUUID().toString();
        DeviceStatusRequest request = new DeviceStatusRequest(requestId);
        
        CompletableFuture<String> future = new CompletableFuture<>();
        pendingRequests.put(requestId, future);
        
        try {
            String jsonMessage = JSON.toJSONString(request);
            log.info("发送MQTT测试消息到主题 {}: {}", requestTopic, jsonMessage);
            
            MqttMessage message = new MqttMessage(jsonMessage.getBytes());
            message.setQos(1);
            
            mqttClient.publish(requestTopic, message);
            log.info("MQTT测试消息发送成功，请求ID: {}", requestId);
            
            // 设置超时
            CompletableFuture.delayedExecutor(15, TimeUnit.SECONDS).execute(() -> {
                CompletableFuture<String> timeoutFuture = pendingRequests.remove(requestId);
                if (timeoutFuture != null && !timeoutFuture.isDone()) {
                    timeoutFuture.completeExceptionally(new RuntimeException("MQTT测试超时"));
                }
            });
            
        } catch (Exception e) {
            pendingRequests.remove(requestId);
            future.completeExceptionally(e);
            log.error("发送MQTT测试消息失败", e);
        }
        
        return future;
    }

    /**
     * 检查MQTT连接状态
     */
    public boolean isConnected() {
        return mqttClient != null && mqttClient.isConnected();
    }

    /**
     * 获取连接信息
     */
    public String getConnectionInfo() {
        return String.format("服务器: %s, 客户端ID: %s, 连接状态: %s, 请求主题: %s, 响应主题: %s", 
                serverUri, clientId, isConnected() ? "已连接" : "未连接", requestTopic, responseTopic);
    }
}
