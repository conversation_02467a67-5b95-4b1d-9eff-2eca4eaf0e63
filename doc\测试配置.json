{"testConfig": {"baseUrl": "http://localhost:8080", "token": "your_jwt_token_here", "timeout": 30000, "retryCount": 3}, "testData": {"users": {"testUserId": 1, "responsibleUserId": 1, "assignedUserId": 2, "inventoryUserId": 3}, "departments": {"testDeptId": 100, "deptIds": [100, 101, 102]}, "assets": {"testAssetId": "ASSET001", "testAssetCode": "A001", "testAssets": [{"assetId": "ASSET001", "assetCode": "A001", "assetName": "测试资产1", "location": "办公室A101", "status": 1, "value": 5000.0}, {"assetId": "ASSET002", "assetCode": "A002", "assetName": "测试资产2", "location": "办公室A102", "status": 1, "value": 3000.0}]}, "categories": {"categoryIds": [1, 2, 3]}, "locations": {"locationIds": [1, 2, 3]}}, "testScenarios": {"stocktakingPlan": {"createPlan": {"planName": "自动化测试盘点计划", "planType": 1, "startDate": "2025-01-15", "endDate": "2025-01-30", "responsibleUserId": 1, "remark": "自动化测试创建的盘点计划", "scopeDetail": {"deptIds": [100, 101], "categoryIds": [1, 2], "locationIds": [1, 2], "statusList": [1, 2], "includeSubDept": true, "minValue": 1000.0, "maxValue": 100000.0}}, "searchPlan": {"planName": "测试", "planType": 1, "status": 1, "pageNum": 1, "pageSize": 10}}, "stocktakingTask": {"createTask": {"taskName": "自动化测试任务", "assignedUserId": 2, "expectedCount": 50, "distributionConfig": {"distributionType": 1, "maxAssetCount": 20, "assignedUserIds": [2, 3], "autoAssign": true, "priority": 2}}, "searchTask": {"assignedUserId": 2, "status": 1, "pageNum": 1, "pageSize": 10}}, "stocktakingRecord": {"createRecord": {"assetId": "TEST001", "assetCode": "T001", "foundStatus": 1, "actualLocation": "测试位置", "actualStatus": 1, "inventoryTime": "2025-01-15 10:30:00", "remark": "自动化测试记录", "scanInfo": {"scanType": 1, "scanContent": "QR_TEST001", "scanTime": "2025-01-15 10:30:00", "deviceInfo": "自动化测试设备"}}, "batchRecords": [{"assetId": "BATCH001", "assetCode": "B001", "foundStatus": 1, "actualLocation": "批量测试位置1", "actualStatus": 1, "remark": "批量测试记录1"}, {"assetId": "BATCH002", "assetCode": "B002", "foundStatus": 0, "remark": "批量测试记录2-未找到"}], "searchRecord": {"foundStatus": 1, "pageNum": 1, "pageSize": 10}}, "stocktakingDifference": {"createDifference": {"assetId": "DIFF001", "diffType": 2, "diffReason": "资产丢失", "handleStatus": 1, "handleSuggestion": "建议核销处理", "bookValue": {"assetName": "测试差异资产", "assetCode": "D001", "assetStatus": 1, "location": "原位置", "deptName": "测试部门", "managerName": "测试管理员", "assetValue": 5000.0}, "actualValue": {"foundStatus": 0, "inventoryTime": "2025-01-15 14:00:00", "inventoryUser": "测试盘点员"}}, "handleInfo": {"handleType": 3, "handleUserId": 1, "handleTime": "2025-01-16 09:00:00", "handleResult": "已核销处理", "needApproval": true}, "searchDifference": {"diffType": 2, "handleStatus": 1, "pageNum": 1, "pageSize": 10}}}, "performanceTest": {"concurrentUsers": 10, "requestsPerUser": 100, "maxResponseTime": 2000, "endpoints": [{"name": "查询计划列表", "method": "POST", "path": "/asset/stocktaking/plan/list", "expectedResponseTime": 500}, {"name": "查询任务列表", "method": "POST", "path": "/asset/stocktaking/task/list", "expectedResponseTime": 500}, {"name": "查询记录列表", "method": "POST", "path": "/asset/stocktaking/record/list", "expectedResponseTime": 800}, {"name": "查询差异列表", "method": "POST", "path": "/asset/stocktaking/difference/list", "expectedResponseTime": 600}]}, "validationRules": {"responseStructure": {"success": {"code": 200, "msg": "操作成功", "data": "object"}, "error": {"code": [400, 401, 403, 404, 500], "msg": "string"}}, "dataValidation": {"planId": "^[0-9]+$", "taskId": "^[0-9]+$", "recordId": "^[0-9]+$", "diffId": "^[0-9]+$", "assetCode": "^[A-Z0-9]+$", "dateFormat": "yyyy-MM-dd", "datetimeFormat": "yyyy-MM-dd HH:mm:ss"}}, "reportConfig": {"outputPath": "doc/test-reports/", "formats": ["json", "csv", "html"], "includeDetails": true, "includePerformanceMetrics": true, "includeErrorLogs": true}}