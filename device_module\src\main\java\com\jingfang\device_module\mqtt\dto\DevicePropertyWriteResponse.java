package com.jingfang.device_module.mqtt.dto;

import lombok.Data;

import java.util.List;

/**
 * MQTT设备属性写入响应DTO
 */
@Data
public class DevicePropertyWriteResponse {
    
    /**
     * 响应ID，对应请求ID
     */
    private String id;
    
    /**
     * 版本号
     */
    private String version;
    
    /**
     * 响应代码，0表示成功
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 参数列表
     */
    private List<DevicePropertyWriteResult> params;
    
    /**
     * 设备属性写入结果
     */
    @Data
    public static class DevicePropertyWriteResult {
        /**
         * 客户端ID（设备IP地址）
         */
        private String clientID;
        
        /**
         * 属性写入结果列表
         */
        private List<PropertyWriteResult> properties;
    }
    
    /**
     * 属性写入结果
     */
    @Data
    public static class PropertyWriteResult {
        /**
         * 属性名称
         */
        private String name;
        
        /**
         * 写入结果代码，0表示成功
         */
        private Integer code;
        
        /**
         * 写入结果消息
         */
        private String message;
        
        /**
         * 写入的值
         */
        private String value;
    }
}
