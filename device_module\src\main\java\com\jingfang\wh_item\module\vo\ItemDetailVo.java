package com.jingfang.wh_item.module.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.jingfang.asset_part_relation.module.vo.AssetPartRelationVo;

/**
 * 物品详情VO
 */
@Data
public class ItemDetailVo implements Serializable {
    
    /**
     * 物品ID
     */
    private String itemId;
    
    /**
     * 物品名称
     */
    private String itemName;
    
    /**
     * 物品编码
     */
    private String itemCode;
    
    /**
     * 物品类别(1-消耗品, 2-备品备件)
     */
    private Integer itemType;
    
    /**
     * 物品类别名称
     */
    private String itemTypeName;
    
    /**
     * 规格型号
     */
    private String specModel;
    
    /**
     * 单位
     */
    private String unit;
    
    /**
     * 图片URL
     */
    private String imageUrl;
    
    /**
     * 库存信息列表（支持多仓库）
     */
    private List<ItemInventoryVo> inventoryList;
    
    /**
     * 总库存数量（所有仓库库存之和）
     */
    private BigDecimal totalQuantity;
    
    /**
     * 企业级安全库存
     */
    private BigDecimal safetyStock;
    
    /**
     * 是否有有效期(0-无, 1-有)
     */
    private Integer hasExpiry;
    
    /**
     * 有效期/保质期(天)
     */
    private Integer expiryPeriod;
    
    /**
     * 生产日期
     */
    private Date productionDate;
    
    /**
     * 存储条件
     */
    private String storageCondition;
    
    /**
     * 适用设备型号
     */
    private String applicableDevice;
    
    /**
     * 备件分类(1-关键, 2-常用, 3-次要)
     */
    private Integer partCategory;
    
    /**
     * 备件分类名称
     */
    private String partCategoryName;
    
    /**
     * 建议更换周期(天)
     */
    private Integer replacementCycle;
    
    /**
     * 维修历史关联
     */
    private String maintenanceHistory;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 关联的资产列表（仅备品备件类型有效）
     */
    private List<AssetPartRelationVo> relatedAssets;
    
    private static final long serialVersionUID = 1L;
} 