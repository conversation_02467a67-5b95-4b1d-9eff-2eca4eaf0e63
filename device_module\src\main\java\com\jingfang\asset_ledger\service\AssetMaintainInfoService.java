package com.jingfang.asset_ledger.service;

import com.jingfang.asset_ledger.module.entity.AssetMaintainInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jingfang.asset_ledger.module.vo.AssetDetailMaintainInfoVo;

/**
* <AUTHOR>
* @description 针对表【asset_maintenance(资产维保信息)】的数据库操作Service
* @createDate 2025-05-06 15:10:04
*/
public interface AssetMaintainInfoService extends IService<AssetMaintainInfo> {

    AssetDetailMaintainInfoVo selectMaintainInfoById(String assetId);
}
