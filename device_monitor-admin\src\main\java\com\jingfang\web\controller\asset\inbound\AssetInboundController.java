package com.jingfang.web.controller.asset.inbound;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.asset_inbound.module.dto.AssetInboundDto;
import com.jingfang.asset_inbound.module.entity.AssetInbound;
import com.jingfang.asset_inbound.module.request.AssetInboundSearchRequest;
import com.jingfang.asset_inbound.module.vo.AssetInboundVo;
import com.jingfang.asset_inbound.service.AssetInboundService;
import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.common.core.domain.model.LoginUser;
import com.jingfang.common.utils.BusinessCodeGenerator;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 资产入库控制器
 */
@Slf4j
@RestController
@RequestMapping("/asset/inbound")
public class AssetInboundController extends BaseController {

    @Resource
    private AssetInboundService assetInboundService;

    @Resource
    private BusinessCodeGenerator codeGenerator;
    
    /**
     * 新增资产入库单
     * 
     * @param inboundDto 入库单信息
     * @return 操作结果
     */
    @PostMapping
    public AjaxResult add(@RequestBody @Validated AssetInboundDto inboundDto) {
        try {
            LoginUser user = getLoginUser();
            log.info("新增入库单请求参数：{}", inboundDto);
            String inboundId = codeGenerator.generateCode("ZCRK");
            AssetInbound assetInbound = inboundDto.getMain();
            assetInbound.setInboundId(inboundId);

            assetInbound.setHandlerId(user.getUserId());

            inboundDto.setMain(assetInbound);
            assetInboundService.addAssetInbound(inboundDto, user.getUser().getNickName());
            return AjaxResult.success("新增入库单成功", inboundId);
        } catch (Exception e) {
            log.error("新增入库单异常：", e);
            return AjaxResult.error("新增入库单失败：" + e.getMessage());
        }
    }
    
    /**
     * 编辑资产入库单
     * 
     * @param inboundId 入库单ID
     * @param inboundDto 入库单信息
     * @return 操作结果
     */
    @PreAuthorize("@ss.hasPermi('asset:inbound:edit')")
    @PutMapping("/{inboundId}")
    public AjaxResult edit(
            @PathVariable("inboundId") String inboundId,
            @RequestBody @Validated AssetInboundDto inboundDto) {
        try {
            log.info("编辑入库单请求参数：inboundId={}, data={}", inboundId, inboundDto);
            assetInboundService.updateAssetInbound(inboundId, inboundDto, getNickName());
            return AjaxResult.success("编辑入库单成功");
        } catch (Exception e) {
            log.error("编辑入库单异常：", e);
            return AjaxResult.error("编辑入库单失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取入库单详情
     * 
     * @param inboundId 入库单ID
     * @return 入库单详情
     */
    @PreAuthorize("@ss.hasPermi('asset:inbound:query')")
    @GetMapping("/{inboundId}")
    public AjaxResult getDetail(@PathVariable("inboundId") String inboundId) {
        try {
            log.info("获取入库单详情，ID: {}", inboundId);
            AssetInboundVo detail = assetInboundService.getInboundDetail(inboundId);
            log.info("入库单详情获取成功，ID: {}, 状态: {}, 制单人: {}, 经手人: {}, 审核人: {}", 
                inboundId, detail.getStatus(), 
                detail.getCreatorName(), detail.getHandlerName(), detail.getAuditorName());
            return AjaxResult.success(detail);
        } catch (Exception e) {
            log.error("获取入库单详情异常：", e);
            return AjaxResult.error("获取入库单详情失败：" + e.getMessage());
        }
    }
    
    /**
     * 分页查询入库单列表
     * 
     * @param request 查询条件
     * @return 分页数据
     */
    @PreAuthorize("@ss.hasPermi('asset:inbound:list')")
    @PostMapping("/list")
    public AjaxResult list(@RequestBody AssetInboundSearchRequest request) {
        IPage<AssetInboundVo> vos = assetInboundService.selectInboundList(request);
        return AjaxResult.success(vos);
    }
    
    /**
     * 提交入库单待确认
     * 
     * @param inboundId 入库单ID
     * @return 操作结果
     */
    @PutMapping("/submit/{inboundId}")
    public AjaxResult submit(@PathVariable("inboundId") String inboundId) {
        try {
            assetInboundService.submitInbound(inboundId, getNickName());
            return AjaxResult.success("提交成功，等待经手人确认");
        } catch (Exception e) {
            log.error("提交入库单异常：", e);
            return AjaxResult.error("提交失败：" + e.getMessage());
        }
    }
    
    /**
     * 经手人确认入库单
     * 
     * @param inboundId 入库单ID
     * @param remark 确认意见
     * @return 操作结果
     */
    @PutMapping("/handle/{inboundId}")
    public AjaxResult handle(
            @PathVariable("inboundId") String inboundId,
            @RequestParam(value = "remark", required = false) String remark) {
        try {
            assetInboundService.handleInbound(inboundId, remark, getNickName());
            return AjaxResult.success("确认操作成功");
        } catch (Exception e) {
            log.error("经手人确认入库单异常：", e);
            return AjaxResult.error("确认操作失败：" + e.getMessage());
        }
    }
    
    /**
     * 审核入库单
     * 
     * @param inboundId 入库单ID
     * @param status 审核结果（4-通过，5-退回）
     * @param remark 审核意见
     * @return 操作结果
     */
    @PutMapping("/audit/{inboundId}/{status}")
    public AjaxResult audit(
            @PathVariable("inboundId") String inboundId,
            @PathVariable("status") Integer status,
            @RequestParam(value = "remark", required = false) String remark) {
        try {
            assetInboundService.auditInbound(inboundId, status, remark, getNickName());
            return AjaxResult.success("审核操作成功");
        } catch (Exception e) {
            log.error("审核入库单异常：", e);
            return AjaxResult.error("审核操作失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除入库单
     * 
     * @param inboundIds 入库单ID数组
     * @return 操作结果
     */
    @PreAuthorize("@ss.hasPermi('asset:inbound:remove')")
    @DeleteMapping("/{inboundIds}")
    public AjaxResult remove(@PathVariable String[] inboundIds) {
        try {
            assetInboundService.deleteInbound(inboundIds, getNickName());
            return AjaxResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除入库单异常：", e);
            return AjaxResult.error("删除失败：" + e.getMessage());
        }
    }
}
