package com.jingfang.asset_stocktaking.module.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 盘点记录数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
public class StocktakingRecordDto implements Serializable {

    /**
     * 盘点记录ID（编辑时使用）
     */
    private String recordId;

    /**
     * 盘点任务ID
     */
    @NotBlank(message = "盘点任务ID不能为空")
    private String taskId;

    /**
     * 资产ID
     */
    @NotBlank(message = "资产ID不能为空")
    private String assetId;

    /**
     * 资产编码
     */
    private String assetCode;

    /**
     * 发现状态：1-找到，0-未找到
     */
    @NotNull(message = "发现状态不能为空")
    private Integer foundStatus;

    /**
     * 实际位置
     */
    private String actualLocation;

    /**
     * 实际状态
     */
    private Integer actualStatus;

    /**
     * 盘点时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inventoryTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 批量盘点记录列表
     */
    private List<BatchRecordItem> batchRecords;

    /**
     * 批量记录项内部类
     */
    @Data
    public static class BatchRecordItem implements Serializable {
        
        /**
         * 资产ID
         */
        @NotBlank(message = "资产ID不能为空")
        private String assetId;
        
        /**
         * 资产编码
         */
        private String assetCode;
        
        /**
         * 发现状态：1-找到，0-未找到
         */
        @NotNull(message = "发现状态不能为空")
        private Integer foundStatus;
        
        /**
         * 实际位置
         */
        private String actualLocation;
        
        /**
         * 实际状态
         */
        private Integer actualStatus;
        
        /**
         * 备注
         */
        private String remark;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 扫码盘点信息
     */
    private ScanInfo scanInfo;

    /**
     * 扫码信息内部类
     */
    @Data
    public static class ScanInfo implements Serializable {
        
        /**
         * 扫码方式：1-二维码，2-条形码，3-RFID
         */
        private Integer scanType;
        
        /**
         * 扫码内容
         */
        private String scanContent;
        
        /**
         * 扫码时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date scanTime;
        
        /**
         * 扫码设备信息
         */
        private String deviceInfo;

        private static final long serialVersionUID = 1L;
    }

    private static final long serialVersionUID = 1L;
}
