package com.jingfang.asset_ledger.module.dto;

import com.jingfang.asset_ledger.module.entity.AssetBaseInfo;
import com.jingfang.common.annotation.Excel;
import lombok.Data;

/**
 * 资产Excel导入导出DTO
 */
@Data
public class AssetExcelDto {

    /**
     * 资产编号（Excel导入导出用）
     */
    @Excel(name = "资产编号", prompt = "系统自动生成，导入时可不填")
    private String assetId;

    /**
     * 资产分类ID（Excel导入导出用）
     */
    @Excel(name = "资产分类", readConverterExp = "1=办公设备,2=IT设备,3=会议设备,4=安防设备,5=其它设备", prompt = "请填写数字：1.办公设备 2.IT设备 3.会议设备 4.安防设备 5.其它设备", type = Excel.Type.IMPORT)
    private Integer categoryId;

    /**
     * 资产名称（Excel导入导出用）
     */
    @Excel(name = "资产名称", prompt = "请填写资产名称")
    private String assetName;

    /**
     * 资产状态（Excel导入导出用）
     */
    @Excel(name = "资产状态", readConverterExp = "1=正常,2=维修中,3=报废,4=闲置", prompt = "请填写数字：1.正常 2.维修中 3.报废 4.闲置", type = Excel.Type.IMPORT)
    private Integer assetStatus;

    /**
     * 使用组织（Excel导入导出用）
     */
    @Excel(name = "使用部门", prompt = "请填写使用部门名称")
    private String deptName;

    /**
     * 部门ID（Excel导入导出用）
     */
    private Long deptId;

    /**
     * 规格型号（Excel导入导出用）
     */
    @Excel(name = "规格型号", prompt = "请填写规格型号")
    private String specModel;

    /**
     * 品牌信息（Excel导入导出用）
     */
    @Excel(name = "品牌", prompt = "请填写品牌")
    private String assetBrand;

    /**
     * 资产用途（Excel导入导出用）
     */
    @Excel(name = "资产用途", prompt = "请填写资产用途")
    private String assetPurpose;

    /**
     * 存放位置（Excel导入导出用）
     */
    @Excel(name = "存放位置", readConverterExp = "1=办公室,2=会议室,3=仓库,4=其它", prompt = "请填写数字：1.办公室 2.会议室 3.仓库 4.其它", type = Excel.Type.IMPORT)
    private Integer storageLocation;

    /**
     * 计量单位（Excel导入导出用）
     */
    @Excel(name = "计量单位", prompt = "请填写计量单位")
    private String assetUnit;

    /**
     * 详细地址（Excel导入导出用）
     */
    @Excel(name = "详细位置", prompt = "请填写详细位置")
    private String detailLocation;

    /**
     * 备注（Excel导入导出用）
     */
    @Excel(name = "备注", prompt = "请填写备注")
    private String remark;

    /**
     * 将Excel数据转换为AssetDto
     */
    public AssetDto toAssetDto() {
        AssetDto assetDto = new AssetDto();
        AssetBaseInfo baseInfo = new AssetBaseInfo();
        
        // 设置基本信息
        if (assetId != null && !assetId.isEmpty()) {
            baseInfo.setAssetId(assetId);
        }
        
        if (categoryId != null) {
            baseInfo.setCategoryId(categoryId);
        }
        
        if (assetName != null) {
            baseInfo.setAssetName(assetName);
        }
        
        if (assetStatus != null) {
            baseInfo.setAssetStatus(assetStatus);
        }
        
        if (deptId != null) {
            baseInfo.setDeptId(deptId);
        }
        
        if (specModel != null) {
            baseInfo.setSpecModel(specModel);
        }
        
        if (assetBrand != null) {
            baseInfo.setAssetBrand(assetBrand);
        }
        
        if (assetPurpose != null) {
            baseInfo.setAssetPurpose(assetPurpose);
        }
        
        if (storageLocation != null) {
            baseInfo.setStorageLocation(storageLocation);
        }
        
        if (assetUnit != null) {
            baseInfo.setAssetUnit(assetUnit);
        }
        
        if (detailLocation != null) {
            baseInfo.setDetailLocation(detailLocation);
        }
        
        if (remark != null) {
            baseInfo.setRemark(remark);
        }
        
        assetDto.setBaseInfo(baseInfo);
        return assetDto;
    }
    
    /**
     * 从AssetDto创建AssetExcelDto
     */
    public static AssetExcelDto fromAssetDto(AssetDto assetDto) {
        AssetExcelDto excelDto = new AssetExcelDto();
        AssetBaseInfo baseInfo = assetDto.getBaseInfo();
        
        if (baseInfo != null) {
            excelDto.setAssetId(baseInfo.getAssetId());
            excelDto.setCategoryId(baseInfo.getCategoryId());
            excelDto.setAssetName(baseInfo.getAssetName());
            excelDto.setAssetStatus(baseInfo.getAssetStatus());
            excelDto.setDeptId(baseInfo.getDeptId());
            excelDto.setSpecModel(baseInfo.getSpecModel());
            excelDto.setAssetBrand(baseInfo.getAssetBrand());
            excelDto.setAssetPurpose(baseInfo.getAssetPurpose());
            excelDto.setStorageLocation(baseInfo.getStorageLocation());
            excelDto.setAssetUnit(baseInfo.getAssetUnit());
            excelDto.setDetailLocation(baseInfo.getDetailLocation());
            excelDto.setRemark(baseInfo.getRemark());
        }
        
        return excelDto;
    }
} 