<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.device_module.mapper.DeviceParamMapper">

    <resultMap id="BaseResultMap" type="com.jingfang.device_module.module.entity.DeviceParam">
            <id property="paramId" column="param_id" />
            <result property="deviceId" column="device_id" />
            <result property="paramName" column="param_name" />
            <result property="paramUnit" column="param_unit" />
            <result property="rangeStart" column="range_start" />
            <result property="rangeEnd" column="range_end" />
            <result property="delFlag" column="del_flag" />
            <result property="rwType" column="rw_type" />
            <result property="paramType" column="param_type" />
    </resultMap>

    <sql id="Base_Column_List">
        param_id,device_id,param_name,param_unit,range_start,range_end,del_flag,rw_type,param_type
    </sql>
</mapper>
