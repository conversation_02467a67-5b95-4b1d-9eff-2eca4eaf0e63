package com.jingfang.device_module.controller;

import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.device_module.service.DeviceInfoService;
import com.jingfang.device_module.mqtt.service.MqttDeviceStatusService;
import com.jingfang.device_module.mqtt.service.SimpleMqttTestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.concurrent.CompletableFuture;

/**
 * 设备状态测试控制器
 */
@Slf4j
@RestController
@RequestMapping("/device/status/test")
public class DeviceStatusTestController {

    @Resource
    private DeviceInfoService deviceInfoService;

    @Resource
    private MqttDeviceStatusService mqttDeviceStatusService;

    @Resource
    private SimpleMqttTestService simpleMqttTestService;

    /**
     * 测试MQTT设备状态查询
     */
    @GetMapping("/mqtt")
    public AjaxResult testMqttDeviceStatus() {
        try {
            String result = deviceInfoService.mqttDeviceStatusTest();
            return AjaxResult.success("MQTT设备状态查询成功", result);
        } catch (Exception e) {
            log.error("MQTT设备状态查询测试失败", e);
            return AjaxResult.error("MQTT设备状态查询失败: " + e.getMessage());
        }
    }

    /**
     * 测试设备连接状态检测（包含MQTT和Socket回退）
     */
    @GetMapping("/connection")
    public AjaxResult testDeviceConnectionStatus() {
        try {
            String result = deviceInfoService.DeviceConnectStatusTest();
            return AjaxResult.success("设备连接状态检测成功", result);
        } catch (Exception e) {
            log.error("设备连接状态检测失败", e);
            return AjaxResult.error("设备连接状态检测失败: " + e.getMessage());
        }
    }

    /**
     * 刷新设备缓存和状态
     */
    @GetMapping("/refresh")
    public AjaxResult refreshDeviceCacheAndStatus() {
        try {
            String result = deviceInfoService.refreshCatch();
            return AjaxResult.success("设备缓存和状态刷新成功", result);
        } catch (Exception e) {
            log.error("设备缓存和状态刷新失败", e);
            return AjaxResult.error("设备缓存和状态刷新失败: " + e.getMessage());
        }
    }

    /**
     * 测试MQTT连接状态
     */
    @GetMapping("/mqtt-connection")
    public AjaxResult testMqttConnection() {
        try {
            boolean isConnected = mqttDeviceStatusService.testMqttConnection();
            if (isConnected) {
                return AjaxResult.success("MQTT连接状态正常");
            } else {
                return AjaxResult.error("MQTT连接状态异常");
            }
        } catch (Exception e) {
            log.error("测试MQTT连接状态失败", e);
            return AjaxResult.error("测试MQTT连接状态失败: " + e.getMessage());
        }
    }

    /**
     * 简单MQTT测试
     */
    @GetMapping("/simple-mqtt")
    public AjaxResult testSimpleMqtt() {
        try {
            log.info("开始简单MQTT测试");

            // 检查连接状态
            String connectionInfo = simpleMqttTestService.getConnectionInfo();
            log.info("MQTT连接信息: {}", connectionInfo);

            if (!simpleMqttTestService.isConnected()) {
                return AjaxResult.error("MQTT未连接: " + connectionInfo);
            }

            // 发送测试消息
            CompletableFuture<String> future = simpleMqttTestService.testDeviceStatusQuery();
            String response = future.get(20, java.util.concurrent.TimeUnit.SECONDS);

            return AjaxResult.success("简单MQTT测试成功", response);
        } catch (Exception e) {
            log.error("简单MQTT测试失败", e);
            return AjaxResult.error("简单MQTT测试失败: " + e.getMessage());
        }
    }
}
