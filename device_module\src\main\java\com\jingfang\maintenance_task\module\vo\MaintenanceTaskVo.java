package com.jingfang.maintenance_task.module.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 维护任务VO
 */
@Data
public class MaintenanceTaskVo {
    
    /**
     * 维护任务ID
     */
    private String taskId;
    
    /**
     * 维护计划ID
     */
    private String planId;
    
    /**
     * 计划名称
     */
    private String planName;
    
    /**
     * 任务标题
     */
    private String taskTitle;
    
    /**
     * 资产ID
     */
    private String assetId;
    
    /**
     * 资产名称
     */
    private String assetName;
    
    /**
     * 资产编号
     */
    private String assetCode;
    
    /**
     * 资产位置
     */
    private String assetLocation;
    
    /**
     * 维护事项描述
     */
    private String maintenanceItems;
    
    /**
     * 计划执行时间
     */
    private Date scheduledTime;
    
    /**
     * 实际开始时间
     */
    private Date actualStartTime;
    
    /**
     * 实际完成时间
     */
    private Date actualEndTime;
    
    /**
     * 负责人类型(1-个人, 2-部门)
     */
    private Integer responsibleType;
    
    /**
     * 负责人类型名称
     */
    private String responsibleTypeName;
    
    /**
     * 负责人ID（用户ID或部门ID）
     */
    private Long responsibleId;
    
    /**
     * 负责人名称
     */
    private String responsibleName;
    
    /**
     * 实际执行人ID
     */
    private Long executorId;
    
    /**
     * 执行人名称
     */
    private String executorName;
    
    /**
     * 任务状态(1-待执行, 2-执行中, 3-草稿, 4-待审核, 5-审核通过, 6-审核不通过, 7-已完成, 8-已取消)
     */
    private Integer status;
    
    /**
     * 任务状态名称
     */
    private String statusName;
    
    /**
     * 任务优先级(1-低, 2-中, 3-高, 4-紧急)
     */
    private Integer priority;
    
    /**
     * 优先级名称
     */
    private String priorityName;
    
    /**
     * 检查结果
     */
    private String checkResult;
    
    /**
     * 维护结果描述
     */
    private String resultDescription;
    
    /**
     * 问题描述
     */
    private String problemDescription;
    
    /**
     * 解决方案
     */
    private String solution;
    
    /**
     * 审核人ID
     */
    private Long reviewerId;
    
    /**
     * 审核人名称
     */
    private String reviewerName;
    
    /**
     * 审核时间
     */
    private Date reviewTime;
    
    /**
     * 审核意见
     */
    private String reviewComment;
    
    /**
     * 委派原因
     */
    private String delegateReason;
    
    /**
     * 委派人ID
     */
    private Long delegateBy;
    
    /**
     * 委派人名称
     */
    private String delegateByName;
    
    /**
     * 委派时间
     */
    private Date delegateTime;
    
    /**
     * 备注说明
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 是否逾期
     */
    private Boolean overdue;
    
    /**
     * 逾期天数
     */
    private Integer overdueDays;
    
    /**
     * 备品备件使用记录列表
     */
    private List<MaintenanceTaskPartVo> partList;
    
    /**
     * 维护任务备品备件使用记录VO
     */
    @Data
    public static class MaintenanceTaskPartVo {
        
        /**
         * 记录ID
         */
        private String recordId;
        
        /**
         * 维护任务ID
         */
        private String taskId;
        
        /**
         * 备品备件ID
         */
        private String partId;
        
        /**
         * 备品备件名称
         */
        private String partName;
        
        /**
         * 规格型号
         */
        private String specModel;
        
        /**
         * 单位
         */
        private String unit;
        
        /**
         * 计划使用数量
         */
        private BigDecimal plannedQuantity;
        
        /**
         * 实际使用数量
         */
        private BigDecimal actualQuantity;
        
        /**
         * 当前库存数量
         */
        private BigDecimal currentStock;
        
        /**
         * 使用状态(1-计划使用, 2-已使用, 3-未使用)
         */
        private Integer useStatus;
        
        /**
         * 使用状态名称
         */
        private String useStatusName;
        
        /**
         * 备注说明
         */
        private String remark;
        
        /**
         * 创建时间
         */
        private Date createTime;
    }
} 