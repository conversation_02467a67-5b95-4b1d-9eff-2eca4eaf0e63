package com.jingfang.asset_ledger.module.vo;

import com.jingfang.asset_ledger.module.dto.AttachmentDto;
import com.jingfang.asset_ledger.module.entity.AssetBaseInfo;
import com.jingfang.common.core.domain.entity.UserLite;
import lombok.Data;

import java.util.List;

@Data
public class AssetDetailBaseInfoVo {
    /**
     * 资产编号
     */
    private String assetId;

    /**
     * 资产分类ID
     */
    private Integer categoryId;

    /**
     * 资产名称
     */
    private String assetName;

    /**
     * 资产状态
     */
    private Integer assetStatus;

    /**
     * 使用组织
     */
    private String deptName;

    private Long deptId;

    /**
     * 管理人员
     */
    private List<UserLite> managers;

    /**
     * 规格型号
     */
    private String specModel;

    /**
     * 品牌信息
     */
    private String assetBrand;

    /**
     * 资产用途
     */
    private String assetPurpose;

    /**
     * 存放位置
     */
    private Integer storageLocation;

    /**
     * 计量单位
     */
    private String assetUnit;

    /**
     * 详细地址
     */
    private String detailLocation;

    /**
     * 设备图片
     */
    private List<String> pictureUrls;

    private List<AttachmentDto> attachmentList;

    /**
     * 备注
     */
    private String remark;

    /**
     * 将VO对象转换为AssetBaseInfo实体对象
     * @return AssetBaseInfo对象
     */
    public AssetBaseInfo toAssetBaseInfo() {
        AssetBaseInfo baseInfo = new AssetBaseInfo();
        baseInfo.setAssetId(this.assetId);
        baseInfo.setCategoryId(this.categoryId);
        baseInfo.setAssetName(this.assetName);
        baseInfo.setAssetStatus(this.assetStatus);
        baseInfo.setDeptId(this.deptId);
        baseInfo.setSpecModel(this.specModel);
        baseInfo.setAssetBrand(this.assetBrand);
        baseInfo.setAssetPurpose(this.assetPurpose);
        baseInfo.setStorageLocation(this.storageLocation);
        baseInfo.setAssetUnit(this.assetUnit);
        baseInfo.setDetailLocation(this.detailLocation);
        baseInfo.setRemark(this.remark);
        baseInfo.setPictureUrls(this.pictureUrls);
        baseInfo.setAttachmentList(this.attachmentList);
        return baseInfo;
    }
}
