# 设备控制参数接口说明

## 概述

新增的设备控制参数接口专门用于获取和管理设备的控制相关参数，只展示 `param_type = 3` 的控制参数。

## 接口详情

### 获取设备控制参数

**接口路径**: `GET /device/control/params`

**请求参数**:
- `deviceId` (Long): 设备ID

**功能说明**:
- 只查询 `param_type = 3` 的控制参数
- 根据 `rw_type` 字段决定是否查询实时值
- 支持MQTT实时查询可读的控制参数
- 只写参数不显示实时值

## 控制参数类型

### 1. 只读控制参数 (rw_type = 0)
- **用途**: 查看当前控制状态
- **示例**: 当前工作模式、运行状态等
- **特点**: 可以通过MQTT查询实时值

### 2. 只写控制参数 (rw_type = 1)
- **用途**: 发送控制指令
- **示例**: 启动命令、停止命令、复位命令等
- **特点**: 不显示实时值，只显示 "--"

### 3. 读写控制参数 (rw_type = 2)
- **用途**: 设定值和配置参数
- **示例**: 温度设定值、速度设定值、工作模式设置等
- **特点**: 可以查询当前设定值，也可以修改

## 响应格式

```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "paramId": 3,
      "paramName": "温度设定值",
      "paramValue": "25.0°C",
      "rangeStart": 10.0,
      "rangeEnd": 40.0,
      "rwType": 2,
      "paramType": 3,
      "alertValue": false
    },
    {
      "paramId": 4,
      "paramName": "启动命令",
      "paramValue": "--",
      "rangeStart": null,
      "rangeEnd": null,
      "rwType": 1,
      "paramType": 3,
      "alertValue": false
    }
  ],
  "total": 2
}
```

## 查询逻辑

### 1. 设备状态检查
- 检查设备是否在线
- 获取设备基础信息

### 2. 参数配置查询
```sql
SELECT * FROM device_param 
WHERE device_id = ? 
  AND del_flag = 0 
  AND param_type = 3
```

### 3. MQTT实时查询
- 只查询可读的控制参数 (`rw_type = 0` 或 `rw_type = 2`)
- 只写参数直接显示 "--"
- 支持超时控制（15秒）

### 4. 错误处理
- 设备离线：返回空列表
- MQTT查询失败：返回配置信息但值显示 "--"
- 异常情况：返回基础配置信息

## 使用示例

### 1. 基本查询
```bash
curl "http://localhost:8080/device/control/params?deviceId=1"
```

### 2. 响应示例
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "paramId": 5,
      "paramName": "工作模式",
      "paramValue": "自动模式",
      "rangeStart": 0.0,
      "rangeEnd": 3.0,
      "rwType": 2,
      "paramType": 3,
      "alertValue": false
    },
    {
      "paramId": 6,
      "paramName": "启动命令",
      "paramValue": "--",
      "rangeStart": null,
      "rangeEnd": null,
      "rwType": 1,
      "paramType": 3,
      "alertValue": false
    }
  ],
  "total": 2
}
```

## 配置示例

### 1. 设定值参数（读写）
```sql
INSERT INTO device_param (
    device_id, param_name, param_unit, 
    range_start, range_end, 
    rw_type, param_type, del_flag
) VALUES (
    1, '温度设定值', '°C', 
    10.0, 40.0, 
    2, 3, 0  -- 读写，控制参数
);
```

### 2. 控制指令（只写）
```sql
INSERT INTO device_param (
    device_id, param_name, param_unit, 
    range_start, range_end, 
    rw_type, param_type, del_flag
) VALUES (
    1, '启动命令', '', 
    NULL, NULL, 
    1, 3, 0  -- 只写，控制参数
);
```

### 3. 状态查询（只读）
```sql
INSERT INTO device_param (
    device_id, param_name, param_unit, 
    range_start, range_end, 
    rw_type, param_type, del_flag
) VALUES (
    1, '当前工作模式', '', 
    0.0, 3.0, 
    0, 3, 0  -- 只读，控制参数
);
```

## 与其他接口的区别

### 运行参数接口 (`/device/list/detail/normal`)
- 查询 `param_type = 0` 的一般参数
- 主要用于监控设备运行状态
- 通常都是只读参数

### 告警信息接口 (`/device/alert/info`)
- 查询 `param_type = 1` 的告警参数
- 主要用于故障诊断
- 包含告警状态判断

### 控制参数接口 (`/device/control/params`)
- 查询 `param_type = 3` 的控制参数
- 主要用于设备控制和配置
- 支持多种读写类型

## 注意事项

1. **权限控制**: 控制参数涉及设备操作，需要适当的权限控制
2. **参数验证**: 写入控制参数时需要验证参数范围和格式
3. **操作日志**: 建议记录控制参数的修改日志
4. **安全性**: 控制指令需要确保安全性，避免误操作
5. **实时性**: 控制参数的查询和设置需要保证实时性

## 后续扩展

1. **参数写入**: 可以扩展支持通过MQTT写入控制参数
2. **批量控制**: 支持批量设置多个控制参数
3. **控制历史**: 记录控制参数的修改历史
4. **权限管理**: 基于用户角色控制参数访问权限
5. **参数验证**: 增强参数值的验证和范围检查

通过这个新接口，可以更好地管理和控制设备的各种控制参数，为设备的精确控制提供支持！
