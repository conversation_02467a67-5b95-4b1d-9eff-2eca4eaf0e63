package com.jingfang.asset_disposal.module.dto;

import lombok.Data;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 资产处置执行DTO
 */
@Data
public class AssetDisposalExecutionDto {
    
    /**
     * 实际处置价值
     */
    private BigDecimal actualDisposalValue;
    
    /**
     * 处置凭证（发票、收据等）
     */
    private String disposalCertificate;
    
    /**
     * 执行说明
     */
    private String executionDescription;
    
    /**
     * 执行状态(1-执行中, 2-已完成, 3-异常)
     */
    @NotNull(message = "执行状态不能为空")
    private Integer executionStatus;
} 