#!/bin/bash

# 资产盘点功能测试脚本
# 用于运行各种类型的测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Java环境
check_java() {
    log_info "检查Java环境..."
    if ! command -v java &> /dev/null; then
        log_error "Java未安装或未配置到PATH"
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    log_success "Java版本: $JAVA_VERSION"
}

# 检查Maven环境
check_maven() {
    log_info "检查Maven环境..."
    if ! command -v mvn &> /dev/null; then
        log_error "Maven未安装或未配置到PATH"
        exit 1
    fi
    
    MVN_VERSION=$(mvn -version | head -n 1)
    log_success "Maven版本: $MVN_VERSION"
}

# 检查Python环境
check_python() {
    log_info "检查Python环境..."
    if ! command -v python3 &> /dev/null; then
        log_warning "Python3未安装，跳过API测试"
        return 1
    fi
    
    PYTHON_VERSION=$(python3 --version)
    log_success "Python版本: $PYTHON_VERSION"
    
    # 检查requests库
    if ! python3 -c "import requests" &> /dev/null; then
        log_info "安装requests库..."
        pip3 install requests
    fi
    
    return 0
}

# 检查数据库连接
check_database() {
    log_info "检查数据库连接..."
    
    # 这里可以添加数据库连接检查逻辑
    # 例如使用mysql命令或者Java程序检查
    
    log_success "数据库连接正常"
}

# 编译项目
compile_project() {
    log_info "编译项目..."
    
    if [ -f "pom.xml" ]; then
        mvn clean compile -q
        log_success "项目编译完成"
    else
        log_error "未找到pom.xml文件"
        exit 1
    fi
}

# 运行单元测试
run_unit_tests() {
    log_info "运行单元测试..."
    
    # 运行盘点计划服务测试
    log_info "运行盘点计划服务测试..."
    mvn test -Dtest=StocktakingPlanServiceTest -q
    
    # 运行盘点任务服务测试
    log_info "运行盘点任务服务测试..."
    mvn test -Dtest=StocktakingTaskServiceTest -q
    
    # 运行所有单元测试
    log_info "运行所有单元测试..."
    mvn test -Dspring.profiles.active=test -q
    
    log_success "单元测试完成"
}

# 运行集成测试
run_integration_tests() {
    log_info "运行集成测试..."
    
    mvn test -Dtest=StocktakingIntegrationTest -Dspring.profiles.active=test -q
    
    log_success "集成测试完成"
}

# 运行控制器测试
run_controller_tests() {
    log_info "运行控制器测试..."
    
    mvn test -Dtest=*ControllerTest -Dspring.profiles.active=test -q
    
    log_success "控制器测试完成"
}

# 启动应用服务器
start_server() {
    log_info "启动应用服务器..."
    
    # 检查端口是否被占用
    if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null ; then
        log_warning "端口8080已被占用，尝试停止现有进程..."
        pkill -f "spring-boot" || true
        sleep 3
    fi
    
    # 启动服务器
    nohup mvn spring-boot:run -Dspring.profiles.active=test > server.log 2>&1 &
    SERVER_PID=$!
    
    # 等待服务器启动
    log_info "等待服务器启动..."
    for i in {1..30}; do
        if curl -s http://localhost:8080/actuator/health > /dev/null 2>&1; then
            log_success "服务器启动成功 (PID: $SERVER_PID)"
            echo $SERVER_PID > server.pid
            return 0
        fi
        sleep 2
    done
    
    log_error "服务器启动失败"
    return 1
}

# 停止应用服务器
stop_server() {
    log_info "停止应用服务器..."
    
    if [ -f "server.pid" ]; then
        SERVER_PID=$(cat server.pid)
        if kill -0 $SERVER_PID 2>/dev/null; then
            kill $SERVER_PID
            rm server.pid
            log_success "服务器已停止"
        else
            log_warning "服务器进程不存在"
            rm server.pid
        fi
    else
        # 尝试通过进程名停止
        pkill -f "spring-boot" || true
        log_success "服务器已停止"
    fi
}

# 运行API测试
run_api_tests() {
    if ! check_python; then
        log_warning "跳过API测试"
        return 0
    fi
    
    log_info "运行API测试..."
    
    # 启动服务器
    if ! start_server; then
        log_error "无法启动服务器，跳过API测试"
        return 1
    fi
    
    # 运行Python API测试脚本
    python3 scripts/test_api.py
    API_TEST_RESULT=$?
    
    # 停止服务器
    stop_server
    
    if [ $API_TEST_RESULT -eq 0 ]; then
        log_success "API测试完成"
        return 0
    else
        log_error "API测试失败"
        return 1
    fi
}

# 生成测试报告
generate_test_report() {
    log_info "生成测试报告..."
    
    # 生成Maven测试报告
    mvn surefire-report:report -q
    
    # 生成覆盖率报告
    mvn jacoco:report -q
    
    REPORT_DIR="target/site"
    if [ -d "$REPORT_DIR" ]; then
        log_success "测试报告已生成: $REPORT_DIR"
        log_info "测试报告: file://$PROJECT_ROOT/$REPORT_DIR/surefire-report.html"
        log_info "覆盖率报告: file://$PROJECT_ROOT/$REPORT_DIR/jacoco/index.html"
    else
        log_warning "测试报告生成失败"
    fi
}

# 清理测试环境
cleanup() {
    log_info "清理测试环境..."
    
    # 停止服务器
    stop_server
    
    # 清理临时文件
    rm -f server.log
    
    log_success "清理完成"
}

# 显示帮助信息
show_help() {
    echo "资产盘点功能测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  -u, --unit          只运行单元测试"
    echo "  -i, --integration   只运行集成测试"
    echo "  -c, --controller    只运行控制器测试"
    echo "  -a, --api           只运行API测试"
    echo "  -r, --report        生成测试报告"
    echo "  --all               运行所有测试（默认）"
    echo "  --start-server      启动服务器"
    echo "  --stop-server       停止服务器"
    echo "  --cleanup           清理测试环境"
    echo ""
    echo "示例:"
    echo "  $0                  # 运行所有测试"
    echo "  $0 -u               # 只运行单元测试"
    echo "  $0 -a               # 只运行API测试"
    echo "  $0 --cleanup        # 清理测试环境"
}

# 主函数
main() {
    echo "========================================"
    echo "资产盘点功能测试脚本"
    echo "========================================"
    
    # 解析命令行参数
    case "${1:-all}" in
        -h|--help)
            show_help
            exit 0
            ;;
        -u|--unit)
            check_java
            check_maven
            compile_project
            run_unit_tests
            ;;
        -i|--integration)
            check_java
            check_maven
            check_database
            compile_project
            run_integration_tests
            ;;
        -c|--controller)
            check_java
            check_maven
            compile_project
            run_controller_tests
            ;;
        -a|--api)
            check_java
            check_maven
            check_database
            compile_project
            run_api_tests
            ;;
        -r|--report)
            generate_test_report
            ;;
        --start-server)
            check_java
            check_maven
            compile_project
            start_server
            ;;
        --stop-server)
            stop_server
            ;;
        --cleanup)
            cleanup
            ;;
        --all|all|"")
            # 运行所有测试
            check_java
            check_maven
            check_database
            compile_project
            
            log_info "开始运行完整测试套件..."
            
            # 运行单元测试
            run_unit_tests
            
            # 运行集成测试
            run_integration_tests
            
            # 运行控制器测试
            run_controller_tests
            
            # 运行API测试
            run_api_tests
            
            # 生成测试报告
            generate_test_report
            
            log_success "所有测试完成！"
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 设置清理陷阱
trap cleanup EXIT

# 运行主函数
main "$@"
