package com.jingfang.maintenance_task.module.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jingfang.maintenance_task.module.entity.MaintenanceTaskPart;
import com.jingfang.maintenance_task.module.vo.MaintenanceTaskVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 维护任务备品备件Mapper接口
 */
@Mapper
public interface MaintenanceTaskPartMapper extends BaseMapper<MaintenanceTaskPart> {
    
    /**
     * 根据任务ID查询备品备件使用记录
     */
    List<MaintenanceTaskVo.MaintenanceTaskPartVo> selectPartsByTaskId(@Param("taskId") String taskId);
    
    /**
     * 批量插入备品备件使用记录
     */
    int batchInsert(@Param("partList") List<MaintenanceTaskPart> partList);
    
    /**
     * 根据任务ID删除备品备件使用记录
     */
    int deleteByTaskId(@Param("taskId") String taskId);
    
    /**
     * 更新备品备件使用状态
     */
    int updateUseStatus(@Param("recordId") String recordId, @Param("useStatus") Integer useStatus, @Param("updateBy") String updateBy);
    
    /**
     * 批量更新备品备件使用记录
     */
    int batchUpdate(@Param("partList") List<MaintenanceTaskPart> partList);
} 