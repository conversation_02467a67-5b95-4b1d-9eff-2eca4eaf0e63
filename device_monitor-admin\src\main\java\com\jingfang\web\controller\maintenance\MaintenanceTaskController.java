package com.jingfang.web.controller.maintenance;

import com.baomidou.mybatisplus.core.metadata.IPage;

import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.common.utils.poi.ExcelUtil;
import com.jingfang.maintenance_task.module.dto.MaintenanceTaskDto;
import com.jingfang.maintenance_task.module.request.MaintenanceTaskSearchRequest;
import com.jingfang.maintenance_task.module.service.MaintenanceTaskService;
import com.jingfang.maintenance_task.module.vo.MaintenanceTaskVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 维护任务控制器
 */
@RestController
@RequestMapping("/maintenance/task")
public class MaintenanceTaskController extends BaseController {
    
    @Autowired
    private MaintenanceTaskService maintenanceTaskService;
    
    /**
     * 分页查询维护任务列表
     */
    @PreAuthorize("@ss.hasPermi('maintenance:task:list')")
    @GetMapping("/list")
    public AjaxResult list(MaintenanceTaskSearchRequest request) {
        IPage<MaintenanceTaskVo> page = maintenanceTaskService.getTaskPage(request);
        return AjaxResult.success(page);
    }
    
    /**
     * 获取维护任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('maintenance:task:query')")
    @GetMapping(value = "/{taskId}")
    public AjaxResult getInfo(@PathVariable("taskId") String taskId) {
        return success(maintenanceTaskService.getTaskById(taskId));
    }
    
    /**
     * 新增维护任务
     */
    @PreAuthorize("@ss.hasPermi('maintenance:task:add')")
    @PostMapping
    public AjaxResult add(@Validated @RequestBody MaintenanceTaskDto taskDto) {
        String taskId = maintenanceTaskService.addTask(taskDto);
        return AjaxResult.success("新增成功", taskId);
    }
    
    /**
     * 修改维护任务
     */
    @PreAuthorize("@ss.hasPermi('maintenance:task:edit')")
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody MaintenanceTaskDto taskDto) {
        return AjaxResult.success(maintenanceTaskService.updateTask(taskDto));
    }
    
    /**
     * 删除维护任务
     */
    @PreAuthorize("@ss.hasPermi('maintenance:task:remove')")
    @DeleteMapping("/{taskIds}")
    public AjaxResult remove(@PathVariable String[] taskIds) {
        return AjaxResult.success(maintenanceTaskService.deleteTasks(Arrays.asList(taskIds)));
    }
    
    /**
     * 开始执行任务
     */
    @PreAuthorize("@ss.hasPermi('maintenance:task:execute')")
    @PostMapping("/start/{taskId}")
    public AjaxResult startTask(@PathVariable("taskId") String taskId) {
        return AjaxResult.success(maintenanceTaskService.startTask(taskId));
    }
    
    /**
     * 保存为草稿
     */
    @PreAuthorize("@ss.hasPermi('maintenance:task:execute')")
    @PostMapping("/draft")
    public AjaxResult saveDraft(@Validated @RequestBody MaintenanceTaskDto taskDto) {
        return AjaxResult.success(maintenanceTaskService.saveDraft(taskDto));
    }
    
    /**
     * 提交任务结果
     */
    @PreAuthorize("@ss.hasPermi('maintenance:task:execute')")
    @PostMapping("/submit")
    public AjaxResult submitTask(@Validated @RequestBody MaintenanceTaskDto taskDto) {
        return AjaxResult.success(maintenanceTaskService.submitTask(taskDto));
    }
    
    /**
     * 审核任务
     */
    @PreAuthorize("@ss.hasPermi('maintenance:task:review')")
    @PostMapping("/review")
    public AjaxResult reviewTask(@RequestBody Map<String, Object> params) {
        String taskId = (String) params.get("taskId");
        Integer reviewResult = (Integer) params.get("reviewResult");
        String reviewComment = (String) params.get("reviewComment");
        return toAjax(maintenanceTaskService.reviewTask(taskId, reviewResult, reviewComment));
    }
    
    /**
     * 委派任务
     */
    @PreAuthorize("@ss.hasPermi('maintenance:task:delegate')")
    @PostMapping("/delegate")
    public AjaxResult delegateTask(@RequestBody Map<String, Object> params) {
        String taskId = (String) params.get("taskId");
        Integer responsibleType = (Integer) params.get("responsibleType");
        Long responsibleId = Long.valueOf(params.get("responsibleId").toString());
        String delegateReason = (String) params.get("delegateReason");
        return toAjax(maintenanceTaskService.delegateTask(taskId, responsibleType, responsibleId, delegateReason));
    }
    
    /**
     * 取消任务
     */
    @PreAuthorize("@ss.hasPermi('maintenance:task:cancel')")
    @PostMapping("/cancel")
    public AjaxResult cancelTask(@RequestBody Map<String, Object> params) {
        String taskId = (String) params.get("taskId");
        String cancelReason = (String) params.get("cancelReason");
        return toAjax(maintenanceTaskService.cancelTask(taskId, cancelReason));
    }
    
    /**
     * 查询我的任务
     */
    @PreAuthorize("@ss.hasPermi('maintenance:task:myTasks')")
    @GetMapping("/myTasks")
    public AjaxResult getMyTasks(@RequestParam(required = false) List<Integer> statusList) {
        List<MaintenanceTaskVo> taskList = maintenanceTaskService.getMyTasks(statusList);
        return success(taskList);
    }
    
    /**
     * 查询待审核任务
     */
    @PreAuthorize("@ss.hasPermi('maintenance:task:review')")
    @GetMapping("/pendingReview")
    public AjaxResult getPendingReviewTasks() {
        List<MaintenanceTaskVo> taskList = maintenanceTaskService.getPendingReviewTasks();
        return success(taskList);
    }
    
    /**
     * 查询即将到期的任务
     */
    @PreAuthorize("@ss.hasPermi('maintenance:task:list')")
    @GetMapping("/upcoming")
    public AjaxResult getUpcomingTasks(@RequestParam(defaultValue = "7") Integer days) {
        List<MaintenanceTaskVo> taskList = maintenanceTaskService.getUpcomingTasks(days);
        return success(taskList);
    }
    
    /**
     * 查询已逾期的任务
     */
    @PreAuthorize("@ss.hasPermi('maintenance:task:list')")
    @GetMapping("/overdue")
    public AjaxResult getOverdueTasks() {
        List<MaintenanceTaskVo> taskList = maintenanceTaskService.getOverdueTasks();
        return success(taskList);
    }
    
    /**
     * 根据维护计划ID查询任务列表
     */
    @PreAuthorize("@ss.hasPermi('maintenance:task:list')")
    @GetMapping("/plan/{planId}")
    public AjaxResult getTasksByPlanId(@PathVariable("planId") String planId) {
        List<MaintenanceTaskVo> taskList = maintenanceTaskService.getTasksByPlanId(planId);
        return success(taskList);
    }
    
    /**
     * 获取任务统计信息
     */
    @PreAuthorize("@ss.hasPermi('maintenance:task:list')")
    @GetMapping("/statistics")
    public AjaxResult getTaskStatistics() {
        Map<String, Integer> statistics = maintenanceTaskService.getTaskStatistics();
        return success(statistics);
    }
    
    /**
     * 手动生成维护任务
     */
    @PreAuthorize("@ss.hasPermi('maintenance:task:generate')")
    @PostMapping("/generate")
    public AjaxResult generateTasks() {
        maintenanceTaskService.generateMaintenanceTasks();
        return success("任务生成完成");
    }
    
    /**
     * 根据维护计划生成任务
     */
    @PreAuthorize("@ss.hasPermi('maintenance:task:generate')")
    @PostMapping("/generate/{planId}")
    public AjaxResult generateTaskFromPlan(@PathVariable("planId") String planId) {
        return toAjax(maintenanceTaskService.generateTaskFromPlan(planId));
    }
    
    /**
     * 导出维护任务列表
     */
    @PreAuthorize("@ss.hasPermi('maintenance:task:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, MaintenanceTaskSearchRequest request) {
        IPage<MaintenanceTaskVo> page = maintenanceTaskService.getTaskPage(request);
        ExcelUtil<MaintenanceTaskVo> util = new ExcelUtil<>(MaintenanceTaskVo.class);
        util.exportExcel(response, page.getRecords(), "维护任务数据");
    }
} 