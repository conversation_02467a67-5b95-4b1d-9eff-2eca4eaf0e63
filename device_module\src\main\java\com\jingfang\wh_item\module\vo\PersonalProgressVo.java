package com.jingfang.wh_item.module.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 个人盘点进度VO
 * 用于显示个人盘点进度信息
 */
@Data
public class PersonalProgressVo implements Serializable {

    /**
     * 分配总数
     */
    private Integer totalAssigned;

    /**
     * 已完成数
     */
    private Integer completed;

    /**
     * 有差异数
     */
    private Integer withDifference;

    /**
     * 完成率
     */
    private BigDecimal completionRate;

    /**
     * 当前进行的盘点任务数
     */
    private Integer activeTaskCount;

    private static final long serialVersionUID = 1L;
}
