package com.jingfang.wh_item.module.request;

import lombok.Data;

/**
 * 物品查询请求
 */
@Data
public class ItemSearchRequest {
    /**
     * 当前页码
     */
    private Integer pageNum;
    
    /**
     * 每页数量
     */
    private Integer pageSize;
    
    /**
     * 物品ID
     */
    private String itemId;
    
    /**
     * 物品名称
     */
    private String itemName;
    
    /**
     * 物品编码
     */
    private String itemCode;
    
    /**
     * 物品类型(1-消耗品, 2-备品备件)
     */
    private Integer itemType;
    
    /**
     * 规格型号
     */
    private String specModel;
    
    /**
     * 库存状态(1-正常, 2-不足, 3-过剩)
     */
    private Integer stockStatus;
    
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    
    /**
     * 是否有效期(0-无, 1-有)
     */
    private Integer hasExpiry;
} 