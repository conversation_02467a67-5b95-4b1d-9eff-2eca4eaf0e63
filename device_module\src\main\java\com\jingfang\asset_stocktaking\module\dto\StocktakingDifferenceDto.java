package com.jingfang.asset_stocktaking.module.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 盘点差异数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
public class StocktakingDifferenceDto implements Serializable {

    /**
     * 差异ID（编辑时使用）
     */
    private String diffId;

    /**
     * 盘点计划ID
     */
    @NotBlank(message = "盘点计划ID不能为空")
    private String planId;

    /**
     * 资产ID
     */
    private String assetId;

    /**
     * 差异类型：1-盘盈，2-盘亏，3-状态差异，4-位置差异
     */
    @NotNull(message = "差异类型不能为空")
    private Integer diffType;

    /**
     * 差异原因
     */
    private String diffReason;

    /**
     * 处理状态：1-待处理，2-处理中，3-已处理
     */
    private Integer handleStatus;

    /**
     * 处理建议
     */
    private String handleSuggestion;

    /**
     * 账面信息
     */
    private BookValueInfo bookValue;

    /**
     * 实际信息
     */
    private ActualValueInfo actualValue;

    /**
     * 差异处理信息
     */
    private DifferenceHandleInfo handleInfo;

    /**
     * 批量差异处理
     */
    private List<String> batchDiffIds;

    private static final long serialVersionUID = 1L;
}
