package com.jingfang.web.controller.maintenance;

import com.jingfang.asset_part_relation.module.vo.AssetPartRelationVo;
import com.jingfang.asset_part_relation.service.AssetPartRelationService;
import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 维护计划辅助控制器
 * 提供维护计划相关的辅助功能
 */
@Slf4j
@RestController
@RequestMapping("/maintenance/helper")
public class MaintenanceHelperController extends BaseController {
    
    @Resource
    private AssetPartRelationService assetPartRelationService;
    
    /**
     * 根据资产ID获取关联的备品备件列表
     * 用于维护计划中选择备品备件时的数据源
     */
    @PreAuthorize("@ss.hasPermi('maintenance:plan:query')")
    @GetMapping("/asset/{assetId}/parts")
    public AjaxResult getAssetParts(@PathVariable("assetId") String assetId) {
        try {
            log.info("查询资产关联的备品备件列表，资产ID：{}", assetId);
            List<AssetPartRelationVo> parts = assetPartRelationService.selectPartsByAssetId(assetId);
            return AjaxResult.success(parts);
        } catch (Exception e) {
            log.error("查询资产关联的备品备件列表异常：", e);
            return AjaxResult.error("查询资产关联的备品备件列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 检查备品备件是否与资产关联
     */
    @PreAuthorize("@ss.hasPermi('maintenance:plan:query')")
    @GetMapping("/check/asset/{assetId}/part/{partId}")
    public AjaxResult checkAssetPartRelation(@PathVariable("assetId") String assetId,
                                           @PathVariable("partId") String partId) {
        try {
            log.info("检查备品备件与资产的关联关系，资产ID：{}，备品备件ID：{}", assetId, partId);
            boolean exists = assetPartRelationService.checkRelationExists(assetId, partId);
            return AjaxResult.success(exists);
        } catch (Exception e) {
            log.error("检查备品备件与资产的关联关系异常：", e);
            return AjaxResult.error("检查关联关系失败：" + e.getMessage());
        }
    }
} 