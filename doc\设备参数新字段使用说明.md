# 设备参数新字段使用说明

## 概述

本文档说明在 `device_param` 表中新增的两个字段：`rw_type`（读写类型）和 `param_type`（参数类型）的使用方法和配置说明。

## 新增字段详情

### 1. rw_type（读写类型）

**字段定义**：
- 类型：`tinyint(1)`
- 默认值：`0`
- 注释：读写类型 0-只读 1-只写 2-读写

**取值说明**：
- **0 - 只读**：参数只能从设备读取，不能向设备写入
  - 适用场景：传感器数据、状态监控、运行参数等
  - 示例：温度、湿度、压力、流量等测量值
  
- **1 - 只写**：参数只能向设备写入，不能从设备读取
  - 适用场景：控制指令、操作命令等
  - 示例：启动命令、停止命令、复位指令等
  
- **2 - 读写**：参数既可以读取也可以写入
  - 适用场景：设定值、配置参数等
  - 示例：温度设定值、速度设定值、工作模式等

### 2. param_type（参数类型）

**字段定义**：
- 类型：`tinyint(1)`
- 默认值：`0`
- 注释：参数类型 0-一般参数 1-告警参数

**取值说明**：
- **0 - 一般参数**：正常的运行参数，用于监控设备状态
  - 适用场景：日常监控、数据采集、状态显示
  - 示例：温度、压力、流量、电压、电流等
  
- **1 - 告警参数**：告警相关的参数，用于故障诊断
  - 适用场景：故障检测、异常报警、安全监控
  - 示例：高温告警、低压告警、故障状态、异常标志等

## 代码修改说明

### 1. 实体类修改

在 `DeviceParam.java` 中新增了两个字段：

```java
/**
 * 读写类型（0-只读，1-只写，2-读写）
 */
private Integer rwType;

/**
 * 参数类型（0-一般参数，1-告警参数）
 */
private Integer paramType;
```

### 2. 查询逻辑优化

**运行参数查询**（`showDeviceParamTypeOne`）：
- 现在只查询 `param_type = 0` 的一般参数
- 提高了查询精度，避免混合显示

**告警参数查询**（`showDeviceParamTypeTwo`）：
- 现在只查询 `param_type = 1` 的告警参数
- 替代了之前通过参数名称关键字筛选的方式

### 3. DTO和VO类更新

在相关的DTO和VO类中都添加了对应的字段，确保数据传输的完整性。

## 使用示例

### 1. 配置一般参数

```sql
INSERT INTO device_param (
    device_id, param_name, param_unit, 
    range_start, range_end, 
    rw_type, param_type, del_flag
) VALUES (
    1, '冷却水进水温度', '°C', 
    0.0, 100.0, 
    0, 0, 0  -- 只读，一般参数
);
```

### 2. 配置告警参数

```sql
INSERT INTO device_param (
    device_id, param_name, param_unit, 
    range_start, range_end, 
    rw_type, param_type, del_flag
) VALUES (
    1, '高温告警', '', 
    NULL, NULL, 
    0, 1, 0  -- 只读，告警参数
);
```

### 3. 配置可读写参数

```sql
INSERT INTO device_param (
    device_id, param_name, param_unit, 
    range_start, range_end, 
    rw_type, param_type, del_flag
) VALUES (
    1, '温度设定值', '°C', 
    10.0, 80.0, 
    2, 0, 0  -- 读写，一般参数
);
```

## API接口影响

### 1. 获取运行参数接口

**接口**：`GET /device/list/detail/normal`

**变化**：
- 现在只返回 `param_type = 0` 的参数
- 响应数据中包含 `rwType` 和 `paramType` 字段

### 2. 获取告警参数接口

**接口**：`GET /device/list/detail/alert`

**变化**：
- 现在只返回 `param_type = 1` 的参数
- 响应数据中包含 `rwType` 和 `paramType` 字段

## 数据迁移

### 1. 执行SQL脚本

运行 `sql/device_param_add_fields.sql` 脚本来添加新字段：

```bash
mysql -u username -p database_name < sql/device_param_add_fields.sql
```

### 2. 更新现有数据

脚本会自动将现有数据的新字段设置为默认值：
- `rw_type = 0`（只读）
- `param_type = 0`（一般参数）

### 3. 手动调整数据

根据实际业务需求，手动调整特定参数的类型：

```sql
-- 将告警相关参数设置为告警类型
UPDATE device_param 
SET param_type = 1 
WHERE param_name LIKE '%告警%' 
   OR param_name LIKE '%报警%' 
   OR param_name LIKE '%故障%';

-- 将设定值参数设置为读写类型
UPDATE device_param 
SET rw_type = 2 
WHERE param_name LIKE '%设定%' 
   OR param_name LIKE '%设置%';
```

## 前端界面调整

### 1. 参数配置表单

在添加/编辑参数的表单中，需要添加两个下拉选择框：

- **读写类型选择**：只读/只写/读写
- **参数类型选择**：一般参数/告警参数

### 2. 参数列表显示

在参数列表中可以显示参数的类型信息，便于用户识别。

## 注意事项

1. **数据一致性**：确保新字段的值与参数的实际用途一致
2. **索引优化**：新字段已添加索引，提高查询性能
3. **向后兼容**：现有代码在添加字段后仍能正常工作
4. **业务逻辑**：根据 `rw_type` 字段控制参数的读写权限
5. **分类管理**：利用 `param_type` 字段实现参数的分类管理

## 扩展功能

### 1. 参数权限控制

可以根据 `rw_type` 字段实现更精细的权限控制：

```java
public boolean canWrite(DeviceParam param) {
    return param.getRwType() == 1 || param.getRwType() == 2;
}

public boolean canRead(DeviceParam param) {
    return param.getRwType() == 0 || param.getRwType() == 2;
}
```

### 2. 告警参数监控

可以专门针对 `param_type = 1` 的参数实现告警监控逻辑。

### 3. 参数分组显示

前端可以根据 `param_type` 字段对参数进行分组显示，提升用户体验。

通过这些新字段的引入，系统的参数管理将更加精确和灵活！
