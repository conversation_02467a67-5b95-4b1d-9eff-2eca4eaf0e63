<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.asset_stocktaking.mapper.StocktakingPlanMapper">

    <!-- 结果映射 -->
    <resultMap id="StocktakingPlanVoResult" type="com.jingfang.asset_stocktaking.module.vo.StocktakingPlanVo">
        <id property="planId" column="plan_id"/>
        <result property="planName" column="plan_name"/>
        <result property="planType" column="plan_type"/>
        <result property="planTypeDesc" column="plan_type_desc"/>
        <result property="planScope" column="plan_scope"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="responsibleUserId" column="responsible_user_id"/>
        <result property="responsibleUserName" column="responsible_user_name"/>
        <result property="status" column="status"/>
        <result property="statusDesc" column="status_desc"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <!-- 分页查询盘点计划列表 -->
    <select id="selectPlanList" resultMap="StocktakingPlanVoResult">
        SELECT 
            p.plan_id,
            p.plan_name,
            p.plan_type,
            CASE p.plan_type 
                WHEN 1 THEN '全盘'
                WHEN 2 THEN '部分盘点'
                ELSE '未知'
            END as plan_type_desc,
            p.plan_scope,
            p.start_date,
            p.end_date,
            p.responsible_user_id,
            u.nick_name as responsible_user_name,
            p.status,
            CASE p.status
                WHEN 1 THEN '草稿'
                WHEN 2 THEN '待审批'
                WHEN 3 THEN '执行中'
                WHEN 4 THEN '已完成'
                WHEN 5 THEN '已取消'
                ELSE '未知'
            END as status_desc,
            p.create_by,
            p.create_time,
            p.update_by,
            p.update_time,
            p.remark
        FROM asset_stocktaking_plan p
        LEFT JOIN sys_user u ON p.responsible_user_id = u.user_id
        <where>
            <if test="request.planName != null and request.planName != ''">
                AND p.plan_name LIKE CONCAT('%', #{request.planName}, '%')
            </if>
            <if test="request.planType != null">
                AND p.plan_type = #{request.planType}
            </if>
            <if test="request.responsibleUserId != null">
                AND p.responsible_user_id = #{request.responsibleUserId}
            </if>
            <if test="request.status != null">
                AND p.status = #{request.status}
            </if>
            <if test="request.statusList != null and request.statusList.size() > 0">
                AND p.status IN
                <foreach collection="request.statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="request.startDateBegin != null">
                AND p.start_date >= #{request.startDateBegin}
            </if>
            <if test="request.startDateEnd != null">
                AND p.start_date &lt;= #{request.startDateEnd}
            </if>
            <if test="request.endDateBegin != null">
                AND p.end_date >= #{request.endDateBegin}
            </if>
            <if test="request.endDateEnd != null">
                AND p.end_date &lt;= #{request.endDateEnd}
            </if>
            <if test="request.createTimeBegin != null">
                AND p.create_time >= #{request.createTimeBegin}
            </if>
            <if test="request.createTimeEnd != null">
                AND p.create_time &lt;= #{request.createTimeEnd}
            </if>
            <if test="request.createBy != null and request.createBy != ''">
                AND p.create_by = #{request.createBy}
            </if>
            <if test="request.keyword != null and request.keyword != ''">
                AND (p.plan_name LIKE CONCAT('%', #{request.keyword}, '%')
                     OR u.nick_name LIKE CONCAT('%', #{request.keyword}, '%')
                     OR p.remark LIKE CONCAT('%', #{request.keyword}, '%'))
            </if>
        </where>
        <choose>
            <when test="request.orderBy != null and request.orderBy != ''">
                ORDER BY ${request.orderBy}
                <if test="request.orderDirection != null and request.orderDirection != ''">
                    ${request.orderDirection}
                </if>
            </when>
            <otherwise>
                ORDER BY p.create_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 根据ID查询盘点计划详情 -->
    <select id="selectPlanById" resultMap="StocktakingPlanVoResult">
        SELECT 
            p.plan_id,
            p.plan_name,
            p.plan_type,
            CASE p.plan_type 
                WHEN 1 THEN '全盘'
                WHEN 2 THEN '部分盘点'
                ELSE '未知'
            END as plan_type_desc,
            p.plan_scope,
            p.start_date,
            p.end_date,
            p.responsible_user_id,
            u.nick_name as responsible_user_name,
            p.status,
            CASE p.status
                WHEN 1 THEN '草稿'
                WHEN 2 THEN '待审批'
                WHEN 3 THEN '执行中'
                WHEN 4 THEN '已完成'
                WHEN 5 THEN '已取消'
                ELSE '未知'
            END as status_desc,
            p.create_by,
            p.create_time,
            p.update_by,
            p.update_time,
            p.remark
        FROM asset_stocktaking_plan p
        LEFT JOIN sys_user u ON p.responsible_user_id = u.user_id
        WHERE p.plan_id = #{planId}
    </select>

    <!-- 查询盘点计划统计信息 -->
    <select id="selectPlanStatistics" resultType="com.jingfang.asset_stocktaking.module.vo.StocktakingPlanVo$PlanStatistics">
        SELECT 
            COALESCE(task_stats.total_tasks, 0) as totalTasks,
            COALESCE(task_stats.completed_tasks, 0) as completedTasks,
            COALESCE(task_stats.in_progress_tasks, 0) as inProgressTasks,
            COALESCE(task_stats.pending_tasks, 0) as pendingTasks,
            COALESCE(record_stats.total_assets, 0) as totalAssets,
            COALESCE(record_stats.inventoried_assets, 0) as inventoriedAssets,
            COALESCE(diff_stats.difference_assets, 0) as differenceAssets,
            CASE 
                WHEN COALESCE(record_stats.total_assets, 0) = 0 THEN 0
                ELSE ROUND(COALESCE(record_stats.inventoried_assets, 0) * 100.0 / record_stats.total_assets, 2)
            END as progressPercentage
        FROM (SELECT #{planId} as plan_id) p
        LEFT JOIN (
            SELECT 
                plan_id,
                COUNT(*) as total_tasks,
                SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_tasks,
                SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as in_progress_tasks,
                SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as pending_tasks
            FROM asset_stocktaking_task 
            WHERE plan_id = #{planId}
            GROUP BY plan_id
        ) task_stats ON p.plan_id = task_stats.plan_id
        LEFT JOIN (
            SELECT 
                t.plan_id,
                SUM(t.expected_count) as total_assets,
                COUNT(r.record_id) as inventoried_assets
            FROM asset_stocktaking_task t
            LEFT JOIN asset_stocktaking_record r ON t.task_id = r.task_id
            WHERE t.plan_id = #{planId}
            GROUP BY t.plan_id
        ) record_stats ON p.plan_id = record_stats.plan_id
        LEFT JOIN (
            SELECT 
                plan_id,
                COUNT(*) as difference_assets
            FROM asset_stocktaking_difference 
            WHERE plan_id = #{planId}
            GROUP BY plan_id
        ) diff_stats ON p.plan_id = diff_stats.plan_id
    </select>

    <!-- 根据状态查询盘点计划 -->
    <select id="selectPlanByStatus" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingPlan">
        SELECT * FROM asset_stocktaking_plan WHERE status = #{status}
    </select>

    <!-- 根据负责人查询盘点计划 -->
    <select id="selectPlanByResponsibleUser" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingPlan">
        SELECT * FROM asset_stocktaking_plan WHERE responsible_user_id = #{responsibleUserId}
    </select>

    <!-- 查询指定时间范围内的盘点计划 -->
    <select id="selectPlanByDateRange" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingPlan">
        SELECT * FROM asset_stocktaking_plan 
        WHERE start_date >= #{startDate} AND end_date &lt;= #{endDate}
    </select>

    <!-- 统计各状态的盘点计划数量 -->
    <select id="countPlanByStatus" resultType="java.util.Map">
        SELECT 
            status,
            CASE status
                WHEN 1 THEN '草稿'
                WHEN 2 THEN '待审批'
                WHEN 3 THEN '执行中'
                WHEN 4 THEN '已完成'
                WHEN 5 THEN '已取消'
                ELSE '未知'
            END as statusDesc,
            COUNT(*) as count
        FROM asset_stocktaking_plan 
        GROUP BY status
    </select>

    <!-- 查询即将到期的盘点计划 -->
    <select id="selectExpiringPlans" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingPlan">
        SELECT * FROM asset_stocktaking_plan 
        WHERE status IN (2, 3) 
        AND DATEDIFF(end_date, CURDATE()) &lt;= #{days}
        AND DATEDIFF(end_date, CURDATE()) >= 0
    </select>

    <!-- 查询逾期的盘点计划 -->
    <select id="selectOverduePlans" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingPlan">
        SELECT * FROM asset_stocktaking_plan 
        WHERE status IN (2, 3) 
        AND end_date &lt; CURDATE()
    </select>

    <!-- 更新计划状态 -->
    <update id="updatePlanStatus">
        UPDATE asset_stocktaking_plan 
        SET status = #{status}, update_by = #{updateBy}, update_time = NOW()
        WHERE plan_id = #{planId}
    </update>

    <!-- 批量更新计划状态 -->
    <update id="batchUpdatePlanStatus">
        UPDATE asset_stocktaking_plan 
        SET status = #{status}, update_by = #{updateBy}, update_time = NOW()
        WHERE plan_id IN
        <foreach collection="planIds" item="planId" open="(" separator="," close=")">
            #{planId}
        </foreach>
    </update>

    <!-- 检查计划名称是否重复 -->
    <select id="checkPlanNameExists" resultType="int">
        SELECT COUNT(*) FROM asset_stocktaking_plan 
        WHERE plan_name = #{planName}
        <if test="excludePlanId != null and excludePlanId != ''">
            AND plan_id != #{excludePlanId}
        </if>
    </select>

    <!-- 查询用户有权限的盘点计划 -->
    <select id="selectPlanByUserPermission" resultType="com.jingfang.asset_stocktaking.module.entity.AssetStocktakingPlan">
        SELECT DISTINCT p.* FROM asset_stocktaking_plan p
        LEFT JOIN asset_stocktaking_task t ON p.plan_id = t.plan_id
        WHERE p.responsible_user_id = #{userId}
           OR t.assigned_user_id = #{userId}
           OR p.create_by = (SELECT user_name FROM sys_user WHERE user_id = #{userId})
    </select>

</mapper>
