<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.wh_item.mapper.ItemOutboundMapper">

    <resultMap id="BaseResultMap" type="com.jingfang.wh_item.module.entity.ItemOutbound">
        <id property="outboundId" column="outbound_id" jdbcType="VARCHAR"/>
        <result property="businessDate" column="business_date" jdbcType="DATE"/>
        <result property="recipientName" column="recipient_name" jdbcType="VARCHAR"/>
        <result property="recipientDept" column="recipient_dept" jdbcType="VARCHAR"/>
        <result property="outboundType" column="outbound_type" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="handlerId" column="handler_id" jdbcType="BIGINT"/>
        <result property="handleTime" column="handle_time" jdbcType="TIMESTAMP"/>
        <result property="handleRemark" column="handle_remark" jdbcType="VARCHAR"/>
        <result property="auditorId" column="auditor_id" jdbcType="BIGINT"/>
        <result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
        <result property="auditRemark" column="audit_remark" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="outboundDescription" column="outbound_description" jdbcType="LONGVARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
    </resultMap>

    <resultMap id="ItemOutboundVoResultMap" type="com.jingfang.wh_item.module.vo.ItemOutboundVo">
        <id property="outboundId" column="outbound_id" jdbcType="VARCHAR"/>
        <result property="businessDate" column="business_date" jdbcType="DATE"/>
        <result property="recipientName" column="recipient_name" jdbcType="VARCHAR"/>
        <result property="recipientDept" column="recipient_dept" jdbcType="VARCHAR"/>
        <result property="outboundType" column="outbound_type" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="handlerId" column="handler_id" jdbcType="BIGINT"/>
        <result property="handlerName" column="handler_name" jdbcType="VARCHAR"/>
        <result property="handleTime" column="handle_time" jdbcType="TIMESTAMP"/>
        <result property="handleRemark" column="handle_remark" jdbcType="VARCHAR"/>
        <result property="auditorId" column="auditor_id" jdbcType="BIGINT"/>
        <result property="auditorName" column="auditor_name" jdbcType="VARCHAR"/>
        <result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
        <result property="auditRemark" column="audit_remark" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="outboundDescription" column="outbound_description" jdbcType="LONGVARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        outbound_id, business_date, recipient_name, recipient_dept, outbound_type, status,
        creator_id, create_time, handler_id, handle_time, handle_remark,
        auditor_id, audit_time, audit_remark, update_time, outbound_description, del_flag
    </sql>

    <!-- 查询出库单详情（包含管理人员姓名） -->
    <select id="selectOutboundDetailById" parameterType="string" resultMap="ItemOutboundVoResultMap">
        SELECT 
            t1.outbound_id, t1.business_date, t1.recipient_name, t1.recipient_dept, 
            t1.outbound_type, t1.status, t1.creator_id, t1.create_time, 
            t1.handler_id, t1.handle_time, t1.handle_remark,
            t1.auditor_id, t1.audit_time, t1.audit_remark, 
            t1.update_time, t1.outbound_description,
            u1.nick_name as creator_name,
            u2.nick_name as handler_name,
            u3.nick_name as auditor_name
        FROM 
            item_outbound t1
            LEFT JOIN sys_user u1 ON t1.creator_id = u1.user_id
            LEFT JOIN sys_user u2 ON t1.handler_id = u2.user_id
            LEFT JOIN sys_user u3 ON t1.auditor_id = u3.user_id
        WHERE 
            t1.outbound_id = #{outboundId}
            AND t1.del_flag = '0'
    </select>

    <!-- 分页查询出库单列表（包含管理人员姓名） -->
    <select id="selectOutboundListWithManager" resultMap="ItemOutboundVoResultMap">
        SELECT 
            t1.outbound_id, t1.business_date, t1.recipient_name, t1.recipient_dept, 
            t1.outbound_type, t1.status, t1.creator_id, t1.create_time, 
            t1.handler_id, t1.handle_time, t1.handle_remark,
            t1.auditor_id, t1.audit_time, t1.audit_remark, 
            t1.update_time, t1.outbound_description,
            u1.nick_name as creator_name,
            u2.nick_name as handler_name,
            u3.nick_name as auditor_name
        FROM 
            item_outbound t1
            LEFT JOIN sys_user u1 ON t1.creator_id = u1.user_id
            LEFT JOIN sys_user u2 ON t1.handler_id = u2.user_id
            LEFT JOIN sys_user u3 ON t1.auditor_id = u3.user_id
        <where>
            t1.del_flag = '0'
            <if test="request.outboundId != null and request.outboundId != ''">
                AND t1.outbound_id = #{request.outboundId}
            </if>
            <if test="request.recipientName != null and request.recipientName != ''">
                AND t1.recipient_name LIKE CONCAT('%', #{request.recipientName}, '%')
            </if>
            <if test="request.recipientDept != null and request.recipientDept != ''">
                AND t1.recipient_dept LIKE CONCAT('%', #{request.recipientDept}, '%')
            </if>
            <if test="request.outboundType != null">
                AND t1.outbound_type = #{request.outboundType}
            </if>
            <if test="request.status != null">
                AND t1.status = #{request.status}
            </if>
            <if test="request.creatorId != null">
                AND t1.creator_id = #{request.creatorId}
            </if>
            <if test="request.businessDateStart != null">
                AND t1.business_date >= #{request.businessDateStart}
            </if>
            <if test="request.businessDateEnd != null">
                AND t1.business_date &lt;= #{request.businessDateEnd}
            </if>
            <if test="request.createTimeStart != null">
                AND t1.create_time >= #{request.createTimeStart}
            </if>
            <if test="request.createTimeEnd != null">
                AND t1.create_time &lt;= #{request.createTimeEnd}
            </if>
            <!-- 根据明细查询条件 -->
            <if test="request.itemName != null and request.itemName != '' or 
                      request.itemCode != null and request.itemCode != '' or 
                      request.itemType != null">
                AND EXISTS (
                    SELECT 1 FROM item_outbound_detail d
                    LEFT JOIN item_base_info i ON d.item_id = i.item_id
                    WHERE d.outbound_id = t1.outbound_id
                    <if test="request.itemName != null and request.itemName != ''">
                        AND i.item_name LIKE CONCAT('%', #{request.itemName}, '%')
                    </if>
                    <if test="request.itemCode != null and request.itemCode != ''">
                        AND i.item_code LIKE CONCAT('%', #{request.itemCode}, '%')
                    </if>
                    <if test="request.itemType != null">
                        AND i.item_type = #{request.itemType}
                    </if>
                )
            </if>
        </where>
        ORDER BY t1.create_time DESC
    </select>

</mapper> 