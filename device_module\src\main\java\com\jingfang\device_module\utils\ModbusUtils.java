package com.jingfang.device_module.utils;


import com.ghgande.j2mod.modbus.ModbusException;
import com.ghgande.j2mod.modbus.facade.ModbusTCPMaster;
import com.ghgande.j2mod.modbus.procimg.InputRegister;
import com.ghgande.j2mod.modbus.procimg.Register;
import com.ghgande.j2mod.modbus.util.BitVector;

import java.io.IOException;

public class ModbusUtils {

    public static float readFloatTypeHoldingRegisterData(int address, ModbusTCPMaster master)  throws ModbusException, IOException{
        try {
            Register[] registers = master.readMultipleRegisters(address, 2);
            int highWord = registers[1].getValue();  // 高位寄存器
            int lowWord = registers[0].getValue();   // 低位寄存器

            // 合并为一个 32 位整数
            int combinedValue = (highWord << 16) | (lowWord & 0xFFFF);

            // 将 32 位整数转换为 32 位浮点数
            return Float.intBitsToFloat(combinedValue);
        } catch (Exception e) {
            e.printStackTrace();
            return -999.99F;
        }
    }

    public static float readFloatTypeInputRegisterData(int address, ModbusTCPMaster master)  throws ModbusException, IOException{
        try {
            InputRegister[] registers = master.readInputRegisters(address, 2);
            int highWord = registers[1].getValue();  // 高位寄存器
            int lowWord = registers[0].getValue();   // 低位寄存器

            // 合并为一个 32 位整数
            int combinedValue = (highWord << 16) | (lowWord & 0xFFFF);

            // 将 32 位整数转换为 32 位浮点数
            return Float.intBitsToFloat(combinedValue);
        } catch (Exception e) {
            e.printStackTrace();
            return -999.99F;
        }
    }


    public static boolean readCoilData(int address, ModbusTCPMaster master)  throws ModbusException, IOException{
        try {
            BitVector bitVector = master.readCoils(address,1);
            return bitVector.getBit(0);
        }catch (Exception e) {
            throw new ModbusException(e.getMessage());
        }
    }

    public static boolean readDiscreteData(int address, ModbusTCPMaster master)  throws ModbusException, IOException{
        try {
            BitVector bitVector = master.readInputDiscretes(address,1);
            return bitVector.getBit(0);
        }catch (Exception e) {
            throw new ModbusException(e.getMessage());
        }
    }


}
