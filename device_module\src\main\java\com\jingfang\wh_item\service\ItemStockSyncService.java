package com.jingfang.wh_item.service;

import java.math.BigDecimal;

/**
 * 物品库存同步服务接口
 * 用于在库存变动时同步更新item_base_info表中的总库存
 */
public interface ItemStockSyncService {
    
    /**
     * 同步更新物品总库存
     * 计算指定物品在所有仓库的库存总和，并更新到item_base_info表
     *
     * @param itemId 物品ID
     * @return 更新后的总库存数量
     */
    BigDecimal syncTotalStock(String itemId);
    
    /**
     * 批量同步更新物品总库存
     * 
     * @param itemIds 物品ID列表
     * @return 成功同步的物品数量
     */
    int batchSyncTotalStock(String... itemIds);
    
    /**
     * 同步所有物品的总库存
     * 
     * @return 成功同步的物品数量
     */
    int syncAllTotalStock();
    
    /**
     * 计算物品的库存状态
     * 基于企业级安全库存和总库存计算状态
     *
     * @param itemId 物品ID
     * @return 库存状态(1-正常, 2-不足, 3-过剩)
     */
    Integer calculateStockStatus(String itemId);
    
    /**
     * 更新物品的企业级安全库存
     * 
     * @param itemId 物品ID
     * @param safetyStock 新的安全库存值
     * @return 是否更新成功
     */
    boolean updateSafetyStock(String itemId, BigDecimal safetyStock);
} 