-- 设备MQTT状态检测定时任务配置
-- 执行此脚本来添加设备状态检测相关的定时任务

-- 删除可能存在的旧任务
DELETE FROM sys_job WHERE job_name IN ('设备基础信息缓存刷新', '设备在线状态检测', '设备缓存和状态综合刷新');

-- 添加设备基础信息缓存刷新任务（每小时执行一次）
INSERT INTO sys_job VALUES(
    NULL, 
    '设备基础信息缓存刷新', 
    'DEFAULT', 
    'DeviceInfoTask.refreshDeviceInfo', 
    '0 0 * * * ?',  -- 每小时执行一次
    '3', 
    '1', 
    '0',  -- 启用状态
    'admin', 
    NOW(), 
    '', 
    NULL, 
    '定期刷新设备基础信息到Redis缓存'
);

-- 添加设备在线状态检测任务（每5分钟执行一次）
INSERT INTO sys_job VALUES(
    NULL, 
    '设备在线状态检测', 
    'DEFAULT', 
    'DeviceInfoTask.mqttRefreshOnlineDeviceList', 
    '0 */5 * * * ?',  -- 每5分钟执行一次
    '3', 
    '1', 
    '0',  -- 启用状态
    'admin', 
    NOW(), 
    '', 
    NULL, 
    '使用MQTT协议定期检测设备在线状态'
);

-- 添加设备缓存和状态综合刷新任务（每30分钟执行一次）
INSERT INTO sys_job VALUES(
    NULL, 
    '设备缓存和状态综合刷新', 
    'DEFAULT', 
    'DeviceInfoTask.refreshCacheAndStatus', 
    '0 */30 * * * ?',  -- 每30分钟执行一次
    '3', 
    '1', 
    '0',  -- 启用状态
    'admin', 
    NOW(), 
    '', 
    NULL, 
    '综合刷新设备缓存和在线状态'
);

-- 查询添加的任务
SELECT job_id, job_name, job_group, invoke_target, cron_expression, status, remark 
FROM sys_job 
WHERE job_name IN ('设备基础信息缓存刷新', '设备在线状态检测', '设备缓存和状态综合刷新')
ORDER BY job_id;
