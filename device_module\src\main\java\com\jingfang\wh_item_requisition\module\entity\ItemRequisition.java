package com.jingfang.wh_item_requisition.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 物品领用单主表
 * @TableName item_requisition
 */
@TableName(value = "item_requisition")
@Data
public class ItemRequisition implements Serializable {
    
    /**
     * 领用单ID
     */
    @TableId(type = IdType.INPUT)
    private String requisitionId;
    
    /**
     * 业务日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date businessDate;
    
    /**
     * 申请人ID
     */
    private Long applicantId;
    
    /**
     * 申请部门ID
     */
    private Long deptId;
    
    /**
     * 领用用途/说明
     */
    private String requisitionPurpose;
    
    /**
     * 状态(1-草稿, 2-待确认, 3-待审核, 4-已通过, 5-已退回, 6-已完成)
     */
    private Integer status;
    
    /**
     * 制单人ID
     */
    private Long creatorId;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 经手人ID
     */
    private Long handlerId;
    
    /**
     * 经手确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date handleTime;
    
    /**
     * 经手人备注
     */
    private String handleRemark;
    
    /**
     * 审核人ID
     */
    private Long auditorId;
    
    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditTime;
    
    /**
     * 审核备注
     */
    private String auditRemark;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    
    /**
     * 删除标志(0-正常, 1-删除)
     */
    private String delFlag;
    
    private static final long serialVersionUID = 1L;
} 