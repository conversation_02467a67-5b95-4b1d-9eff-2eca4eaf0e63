# MQTT网关参数采集迁移指南

## 概述

本指南用于将现有的Modbus TCP参数采集方式迁移到MQTT网关参数采集方式。新方式更简单，只需要配置MQTT属性名称即可获取设备参数值。

## 主要变化

### 1. 数据库表结构变化

#### 新增字段
- `mqtt_property_name`: MQTT属性名称，用于从网关获取数据
- `param_description`: 参数描述
- `is_alert_param`: 是否为告警参数（0-运行参数，1-告警参数）
- `alert_threshold_min`: 告警阈值下限
- `alert_threshold_max`: 告警阈值上限
- `created_time`: 创建时间
- `updated_time`: 更新时间

#### 废弃字段（保留但不再使用）
- `storage_type`: 存储类型（Modbus相关）
- `param_address`: 参数地址（Modbus相关）

#### 新增表
- `device_param_category`: 设备参数分类表
- `device_param_template`: 设备参数配置模板表

### 2. 参数采集方式变化

#### 原方式（Modbus TCP）
```java
// 需要配置存储类型、参数地址等
if (item.getStorageType()==0){
    register = master.readInputRegisters(item.getParamAddress(), 1);
}else {
    register = master.readMultipleRegisters(item.getParamAddress(),1);
}
```

#### 新方式（MQTT网关）
```java
// 只需要MQTT属性名称
String propertyName = item.getMqttPropertyName();
// 通过MQTT订阅获取属性值
```

## 迁移步骤

### 第一步：执行数据库迁移脚本

```bash
# 连接到数据库
mysql -u username -p database_name

# 执行迁移脚本
source sql/device_tables_mqtt_update.sql;
```

### 第二步：配置MQTT属性名称

迁移脚本会为现有参数设置临时的MQTT属性名称，您需要根据实际的MQTT网关配置进行更新：

```sql
-- 示例：更新温度参数的MQTT属性名称
UPDATE device_param 
SET mqtt_property_name = 'temperature' 
WHERE param_name LIKE '%温度%';

-- 示例：更新湿度参数的MQTT属性名称
UPDATE device_param 
SET mqtt_property_name = 'humidity' 
WHERE param_name LIKE '%湿度%';

-- 示例：更新压力参数的MQTT属性名称
UPDATE device_param 
SET mqtt_property_name = 'pressure' 
WHERE param_name LIKE '%压力%';
```

### 第三步：更新Java实体类

需要更新`DeviceParam`实体类，添加新字段：

```java
@Data
public class DeviceParam implements Serializable {
    // ... 现有字段 ...
    
    /**
     * MQTT属性名称
     */
    private String mqttPropertyName;
    
    /**
     * 参数描述
     */
    private String paramDescription;
    
    /**
     * 是否为告警参数
     */
    private Integer isAlertParam;
    
    /**
     * 告警阈值下限
     */
    private Double alertThresholdMin;
    
    /**
     * 告警阈值上限
     */
    private Double alertThresholdMax;
    
    /**
     * 创建时间
     */
    private Date createdTime;
    
    /**
     * 更新时间
     */
    private Date updatedTime;
}
```

### 第四步：更新Mapper映射

更新`DeviceParamMapper.xml`文件：

```xml
<resultMap id="BaseResultMap" type="com.jingfang.device_module.module.entity.DeviceParam">
    <!-- 现有字段映射 -->
    <result property="mqttPropertyName" column="mqtt_property_name" />
    <result property="paramDescription" column="param_description" />
    <result property="isAlertParam" column="is_alert_param" />
    <result property="alertThresholdMin" column="alert_threshold_min" />
    <result property="alertThresholdMax" column="alert_threshold_max" />
    <result property="createdTime" column="created_time" />
    <result property="updatedTime" column="updated_time" />
</resultMap>

<sql id="Base_Column_List">
    param_id,device_id,param_key,param_name,mqtt_property_name,param_description,
    param_unit,data_type,category_id,is_alert_param,range_start,range_end,
    alert_threshold_min,alert_threshold_max,del_flag,created_time,updated_time
</sql>
```

### 第五步：创建MQTT参数采集服务

创建新的MQTT参数采集服务来替代Modbus采集：

```java
@Service
public class MqttDeviceParamService {
    
    /**
     * 通过MQTT获取设备参数
     */
    public List<DeviceParamVo> getDeviceParamsByMqtt(Long deviceId) {
        // 1. 获取设备的参数配置
        List<DeviceParam> params = deviceParamMapper.selectByDeviceId(deviceId);
        
        // 2. 通过MQTT订阅获取参数值
        List<DeviceParamVo> result = new ArrayList<>();
        for (DeviceParam param : params) {
            DeviceParamVo vo = new DeviceParamVo();
            BeanUtils.copyProperties(param, vo);
            
            // 通过MQTT属性名称获取值
            String value = mqttClient.getPropertyValue(param.getMqttPropertyName());
            vo.setParamValue(value + param.getParamUnit());
            
            // 判断是否告警
            if (param.getIsAlertParam() == 1) {
                vo.setAlertValue(checkAlert(value, param));
            }
            
            result.add(vo);
        }
        
        return result;
    }
    
    private boolean checkAlert(String value, DeviceParam param) {
        // 实现告警逻辑
        // ...
    }
}
```

## 配置示例

### 常见设备参数MQTT属性名称映射

| 参数类型 | 中文名称 | 建议MQTT属性名 | 数据类型 |
|---------|---------|---------------|---------|
| 温度 | 环境温度 | temperature | 浮点型 |
| 湿度 | 环境湿度 | humidity | 浮点型 |
| 压力 | 系统压力 | pressure | 浮点型 |
| 电压 | 输入电压 | voltage | 浮点型 |
| 电流 | 输入电流 | current | 浮点型 |
| 状态 | 运行状态 | status | 布尔型 |
| 转速 | 电机转速 | speed | 整型 |

### MQTT主题配置

根据您的MQTT网关配置，参数数据通常通过以下主题发布：

```
/sys/thing/node/property/post/device_monitor_client
```

数据格式示例：
```json
{
    "clientID": "192.168.1.100",
    "properties": [
        {
            "name": "temperature",
            "value": 25.6
        },
        {
            "name": "humidity", 
            "value": 45.2
        },
        {
            "name": "status",
            "value": true
        }
    ]
}
```

## 验证迁移结果

### 1. 检查数据完整性

```sql
-- 检查参数配置完整性
SELECT 
    COUNT(*) as total_params,
    COUNT(CASE WHEN mqtt_property_name IS NOT NULL AND mqtt_property_name != '' THEN 1 END) as configured_params,
    COUNT(CASE WHEN is_alert_param = 1 THEN 1 END) as alert_params
FROM device_param 
WHERE del_flag = 0;
```

### 2. 测试MQTT连接

使用现有的测试接口验证MQTT连接：

```http
GET /device/status/test/mqtt
```

### 3. 测试参数采集

在设备详情页面验证参数数据是否正常显示。

## 回退方案

如果需要回退到Modbus方式，可以执行以下操作：

1. 恢复备份数据：
```sql
-- 恢复参数配置
INSERT INTO device_param SELECT * FROM device_param_backup WHERE param_id NOT IN (SELECT param_id FROM device_param);
```

2. 切换服务实现，重新启用Modbus采集代码。

## 注意事项

1. **数据备份**：执行迁移前务必备份现有数据
2. **逐步迁移**：建议先在测试环境验证，再在生产环境执行
3. **属性名称**：确保MQTT属性名称与网关配置一致
4. **告警配置**：重新配置告警阈值，确保告警功能正常
5. **性能监控**：迁移后监控系统性能，确保MQTT采集效率满足要求

## 技术支持

如果在迁移过程中遇到问题，请检查：

1. MQTT连接状态
2. 网关设备配置
3. 属性名称映射
4. 数据格式兼容性

迁移完成后，您的设备参数采集将更加简单和可靠！
