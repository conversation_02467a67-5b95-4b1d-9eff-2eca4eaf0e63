package com.jingfang.wh_item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jingfang.wh_item.module.entity.ItemInbound;
import com.jingfang.wh_item.module.request.ItemInboundSearchRequest;
import com.jingfang.wh_item.module.vo.ItemInboundVo;
import io.lettuce.core.dynamic.annotation.Param;

public interface ItemInboundMapper extends BaseMapper<ItemInbound> {

    /**
     * 查询入库单列表带管理人员名称
     */
    IPage<ItemInboundVo> selectInboundListWithManager(Page<ItemInboundVo> page, @Param("request") ItemInboundSearchRequest request);

    /**
     * 通过ID查询入库单详情带管理人员名称
     */
    ItemInboundVo selectInboundDetailById(@Param("inboundId") String inboundId);
}