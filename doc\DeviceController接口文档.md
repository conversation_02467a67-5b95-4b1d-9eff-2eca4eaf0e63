# DeviceController 接口文档

## 概述

DeviceController 是设备管理模块的核心控制器，提供设备信息管理、设备参数管理、设备状态监控等功能。基于RuoYi框架开发，支持MQTT协议进行设备通信。

**基础路径**: `/device`

## 接口列表

### 1. 设备信息管理

#### 1.1 添加新设备
- **接口路径**: `POST /device/add`
- **功能描述**: 添加新的设备信息
- **请求参数**: 
  ```json
  {
    "id": "设备ID（可选）",
    "deviceName": "设备名称",
    "icon": "设备图标",
    "deviceType": "设备类型（整数）",
    "deviceModel": "设备型号",
    "ipAddress": "IP地址",
    "devicePort": "设备端口",
    "location": "设备位置",
    "manufacturer": "制造商",
    "serialNumber": "序列号",
    "installationDate": "安装日期（yyyy-MM-dd格式）"
  }
  ```
- **返回结果**: 
  ```json
  {
    "code": 200,
    "msg": "add success",
    "data": null
  }
  ```

#### 1.2 获取设备列表
- **接口路径**: `POST /device/list`
- **功能描述**: 分页查询设备信息列表
- **请求参数**: 
  ```json
  {
    "pageNum": "页码",
    "pageSize": "每页数量",
    "id": "设备ID（可选）",
    "deviceName": "设备名称（可选）",
    "deviceModel": "设备型号（可选）",
    "deviceStatus": "设备状态（可选）",
    "location": "设备位置（可选）"
  }
  ```
- **返回结果**: 
  ```json
  {
    "total": "总数量",
    "rows": [
      {
        "id": "设备ID",
        "deviceName": "设备名称",
        "icon": "设备图标",
        "deviceType": "设备类型",
        "deviceStatus": "设备状态",
        "runningStatus": "运行状态"
      }
    ]
  }
  ```

#### 1.3 获取设备详细信息
- **接口路径**: `GET /device/list/detail`
- **功能描述**: 获取指定设备的详细信息
- **请求参数**: 
  - `deviceId`: 设备ID（Long类型）
- **返回结果**: 
  ```json
  {
    "code": 200,
    "msg": "操作成功",
    "data": {
      "id": "设备ID",
      "deviceName": "设备名称",
      "pictureUrls": ["图片URL列表"],
      "deviceType": "设备类型",
      "deviceModel": "设备型号",
      "ipAddress": "IP地址",
      "devicePort": "设备端口",
      "location": "设备位置",
      "manufacturer": "制造商",
      "serialNumber": "序列号",
      "installationDate": "安装日期",
      "deviceStatus": "设备状态"
    }
  }
  ```

#### 1.4 刷新设备缓存
- **接口路径**: `GET /device/refresh`
- **功能描述**: 刷新设备缓存和状态信息
- **请求参数**: 无
- **返回结果**: 
  ```json
  {
    "code": 200,
    "msg": "操作成功",
    "data": "刷新结果信息"
  }
  ```

### 2. 设备图片管理

#### 2.1 添加设备图片
- **接口路径**: `GET /device/add/picture`
- **功能描述**: 为设备添加图片URL
- **请求参数**: 
  - `deviceId`: 设备ID（Long类型）
  - `pictureUrl`: 图片URL（String类型）
- **返回结果**: 
  ```json
  {
    "code": 200,
    "msg": "操作成功",
    "data": "操作结果"
  }
  ```

#### 2.2 删除设备图片
- **接口路径**: `DELETE /device/delete/picture`
- **功能描述**: 删除设备的指定图片
- **请求参数**: 
  - `deviceId`: 设备ID（Long类型）
  - `pictureUrl`: 图片URL（String类型）
- **返回结果**: 
  ```json
  {
    "code": 200,
    "msg": "操作成功",
    "data": "操作结果"
  }
  ```

### 3. 设备参数管理

#### 3.1 添加一般参数
- **接口路径**: `POST /device/list/detail/add/normal`
- **功能描述**: 为设备添加一般参数（param_type=0）
- **请求参数**: 
  ```json
  {
    "deviceId": "设备ID",
    "paramKey": "参数键",
    "paramName": "参数名称",
    "paramUnit": "参数单位",
    "rangeStart": "范围起始值",
    "rangeEnd": "范围结束值",
    "dataType": "数据类型",
    "storageType": "存储类型",
    "categoryId": "分类ID",
    "paramAddress": "参数地址",
    "rwType": "读写类型（0-只读，1-只写，2-读写）",
    "paramType": "参数类型（0-一般参数）"
  }
  ```
- **返回结果**: 
  ```json
  {
    "code": 200,
    "msg": "操作成功",
    "data": "添加结果"
  }
  ```

#### 3.2 添加告警参数
- **接口路径**: `POST /device/list/detail/add/alert`
- **功能描述**: 为设备添加告警参数（param_type=1）
- **请求参数**: 
  ```json
  {
    "deviceId": "设备ID",
    "paramKey": "参数键",
    "paramName": "参数名称",
    "dataType": "数据类型",
    "storageType": "存储类型",
    "categoryId": "分类ID",
    "paramAddress": "参数地址",
    "rwType": "读写类型",
    "paramType": "参数类型（1-告警参数）"
  }
  ```
- **返回结果**: 
  ```json
  {
    "code": 200,
    "msg": "操作成功",
    "data": "添加结果"
  }
  ```

### 4. 设备参数查询

#### 4.1 获取设备一般参数
- **接口路径**: `GET /device/list/detail/normal`
- **功能描述**: 获取设备的一般参数列表（param_type=0）
- **请求参数**: 
  - `deviceId`: 设备ID（Long类型）
- **返回结果**: 
  ```json
  {
    "total": "总数量",
    "rows": [
      {
        "paramId": "参数ID",
        "paramKey": "参数键",
        "paramName": "参数名称",
        "rangeStart": "范围起始值",
        "rangeEnd": "范围结束值",
        "paramValue": "参数值",
        "alertValue": "告警状态",
        "rwType": "读写类型",
        "paramType": "参数类型"
      }
    ]
  }
  ```

#### 4.2 获取设备告警参数（旧接口）
- **接口路径**: `GET /device/list/detail/alert`
- **功能描述**: 获取设备的告警参数列表
- **请求参数**: 
  - `deviceId`: 设备ID（Long类型）
- **返回结果**: 同4.1格式

#### 4.3 获取设备告警信息（新接口）
- **接口路径**: `GET /device/alert/info`
- **功能描述**: 获取设备的告警信息（param_type=1），支持MQTT实时查询
- **请求参数**: 
  - `deviceId`: 设备ID（Long类型）
- **返回结果**: 同4.1格式

#### 4.4 获取设备控制参数
- **接口路径**: `GET /device/control/params`
- **功能描述**: 获取设备的控制参数（param_type=3）
- **请求参数**: 
  - `deviceId`: 设备ID（Long类型）
- **返回结果**: 同4.1格式

### 5. 设备参数写入

#### 5.1 写入单个设备参数
- **接口路径**: `POST /device/param/write`
- **功能描述**: 通过MQTT协议写入单个设备参数
- **请求参数**: 
  - `deviceId`: 设备ID（Long类型）
  - `paramName`: 参数名称（String类型）
  - `paramValue`: 参数值（String类型）
- **返回结果**: 
  ```json
  {
    "code": 200,
    "msg": "参数写入成功",
    "data": null
  }
  ```

#### 5.2 批量写入设备参数
- **接口路径**: `POST /device/params/write`
- **功能描述**: 通过MQTT协议批量写入设备参数
- **请求参数**: 
  - URL参数: `deviceId`: 设备ID（Long类型）
  - 请求体: 
    ```json
    {
      "参数名称1": "参数值1",
      "参数名称2": "参数值2"
    }
    ```
- **返回结果**: 
  ```json
  {
    "code": 200,
    "msg": "参数批量写入成功",
    "data": null
  }
  ```

### 6. 测试接口

#### 6.1 测试接口
- **接口路径**: `GET /device/aaa/bbb/ccc`
- **功能描述**: 用于测试的接口
- **请求参数**: 无
- **返回结果**: 
  ```json
  {
    "code": 200,
    "msg": "操作成功",
    "data": "11"
  }
  ```

## 数据模型说明

### DeviceInfoDto（设备信息DTO）
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 否 | 设备ID |
| deviceName | String | 是 | 设备名称 |
| icon | String | 否 | 设备图标 |
| deviceType | int | 是 | 设备类型 |
| deviceModel | String | 否 | 设备型号 |
| ipAddress | String | 是 | IP地址 |
| devicePort | String | 否 | 设备端口 |
| location | String | 否 | 设备位置 |
| manufacturer | String | 否 | 制造商 |
| serialNumber | String | 否 | 序列号 |
| installationDate | String | 否 | 安装日期（yyyy-MM-dd） |

### DeviceParamVo（设备参数VO）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| paramId | Long | 参数ID |
| paramKey | String | 参数键 |
| paramName | String | 参数名称 |
| rangeStart | Double | 范围起始值 |
| rangeEnd | Double | 范围结束值 |
| paramValue | String | 参数值 |
| alertValue | boolean | 告警状态 |
| rwType | Integer | 读写类型（0-只读，1-只写，2-读写） |
| paramType | Integer | 参数类型（0-一般参数，1-告警参数，3-控制参数） |

### 参数类型说明
- **param_type = 0**: 一般参数（运行参数）
- **param_type = 1**: 告警参数
- **param_type = 3**: 控制参数

### 读写类型说明
- **rw_type = 0**: 只读参数
- **rw_type = 1**: 只写参数
- **rw_type = 2**: 读写参数

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 500 | 服务器内部错误 |

## 注意事项

1. 所有接口都基于RuoYi框架的AjaxResult返回格式
2. 设备参数查询支持MQTT实时查询，确保数据的实时性
3. 设备参数写入通过MQTT协议进行，支持单个和批量写入
4. 分页查询接口使用TableDataInfo格式返回
5. 异常情况下会返回相应的错误信息
6. 建议在调用写入接口前先检查参数的读写类型（rwType）

## 技术栈

- **框架**: Spring Boot + RuoYi
- **通信协议**: MQTT
- **数据库**: MySQL + MyBatis Plus
- **缓存**: Redis
