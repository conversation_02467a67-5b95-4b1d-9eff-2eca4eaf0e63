package com.jingfang.collaboration.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 表格视图对象
 */
@Data
public class SpreadsheetVo {
    
    /**
     * 表格ID
     */
    private String id;
    
    /**
     * 表格标题
     */
    private String title;
    
    /**
     * 表格描述
     */
    private String description;
    
    /**
     * 表格数据（JSON格式）
     */
    private String data;
    
    /**
     * 创建者ID
     */
    private Long createBy;
    
    /**
     * 创建者姓名
     */
    private String createByName;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 更新者ID
     */
    private Long updateBy;
    
    /**
     * 更新者姓名
     */
    private String updateByName;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    
    /**
     * 状态（0正常 1停用）
     */
    private String status;
    
    /**
     * 是否公开（0私有 1公开）
     */
    private String isPublic;
    
    /**
     * 分享链接token
     */
    private String shareToken;
    
    /**
     * 分享过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date shareExpireTime;
    
    /**
     * 版本号
     */
    private Integer version;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 当前用户权限
     */
    private String userPermission;
    
    /**
     * 协作者数量
     */
    private Integer collaboratorCount;
    
    /**
     * 在线用户数量
     */
    private Integer onlineUserCount;
    
    /**
     * 协作者列表
     */
    private List<CollaboratorVo> collaborators;
}
