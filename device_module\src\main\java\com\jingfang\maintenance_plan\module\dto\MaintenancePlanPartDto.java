package com.jingfang.maintenance_plan.module.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 维护计划备品备件DTO
 */
@Data
public  class MaintenancePlanPartDto {

    /**
     * 关联ID
     */
    private String relationId;

    /**
     * 备品备件ID
     */
    @NotBlank(message = "备品备件ID不能为空")
    private String partId;

    /**
     * 备品备件名称（用于显示）
     */
    private String partName;

    /**
     * 规格型号
     */
    private String specModel;

    /**
     * 单位
     */
    private String unit;

    /**
     * 每次维护所需数量
     */
    @NotNull(message = "所需数量不能为空")
    private BigDecimal requiredQuantity;

    /**
     * 备注说明
     */
    private String remark;
}
