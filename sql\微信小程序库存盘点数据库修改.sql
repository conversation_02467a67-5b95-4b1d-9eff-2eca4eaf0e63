-- 微信小程序库存盘点功能数据库修改脚本
-- 执行日期：2025-01-11

-- 1. 为盘点明细表添加照片字段
-- 在item_stocktaking_detail表中增加照片字段，用于存储JSON格式的照片URL列表
ALTER TABLE item_stocktaking_detail 
ADD COLUMN photos TEXT COMMENT '照片URL列表，JSON格式存储，如：["url1","url2","url3"]';

-- 2. 可选：新增用户任务分配表（如果需要更精细的任务分配控制）
-- 注意：这个表是可选的，如果使用简单的任务分配方案（通过创建人或部门过滤），可以不创建此表
CREATE TABLE IF NOT EXISTS item_stocktaking_user_assignment (
    assignment_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分配ID',
    stocktaking_id VARCHAR(50) NOT NULL COMMENT '盘点单ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    warehouse_id INT COMMENT '分配的仓库ID（可选）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_stocktaking_user (stocktaking_id, user_id),
    INDEX idx_user_stocktaking (user_id, stocktaking_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='盘点任务用户分配表';

-- 3. 验证修改结果
-- 查看item_stocktaking_detail表结构，确认photos字段已添加
-- DESCRIBE item_stocktaking_detail;

-- 4. 测试数据（可选）
-- 如果需要测试，可以插入一些测试数据
-- UPDATE item_stocktaking_detail 
-- SET photos = '["http://example.com/photo1.jpg", "http://example.com/photo2.jpg"]' 
-- WHERE detail_id = 'test_detail_id';
