package com.jingfang.asset_stocktaking.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.asset_stocktaking.module.entity.AssetStocktakingRecord;
import com.jingfang.asset_stocktaking.module.request.RecordSearchRequest;
import com.jingfang.asset_stocktaking.module.vo.StocktakingRecordVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 盘点记录数据访问层
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Mapper
public interface StocktakingRecordMapper extends BaseMapper<AssetStocktakingRecord> {

    /**
     * 分页查询盘点记录列表
     * 
     * @param page 分页对象
     * @param request 查询条件
     * @return 盘点记录列表
     */
    IPage<StocktakingRecordVo> selectRecordList(IPage<StocktakingRecordVo> page, @Param("request") RecordSearchRequest request);

    /**
     * 根据ID查询盘点记录详情
     * 
     * @param recordId 记录ID
     * @return 盘点记录详情
     */
    StocktakingRecordVo selectRecordById(@Param("recordId") String recordId);

    /**
     * 根据任务ID查询记录列表
     * 
     * @param taskId 任务ID
     * @return 记录列表
     */
    List<AssetStocktakingRecord> selectRecordByTaskId(@Param("taskId") String taskId);

    /**
     * 根据资产ID查询记录列表
     * 
     * @param assetId 资产ID
     * @return 记录列表
     */
    List<AssetStocktakingRecord> selectRecordByAssetId(@Param("assetId") String assetId);

    /**
     * 根据盘点人查询记录列表
     * 
     * @param inventoryUserId 盘点人ID
     * @return 记录列表
     */
    List<AssetStocktakingRecord> selectRecordByInventoryUser(@Param("inventoryUserId") Long inventoryUserId);

    /**
     * 查询异常记录（未找到或状态异常）
     * 
     * @param taskId 任务ID
     * @return 异常记录列表
     */
    List<AssetStocktakingRecord> selectAbnormalRecords(@Param("taskId") String taskId);

    /**
     * 查询差异记录（位置或状态与账面不符）
     * 
     * @param taskId 任务ID
     * @return 差异记录列表
     */
    List<StocktakingRecordVo> selectDifferenceRecords(@Param("taskId") String taskId);

    /**
     * 统计任务的记录情况
     * 
     * @param taskId 任务ID
     * @return 记录统计
     */
    java.util.Map<String, Object> countRecordsByTask(@Param("taskId") String taskId);

    /**
     * 统计计划的记录情况
     * 
     * @param planId 计划ID
     * @return 记录统计
     */
    java.util.Map<String, Object> countRecordsByPlan(@Param("planId") String planId);

    /**
     * 查询重复盘点的资产
     * 
     * @param planId 计划ID
     * @return 重复盘点的资产列表
     */
    List<java.util.Map<String, Object>> selectDuplicateRecords(@Param("planId") String planId);

    /**
     * 查询未盘点的资产
     * 
     * @param taskId 任务ID
     * @return 未盘点的资产列表
     */
    List<java.util.Map<String, Object>> selectMissingAssets(@Param("taskId") String taskId);

    /**
     * 批量插入盘点记录
     * 
     * @param records 记录列表
     * @return 影响行数
     */
    int batchInsertRecords(@Param("records") List<AssetStocktakingRecord> records);

    /**
     * 批量更新记录状态
     * 
     * @param recordIds 记录ID列表
     * @param foundStatus 发现状态
     * @return 影响行数
     */
    int batchUpdateFoundStatus(@Param("recordIds") List<String> recordIds, @Param("foundStatus") Integer foundStatus);

    /**
     * 根据资产编码查询记录
     * 
     * @param assetCode 资产编码
     * @param taskId 任务ID
     * @return 盘点记录
     */
    AssetStocktakingRecord selectRecordByAssetCode(@Param("assetCode") String assetCode, @Param("taskId") String taskId);

    /**
     * 检查资产是否已盘点
     * 
     * @param assetId 资产ID
     * @param taskId 任务ID
     * @return 是否已盘点
     */
    boolean checkAssetInventoried(@Param("assetId") String assetId, @Param("taskId") String taskId);

    /**
     * 查询任务的盘点进度
     * 
     * @param taskId 任务ID
     * @return 进度信息
     */
    java.util.Map<String, Object> selectTaskInventoryProgress(@Param("taskId") String taskId);

    /**
     * 删除任务的所有记录
     * 
     * @param taskId 任务ID
     * @return 影响行数
     */
    int deleteRecordsByTaskId(@Param("taskId") String taskId);
}
