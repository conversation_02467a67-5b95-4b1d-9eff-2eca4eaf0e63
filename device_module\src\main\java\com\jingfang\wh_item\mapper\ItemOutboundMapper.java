package com.jingfang.wh_item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jingfang.wh_item.module.entity.ItemOutbound;
import com.jingfang.wh_item.module.request.ItemOutboundSearchRequest;
import com.jingfang.wh_item.module.vo.ItemOutboundVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ItemOutboundMapper extends BaseMapper<ItemOutbound> {

    /**
     * 查询出库单详情（包含管理人员姓名）
     */
    ItemOutboundVo selectOutboundDetailById(@Param("outboundId") String outboundId);

    /**
     * 分页查询出库单列表（包含管理人员姓名）
     */
    IPage<ItemOutboundVo> selectOutboundListWithManager(
            Page<ItemOutboundVo> page, 
            @Param("request") ItemOutboundSearchRequest request
    );
} 