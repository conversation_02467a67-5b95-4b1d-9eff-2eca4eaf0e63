package com.jingfang.framework.websocket;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

/**
 * websocket 配置
 *
 * <AUTHOR>
 */
@Configuration
public class WebSocketConfig
{
    private static final Logger LOGGER = LoggerFactory.getLogger(WebSocketConfig.class);

    @Bean
    public ServerEndpointExporter serverEndpointExporter()
    {
        LOGGER.info("=== WebSocket 服务配置初始化 ===");
        LOGGER.info("正在创建 ServerEndpointExporter Bean...");
        ServerEndpointExporter exporter = new ServerEndpointExporter();
        LOGGER.info("ServerEndpointExporter 创建成功");
        LOGGER.info("WebSocket 端点将自动扫描并注册");
        LOGGER.info("=== WebSocket 服务配置完成 ===");
        return exporter;
    }
}
