package com.jingfang.wh_item.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 库存信息表
 * @TableName item_inventory
 */
@TableName(value ="item_inventory")
@Data
public class ItemInventory implements Serializable {
    /**
     * 库存ID
     */
    @TableId(type = IdType.INPUT)
    private String inventoryId;

    /**
     * 物品ID
     */
    private String itemId;

    /**
     * 当前库存数量
     */
    @TableField("current_quantity")
    private BigDecimal currentQuantity;

    /**
     * 安全库存(最低库存)
     */
    @TableField("safety_stock")
    private BigDecimal safetyStock;

    /**
     * 库存状态(1-正常, 2-不足, 3-过剩)
     */
    @TableField("stock_status")
    private Integer stockStatus;

    /**
     * 仓库ID
     */
    @TableField("warehouse_id")
    private Integer warehouseId;

    /**
     * 货架/库位编号
     */
    @TableField("shelf_location")
    private String shelfLocation;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
} 