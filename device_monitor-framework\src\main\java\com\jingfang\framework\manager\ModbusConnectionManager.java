package com.jingfang.framework.manager;

import com.ghgande.j2mod.modbus.facade.ModbusTCPMaster;
import com.jingfang.framework.pool.ModbusConnectionPool;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class ModbusConnectionManager {
    private Map<String, ModbusConnectionPool> connectionPools;
    private GenericObjectPoolConfig<ModbusTCPMaster> poolConfig;

    @PostConstruct
    public void init() {
        connectionPools = new ConcurrentHashMap<>();
        initializePoolConfig();
        log.info("ModbusConnectionManager initialized");
    }

    private void initializePoolConfig() {
        poolConfig = new GenericObjectPoolConfig<>();
        poolConfig.setMaxTotal(5);
        poolConfig.setMaxIdle(3);
        poolConfig.setMinIdle(1);
        poolConfig.setTestOnBorrow(true);
        poolConfig.setTestOnReturn(true);
        poolConfig.setTimeBetweenEvictionRunsMillis(30000);
    }

    /**
     * 获取或创建设备连接池
     * @param deviceId 设备ID
     * @param host 设备IP地址
     * @param port 设备端口
     * @return ModbusTCPMaster实例
     */
    public synchronized ModbusTCPMaster getConnection(String deviceId, String host, int port) throws Exception {
        ModbusConnectionPool pool = connectionPools.get(deviceId);

        // 如果连接池不存在，创建新的连接池
        if (pool == null) {
            pool = createConnectionPool(deviceId, host, port);
        }

        return pool.borrowConnection();
    }

    /**
     * 创建新的连接池
     * @param deviceId 设备ID
     * @param host 设备IP地址
     * @param port 设备端口
     * @return 创建的连接池实例
     */
    private ModbusConnectionPool createConnectionPool(String deviceId, String host, int port) {
        log.info("Creating new connection pool for device {}: {}:{}", deviceId, host, port);
        ModbusConnectionPool pool = new ModbusConnectionPool(host, port, poolConfig);
        connectionPools.put(deviceId, pool);
        return pool;
    }

    /**
     * 归还连接到连接池
     * @param deviceId 设备ID
     * @param master ModbusTCPMaster实例
     */
    public void returnConnection(String deviceId, ModbusTCPMaster master) {
        ModbusConnectionPool pool = connectionPools.get(deviceId);
        if (pool != null) {
            pool.returnConnection(master);
        } else {
            log.warn("Attempting to return connection to non-existent pool for device: {}", deviceId);
        }
    }

    /**
     * 关闭指定设备的连接池
     * @param deviceId 设备ID
     */
    public synchronized void closeConnectionPool(String deviceId) {
        ModbusConnectionPool pool = connectionPools.remove(deviceId);
        if (pool != null) {
            try {
                pool.closePool();
                log.info("Connection pool closed for device: {}", deviceId);
            } catch (Exception e) {
                log.error("Error closing connection pool for device {}: {}", deviceId, e.getMessage());
            }
        }
    }

    /**
     * 关闭所有连接池
     */
    @PreDestroy
    public void closeAllPools() {
        for (Map.Entry<String, ModbusConnectionPool> entry : connectionPools.entrySet()) {
            try {
                entry.getValue().closePool();
                log.info("Connection pool closed for device: {}", entry.getKey());
            } catch (Exception e) {
                log.error("Error closing connection pool for device {}: {}", entry.getKey(), e.getMessage());
            }
        }
        connectionPools.clear();
    }

    /**
     * 检查设备连接池是否存在
     * @param deviceId 设备ID
     * @return 是否存在
     */
    public boolean hasConnectionPool(String deviceId) {
        return connectionPools.containsKey(deviceId);
    }

    /**
     * 获取当前活跃的连接池数量
     * @return 连接池数量
     */
    public int getActivePoolCount() {
        return connectionPools.size();
    }
}
