package com.jingfang.asset_stocktaking.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jingfang.asset_stocktaking.module.dto.ActualValueInfo;
import com.jingfang.asset_stocktaking.module.dto.BookValueInfo;
import com.jingfang.asset_stocktaking.module.dto.DifferenceHandleInfo;
import com.jingfang.asset_stocktaking.module.dto.StocktakingDifferenceDto;
import com.jingfang.asset_stocktaking.module.entity.AssetStocktakingDifference;
import com.jingfang.asset_stocktaking.module.vo.DeptStatistics;
import com.jingfang.asset_stocktaking.module.vo.DifferenceStatistics;

import java.util.List;

/**
 * 盘点差异服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface StocktakingDifferenceService extends IService<AssetStocktakingDifference> {

    /**
     * 执行差异分析
     * 
     * @param planId 计划ID
     * @return 是否成功
     */
    boolean analyzeDifferences(String planId);

    /**
     * 创建差异记录
     * 
     * @param differenceDto 差异数据
     * @return 是否成功
     */
    boolean createDifference(StocktakingDifferenceDto differenceDto);

    /**
     * 编辑差异记录
     * 
     * @param differenceDto 差异数据
     * @return 是否成功
     */
    boolean editDifference(StocktakingDifferenceDto differenceDto);

    /**
     * 删除差异记录
     * 
     * @param diffId 差异ID
     * @return 是否成功
     */
    boolean deleteDifference(String diffId);

    /**
     * 批量删除差异记录
     * 
     * @param diffIds 差异ID列表
     * @return 是否成功
     */
    boolean batchDeleteDifferences(List<String> diffIds);

    /**
     * 分页查询差异列表
     * 
     * @param planId 计划ID
     * @param diffType 差异类型
     * @param handleStatus 处理状态
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    IPage<AssetStocktakingDifference> selectDifferenceList(String planId, Integer diffType, 
                                                          Integer handleStatus, Integer pageNum, Integer pageSize);

    /**
     * 根据计划ID查询差异列表
     * 
     * @param planId 计划ID
     * @return 差异列表
     */
    List<AssetStocktakingDifference> selectDifferenceByPlanId(String planId);

    /**
     * 根据差异类型查询差异列表
     * 
     * @param planId 计划ID
     * @param diffType 差异类型
     * @return 差异列表
     */
    List<AssetStocktakingDifference> selectDifferenceByType(String planId, Integer diffType);

    /**
     * 查询盘盈差异列表
     * 
     * @param planId 计划ID
     * @return 盘盈差异列表
     */
    List<AssetStocktakingDifference> selectSurplusDifferences(String planId);

    /**
     * 查询盘亏差异列表
     * 
     * @param planId 计划ID
     * @return 盘亏差异列表
     */
    List<AssetStocktakingDifference> selectDeficitDifferences(String planId);

    /**
     * 查询状态差异列表
     * 
     * @param planId 计划ID
     * @return 状态差异列表
     */
    List<AssetStocktakingDifference> selectStatusDifferences(String planId);

    /**
     * 查询位置差异列表
     * 
     * @param planId 计划ID
     * @return 位置差异列表
     */
    List<AssetStocktakingDifference> selectLocationDifferences(String planId);

    /**
     * 查询待处理差异列表
     * 
     * @param planId 计划ID
     * @return 待处理差异列表
     */
    List<AssetStocktakingDifference> selectPendingDifferences(String planId);

    /**
     * 统计计划的差异情况
     * 
     * @param planId 计划ID
     * @return 差异统计
     */
    DifferenceStatistics selectDifferenceStatistics(String planId);

    /**
     * 统计各类型差异数量
     * 
     * @param planId 计划ID
     * @return 类型统计
     */
    List<java.util.Map<String, Object>> countDifferenceByType(String planId);

    /**
     * 统计各处理状态差异数量
     * 
     * @param planId 计划ID
     * @return 状态统计
     */
    List<java.util.Map<String, Object>> countDifferenceByHandleStatus(String planId);

    /**
     * 处理差异
     * 
     * @param diffId 差异ID
     * @param handleInfo 处理信息
     * @return 是否成功
     */
    boolean handleDifference(String diffId, DifferenceHandleInfo handleInfo);

    /**
     * 批量处理差异
     *
     * @param diffIds 差异ID列表
     * @param handleInfo 处理信息
     * @return 是否成功
     */
    boolean batchHandleDifferences(List<String> diffIds, DifferenceHandleInfo handleInfo);

    /**
     * 更新差异处理状态
     * 
     * @param diffId 差异ID
     * @param handleStatus 处理状态
     * @param handleSuggestion 处理建议
     * @return 是否成功
     */
    boolean updateDifferenceHandle(String diffId, Integer handleStatus, String handleSuggestion);

    /**
     * 批量更新差异处理状态
     * 
     * @param diffIds 差异ID列表
     * @param handleStatus 处理状态
     * @return 是否成功
     */
    boolean batchUpdateHandleStatus(List<String> diffIds, Integer handleStatus);

    /**
     * 根据资产ID查询差异记录
     * 
     * @param assetId 资产ID
     * @param planId 计划ID
     * @return 差异记录
     */
    AssetStocktakingDifference selectDifferenceByAssetId(String assetId, String planId);

    /**
     * 检查资产是否存在差异
     * 
     * @param assetId 资产ID
     * @param planId 计划ID
     * @return 是否存在差异
     */
    boolean checkAssetHasDifference(String assetId, String planId);

    /**
     * 生成差异处理建议
     * 
     * @param difference 差异记录
     * @return 处理建议
     */
    String generateHandleSuggestion(AssetStocktakingDifference difference);

    /**
     * 识别差异类型
     * 
     * @param assetId 资产ID
     * @param bookValue 账面信息
     * @param actualValue 实际信息
     * @return 差异类型
     */
    Integer identifyDifferenceType(String assetId,
                                  BookValueInfo bookValue,
                                  ActualValueInfo actualValue);

    /**
     * 查询部门差异统计
     * 
     * @param planId 计划ID
     * @return 部门差异统计列表
     */
    List<DeptStatistics> selectDeptDifferenceStatistics(String planId);

    /**
     * 导出差异记录
     * 
     * @param planId 计划ID
     * @param diffType 差异类型
     * @param handleStatus 处理状态
     * @return 差异列表
     */
    List<AssetStocktakingDifference> exportDifferences(String planId, Integer diffType, Integer handleStatus);
}
