package com.jingfang.asset_ledger.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jingfang.asset_ledger.module.dto.AssetDto;
import com.jingfang.asset_ledger.module.entity.AssetBaseInfo;
import com.jingfang.asset_ledger.module.entity.AssetMaintainInfo;
import com.jingfang.asset_ledger.module.request.AssetSearchRequest;
import com.jingfang.asset_ledger.module.vo.*;
import com.jingfang.asset_ledger.service.AssetBaseInfoService;
import com.jingfang.asset_ledger.service.AssetMaintainInfoService;
import com.jingfang.asset_part_relation.service.AssetPartRelationService;
import com.jingfang.maintenance_plan.service.MaintenancePlanService;
import com.jingfang.maintenance_task.module.service.MaintenanceTaskService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


@Slf4j
@Service
public class AssetService {

    @Resource
    private AssetBaseInfoService baseInfoService;

    @Resource
    private AssetMaintainInfoService maintainInfoService;

    @Resource
    private AssetPartRelationService assetPartRelationService;
    
    @Resource
    private MaintenancePlanService maintenancePlanService;
    
    @Resource
    private MaintenanceTaskService maintenanceTaskService;





    @Transactional
    public boolean add(AssetDto dto) {
        try {
            AssetBaseInfo baseInfo = dto.getBaseInfo();
            baseInfoService.add(baseInfo);
            AssetMaintainInfo maintainInfo = dto.getMaintainInfo();
            maintainInfo.setAssetId(baseInfo.getAssetId());
            maintainInfoService.save(maintainInfo);
            return true;
        }catch (Exception e){
            log.error(e.getMessage());
            return false;
        }
    }

    @Transactional
    public boolean edit(AssetDto dto) {
        try {
            baseInfoService.edit(dto.getBaseInfo());
            return true;
        }catch (Exception e){
            log.error(e.getMessage());
            return false;
        }
    }

    @Transactional
    public AssetDetailVo getDetail(String assetId) {
        AssetDetailVo vo = new AssetDetailVo();
        AssetDetailBaseInfoVo baseInfo = baseInfoService.selectBaseInfoById(assetId);
        AssetDetailMaintainInfoVo maintainInfoVo = maintainInfoService.selectMaintainInfoById(assetId);
        if(Optional.ofNullable(baseInfo).isPresent()){
            vo.setBaseInfo(baseInfo);
        }
        if (Optional.ofNullable(maintainInfoVo).isPresent()){
            vo.setMaintainInfo(maintainInfoVo);
        }
        
        // 查询关联的备品备件列表
        try {
            vo.setRelatedParts(assetPartRelationService.selectPartsByAssetId(assetId));
        } catch (Exception e) {
            log.warn("查询资产{}关联的备品备件失败: {}", assetId, e.getMessage());
            vo.setRelatedParts(new ArrayList<>());
        }
        
        // 查询关联的维护计划列表
        try {
            vo.setMaintenancePlans(maintenancePlanService.selectMaintenancePlansByAssetId(assetId));
        } catch (Exception e) {
            log.warn("查询资产{}关联的维护计划失败: {}", assetId, e.getMessage());
            vo.setMaintenancePlans(new ArrayList<>());
        }
        
        // 查询关联的维护任务列表（最近20条）
        try {
            List<com.jingfang.maintenance_task.module.vo.MaintenanceTaskVo> allTasks = maintenanceTaskService.getTasksByAssetId(assetId);
            // 只取最近的20条任务
            List<com.jingfang.maintenance_task.module.vo.MaintenanceTaskVo> recentTasks = allTasks.stream()
                    .limit(20)
                    .collect(java.util.stream.Collectors.toList());
            vo.setMaintenanceTasks(recentTasks);
        } catch (Exception e) {
            log.warn("查询资产{}关联的维护任务失败: {}", assetId, e.getMessage());
            vo.setMaintenanceTasks(new ArrayList<>());
        }
        
        // 构建维护统计信息
        try {
            MaintenanceStatistics statistics = buildMaintenanceStatistics(assetId);
            vo.setMaintenanceStatistics(statistics);
        } catch (Exception e) {
            log.warn("构建资产{}维护统计信息失败: {}", assetId, e.getMessage());
            vo.setMaintenanceStatistics(new MaintenanceStatistics());
        }
        
        return vo;
    }


    @Transactional
    public boolean deleteAsset(String assetId) {
        try {
            baseInfoService.deleteBaseInfo(assetId);
            return true;
        }catch (Exception e){
            log.error(e.getMessage());
            return false;
        }
    }

    @Transactional
    public String batchAddByExcel(String jsonBody) {
        int count = 0;
        try {
            List<AssetDto> dtoList = JSON.parseArray(jsonBody,AssetDto.class);
            if(!dtoList.isEmpty()){
                dtoList.forEach(this::add);
                count++;
            }
            return "批量添加"+count+"条数据成功";
        }catch (Exception e){
            log.error(e.getMessage());
            count++;
            return "添加第"+count+"数据时出现错误，添加失败";
        }
    }
    
    /**
     * 导入Excel数据
     * 
     * @param assetList Excel导入的资产列表
     * @return 导入结果信息
     */
    @Transactional
    public String importData(List<AssetDto> assetList) {
        if (assetList == null || assetList.isEmpty()) {
            return "导入数据为空";
        }
        
        int successCount = 0;
        int failCount = 0;
        StringBuilder failMsg = new StringBuilder();
        
        for (int i = 0; i < assetList.size(); i++) {
            try {
                AssetDto asset = assetList.get(i);
                boolean result = this.add(asset);
                if (result) {
                    successCount++;
                } else {
                    failCount++;
                    failMsg.append("<br/>第 ").append(i + 1).append(" 条数据导入失败");
                }
            } catch (Exception e) {
                failCount++;
                String msg = "<br/>第 " + (i + 1) + " 条数据导入失败：" + e.getMessage();
                log.error(msg, e);
                failMsg.append(msg);
            }
        }
        
        if (failCount > 0) {
            return "成功导入 " + successCount + " 条，失败 " + failCount + " 条" + failMsg;
        } else {
            return "成功导入 " + successCount + " 条";
        }
    }
    
    /**
     * 查询资产列表数据用于导出
     * 
     * @param request 查询条件
     * @return 资产数据列表
     */
    public List<AssetDto> selectAssetListForExport(AssetSearchRequest request) {
        try {
            // 查询资产列表，不分页
            IPage<AssetBaseInfoVo> page = new Page<>(1, Integer.MAX_VALUE);
            IPage<AssetBaseInfoVo> assetPage = baseInfoService.selectAssetList(request, page);
            
            List<AssetDto> exportList = new ArrayList<>();
            if (assetPage != null && assetPage.getRecords() != null) {
                for (AssetBaseInfoVo vo : assetPage.getRecords()) {
                    // 查询详细信息
                    AssetDetailVo detailVo = getDetail(vo.getAssetId());
                    
                    // 创建AssetDto对象
                    AssetDto dto = new AssetDto();
                    if (detailVo != null && detailVo.getBaseInfo() != null) {
                        // 构建完整的AssetBaseInfo对象
                        dto.setBaseInfo(detailVo.getBaseInfo().toAssetBaseInfo());
                    }
                    
                    exportList.add(dto);
                }
            }
            
            return exportList;
        } catch (Exception e) {
            log.error("查询导出数据失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 构建维护统计信息
     * 
     * @param assetId 资产ID
     * @return 维护统计信息
     */
    private MaintenanceStatistics buildMaintenanceStatistics(String assetId) {
        MaintenanceStatistics statistics = new MaintenanceStatistics();
        
        // 查询维护计划统计
        try {
            List<com.jingfang.maintenance_plan.module.vo.MaintenancePlanVo> plans = maintenancePlanService.selectMaintenancePlansByAssetId(assetId);
            statistics.setTotalPlans(plans.size());
            
            long activePlans = plans.stream()
                    .filter(plan -> plan.getStatus() != null && plan.getStatus() == 1)
                    .count();
            statistics.setActivePlans((int) activePlans);
            
            // 获取下次计划维护时间（最近的一次）
            java.util.Date nextMaintenanceTime = plans.stream()
                    .filter(plan -> plan.getNextMaintenanceTime() != null && plan.getStatus() == 1)
                    .map(com.jingfang.maintenance_plan.module.vo.MaintenancePlanVo::getNextMaintenanceTime)
                    .min(java.util.Date::compareTo)
                    .orElse(null);
            statistics.setNextMaintenanceTime(nextMaintenanceTime);
        } catch (Exception e) {
            log.warn("查询资产{}维护计划统计失败: {}", assetId, e.getMessage());
            statistics.setTotalPlans(0);
            statistics.setActivePlans(0);
        }
        
        // 查询维护任务统计
        try {
            java.util.Map<String, Integer> taskStats = maintenanceTaskService.getTaskStatisticsByAssetId(assetId);
            statistics.setTotalTasks(taskStats.getOrDefault("totalTasks", 0));
            statistics.setPendingTasks(taskStats.getOrDefault("pendingTasks", 0));
            statistics.setInProgressTasks(taskStats.getOrDefault("inProgressTasks", 0));
            statistics.setCompletedTasks(taskStats.getOrDefault("completedTasks", 0));
            statistics.setOverdueTasks(taskStats.getOrDefault("overdueTasks", 0));
            
            // 获取最近一次维护时间（最近完成的任务）
            List<com.jingfang.maintenance_task.module.vo.MaintenanceTaskVo> completedTasks = maintenanceTaskService.getTasksByAssetId(assetId)
                    .stream()
                    .filter(task -> task.getStatus() != null && task.getStatus() == 7 && task.getActualEndTime() != null)
                    .sorted((t1, t2) -> t2.getActualEndTime().compareTo(t1.getActualEndTime()))
                    .collect(java.util.stream.Collectors.toList());
            
            if (!completedTasks.isEmpty()) {
                statistics.setLastMaintenanceTime(completedTasks.get(0).getActualEndTime());
            }
        } catch (Exception e) {
            log.warn("查询资产{}维护任务统计失败: {}", assetId, e.getMessage());
            statistics.setTotalTasks(0);
            statistics.setPendingTasks(0);
            statistics.setInProgressTasks(0);
            statistics.setCompletedTasks(0);
            statistics.setOverdueTasks(0);
        }
        
        return statistics;
    }
}
