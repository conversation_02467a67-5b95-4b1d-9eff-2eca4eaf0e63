# MQTT设备参数写入功能说明

## 概述

本文档介绍通过MQTT协议向设备写入参数的功能实现。系统支持单个参数写入和批量参数写入，通过向指定的MQTT主题发送消息来实现设备参数的远程控制。

## MQTT通信协议

### 写入请求格式

**主题**: `/sys/thing/node/property/set/device_monitor_client`

**JSON格式**:
```json
{
  "id": "0",
  "version": "1.0",
  "ack": 0,
  "params": [
    {
      "clientID": "************",
      "properties": [
        {
          "name": "阀自动模式下调节步长",
          "value": "6"
        }
      ]
    }
  ]
}
```

### 写入响应格式

**主题**: `/sys/thing/node/property/set_reply/device_monitor_client`

**JSON格式**:
```json
{
  "id": "0",
  "version": "1.0",
  "code": 0,
  "message": "success",
  "params": [
    {
      "clientID": "************",
      "properties": [
        {
          "name": "阀自动模式下调节步长",
          "code": 0,
          "message": "success",
          "value": "6"
        }
      ]
    }
  ]
}
```

## 字段说明

### 请求字段
- `id`: 请求ID，固定为"0"
- `version`: 版本号，固定为"1.0"
- `ack`: 确认标志，固定为0
- `params`: 参数列表
  - `clientID`: 设备IP地址，用于确定目标设备
  - `properties`: 要写入的属性列表
    - `name`: 参数名称
    - `value`: 要写入的参数值

### 响应字段
- `id`: 响应ID，对应请求ID
- `version`: 版本号
- `code`: 响应代码，0表示成功
- `message`: 响应消息
- `params`: 写入结果列表
  - `clientID`: 设备IP地址
  - `properties`: 属性写入结果
    - `name`: 参数名称
    - `code`: 写入结果代码，0表示成功
    - `message`: 写入结果消息
    - `value`: 写入的值

## API接口

### 1. 单个参数写入

**接口**: `POST /device/param/write`

**参数**:
- `deviceId` (Long): 设备ID
- `paramName` (String): 参数名称
- `paramValue` (String): 参数值

**响应**:
```json
{
  "code": 200,
  "msg": "参数写入成功"
}
```

### 2. 批量参数写入

**接口**: `POST /device/params/write`

**参数**:
- `deviceId` (Long): 设备ID（URL参数）
- `paramMap` (Map): 参数映射（请求体JSON）

**请求体示例**:
```json
{
  "温度设定值": "25.0",
  "工作模式": "1",
  "运行状态": "1"
}
```

**响应**:
```json
{
  "code": 200,
  "msg": "参数批量写入成功"
}
```

## 参数验证

### 1. 设备状态检查
- 设备必须在线才能进行参数写入
- 系统会检查Redis中的设备在线状态

### 2. 参数权限验证
- 只有 `rw_type = 1`（只写）或 `rw_type = 2`（读写）的参数才能写入
- `rw_type = 0`（只读）的参数会被拒绝写入

### 3. 参数范围验证
- 如果参数配置了 `range_start` 和 `range_end`，系统会验证写入值是否在有效范围内
- 非数值类型的参数不进行范围验证

### 4. 参数存在性验证
- 系统会检查参数是否在 `device_param` 表中存在
- 只有已配置的参数才能进行写入操作

## 使用示例

### 1. 单个参数写入
```bash
curl -X POST "http://localhost:8080/device/param/write" \
  -d "deviceId=1&paramName=温度设定值&paramValue=25.0"
```

### 2. 批量参数写入
```bash
curl -X POST "http://localhost:8080/device/params/write?deviceId=1" \
  -H "Content-Type: application/json" \
  -d '{
    "温度设定值": "25.0",
    "工作模式": "1",
    "运行状态": "1"
  }'
```

### 3. 测试接口

#### 直接MQTT写入测试
```bash
curl -X POST "http://localhost:8080/device/property/test/write" \
  -d "deviceIp=************&propertyName=阀自动模式下调节步长&propertyValue=6"
```

#### 通过Service写入测试
```bash
curl -X POST "http://localhost:8080/device/property/test/write-param" \
  -d "deviceId=1&paramName=温度设定值&paramValue=25.0"
```

## 配置说明

### 1. MQTT配置

在 `application.yml` 中配置MQTT写入相关主题：

```yaml
mqtt:
  server-uri: tcp://***************:10883
  client-id: device_monitor_client
  username: admin
  password: 429498517
  device:
    property:
      write:
        topic: /sys/thing/node/property/set/device_monitor_client
      write:
        response:
          topic: /sys/thing/node/property/set_reply/device_monitor_client
      timeout: 15
```

### 2. 参数配置

确保要写入的参数在 `device_param` 表中正确配置：

```sql
INSERT INTO device_param (
    device_id, param_name, param_unit, 
    range_start, range_end, 
    rw_type, param_type, del_flag
) VALUES (
    1, '温度设定值', '°C', 
    10.0, 40.0, 
    2, 3, 0  -- 读写，控制参数
);
```

## 错误处理

### 1. 常见错误码

- **设备离线**: 设备不在线时无法写入参数
- **参数不存在**: 参数未在数据库中配置
- **权限不足**: 参数为只读类型
- **值超出范围**: 参数值超出配置的有效范围
- **MQTT超时**: MQTT通信超时（默认15秒）

### 2. 错误响应示例

```json
{
  "code": 500,
  "msg": "参数写入失败",
  "data": null
}
```

## 安全注意事项

1. **权限控制**: 确保只有授权用户才能执行参数写入操作
2. **参数验证**: 严格验证写入参数的合法性和安全性
3. **操作日志**: 记录所有参数写入操作的日志
4. **范围限制**: 设置合理的参数值范围，防止异常值
5. **设备保护**: 避免频繁写入操作对设备造成影响

## 性能优化

1. **批量写入**: 使用批量写入接口减少MQTT通信次数
2. **异步处理**: MQTT写入使用异步方式，支持超时控制
3. **连接复用**: 复用MQTT连接，减少连接开销
4. **参数缓存**: 缓存参数配置信息，减少数据库查询

## 扩展功能

1. **写入历史**: 记录参数写入历史，便于追踪和审计
2. **批量设备**: 支持向多个设备同时写入相同参数
3. **定时写入**: 支持定时或周期性的参数写入
4. **条件写入**: 基于设备状态或其他条件的智能写入

通过这个MQTT写数据功能，您可以实现对设备参数的远程控制和配置！
