package com.jingfang.maintenance_task.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 维护任务备品备件使用记录表
 * @TableName maintenance_task_part
 */
@TableName(value = "maintenance_task_part")
@Data
public class MaintenanceTaskPart implements Serializable {
    
    /**
     * 记录ID
     */
    @TableId(type = IdType.INPUT)
    private String recordId;
    
    /**
     * 维护任务ID
     */
    private String taskId;
    
    /**
     * 备品备件ID
     */
    private String partId;
    
    /**
     * 计划使用数量
     */
    private BigDecimal plannedQuantity;
    
    /**
     * 实际使用数量
     */
    private BigDecimal actualQuantity;
    
    /**
     * 使用状态(1-计划使用, 2-已使用, 3-未使用)
     */
    private Integer useStatus;
    
    /**
     * 备注说明
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 是否删除(0-否, 1-是)
     */
    private Integer deleted;
    
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
} 