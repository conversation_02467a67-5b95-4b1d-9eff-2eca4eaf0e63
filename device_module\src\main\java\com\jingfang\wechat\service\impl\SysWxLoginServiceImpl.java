package com.jingfang.wechat.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.common.core.domain.entity.SysUser;
import com.jingfang.common.core.domain.model.LoginUser;
import com.jingfang.common.exception.ServiceException;
import com.jingfang.common.utils.DateUtils;
import com.jingfang.common.utils.SecurityUtils;
import com.jingfang.common.utils.StringUtils;
import com.jingfang.common.utils.http.HttpUtils;
import com.jingfang.framework.web.service.TokenService;
import com.jingfang.framework.web.service.UserDetailsServiceImpl;
import com.jingfang.wechat.mapper.SysUserWxMapper;
import com.jingfang.wechat.mapper.SysWxUserMapper;
import com.jingfang.system.service.ISysUserService;
import com.jingfang.wechat.module.entity.SysUserWx;
import com.jingfang.wechat.module.entity.SysWxUser;
import com.jingfang.wechat.service.ISysWxLoginService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信登录服务实现
 */
@Service
public class SysWxLoginServiceImpl implements ISysWxLoginService {
    private static final Logger log = LoggerFactory.getLogger(SysWxLoginServiceImpl.class);

    @Resource
    private SysWxUserMapper wxUserMapper;

    @Resource
    private SysUserWxMapper userWxMapper;

    @Resource
    private ISysUserService userService;

    @Resource
    private UserDetailsServiceImpl userDetailsService;

    @Resource
    private TokenService tokenService;

    @Value("${wx.appid}")
    private String appid;

    @Value("${wx.secret}")
    private String secret;

    /**
     * 微信小程序登录
     */
    @Override
    public AjaxResult wxLogin(String code, Map<String, Object> userInfo)
    {
        if (StringUtils.isEmpty(code))
        {
            return AjaxResult.error("登录凭证不能为空");
        }

        try
        {
            // 1. 通过code获取openid
            String url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + appid +
                    "&secret=" + secret + "&js_code=" + code + "&grant_type=authorization_code";
            String result = HttpUtils.sendGet(url);

            if (StringUtils.isEmpty(result))
            {
                log.error("调用微信接口获取openid失败");
                return AjaxResult.error("微信登录失败，请稍后重试");
            }

            JSONObject json = JSONObject.parseObject(result);

            if (json.containsKey("errcode") && json.getIntValue("errcode") != 0)
            {
                log.error("微信登录失败：{}", json.getString("errmsg"));
                return AjaxResult.error("微信登录失败：" + json.getString("errmsg"));
            }

            String openid = json.getString("openid");
            String sessionKey = json.getString("session_key");

            if (StringUtils.isEmpty(openid))
            {
                log.error("获取微信openid失败");
                return AjaxResult.error("微信登录失败，请稍后重试");
            }

            // 2. 保存或更新微信用户信息
            SysWxUser wxUser = wxUserMapper.selectWxUserByOpenid(openid);
            if (wxUser == null)
            {
                wxUser = new SysWxUser();
                wxUser.setOpenid(openid);
                if (userInfo != null)
                {
                    wxUser.setNickName((String) userInfo.get("nickName"));
                    wxUser.setAvatarUrl((String) userInfo.get("avatarUrl"));
                    wxUser.setGender(String.valueOf(userInfo.get("gender")));
                    wxUser.setCountry((String) userInfo.get("country"));
                    wxUser.setProvince((String) userInfo.get("province"));
                    wxUser.setCity((String) userInfo.get("city"));
                }
                wxUser.setCreateTime(DateUtils.getNowDate());
                wxUserMapper.insertWxUser(wxUser);
            }
            else
            {
                if (userInfo != null)
                {
                    wxUser.setNickName((String) userInfo.get("nickName"));
                    wxUser.setAvatarUrl((String) userInfo.get("avatarUrl"));
                    wxUser.setGender(String.valueOf(userInfo.get("gender")));
                    wxUser.setCountry((String) userInfo.get("country"));
                    wxUser.setProvince((String) userInfo.get("province"));
                    wxUser.setCity((String) userInfo.get("city"));
                }
                wxUser.setUpdateTime(DateUtils.getNowDate());
                wxUserMapper.updateWxUser(wxUser);
            }

            // 3. 查询是否已绑定系统用户
            SysUserWx userWx = userWxMapper.selectUserWxByWxUserId(wxUser.getWxUserId());

            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("openid", openid);
            resultMap.put("sessionKey", sessionKey);
            resultMap.put("isBind", false);

            if (userWx != null && "0".equals(userWx.getStatus()))
            {
                // 已绑定，返回系统用户token
                SysUser user = userService.selectUserById(userWx.getUserId());
                if (user != null && "0".equals(user.getStatus()))
                {
                    String token = tokenService.createToken((LoginUser) userDetailsService.createLoginUser(user));
                    resultMap.put("token", token);
                    resultMap.put("isBind", true);
                    resultMap.put("user", user);
                }
            }

            return AjaxResult.success(resultMap);
        }
        catch (Exception e)
        {
            log.error("微信登录异常", e);
            return AjaxResult.error("微信登录失败，请稍后重试");
        }
    }

    /**
     * 绑定系统账号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult bindAccount(String openid, String username, String password, String bindType)
    {
        if (StringUtils.isEmpty(openid))
        {
            return AjaxResult.error("微信用户标识不能为空");
        }

        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password))
        {
            return AjaxResult.error("用户名和密码不能为空");
        }

        try
        {
            // 1. 验证系统账号
            SysUser user = userService.selectUserByUserName(username);
            if (user == null)
            {
                return AjaxResult.error("用户不存在");
            }

            if (!"0".equals(user.getStatus()))
            {
                return AjaxResult.error("用户已被禁用");
            }

            if (!SecurityUtils.matchesPassword(password, user.getPassword()))
            {
                return AjaxResult.error("用户名或密码错误");
            }

            // 2. 获取微信用户
            SysWxUser wxUser = wxUserMapper.selectWxUserByOpenid(openid);
            if (wxUser == null)
            {
                return AjaxResult.error("微信用户不存在");
            }

            // 3. 检查是否已绑定
            SysUserWx userWx = userWxMapper.selectUserWxByUserIdAndWxUserId(user.getUserId(), wxUser.getWxUserId());
            if (userWx != null)
            {
                if ("0".equals(userWx.getStatus()))
                {
                    return AjaxResult.error("该账号已绑定，请勿重复绑定");
                }
                else
                {
                    // 更新为已绑定状态
                    userWx.setBindTime(DateUtils.getNowDate());
                    userWx.setBindType(StringUtils.isEmpty(bindType) ? "0" : bindType);
                    userWx.setStatus("0");
                    userWxMapper.updateUserWx(userWx);
                }
            }
            else
            {
                // 4. 创建绑定关系
                userWx = new SysUserWx();
                userWx.setUserId(user.getUserId());
                userWx.setWxUserId(wxUser.getWxUserId());
                userWx.setBindTime(DateUtils.getNowDate());
                userWx.setBindType(StringUtils.isEmpty(bindType) ? "0" : bindType);
                userWx.setStatus("0");
                userWxMapper.insertUserWx(userWx);
            }

            // 5. 生成token
            String token = tokenService.createToken((LoginUser) userDetailsService.createLoginUser(user));

            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("token", token);
            resultMap.put("user", user);

            return AjaxResult.success("绑定成功", resultMap);
        }
        catch (Exception e)
        {
            log.error("绑定系统账号异常", e);
            throw new ServiceException("绑定失败，请稍后重试");
        }
    }

    /**
     * 解绑系统账号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult unbindAccount(String openid, Long userId)
    {
        if (StringUtils.isEmpty(openid))
        {
            return AjaxResult.error("微信用户标识不能为空");
        }

        if (userId == null)
        {
            return AjaxResult.error("用户ID不能为空");
        }

        try
        {
            // 1. 获取微信用户
            SysWxUser wxUser = wxUserMapper.selectWxUserByOpenid(openid);
            if (wxUser == null)
            {
                return AjaxResult.error("微信用户不存在");
            }

            // 2. 查询绑定关系
            SysUserWx userWx = userWxMapper.selectUserWxByUserIdAndWxUserId(userId, wxUser.getWxUserId());
            if (userWx == null || !"0".equals(userWx.getStatus()))
            {
                return AjaxResult.error("未找到绑定关系");
            }

            // 3. 更新为解绑状态
            userWx.setStatus("1");
            userWxMapper.updateUserWx(userWx);

            return AjaxResult.success("解绑成功");
        }
        catch (Exception e)
        {
            log.error("解绑系统账号异常", e);
            throw new ServiceException("解绑失败，请稍后重试");
        }
    }
}
