#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资产盘点功能API测试脚本
用于测试盘点相关的REST API接口

@author: jingfang
@date: 2025-01-14
"""

import requests
import json
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

class StocktakingAPITester:
    """盘点API测试类"""
    
    def __init__(self, base_url: str = "http://localhost:8080", username: str = "admin", password: str = "admin123"):
        self.base_url = base_url
        self.username = username
        self.password = password
        self.token = None
        self.headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
    def login(self) -> bool:
        """登录获取token"""
        login_url = f"{self.base_url}/login"
        login_data = {
            "username": self.username,
            "password": self.password
        }
        
        try:
            response = requests.post(login_url, json=login_data, headers=self.headers)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 200:
                    self.token = result.get('token')
                    self.headers['Authorization'] = f"Bearer {self.token}"
                    print(f"✅ 登录成功，获取到token: {self.token[:20]}...")
                    return True
            print(f"❌ 登录失败: {response.text}")
            return False
        except Exception as e:
            print(f"❌ 登录异常: {str(e)}")
            return False
    
    def test_plan_crud(self) -> Optional[str]:
        """测试盘点计划CRUD操作"""
        print("\n🧪 开始测试盘点计划CRUD操作...")
        
        # 1. 创建盘点计划
        plan_data = {
            "planName": f"API测试盘点计划_{int(time.time())}",
            "planType": 1,  # 全盘
            "startDate": datetime.now().strftime("%Y-%m-%d"),
            "endDate": (datetime.now() + timedelta(days=7)).strftime("%Y-%m-%d"),
            "responsibleUserId": 1,
            "remark": "这是一个API测试盘点计划",
            "scopeDetail": {
                "includeSubDept": True,
                "minValue": 0.0,
                "maxValue": 100000.0,
                "deptIds": [100, 101],
                "categoryIds": [1, 2],
                "locationIds": [1, 2]
            }
        }
        
        # 创建计划
        create_url = f"{self.base_url}/asset/stocktaking/plan"
        response = requests.post(create_url, json=plan_data, headers=self.headers)
        
        if response.status_code != 200 or response.json().get('code') != 200:
            print(f"❌ 创建盘点计划失败: {response.text}")
            return None
        
        print("✅ 创建盘点计划成功")
        
        # 2. 查询计划列表获取ID
        list_url = f"{self.base_url}/asset/stocktaking/plan/list"
        search_data = {
            "planName": plan_data["planName"],
            "pageNum": 1,
            "pageSize": 10
        }
        
        response = requests.post(list_url, json=search_data, headers=self.headers)
        if response.status_code != 200:
            print(f"❌ 查询计划列表失败: {response.text}")
            return None
        
        result = response.json()
        if result.get('code') != 200 or not result.get('rows'):
            print(f"❌ 未找到创建的计划: {result}")
            return None
        
        plan_id = result['rows'][0]['planId']
        print(f"✅ 查询到计划ID: {plan_id}")
        
        # 3. 查询计划详情
        detail_url = f"{self.base_url}/asset/stocktaking/plan/{plan_id}"
        response = requests.get(detail_url, headers=self.headers)
        
        if response.status_code != 200 or response.json().get('code') != 200:
            print(f"❌ 查询计划详情失败: {response.text}")
            return None
        
        print("✅ 查询计划详情成功")
        
        # 4. 修改计划
        plan_data["planId"] = plan_id
        plan_data["planName"] = f"修改后的API测试计划_{int(time.time())}"
        plan_data["remark"] = "修改后的备注"
        
        update_url = f"{self.base_url}/asset/stocktaking/plan"
        response = requests.put(update_url, json=plan_data, headers=self.headers)
        
        if response.status_code != 200 or response.json().get('code') != 200:
            print(f"❌ 修改计划失败: {response.text}")
            return None
        
        print("✅ 修改计划成功")
        
        return plan_id
    
    def test_plan_workflow(self, plan_id: str) -> bool:
        """测试盘点计划工作流"""
        print(f"\n🔄 开始测试盘点计划工作流，计划ID: {plan_id}")
        
        # 1. 提交审批
        submit_url = f"{self.base_url}/asset/stocktaking/plan/{plan_id}/submit"
        response = requests.post(submit_url, headers=self.headers)
        
        if response.status_code != 200 or response.json().get('code') != 200:
            print(f"❌ 提交审批失败: {response.text}")
            return False
        
        print("✅ 提交审批成功")
        
        # 2. 审批通过
        approve_url = f"{self.base_url}/asset/stocktaking/plan/{plan_id}/approve"
        approve_data = {"approvalComment": "API测试审批通过"}
        response = requests.post(approve_url, params=approve_data, headers=self.headers)
        
        if response.status_code != 200 or response.json().get('code') != 200:
            print(f"❌ 审批通过失败: {response.text}")
            return False
        
        print("✅ 审批通过成功")
        
        return True
    
    def test_task_operations(self, plan_id: str) -> Optional[str]:
        """测试任务操作"""
        print(f"\n📋 开始测试任务操作，计划ID: {plan_id}")
        
        # 1. 创建任务
        task_data = {
            "planId": plan_id,
            "taskName": f"API测试任务_{int(time.time())}",
            "assignedUserId": 1,
            "expectedCount": 10,
            "distributionConfig": {
                "distributionType": 1,
                "maxAssetCount": 50,
                "autoAssign": True,
                "priority": 2
            }
        }
        
        create_url = f"{self.base_url}/asset/stocktaking/task"
        response = requests.post(create_url, json=task_data, headers=self.headers)
        
        if response.status_code != 200 or response.json().get('code') != 200:
            print(f"❌ 创建任务失败: {response.text}")
            return None
        
        print("✅ 创建任务成功")
        
        # 2. 查询任务列表
        list_url = f"{self.base_url}/asset/stocktaking/task/list"
        search_data = {
            "planId": plan_id,
            "pageNum": 1,
            "pageSize": 10
        }
        
        response = requests.post(list_url, json=search_data, headers=self.headers)
        if response.status_code != 200:
            print(f"❌ 查询任务列表失败: {response.text}")
            return None
        
        result = response.json()
        if result.get('code') != 200 or not result.get('rows'):
            print(f"❌ 未找到创建的任务: {result}")
            return None
        
        task_id = result['rows'][0]['taskId']
        print(f"✅ 查询到任务ID: {task_id}")
        
        # 3. 开始任务
        start_url = f"{self.base_url}/asset/stocktaking/task/{task_id}/start"
        response = requests.post(start_url, headers=self.headers)
        
        if response.status_code != 200 or response.json().get('code') != 200:
            print(f"❌ 开始任务失败: {response.text}")
            return None
        
        print("✅ 开始任务成功")
        
        return task_id
    
    def test_record_operations(self, task_id: str) -> bool:
        """测试记录操作"""
        print(f"\n📝 开始测试记录操作，任务ID: {task_id}")
        
        # 1. 创建单个记录
        record_data = {
            "taskId": task_id,
            "assetId": f"test-asset-{int(time.time())}",
            "assetCode": f"TEST-{int(time.time())}",
            "foundStatus": 1,
            "actualLocation": "API测试位置",
            "actualStatus": 1,
            "remark": "API测试记录"
        }
        
        create_url = f"{self.base_url}/asset/stocktaking/record"
        response = requests.post(create_url, json=record_data, headers=self.headers)
        
        if response.status_code != 200 or response.json().get('code') != 200:
            print(f"❌ 创建记录失败: {response.text}")
            return False
        
        print("✅ 创建单个记录成功")
        
        # 2. 批量创建记录
        batch_data = {
            "taskId": task_id,
            "batchRecords": [
                {
                    "assetId": f"batch-asset-{i}",
                    "assetCode": f"BATCH-{i:03d}",
                    "foundStatus": 1 if i % 2 == 0 else 0,
                    "actualLocation": f"批量测试位置{i}",
                    "actualStatus": 1,
                    "remark": f"批量测试记录{i}"
                }
                for i in range(1, 6)
            ]
        }
        
        batch_url = f"{self.base_url}/asset/stocktaking/record/batch"
        response = requests.post(batch_url, json=batch_data, headers=self.headers)
        
        if response.status_code != 200 or response.json().get('code') != 200:
            print(f"❌ 批量创建记录失败: {response.text}")
            return False
        
        print("✅ 批量创建记录成功")
        
        # 3. 查询记录列表
        list_url = f"{self.base_url}/asset/stocktaking/record/list"
        search_data = {
            "taskId": task_id,
            "pageNum": 1,
            "pageSize": 20
        }
        
        response = requests.post(list_url, json=search_data, headers=self.headers)
        if response.status_code != 200 or response.json().get('code') != 200:
            print(f"❌ 查询记录列表失败: {response.text}")
            return False
        
        print("✅ 查询记录列表成功")
        
        # 4. 测试扫码盘点
        scan_url = f"{self.base_url}/asset/stocktaking/record/scan"
        scan_data = {
            "taskId": task_id,
            "scanContent": f"SCAN-{int(time.time())}"
        }
        
        response = requests.post(scan_url, params=scan_data, headers=self.headers)
        if response.status_code != 200 or response.json().get('code') != 200:
            print(f"❌ 扫码盘点失败: {response.text}")
            return False
        
        print("✅ 扫码盘点成功")
        
        return True
    
    def test_difference_operations(self, plan_id: str) -> bool:
        """测试差异操作"""
        print(f"\n🔍 开始测试差异操作，计划ID: {plan_id}")
        
        # 1. 执行差异分析
        analyze_url = f"{self.base_url}/asset/stocktaking/difference/analyze/{plan_id}"
        response = requests.post(analyze_url, headers=self.headers)
        
        if response.status_code != 200 or response.json().get('code') != 200:
            print(f"❌ 执行差异分析失败: {response.text}")
            return False
        
        print("✅ 执行差异分析成功")
        
        # 2. 查询差异列表
        list_url = f"{self.base_url}/asset/stocktaking/difference/list"
        params = {
            "planId": plan_id,
            "pageNum": 1,
            "pageSize": 10
        }
        
        response = requests.get(list_url, params=params, headers=self.headers)
        if response.status_code != 200 or response.json().get('code') != 200:
            print(f"❌ 查询差异列表失败: {response.text}")
            return False
        
        print("✅ 查询差异列表成功")
        
        # 3. 查询差异统计
        stats_url = f"{self.base_url}/asset/stocktaking/difference/statistics/{plan_id}"
        response = requests.get(stats_url, headers=self.headers)
        
        if response.status_code != 200 or response.json().get('code') != 200:
            print(f"❌ 查询差异统计失败: {response.text}")
            return False
        
        print("✅ 查询差异统计成功")
        
        return True
    
    def test_report_operations(self, plan_id: str) -> bool:
        """测试报告操作"""
        print(f"\n📊 开始测试报告操作，计划ID: {plan_id}")
        
        # 1. 生成汇总报告
        summary_url = f"{self.base_url}/asset/stocktaking/report/summary/{plan_id}"
        response = requests.get(summary_url, headers=self.headers)
        
        if response.status_code != 200 or response.json().get('code') != 200:
            print(f"❌ 生成汇总报告失败: {response.text}")
            return False
        
        print("✅ 生成汇总报告成功")
        
        # 2. 生成差异明细报告
        detail_url = f"{self.base_url}/asset/stocktaking/report/difference/{plan_id}"
        response = requests.get(detail_url, headers=self.headers)
        
        if response.status_code != 200 or response.json().get('code') != 200:
            print(f"❌ 生成差异明细报告失败: {response.text}")
            return False
        
        print("✅ 生成差异明细报告成功")
        
        # 3. 生成图表数据
        chart_url = f"{self.base_url}/asset/stocktaking/report/chart/{plan_id}"
        response = requests.get(chart_url, headers=self.headers)
        
        if response.status_code != 200 or response.json().get('code') != 200:
            print(f"❌ 生成图表数据失败: {response.text}")
            return False
        
        print("✅ 生成图表数据成功")
        
        return True
    
    def run_complete_test(self) -> bool:
        """运行完整测试流程"""
        print("🚀 开始运行资产盘点功能完整测试流程...")
        
        # 1. 登录
        if not self.login():
            return False
        
        # 2. 测试计划CRUD
        plan_id = self.test_plan_crud()
        if not plan_id:
            return False
        
        # 3. 测试计划工作流
        if not self.test_plan_workflow(plan_id):
            return False
        
        # 4. 测试任务操作
        task_id = self.test_task_operations(plan_id)
        if not task_id:
            return False
        
        # 5. 测试记录操作
        if not self.test_record_operations(task_id):
            return False
        
        # 6. 测试差异操作
        if not self.test_difference_operations(plan_id):
            return False
        
        # 7. 测试报告操作
        if not self.test_report_operations(plan_id):
            return False
        
        print("\n🎉 所有测试完成！资产盘点功能API测试通过！")
        return True

def main():
    """主函数"""
    print("=" * 60)
    print("资产盘点功能API测试脚本")
    print("=" * 60)
    
    # 创建测试实例
    tester = StocktakingAPITester()
    
    # 运行测试
    success = tester.run_complete_test()
    
    if success:
        print("\n✅ 测试结果：全部通过")
        exit(0)
    else:
        print("\n❌ 测试结果：存在失败")
        exit(1)

if __name__ == "__main__":
    main()
