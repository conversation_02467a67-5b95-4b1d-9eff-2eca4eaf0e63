package com.jingfang.wh_item_requisition.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 物品领用单明细表
 * @TableName item_requisition_detail
 */
@TableName(value = "item_requisition_detail")
@Data
public class ItemRequisitionDetail implements Serializable {
    
    /**
     * 明细ID
     */
    @TableId(type = IdType.AUTO)
    private Long detailId;
    
    /**
     * 领用单ID
     */
    private String requisitionId;
    
    /**
     * 物品ID
     */
    private String itemId;
    
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    
    /**
     * 货架位置
     */
    private String shelfLocation;
    
    /**
     * 申请数量
     */
    private BigDecimal requisitionQuantity;
    
    /**
     * 批准数量
     */
    private BigDecimal approvedQuantity;
    
    /**
     * 实际领用数量
     */
    private BigDecimal actualQuantity;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    
    private static final long serialVersionUID = 1L;
} 