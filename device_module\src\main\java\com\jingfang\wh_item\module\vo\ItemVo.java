package com.jingfang.wh_item.module.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 物品信息VO，用于列表展示
 */
@Data
public class ItemVo implements Serializable {
    
    /**
     * 物品ID
     */
    private String itemId;
    
    /**
     * 物品名称
     */
    private String itemName;
    
    /**
     * 物品编码
     */
    private String itemCode;
    
    /**
     * 物品类别(1-消耗品, 2-备品备件)
     */
    private Integer itemType;
    
    /**
     * 规格型号
     */
    private String specModel;
    
    /**
     * 单位
     */
    private String unit;
    
    /**
     * 总库存数量（所有仓库库存之和）
     */
    private BigDecimal totalQuantity;
    
    /**
     * 企业级安全库存
     */
    private BigDecimal safetyStock;
    
    /**
     * 最低库存状态（所有仓库中最严重的状态）
     * 1-正常, 2-不足, 3-过剩
     */
    private Integer minStockStatus;
    
    /**
     * 库存状态名称
     */
    private String stockStatusName;
    
    /**
     * 仓库数量（该物品分布在多少个仓库）
     */
    private Integer warehouseCount;
    
    /**
     * 仓库ID列表（该物品分布在哪些仓库中）
     */
    private List<Integer> warehouseIds;
    
    /**
     * 主要仓库ID（库存最多的仓库）
     */
    private Integer warehouseId;
    
    /**
     * 货架位置
     */
    private String shelfLocation;
    
    /**
     * 可用数量（主要仓库的当前库存）
     */
    private BigDecimal availableQuantity;
    
    /**
     * 是否有效期(0-无, 1-有)
     */
    private Integer hasExpiry;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    private static final long serialVersionUID = 1L;
} 