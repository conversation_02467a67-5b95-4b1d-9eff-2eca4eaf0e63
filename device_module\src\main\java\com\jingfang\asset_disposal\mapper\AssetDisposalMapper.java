package com.jingfang.asset_disposal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.asset_disposal.module.entity.AssetDisposal;
import com.jingfang.asset_disposal.module.request.AssetDisposalSearchRequest;
import com.jingfang.asset_disposal.module.vo.AssetDisposalVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 资产处置Mapper接口
 */
@Mapper
public interface AssetDisposalMapper extends BaseMapper<AssetDisposal> {
    
    /**
     * 分页查询资产处置申请
     */
    IPage<AssetDisposalVo> selectDisposalList(IPage<AssetDisposalVo> page, @Param("request") AssetDisposalSearchRequest request);
    
    /**
     * 根据ID查询资产处置详情
     */
    AssetDisposalVo selectDisposalById(@Param("disposalId") String disposalId);
    
    /**
     * 查询待审批的处置申请
     */
    List<AssetDisposalVo> selectPendingApprovalList(@Param("userId") Long userId);
    
    /**
     * 更新处置状态
     */
    int updateDisposalStatus(@Param("disposalId") String disposalId, @Param("status") Integer status);
    
    /**
     * 统计各状态的处置数量
     */
    List<Map<String, Object>> selectDisposalStatistics();

    /**
     * 统计待审核处置申请数
     */
    Long countPendingApproval();

    /**
     * 统计处置中申请数
     */
    Long countProcessing();

    /**
     * 统计本月完成处置数
     */
    Long countMonthlyCompleted(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 统计本月处置资产值
     */
    BigDecimal sumMonthlyDisposalValue(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 按类型统计处置数据
     */
    List<Map<String, Object>> selectDisposalByType();

    /**
     * 统计处置趋势数据
     */
    List<Map<String, Object>> selectDisposalTrends(@Param("startDate") String startDate, @Param("endDate") String endDate);
} 