package com.jingfang.maintenance_task.module.request;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 维护任务查询请求
 */
@Data
public class MaintenanceTaskSearchRequest {
    
    /**
     * 任务标题（模糊查询）
     */
    private String taskTitle;
    
    /**
     * 维护计划ID
     */
    private String planId;
    
    /**
     * 资产ID
     */
    private String assetId;
    
    /**
     * 资产名称（模糊查询）
     */
    private String assetName;
    
    /**
     * 负责人类型(1-个人, 2-部门)
     */
    private Integer responsibleType;
    
    /**
     * 负责人ID（用户ID或部门ID）
     */
    private Long responsibleId;
    
    /**
     * 实际执行人ID
     */
    private Long executorId;
    
    /**
     * 任务状态列表
     */
    private List<Integer> statusList;
    
    /**
     * 任务优先级列表
     */
    private List<Integer> priorityList;
    
    /**
     * 计划执行时间开始
     */
    private Date scheduledTimeStart;
    
    /**
     * 计划执行时间结束
     */
    private Date scheduledTimeEnd;
    
    /**
     * 实际开始时间开始
     */
    private Date actualStartTimeStart;
    
    /**
     * 实际开始时间结束
     */
    private Date actualStartTimeEnd;
    
    /**
     * 实际完成时间开始
     */
    private Date actualEndTimeStart;
    
    /**
     * 实际完成时间结束
     */
    private Date actualEndTimeEnd;
    
    /**
     * 是否逾期
     */
    private Boolean overdue;
    
    /**
     * 创建时间开始
     */
    private Date createTimeStart;
    
    /**
     * 创建时间结束
     */
    private Date createTimeEnd;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 是否查询我的任务（当前用户相关的任务）
     */
    private Boolean myTasks;
    
    /**
     * 是否查询待审核任务
     */
    private Boolean pendingReview;
    
    /**
     * 排序字段
     */
    private String orderBy;
    
    /**
     * 排序方向（asc/desc）
     */
    private String orderDirection;
    
    /**
     * 页码
     */
    private Integer pageNum = 1;
    
    /**
     * 页大小
     */
    private Integer pageSize = 10;
} 