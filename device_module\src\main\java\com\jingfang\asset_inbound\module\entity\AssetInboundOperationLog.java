package com.jingfang.asset_inbound.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 入库单操作日志表
 * @TableName asset_inbound_operation_log
 */
@TableName(value ="asset_inbound_operation_log")
@Data
public class AssetInboundOperationLog implements Serializable {
    /**
     * 日志ID
     */
    @TableId(type = IdType.AUTO)
    private Long logId;

    /**
     * 入库单ID
     */
    private String inboundId;

    /**
     * 操作类型(1:创建,2:修改,3:提交,4:经手确认,5:审核通过,6:审核退回,9:删除)
     */
    private Integer operationType;

    /**
     * 操作内容
     */
    private String operationContent;

    /**
     * 操作时间
     */
    private Date operationTime;

    /**
     * 操作人ID
     */
    private Long operatorId;
    
    /**
     * 操作人姓名
     */
    private String operatorName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}