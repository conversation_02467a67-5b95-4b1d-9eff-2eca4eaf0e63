package com.jingfang.device_module.mqtt.dto;

import lombok.Data;

import java.util.List;

/**
 * MQTT设备属性写入请求DTO
 */
@Data
public class DevicePropertyWriteRequest {
    
    /**
     * 请求ID，固定为"0"
     */
    private String id;
    
    /**
     * 版本号，固定为"1.0"
     */
    private String version;
    
    /**
     * 确认标志，固定为0
     */
    private Integer ack;
    
    /**
     * 参数列表
     */
    private List<DevicePropertyWriteParam> params;
    
    /**
     * 设备属性写入参数
     */
    @Data
    public static class DevicePropertyWriteParam {
        /**
         * 客户端ID（设备IP地址）
         */
        private String clientID;
        
        /**
         * 属性列表
         */
        private List<PropertyWriteInfo> properties;
    }
    
    /**
     * 属性写入信息
     */
    @Data
    public static class PropertyWriteInfo {
        /**
         * 属性名称
         */
        private String name;
        
        /**
         * 属性值
         */
        private String value;
    }
    
    /**
     * 默认构造方法
     */
    public DevicePropertyWriteRequest() {
        this.id = "0";
        this.version = "1.0";
        this.ack = 0;
    }
    
    /**
     * 构造方法
     * @param clientID 客户端ID（设备IP地址）
     * @param propertyName 属性名称
     * @param propertyValue 属性值
     */
    public DevicePropertyWriteRequest(String clientID, String propertyName, String propertyValue) {
        this();
        
        DevicePropertyWriteParam param = new DevicePropertyWriteParam();
        param.setClientID(clientID);
        
        PropertyWriteInfo property = new PropertyWriteInfo();
        property.setName(propertyName);
        property.setValue(propertyValue);
        
        param.setProperties(List.of(property));
        this.params = List.of(param);
    }
    
    /**
     * 构造方法（支持多个属性）
     * @param clientID 客户端ID（设备IP地址）
     * @param properties 属性列表
     */
    public DevicePropertyWriteRequest(String clientID, List<PropertyWriteInfo> properties) {
        this();
        
        DevicePropertyWriteParam param = new DevicePropertyWriteParam();
        param.setClientID(clientID);
        param.setProperties(properties);
        
        this.params = List.of(param);
    }
}
