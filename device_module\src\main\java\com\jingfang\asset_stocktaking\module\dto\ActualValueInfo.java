package com.jingfang.asset_stocktaking.module.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 实际信息数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
public class ActualValueInfo implements Serializable {
    
    /**
     * 实际状态
     */
    private Integer actualStatus;
    
    /**
     * 实际位置
     */
    private String actualLocation;
    
    /**
     * 发现状态：1-找到，0-未找到
     */
    private Integer foundStatus;
    
    /**
     * 盘点时间
     */
    private String inventoryTime;
    
    /**
     * 盘点人员
     */
    private String inventoryUser;

    private static final long serialVersionUID = 1L;
}
