<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.asset_inbound.mapper.AssetInboundDetailMapper">

    <resultMap id="BaseResultMap" type="com.jingfang.asset_inbound.module.entity.AssetInboundDetail">
            <id property="detailId" column="detail_id" />
            <result property="inboundId" column="inbound_id" />
            <result property="assetId" column="asset_id" />
            <result property="assetName" column="asset_name" />
            <result property="categoryId" column="category_id" />
            <result property="assetStatus" column="asset_status" />
            <result property="specModel" column="spec_model" />
            <result property="assetBrand" column="asset_brand" />
            <result property="assetPurpose" column="asset_purpose" />
            <result property="assetUnit" column="asset_unit" />
            <result property="storageLocation" column="storage_location" />
            <result property="detailLocation" column="detail_location" />
            <result property="quantity" column="quantity" />
            <result property="unitPrice" column="unit_price" />
            <result property="amount" column="amount" />
            <result property="maintainVendor" column="maintain_vendor" />
            <result property="startTime" column="start_time" />
            <result property="endTime" column="end_time" />
            <result property="maintainStatus" column="maintain_status" />
            <result property="contactNumber" column="contact_number" />
            <result property="managerId" column="manager_id" />
            <result property="maintainMethod" column="maintain_method" />
            <result property="remark" column="remark" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        detail_id,inbound_id,asset_id,asset_name,category_id,asset_status,
        spec_model,asset_brand,asset_purpose,asset_unit,storage_location,
        detail_location,quantity,unit_price,amount,maintain_vendor,
        start_time,end_time,maintain_status,contact_number,manager_id,
        maintain_method,remark,create_time,update_time
    </sql>
</mapper>
