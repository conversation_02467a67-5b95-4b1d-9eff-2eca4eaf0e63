package com.jingfang.collaboration.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;


import java.util.List;

/**
 * 邀请协作数据传输对象
 */
@Data
public class InviteDto {
    
    /**
     * 表格ID
     */
    @NotBlank(message = "表格ID不能为空")
    private String spreadsheetId;
    
    /**
     * 被邀请的用户ID列表
     */
    @NotEmpty(message = "被邀请用户列表不能为空")
    private List<Long> userIds;
    
    /**
     * 权限类型（editor:编辑者 commenter:评论者 viewer:查看者）
     */
    @NotBlank(message = "权限类型不能为空")
    private String permission;
    
    /**
     * 邀请消息
     */
    private String message;
    
    /**
     * 是否发送邮件通知
     */
    private Boolean sendEmail = false;
    
    /**
     * 是否发送站内消息
     */
    private Boolean sendMessage = true;
}
