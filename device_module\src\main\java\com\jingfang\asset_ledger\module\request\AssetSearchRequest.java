package com.jingfang.asset_ledger.module.request;

import com.jingfang.common.request.PageRequest;
import lombok.Data;

import java.io.Serializable;

@Data
public class AssetSearchRequest extends PageRequest implements Serializable {

    /**
     * 办理状态
     */
    private Integer confirmStatus;

    /**
     * 资产编号
     */
    private String assetId;

    /**
     * 规格型号
     */
    private String specModel;

    /**
     * 资产名称
     */
    private String assetName;

    /**
     * 使用组织（部门ID）
     */
    private Long deptId;

    /**
     * 存放位置
     */
    private Integer storageLocation;

    /**
     * 管理人员ID
     */
    private Long managerId;

    /**
     * 资产分类ID
     */
    private Integer categoryId;
}
