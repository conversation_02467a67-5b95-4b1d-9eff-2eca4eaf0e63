-- 准备微信小程序库存盘点接口测试数据
-- 执行时间：2025年7月11日

-- 1. 创建测试物品（条码：TEST001）
INSERT INTO item_base_info (
    item_id,
    item_name,
    item_code,
    spec_model,
    unit,
    item_type,
    safety_stock,
    deleted,
    create_time,
    update_time
) VALUES (
    'test_item_001',
    '测试物品A',
    'TEST001',
    '规格A',
    '个',
    1,
    10.00,
    0,
    NOW(),
    NOW()
) ON DUPLICATE KEY UPDATE
    item_name = VALUES(item_name),
    spec_model = VALUES(spec_model),
    update_time = NOW();

-- 2. 创建测试物品的库存记录
INSERT INTO item_stock (
    stock_id,
    item_id,
    warehouse_id,
    current_stock,
    available_stock,
    reserved_stock,
    create_time,
    update_time
) VALUES (
    'test_stock_001',
    'test_item_001',
    1,
    100.00,
    100.00,
    0.00,
    NOW(),
    NOW()
) ON DUPLICATE KEY UPDATE
    current_stock = VALUES(current_stock),
    available_stock = VALUES(available_stock),
    update_time = NOW();

-- 3. 为现有盘点任务创建测试明细
-- 获取第一个进行中的盘点任务ID
SET @stocktaking_id = (
    SELECT stocktaking_id 
    FROM item_stocktaking 
    WHERE status = 1 
    LIMIT 1
);

-- 创建盘点明细
INSERT INTO item_stocktaking_detail (
    detail_id,
    stocktaking_id,
    item_id,
    warehouse_id,
    shelf_location,
    book_quantity,
    actual_quantity,
    difference_quantity,
    difference_reason,
    checker_id,
    checker_name,
    check_time,
    status,
    create_time,
    update_time,
    photos
) VALUES (
    'test_detail_001',
    @stocktaking_id,
    'test_item_001',
    1,
    'A-01-01',
    100.00,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    NULL,
    0,
    NOW(),
    NOW(),
    '[]'
) ON DUPLICATE KEY UPDATE
    book_quantity = VALUES(book_quantity),
    update_time = NOW();

-- 4. 创建一些历史盘点记录用于测试
INSERT INTO item_stocktaking_detail (
    detail_id,
    stocktaking_id,
    item_id,
    warehouse_id,
    shelf_location,
    book_quantity,
    actual_quantity,
    difference_quantity,
    difference_reason,
    checker_id,
    checker_name,
    check_time,
    status,
    create_time,
    update_time,
    photos
) VALUES (
    'test_detail_history_001',
    @stocktaking_id,
    'test_item_001',
    1,
    'A-01-02',
    50.00,
    48.00,
    -2.00,
    '部分物品损耗',
    'admin',
    'admin',
    NOW() - INTERVAL 1 DAY,
    1,
    NOW() - INTERVAL 2 DAY,
    NOW() - INTERVAL 1 DAY,
    '["http://example.com/photo1.jpg", "http://example.com/photo2.jpg"]'
) ON DUPLICATE KEY UPDATE
    actual_quantity = VALUES(actual_quantity),
    difference_quantity = VALUES(difference_quantity),
    update_time = NOW();

-- 5. 验证测试数据
SELECT '=== 测试物品信息 ===' as info;
SELECT item_id, item_name, item_code, spec_model, unit 
FROM item_base_info 
WHERE item_code = 'TEST001';

SELECT '=== 测试库存信息 ===' as info;
SELECT stock_id, item_id, warehouse_id, current_stock 
FROM item_stock 
WHERE item_id = 'test_item_001';

SELECT '=== 测试盘点明细 ===' as info;
SELECT detail_id, stocktaking_id, item_id, warehouse_id, book_quantity, actual_quantity, status, photos
FROM item_stocktaking_detail 
WHERE item_id = 'test_item_001';

SELECT '=== 当前进行中的盘点任务 ===' as info;
SELECT stocktaking_id, stocktaking_code, stocktaking_name, status 
FROM item_stocktaking 
WHERE status = 1;
