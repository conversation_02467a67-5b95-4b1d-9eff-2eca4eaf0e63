# 微信小程序库存盘点接口测试检查清单

## 测试前准备 ✅

### 环境准备
- [ ] 后端服务正常启动
- [ ] 数据库连接正常
- [ ] 已执行数据库修改脚本 (`sql/微信小程序库存盘点数据库修改.sql`)
- [ ] 确认`item_stocktaking_detail`表已添加`photos`字段

### 测试数据准备
- [ ] 创建测试用户账号
- [ ] 获取有效的JWT Token
- [ ] 创建测试物品（物品条码：TEST001）
- [ ] 创建测试仓库
- [ ] 创建盘点计划并生成明细
- [ ] 开始盘点任务

### 权限配置
- [ ] 确认测试用户具有以下权限：
  - [ ] `item:stocktaking:list`
  - [ ] `item:stocktaking:query`
  - [ ] `item:stocktaking:record`
  - [ ] `item:item:query`

## P0级别核心功能测试 🔥

### 1. 获取我的盘点任务列表
- [ ] **接口**: `GET /item/stocktaking/my-tasks`
- [ ] **状态码**: 200
- [ ] **返回数据**: 包含盘点任务列表
- [ ] **字段验证**: 
  - [ ] `stocktakingId` 不为空
  - [ ] `stocktakingCode` 格式正确
  - [ ] `progress` 计算正确
- [ ] **业务逻辑**: 只返回当前用户的任务

### 2. 根据物品条码查询物品信息
- [ ] **接口**: `GET /item/by-code/{itemCode}`
- [ ] **正常场景**: 
  - [ ] 状态码：200
  - [ ] 返回完整物品信息
- [ ] **异常场景**:
  - [ ] 条码不存在时返回错误信息
  - [ ] 条码为空时返回400错误

### 3. 根据物品信息查找盘点明细
- [ ] **接口**: `GET /item/stocktaking/detail/by-item`
- [ ] **参数验证**:
  - [ ] `stocktakingId` 必填
  - [ ] `itemId` 必填
  - [ ] `warehouseId` 可选
- [ ] **返回数据**:
  - [ ] 包含完整明细信息
  - [ ] `photos` 字段存在
- [ ] **异常场景**: 明细不存在时返回错误

### 4. 更新盘点明细
- [ ] **接口**: `PUT /item/stocktaking/detail/{detailId}`
- [ ] **请求体验证**:
  - [ ] `actualQuantity` 数值类型
  - [ ] `photos` 数组格式
- [ ] **业务逻辑**:
  - [ ] 自动计算差异数量
  - [ ] 自动更新状态
  - [ ] 照片JSON序列化正确
- [ ] **数据库验证**:
  - [ ] 明细记录已更新
  - [ ] `photos` 字段存储JSON格式

## P1级别重要功能测试 ⭐

### 5. 获取盘点进度
- [ ] **接口**: `GET /item/stocktaking/{stocktakingId}/progress`
- [ ] **返回数据**:
  - [ ] `totalItems` 总数正确
  - [ ] `completedItems` 已完成数正确
  - [ ] `completionRate` 完成率计算正确
- [ ] **计算逻辑**: 进度百分比 = 已完成数 / 总数 * 100

### 6. 获取个人盘点进度
- [ ] **接口**: `GET /item/stocktaking/my-progress`
- [ ] **返回数据**:
  - [ ] `totalAssigned` 个人分配总数
  - [ ] `completed` 个人已完成数
  - [ ] `activeTaskCount` 进行中任务数
- [ ] **业务逻辑**: 只统计当前用户的数据

## P2级别优化功能测试 ✨

### 7. 获取个人盘点记录
- [ ] **接口**: `GET /item/stocktaking/my-records`
- [ ] **时间范围测试**:
  - [ ] `timeRange=today` 今日记录
  - [ ] `timeRange=week` 本周记录
  - [ ] `timeRange=month` 本月记录
  - [ ] `timeRange=custom` 自定义范围
- [ ] **自定义时间**:
  - [ ] `startDate` 和 `endDate` 参数生效
  - [ ] 日期格式验证 (yyyy-MM-dd)
- [ ] **返回数据**: 按时间倒序排列

### 8. 获取物品盘点历史
- [ ] **接口**: `GET /item/stocktaking/item-history/{itemId}`
- [ ] **返回数据**:
  - [ ] 包含该物品所有盘点历史
  - [ ] 按时间倒序排列
  - [ ] 包含照片信息
- [ ] **异常场景**: 物品ID不存在时的处理

## 错误场景测试 ❌

### 认证和权限
- [ ] 无Token访问返回401
- [ ] 无效Token返回401
- [ ] 无权限访问返回403

### 参数验证
- [ ] 必填参数缺失返回400
- [ ] 参数格式错误返回400
- [ ] 数值类型参数传入非数值

### 业务异常
- [ ] 资源不存在返回404或业务错误码
- [ ] 业务状态不符合返回相应错误信息

## 性能测试 🚀

### 响应时间
- [ ] 查询接口响应时间 < 500ms
- [ ] 更新接口响应时间 < 1000ms
- [ ] 复杂统计接口响应时间 < 2000ms

### 并发测试
- [ ] 10个用户同时查询任务列表
- [ ] 5个用户同时更新不同明细
- [ ] 大量历史记录查询性能

### 数据量测试
- [ ] 1000+明细的盘点任务查询
- [ ] 大量照片URL的存储和查询
- [ ] 长时间范围的历史记录查询

## 数据一致性测试 🔄

### 状态同步
- [ ] 明细状态更新后进度计算正确
- [ ] 多用户操作时数据一致性
- [ ] 照片数据的完整性

### 事务处理
- [ ] 更新明细时的事务回滚测试
- [ ] 并发更新时的数据锁定

## 安全测试 🔒

### 数据安全
- [ ] SQL注入防护
- [ ] XSS攻击防护
- [ ] 敏感信息不泄露

### 访问控制
- [ ] 用户只能访问自己的数据
- [ ] 跨用户数据访问被阻止

## 兼容性测试 🔧

### 数据格式
- [ ] JSON格式正确
- [ ] 日期时间格式统一
- [ ] 数值精度处理

### 向后兼容
- [ ] 新增字段不影响现有功能
- [ ] 老版本客户端兼容性

## 测试完成确认 ✅

### 功能完整性
- [ ] 所有P0级别功能正常
- [ ] 所有P1级别功能正常
- [ ] 所有P2级别功能正常

### 质量标准
- [ ] 无严重Bug
- [ ] 性能满足要求
- [ ] 安全性通过验证

### 文档完整性
- [ ] 接口文档准确
- [ ] 错误码文档完整
- [ ] 部署文档清晰

## 测试报告模板

```markdown
# 微信小程序库存盘点接口测试报告

## 测试概述
- 测试时间：YYYY-MM-DD
- 测试环境：开发/测试/预生产
- 测试人员：XXX

## 测试结果汇总
- P0级别：✅ 4/4 通过
- P1级别：✅ 2/2 通过  
- P2级别：✅ 2/2 通过
- 错误场景：✅ 通过
- 性能测试：✅ 通过

## 发现问题
1. [问题描述]
   - 严重程度：高/中/低
   - 复现步骤：...
   - 期望结果：...
   - 实际结果：...

## 测试结论
- [ ] 通过，可以发布
- [ ] 有问题，需要修复后重测
```

---

**使用说明**：
1. 按照清单逐项测试
2. 在每个测试项前打勾确认
3. 记录发现的问题
4. 生成测试报告
