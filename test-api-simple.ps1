# 简单的API测试脚本
param(
    [string]$BaseUrl = "http://localhost:8080",
    [string]$Token = "your_jwt_token_here"
)

Write-Host "开始测试微信小程序库存盘点接口" -ForegroundColor Green
Write-Host "基础URL: $BaseUrl" -ForegroundColor Cyan

# 测试函数
function Test-StocktakingApi {
    param(
        [string]$TestName,
        [string]$Method,
        [string]$Url,
        [string]$Body = $null
    )
    
    Write-Host "`n测试: $TestName" -ForegroundColor Yellow
    Write-Host "URL: $Method $Url" -ForegroundColor Gray
    
    try {
        if ($Method -eq "GET") {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers @{'Authorization' = "Bearer $Token"; 'Content-Type' = 'application/json'}
        } else {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers @{'Authorization' = "Bearer $Token"; 'Content-Type' = 'application/json'} -Body $Body
        }
        
        Write-Host "✓ 成功" -ForegroundColor Green
        Write-Host "响应: $($response | ConvertTo-Json -Depth 2)" -ForegroundColor Cyan
        return $response
    }
    catch {
        Write-Host "✗ 失败: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        }
        return $null
    }
}

# 开始测试
Write-Host "`n=== P0级别接口测试 ===" -ForegroundColor Blue

# 1. 获取我的盘点任务列表
Test-StocktakingApi -TestName "获取我的盘点任务列表" -Method "GET" -Url "$BaseUrl/item/stocktaking/my-tasks"

# 2. 根据物品条码查询物品信息
Test-StocktakingApi -TestName "根据物品条码查询物品信息" -Method "GET" -Url "$BaseUrl/item/by-code/TEST001"

# 3. 根据物品信息查找盘点明细
$detailUrl = "$BaseUrl/item/stocktaking/detail/by-item?stocktakingId=test_id&itemId=test_item&warehouseId=1"
Test-StocktakingApi -TestName "根据物品信息查找盘点明细" -Method "GET" -Url $detailUrl

# 4. 更新盘点明细
$updateJson = '{"actualQuantity": 95.00, "differenceReason": "测试差异", "photos": ["http://example.com/photo1.jpg"]}'
Test-StocktakingApi -TestName "更新盘点明细" -Method "PUT" -Url "$BaseUrl/item/stocktaking/detail/test_detail_id" -Body $updateJson

Write-Host "`n=== P1级别接口测试 ===" -ForegroundColor Blue

# 5. 获取盘点进度
Test-StocktakingApi -TestName "获取盘点进度" -Method "GET" -Url "$BaseUrl/item/stocktaking/test_stocktaking_id/progress"

# 6. 获取个人盘点进度
Test-StocktakingApi -TestName "获取个人盘点进度" -Method "GET" -Url "$BaseUrl/item/stocktaking/my-progress"

Write-Host "`n=== P2级别接口测试 ===" -ForegroundColor Blue

# 7. 获取个人盘点记录
Test-StocktakingApi -TestName "获取个人盘点记录(今日)" -Method "GET" -Url "$BaseUrl/item/stocktaking/my-records?timeRange=today"
Test-StocktakingApi -TestName "获取个人盘点记录(本周)" -Method "GET" -Url "$BaseUrl/item/stocktaking/my-records?timeRange=week"
Test-StocktakingApi -TestName "获取个人盘点记录(本月)" -Method "GET" -Url "$BaseUrl/item/stocktaking/my-records?timeRange=month"

# 8. 获取物品盘点历史
Test-StocktakingApi -TestName "获取物品盘点历史" -Method "GET" -Url "$BaseUrl/item/stocktaking/item-history/test_item_id"

Write-Host "`n=== 错误场景测试 ===" -ForegroundColor Red

# 测试不存在的物品条码
Test-StocktakingApi -TestName "查询不存在的物品条码" -Method "GET" -Url "$BaseUrl/item/by-code/NOTEXIST"

# 测试不存在的盘点明细
$errorDetailUrl = "$BaseUrl/item/stocktaking/detail/by-item?stocktakingId=not_exist&itemId=not_exist&warehouseId=999"
Test-StocktakingApi -TestName "查询不存在的盘点明细" -Method "GET" -Url $errorDetailUrl

# 测试更新不存在的明细
$errorUpdateJson = '{"actualQuantity": 100.00}'
Test-StocktakingApi -TestName "更新不存在的明细" -Method "PUT" -Url "$BaseUrl/item/stocktaking/detail/not_exist_detail" -Body $errorUpdateJson

Write-Host "`n测试完成!" -ForegroundColor Green
