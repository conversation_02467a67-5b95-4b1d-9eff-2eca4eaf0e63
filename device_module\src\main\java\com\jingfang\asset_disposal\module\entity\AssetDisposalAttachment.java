package com.jingfang.asset_disposal.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 资产处置附件表
 * @TableName asset_disposal_attachment
 */
@TableName(value = "asset_disposal_attachment")
@Data
public class AssetDisposalAttachment implements Serializable {
    
    /**
     * 附件ID
     */
    @TableId(type = IdType.INPUT)
    private String attachmentId;
    
    /**
     * 处置单ID
     */
    private String disposalId;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 文件类型
     */
    private String fileType;
    
    /**
     * 文件大小
     */
    private Long fileSize;
    
    /**
     * 存储路径
     */
    private String storagePath;
    
    /**
     * 上传时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date uploadTime;
    
    /**
     * 上传人
     */
    private String uploadBy;
    
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
} 