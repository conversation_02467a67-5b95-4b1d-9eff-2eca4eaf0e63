package com.jingfang.asset_stocktaking.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingfang.asset_stocktaking.mapper.StocktakingDifferenceMapper;
import com.jingfang.asset_stocktaking.mapper.StocktakingRecordMapper;
import com.jingfang.asset_stocktaking.mapper.StocktakingTaskMapper;
import com.jingfang.asset_stocktaking.module.dto.ActualValueInfo;
import com.jingfang.asset_stocktaking.module.dto.BookValueInfo;
import com.jingfang.asset_stocktaking.module.dto.DifferenceHandleInfo;
import com.jingfang.asset_stocktaking.module.dto.StocktakingDifferenceDto;
import com.jingfang.asset_stocktaking.module.entity.AssetStocktakingDifference;
import com.jingfang.asset_stocktaking.module.entity.AssetStocktakingRecord;
import com.jingfang.asset_stocktaking.module.entity.AssetStocktakingTask;
import com.jingfang.asset_stocktaking.module.vo.DeptStatistics;
import com.jingfang.asset_stocktaking.module.vo.DifferenceStatistics;
import com.jingfang.asset_stocktaking.service.StocktakingDifferenceService;
import com.jingfang.common.utils.uuid.IdUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 盘点差异服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Slf4j
@Service
public class StocktakingDifferenceServiceImpl extends ServiceImpl<StocktakingDifferenceMapper, AssetStocktakingDifference> 
        implements StocktakingDifferenceService {

    @Resource
    private StocktakingDifferenceMapper differenceMapper;

    @Resource
    private StocktakingRecordMapper recordMapper;

    @Resource
    private StocktakingTaskMapper taskMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean analyzeDifferences(String planId) {
        try {
            // 清除之前的差异记录
            differenceMapper.deleteDifferencesByPlanId(planId);

            // 获取计划下的所有任务
            List<AssetStocktakingTask> tasks = taskMapper.selectTaskByPlanId(planId);
            
            List<AssetStocktakingDifference> differences = new ArrayList<>();

            for (AssetStocktakingTask task : tasks) {
                // 获取任务的盘点记录
                List<AssetStocktakingRecord> records = recordMapper.selectRecordByTaskId(task.getTaskId());
                
                for (AssetStocktakingRecord record : records) {
                    // 分析每个记录的差异
                    AssetStocktakingDifference difference = analyzeRecordDifference(planId, record);
                    if (difference != null) {
                        differences.add(difference);
                    }
                }
            }

            // 批量插入差异记录
            if (!differences.isEmpty()) {
                return differenceMapper.batchInsertDifferences(differences) > 0;
            }

            return true;
        } catch (Exception e) {
            log.error("执行差异分析失败", e);
            return false;
        }
    }

    /**
     * 分析单个记录的差异
     */
    private AssetStocktakingDifference analyzeRecordDifference(String planId, AssetStocktakingRecord record) {
        try {
            // TODO: 这里需要查询资产台账信息进行对比
            // 暂时使用模拟逻辑
            
            AssetStocktakingDifference difference = null;
            
            // 检查是否未找到（盘亏）
            if (record.getFoundStatus() == 0) {
                difference = new AssetStocktakingDifference();
                difference.setDiffId(IdUtils.fastSimpleUUID());
                difference.setPlanId(planId);
                difference.setAssetId(record.getAssetId());
                difference.setDiffType(AssetStocktakingDifference.DiffType.DEFICIT);
                difference.setDiffReason("盘点时未找到该资产");
                difference.setHandleStatus(AssetStocktakingDifference.HandleStatus.PENDING);
                difference.setHandleSuggestion(generateHandleSuggestion(difference));
                difference.setCreateTime(new Date());
                
                // 设置账面信息和实际信息
                BookValueInfo bookValue = new BookValueInfo();
                bookValue.setAssetCode(record.getAssetCode());
                // TODO: 从资产台账获取更多信息

                ActualValueInfo actualValue = new ActualValueInfo();
                actualValue.setFoundStatus(record.getFoundStatus());
                actualValue.setInventoryTime(record.getInventoryTime().toString());
                
                difference.setBookValue(JSON.toJSONString(bookValue));
                difference.setActualValue(JSON.toJSONString(actualValue));
            }
            
            // 检查状态差异
            // TODO: 实现状态差异检查逻辑
            
            // 检查位置差异
            // TODO: 实现位置差异检查逻辑
            
            return difference;
        } catch (Exception e) {
            log.error("分析记录差异失败", e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createDifference(StocktakingDifferenceDto differenceDto) {
        try {
            AssetStocktakingDifference difference = new AssetStocktakingDifference();
            BeanUtils.copyProperties(differenceDto, difference);
            
            difference.setDiffId(IdUtils.fastSimpleUUID());
            difference.setCreateTime(new Date());

            // 处理账面信息和实际信息
            if (differenceDto.getBookValue() != null) {
                difference.setBookValue(JSON.toJSONString(differenceDto.getBookValue()));
            }
            if (differenceDto.getActualValue() != null) {
                difference.setActualValue(JSON.toJSONString(differenceDto.getActualValue()));
            }

            return save(difference);
        } catch (Exception e) {
            log.error("创建差异记录失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean editDifference(StocktakingDifferenceDto differenceDto) {
        try {
            AssetStocktakingDifference difference = new AssetStocktakingDifference();
            BeanUtils.copyProperties(differenceDto, difference);

            // 处理账面信息和实际信息
            if (differenceDto.getBookValue() != null) {
                difference.setBookValue(JSON.toJSONString(differenceDto.getBookValue()));
            }
            if (differenceDto.getActualValue() != null) {
                difference.setActualValue(JSON.toJSONString(differenceDto.getActualValue()));
            }

            return updateById(difference);
        } catch (Exception e) {
            log.error("编辑差异记录失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDifference(String diffId) {
        try {
            return removeById(diffId);
        } catch (Exception e) {
            log.error("删除差异记录失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteDifferences(List<String> diffIds) {
        try {
            return removeByIds(diffIds);
        } catch (Exception e) {
            log.error("批量删除差异记录失败", e);
            return false;
        }
    }

    @Override
    public IPage<AssetStocktakingDifference> selectDifferenceList(String planId, Integer diffType, 
                                                                Integer handleStatus, Integer pageNum, Integer pageSize) {
        Page<AssetStocktakingDifference> page = new Page<>(pageNum, pageSize);
        return differenceMapper.selectDifferenceList(page, planId, diffType, handleStatus);
    }

    @Override
    public List<AssetStocktakingDifference> selectDifferenceByPlanId(String planId) {
        return differenceMapper.selectDifferenceByPlanId(planId);
    }

    @Override
    public List<AssetStocktakingDifference> selectDifferenceByType(String planId, Integer diffType) {
        return differenceMapper.selectDifferenceByType(planId, diffType);
    }

    @Override
    public List<AssetStocktakingDifference> selectSurplusDifferences(String planId) {
        return differenceMapper.selectSurplusDifferences(planId);
    }

    @Override
    public List<AssetStocktakingDifference> selectDeficitDifferences(String planId) {
        return differenceMapper.selectDeficitDifferences(planId);
    }

    @Override
    public List<AssetStocktakingDifference> selectStatusDifferences(String planId) {
        return differenceMapper.selectStatusDifferences(planId);
    }

    @Override
    public List<AssetStocktakingDifference> selectLocationDifferences(String planId) {
        return differenceMapper.selectLocationDifferences(planId);
    }

    @Override
    public List<AssetStocktakingDifference> selectPendingDifferences(String planId) {
        return differenceMapper.selectPendingDifferences(planId);
    }

    @Override
    public DifferenceStatistics selectDifferenceStatistics(String planId) {
        return differenceMapper.selectDifferenceStatistics(planId);
    }

    @Override
    public List<java.util.Map<String, Object>> countDifferenceByType(String planId) {
        return differenceMapper.countDifferenceByType(planId);
    }

    @Override
    public List<java.util.Map<String, Object>> countDifferenceByHandleStatus(String planId) {
        return differenceMapper.countDifferenceByHandleStatus(planId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleDifference(String diffId, DifferenceHandleInfo handleInfo) {
        try {
            AssetStocktakingDifference difference = getById(diffId);
            if (difference == null) {
                return false;
            }

            // 更新处理状态
            difference.setHandleStatus(AssetStocktakingDifference.HandleStatus.PROCESSING);
            
            // TODO: 根据处理方式执行具体的处理逻辑
            // 1. 台账更新
            // 2. 资产入库
            // 3. 资产核销
            // 4. 位置调整
            // 5. 状态更新

            return updateById(difference);
        } catch (Exception e) {
            log.error("处理差异失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchHandleDifferences(List<String> diffIds, DifferenceHandleInfo handleInfo) {
        try {
            for (String diffId : diffIds) {
                if (!handleDifference(diffId, handleInfo)) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("批量处理差异失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDifferenceHandle(String diffId, Integer handleStatus, String handleSuggestion) {
        try {
            return differenceMapper.updateDifferenceHandle(diffId, handleStatus, handleSuggestion) > 0;
        } catch (Exception e) {
            log.error("更新差异处理信息失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateHandleStatus(List<String> diffIds, Integer handleStatus) {
        try {
            return differenceMapper.batchUpdateHandleStatus(diffIds, handleStatus) > 0;
        } catch (Exception e) {
            log.error("批量更新差异处理状态失败", e);
            return false;
        }
    }

    @Override
    public AssetStocktakingDifference selectDifferenceByAssetId(String assetId, String planId) {
        return differenceMapper.selectDifferenceByAssetId(assetId, planId);
    }

    @Override
    public boolean checkAssetHasDifference(String assetId, String planId) {
        return differenceMapper.checkAssetHasDifference(assetId, planId);
    }

    @Override
    public String generateHandleSuggestion(AssetStocktakingDifference difference) {
        if (difference.getDiffType() == null) {
            return "请人工确认处理方式";
        }

        switch (difference.getDiffType()) {
            case AssetStocktakingDifference.DiffType.SURPLUS:
                return "建议：1.确认是否为新增资产；2.如确认为新增，请办理入库手续；3.更新资产台账";
            case AssetStocktakingDifference.DiffType.DEFICIT:
                return "建议：1.再次确认资产是否确实丢失；2.如确认丢失，请查明原因；3.按规定办理核销手续";
            case AssetStocktakingDifference.DiffType.STATUS_DIFF:
                return "建议：1.确认资产实际状态；2.更新资产台账状态信息；3.如需维修或报废，按流程处理";
            case AssetStocktakingDifference.DiffType.LOCATION_DIFF:
                return "建议：1.确认资产实际存放位置；2.更新资产台账位置信息；3.如需调拨，按流程办理";
            default:
                return "请人工确认处理方式";
        }
    }

    @Override
    public Integer identifyDifferenceType(String assetId,
                                        BookValueInfo bookValue,
                                        ActualValueInfo actualValue) {
        // 如果未找到，则为盘亏
        if (actualValue.getFoundStatus() == 0) {
            return AssetStocktakingDifference.DiffType.DEFICIT;
        }

        // 比较状态
        if (bookValue.getAssetStatus() != null && actualValue.getActualStatus() != null 
            && !bookValue.getAssetStatus().equals(actualValue.getActualStatus())) {
            return AssetStocktakingDifference.DiffType.STATUS_DIFF;
        }

        // 比较位置
        if (bookValue.getLocation() != null && actualValue.getActualLocation() != null 
            && !bookValue.getLocation().equals(actualValue.getActualLocation())) {
            return AssetStocktakingDifference.DiffType.LOCATION_DIFF;
        }

        return null; // 无差异
    }

    @Override
    public List<DeptStatistics> selectDeptDifferenceStatistics(String planId) {
        return differenceMapper.selectDeptDifferenceStatistics(planId);
    }

    @Override
    public List<AssetStocktakingDifference> exportDifferences(String planId, Integer diffType, Integer handleStatus) {
        return differenceMapper.selectDifferenceList(new Page<>(1, 10000), planId, diffType, handleStatus).getRecords();
    }
}
