# 设备维护模块前端功能清单

## 概述

基于设备维护模块接口文档，设计前端功能清单。前端采用Vue.js + Element UI框架，遵循RuoYi框架的前端开发规范，实现设备维护的完整业务流程。

## 功能模块结构

```
设备维护模块
├── 维护计划管理
│   ├── 维护计划列表
│   ├── 维护计划详情
│   ├── 新增维护计划
│   ├── 编辑维护计划
│   └── 维护计划状态管理
├── 维护任务管理
│   ├── 维护任务列表
│   ├── 维护任务详情
│   ├── 任务执行
│   ├── 任务审核
│   ├── 我的任务
│   └── 任务统计
└── 辅助功能
    ├── 数据字典管理
    └── 报表导出
```

## 1. 维护计划管理模块

### 1.1 维护计划列表页面 (`/maintenance/plan/index`)

**页面功能**:
- 维护计划列表展示（分页表格）
- 高级搜索功能
- 批量操作（删除、启用/停用）
- 快速筛选（状态、到期时间等）

**主要组件**:
- 搜索表单组件
- 数据表格组件
- 操作按钮组件
- 状态标签组件

**搜索条件**:
- 计划名称（模糊搜索）
- 资产名称/编号
- 负责人类型和负责人
- 计划状态（启用/停用）
- 创建时间范围
- 下次维护时间范围

**表格字段**:
- 计划编号
- 计划名称
- 关联资产
- 维护类型
- 维护周期
- 负责人
- 下次维护时间
- 状态
- 创建时间
- 操作列（查看、编辑、删除、启用/停用）

**操作功能**:
- 新增维护计划
- 批量删除
- 批量启用/停用
- 导出数据
- 刷新列表

### 1.2 维护计划详情页面 (`/maintenance/plan/detail/:id`)

**页面功能**:
- 维护计划基本信息展示
- 关联任务列表
- 执行历史记录
- 相关文档附件

**信息展示**:
- 基本信息卡片
- 维护内容详情
- 备品备件清单
- 任务生成记录
- 操作日志

### 1.3 新增/编辑维护计划页面 (`/maintenance/plan/add`, `/maintenance/plan/edit/:id`)

**表单字段**:
- 基本信息
  - 计划名称（必填）
  - 计划描述
  - 维护类型选择
  - 优先级选择
- 资产信息
  - 资产选择器（支持搜索）
  - 资产详情展示
- 维护周期设置
  - 周期类型（按时间/按使用量）
  - 周期值设置
  - 提前提醒天数
- 负责人设置
  - 负责人类型（个人/部门）
  - 负责人选择器
- 维护内容
  - 维护项目清单
  - 维护标准和要求
  - 预计耗时
- 备品备件
  - 备品备件选择器
  - 数量设置
  - 备注信息

**表单验证**:
- 必填字段验证
- 格式验证
- 业务逻辑验证

**辅助功能**:
- 资产关联备品备件自动加载
- 模板选择功能
- 草稿保存功能

### 1.4 特殊查询页面

#### 即将到期计划页面 (`/maintenance/plan/upcoming`)
- 可配置提前天数
- 按紧急程度排序
- 快速生成任务功能

#### 已过期计划页面 (`/maintenance/plan/overdue`)
- 过期时间统计
- 影响分析
- 紧急处理功能

## 2. 维护任务管理模块

### 2.1 维护任务列表页面 (`/maintenance/task/index`)

**页面功能**:
- 任务列表展示（分页表格）
- 多维度筛选
- 任务状态流转
- 批量操作

**搜索条件**:
- 任务编号
- 任务名称
- 关联计划
- 关联资产
- 任务状态
- 优先级
- 负责人
- 计划执行时间
- 实际执行时间

**表格字段**:
- 任务编号
- 任务名称
- 关联计划
- 关联资产
- 任务类型
- 优先级标签
- 负责人
- 任务状态
- 计划执行时间
- 实际执行时间
- 操作列

**状态标识**:
- 不同状态用不同颜色标识
- 逾期任务特殊标记
- 紧急任务醒目提示

### 2.2 维护任务详情页面 (`/maintenance/task/detail/:id`)

**页面布局**:
- 任务基本信息
- 执行进度跟踪
- 备品备件使用情况
- 执行记录和附件
- 审核记录

**功能按钮**:
- 开始执行
- 保存草稿
- 提交审核
- 委派任务
- 取消任务

### 2.3 任务执行页面 (`/maintenance/task/execute/:id`)

**执行表单**:
- 任务信息展示（只读）
- 执行内容记录
  - 维护项目检查清单
  - 实际执行情况
  - 发现问题记录
  - 处理措施记录
- 备品备件使用
  - 使用数量记录
  - 使用状态更新
- 执行结果
  - 执行总结
  - 质量评估
  - 建议和改进
- 附件上传
  - 执行照片
  - 相关文档

**操作功能**:
- 草稿保存
- 提交审核
- 打印任务单

### 2.4 任务审核页面 (`/maintenance/task/review/:id`)

**审核功能**:
- 任务执行情况查看
- 审核意见填写
- 审核结果选择（通过/不通过）
- 审核历史记录

### 2.5 我的任务页面 (`/maintenance/task/my`)

**个人任务管理**:
- 待执行任务
- 执行中任务
- 待审核任务
- 已完成任务
- 任务日历视图
- 任务提醒设置

### 2.6 任务统计页面 (`/maintenance/task/statistics`)

**统计图表**:
- 任务状态分布饼图
- 任务完成趋势图
- 部门任务统计
- 个人绩效统计
- 设备维护频次统计

**数据展示**:
- 关键指标卡片
- 数据表格
- 可视化图表
- 导出功能

## 3. 辅助功能模块

### 3.1 数据选择器组件

#### 资产选择器
- 支持搜索和筛选
- 树形结构展示
- 多选支持
- 资产详情预览

#### 用户选择器
- 部门树形结构
- 用户搜索
- 角色筛选
- 批量选择

#### 备品备件选择器
- 分类筛选
- 库存状态显示
- 规格参数展示
- 关联资产筛选

### 3.2 通用组件

#### 状态标签组件
- 任务状态标签
- 计划状态标签
- 优先级标签
- 自定义样式

#### 时间选择组件
- 日期范围选择
- 时间点选择
- 周期设置
- 提醒设置

#### 文件上传组件
- 多文件上传
- 文件类型限制
- 预览功能
- 进度显示

### 3.3 报表导出功能

#### Excel导出
- 维护计划导出
- 维护任务导出
- 统计报表导出
- 自定义字段选择

#### 打印功能
- 任务单打印
- 计划单打印
- 统计报表打印
- 打印预览

## 4. 页面路由设计

```javascript
// 维护计划路由
{
  path: '/maintenance/plan',
  component: Layout,
  children: [
    {
      path: 'index',
      component: () => import('@/views/maintenance/plan/index'),
      name: 'MaintenancePlan',
      meta: { title: '维护计划', icon: 'plan' }
    },
    {
      path: 'add',
      component: () => import('@/views/maintenance/plan/add'),
      name: 'AddMaintenancePlan',
      meta: { title: '新增维护计划' }
    },
    {
      path: 'edit/:id',
      component: () => import('@/views/maintenance/plan/edit'),
      name: 'EditMaintenancePlan',
      meta: { title: '编辑维护计划' }
    },
    {
      path: 'detail/:id',
      component: () => import('@/views/maintenance/plan/detail'),
      name: 'MaintenancePlanDetail',
      meta: { title: '维护计划详情' }
    }
  ]
}

// 维护任务路由
{
  path: '/maintenance/task',
  component: Layout,
  children: [
    {
      path: 'index',
      component: () => import('@/views/maintenance/task/index'),
      name: 'MaintenanceTask',
      meta: { title: '维护任务', icon: 'task' }
    },
    {
      path: 'my',
      component: () => import('@/views/maintenance/task/my'),
      name: 'MyMaintenanceTask',
      meta: { title: '我的任务' }
    },
    {
      path: 'execute/:id',
      component: () => import('@/views/maintenance/task/execute'),
      name: 'ExecuteMaintenanceTask',
      meta: { title: '执行任务' }
    },
    {
      path: 'review/:id',
      component: () => import('@/views/maintenance/task/review'),
      name: 'ReviewMaintenanceTask',
      meta: { title: '审核任务' }
    },
    {
      path: 'statistics',
      component: () => import('@/views/maintenance/task/statistics'),
      name: 'MaintenanceTaskStatistics',
      meta: { title: '任务统计' }
    }
  ]
}
```

## 5. API接口封装

### 5.1 维护计划API (`@/api/maintenance/plan.js`)
```javascript
// 维护计划相关接口
export function listMaintenancePlan(query)
export function getMaintenancePlan(planId)
export function addMaintenancePlan(data)
export function updateMaintenancePlan(data)
export function delMaintenancePlan(planIds)
export function changeMaintenancePlanStatus(planId, status)
export function getUpcomingPlans(days)
export function getOverduePlans()
export function getPlansByAssetId(assetId)
export function getPlansByResponsible(responsibleType, responsibleId)
```

### 5.2 维护任务API (`@/api/maintenance/task.js`)
```javascript
// 维护任务相关接口
export function listMaintenanceTask(query)
export function getMaintenanceTask(taskId)
export function addMaintenanceTask(data)
export function updateMaintenanceTask(data)
export function delMaintenanceTask(taskIds)
export function startTask(taskId)
export function saveDraft(data)
export function submitTask(data)
export function reviewTask(data)
export function delegateTask(data)
export function cancelTask(data)
export function getMyTasks(statusList)
export function getPendingReviewTasks()
export function getUpcomingTasks(days)
export function getOverdueTasks()
export function getTasksByPlanId(planId)
export function getTaskStatistics()
export function generateTasks()
export function generateTaskFromPlan(planId)
```

### 5.3 辅助功能API (`@/api/maintenance/helper.js`)
```javascript
// 辅助功能接口
export function getAssetParts(assetId)
export function checkAssetPartRelation(assetId, partId)
export function getUserList()
export function getDeptList()
export function getStatusOptions()
export function getPriorityOptions()
export function getResponsibleTypeOptions()
export function getUseStatusOptions()
```

## 6. 权限控制

### 6.1 页面权限
- 基于RuoYi框架的权限指令
- 路由守卫权限验证
- 菜单权限控制

### 6.2 按钮权限
```javascript
// 权限指令使用示例
v-hasPermi="['maintenance:plan:add']"
v-hasPermi="['maintenance:task:execute']"
v-hasPermi="['maintenance:task:review']"
```

### 6.3 数据权限
- 基于部门的数据权限
- 基于角色的功能权限
- 个人数据权限控制

## 7. 用户体验优化

### 7.1 响应式设计
- 移动端适配
- 平板端优化
- 桌面端完整功能

### 7.2 交互优化
- 加载状态提示
- 操作确认对话框
- 成功/失败消息提示
- 表单验证提示

### 7.3 性能优化
- 列表分页加载
- 图片懒加载
- 组件按需加载
- 数据缓存策略

## 8. 开发规范

### 8.1 代码规范
- 遵循RuoYi前端开发规范
- Vue.js最佳实践
- Element UI组件规范使用

### 8.2 文件组织
```
src/views/maintenance/
├── plan/
│   ├── index.vue          # 维护计划列表
│   ├── add.vue           # 新增维护计划
│   ├── edit.vue          # 编辑维护计划
│   └── detail.vue        # 维护计划详情
├── task/
│   ├── index.vue         # 维护任务列表
│   ├── my.vue           # 我的任务
│   ├── execute.vue      # 执行任务
│   ├── review.vue       # 审核任务
│   └── statistics.vue   # 任务统计
└── components/
    ├── AssetSelector.vue    # 资产选择器
    ├── UserSelector.vue     # 用户选择器
    ├── PartSelector.vue     # 备品备件选择器
    └── StatusTag.vue        # 状态标签
```

## 9. 测试要求

### 9.1 功能测试
- 各页面功能完整性测试
- 表单验证测试
- 权限控制测试
- 数据交互测试

### 9.2 兼容性测试
- 浏览器兼容性
- 移动端兼容性
- 分辨率适配测试

### 9.3 性能测试
- 页面加载性能
- 大数据量处理
- 并发操作测试

## 10. 部署和维护

### 10.1 构建配置
- 开发环境配置
- 生产环境配置
- 测试环境配置

### 10.2 监控和日志
- 错误监控
- 性能监控
- 用户行为分析

这份前端功能清单涵盖了设备维护模块的完整前端实现方案，包括页面设计、组件开发、API封装、权限控制等各个方面，为前端开发提供了详细的指导。
