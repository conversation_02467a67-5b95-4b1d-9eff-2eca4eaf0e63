package com.jingfang.web.controller.asset.disposal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.asset_disposal.module.dto.AssetDisposalDto;
import com.jingfang.asset_disposal.module.dto.DisposalApprovalDto;
import com.jingfang.asset_disposal.module.dto.AssetDisposalExecutionDto;
import com.jingfang.asset_disposal.module.request.AssetDisposalSearchRequest;
import com.jingfang.asset_disposal.module.vo.AssetDisposalVo;
import com.jingfang.asset_disposal.module.vo.AssetDisposalDetailVo;
import com.jingfang.asset_disposal.service.AssetDisposalService;
import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.common.utils.SecurityUtils;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 资产处置控制器
 */
@Slf4j
@RestController
@RequestMapping("/asset/disposal")
public class AssetDisposalController extends BaseController {
    
    @Resource
    private AssetDisposalService disposalService;
    
    /**
     * 创建处置申请
     */
    @PostMapping("/create")
    public AjaxResult createDisposal(@Valid @RequestBody AssetDisposalDto dto) {
        try {
            boolean result = disposalService.createDisposalRequest(dto);
            if (result) {
                return AjaxResult.success("创建成功");
            } else {
                return AjaxResult.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建处置申请失败", e);
            return AjaxResult.error("创建失败：" + e.getMessage());
        }
    }
    
    /**
     * 提交审核
     */
    @PostMapping("/submit/{disposalId}")
    public AjaxResult submitForApproval(@PathVariable String disposalId) {
        try {
            boolean result = disposalService.submitForApproval(disposalId);
            if (result) {
                return AjaxResult.success("提交成功");
            } else {
                return AjaxResult.error("提交失败");
            }
        } catch (Exception e) {
            log.error("提交审核失败", e);
            return AjaxResult.error("提交失败：" + e.getMessage());
        }
    }
    
    /**
     * 审批处置申请
     */
    @PostMapping("/approve")
    public AjaxResult approveDisposal(@Valid @RequestBody DisposalApprovalDto dto) {
        try {
            boolean result = disposalService.approveDisposal(dto);
            if (result) {
                return AjaxResult.success("审批成功");
            } else {
                return AjaxResult.error("审批失败");
            }
        } catch (Exception e) {
            log.error("审批处置申请失败", e);
            return AjaxResult.error("审批失败：" + e.getMessage());
        }
    }
    
    /**
     * 执行处置
     */
    @PostMapping("/execute/{disposalId}")
    public AjaxResult executeDisposal(@PathVariable String disposalId, 
                                     @Valid @RequestBody AssetDisposalExecutionDto dto) {
        try {
            boolean result = disposalService.executeDisposal(disposalId, dto);
            if (result) {
                return AjaxResult.success("执行成功");
            } else {
                return AjaxResult.error("执行失败");
            }
        } catch (Exception e) {
            log.error("执行处置失败", e);
            return AjaxResult.error("执行失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询处置列表
     */
    @PostMapping("/list")
    public AjaxResult getDisposalList(@RequestBody AssetDisposalSearchRequest request) {
        try {
            IPage<AssetDisposalVo> page = disposalService.selectDisposalList(request);
            return AjaxResult.success(page);
        } catch (Exception e) {
            log.error("查询处置列表失败", e);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询处置详情
     */
    @GetMapping("/detail/{disposalId}")
    public AjaxResult getDisposalDetail(@PathVariable String disposalId) {
        try {
            AssetDisposalDetailVo detail = disposalService.getDisposalDetail(disposalId);
            return AjaxResult.success(detail);
        } catch (Exception e) {
            log.error("查询处置详情失败", e);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询待审批列表
     */
    @GetMapping("/pending")
    public AjaxResult getPendingApprovalList() {
        try {
            Long userId = SecurityUtils.getUserId();
            List<AssetDisposalVo> list = disposalService.getPendingApprovalList(userId);
            return AjaxResult.success(list);
        } catch (Exception e) {
            log.error("查询待审批列表失败", e);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 取消处置申请
     */
    @PostMapping("/cancel/{disposalId}")
    public AjaxResult cancelDisposal(@PathVariable String disposalId, 
                                    @RequestParam(required = false) String reason) {
        try {
            boolean result = disposalService.cancelDisposal(disposalId, reason);
            if (result) {
                return AjaxResult.success("取消成功");
            } else {
                return AjaxResult.error("取消失败");
            }
        } catch (Exception e) {
            log.error("取消处置申请失败", e);
            return AjaxResult.error("取消失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取处置统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getDisposalStatistics() {
        try {
            Map<String, Object> statistics = disposalService.getDisposalStatistics();
            return AjaxResult.success(statistics);
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            return AjaxResult.error("获取失败：" + e.getMessage());
        }
    }
} 