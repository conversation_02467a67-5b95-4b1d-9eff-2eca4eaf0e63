package com.jingfang.wh_item_requisition.module.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 物品领用单查询请求
 */
@Data
public class ItemRequisitionSearchRequest {
    
    /**
     * 当前页码
     */
    private Integer pageNum;
    
    /**
     * 每页数量
     */
    private Integer pageSize;
    
    /**
     * 领用单ID
     */
    private String requisitionId;
    
    /**
     * 申请人ID
     */
    private Long applicantId;
    
    /**
     * 申请人姓名
     */
    private String applicantName;
    
    /**
     * 申请部门ID
     */
    private Long deptId;
    
    /**
     * 申请部门名称
     */
    private String deptName;
    
    /**
     * 状态(1-草稿, 2-待确认, 3-待审核, 4-已通过, 5-已退回, 6-已完成)
     */
    private Integer status;
    
    /**
     * 制单人ID
     */
    private Long creatorId;
    
    /**
     * 经手人ID
     */
    private Long handlerId;
    
    /**
     * 审核人ID
     */
    private Long auditorId;
    
    /**
     * 业务日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date businessDateStart;
    
    /**
     * 业务日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date businessDateEnd;
    
    /**
     * 创建时间开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimeStart;
    
    /**
     * 创建时间结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimeEnd;
    
    /**
     * 物品ID（查询包含指定物品的领用单）
     */
    private String itemId;
    
    /**
     * 物品名称（查询包含指定物品的领用单）
     */
    private String itemName;
    
    /**
     * 仓库ID（查询包含指定仓库物品的领用单）
     */
    private Integer warehouseId;
} 