# Asset Stocktaking API Test Script (PowerShell)
# Author: AI Assistant
# Date: 2025-01-15
# Description: PowerShell script for testing asset stocktaking APIs

# Configuration
$baseUrl = "http://localhost:8080"
$token = "your_jwt_token_here"  # Replace with actual JWT Token
$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

# Test results storage
$testResults = @()

# Helper function: Record test result
function Record-TestResult {
    param(
        [string]$TestName,
        [string]$Method,
        [string]$Endpoint,
        [bool]$Success,
        [double]$ResponseTime,
        [string]$ErrorMessage = ""
    )

    $result = [PSCustomObject]@{
        TestName = $TestName
        Method = $Method
        Endpoint = $Endpoint
        Success = $Success
        ResponseTime = $ResponseTime
        ErrorMessage = $ErrorMessage
        Timestamp = Get-Date
    }

    $script:testResults += $result

    $status = if ($Success) { "PASS" } else { "FAIL" }
    Write-Host "$TestName - $status (${ResponseTime}ms)" -ForegroundColor $(if ($Success) { "Green" } else { "Red" })
    if ($ErrorMessage) {
        Write-Host "  Error: $ErrorMessage" -ForegroundColor Red
    }
}

# Helper function: Send HTTP request
function Invoke-ApiRequest {
    param(
        [string]$Method,
        [string]$Uri,
        [hashtable]$Headers,
        [string]$Body = $null
    )

    try {
        $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()

        $params = @{
            Uri = $Uri
            Method = $Method
            Headers = $Headers
        }

        if ($Body) {
            $params.Body = $Body
        }

        $response = Invoke-RestMethod @params
        $stopwatch.Stop()

        return @{
            Success = $true
            Data = $response
            ResponseTime = $stopwatch.ElapsedMilliseconds
            ErrorMessage = ""
        }
    }
    catch {
        $stopwatch.Stop()
        return @{
            Success = $false
            Data = $null
            ResponseTime = $stopwatch.ElapsedMilliseconds
            ErrorMessage = $_.Exception.Message
        }
    }
}

# Main test function
function Start-StocktakingApiTest {
    Write-Host "=== Starting Asset Stocktaking API Tests ===" -ForegroundColor Yellow
    Write-Host "Test Time: $(Get-Date)" -ForegroundColor Cyan
    Write-Host "Server URL: $baseUrl" -ForegroundColor Cyan
    Write-Host ""

    # 1. Stocktaking Plan API Tests
    Test-StocktakingPlan

    # 2. Stocktaking Task API Tests
    Test-StocktakingTask

    # 3. Stocktaking Record API Tests
    Test-StocktakingRecord

    # 4. Stocktaking Difference API Tests
    Test-StocktakingDifference

    # 5. Stocktaking Report API Tests
    Test-StocktakingReport

    # Generate test report
    Generate-TestReport
}

# Stocktaking Plan Tests
function Test-StocktakingPlan {
    Write-Host "=== Stocktaking Plan API Tests ===" -ForegroundColor Blue

    # 1.1 Query stocktaking plan list
    $body = @{
        planName = "Test Plan"
        planType = 1
        status = 1
        pageNum = 1
        pageSize = 10
    } | ConvertTo-Json

    $result = Invoke-ApiRequest -Method "POST" -Uri "$baseUrl/asset/stocktaking/plan/list" -Headers $headers -Body $body
    Record-TestResult -TestName "Query Plan List" -Method "POST" -Endpoint "/asset/stocktaking/plan/list" -Success $result.Success -ResponseTime $result.ResponseTime -ErrorMessage $result.ErrorMessage

    # 1.2 Create stocktaking plan
    $createPlanBody = @{
        planName = "PowerShell Test Plan"
        planType = 1
        startDate = "2025-01-15"
        endDate = "2025-01-30"
        responsibleUserId = 1
        remark = "PowerShell automation test"
        scopeDetail = @{
            deptIds = @(100, 101)
            categoryIds = @(1, 2)
            includeSubDept = $true
            minValue = 1000.0
            maxValue = 100000.0
        }
    } | ConvertTo-Json -Depth 3

    $result = Invoke-ApiRequest -Method "POST" -Uri "$baseUrl/asset/stocktaking/plan" -Headers $headers -Body $createPlanBody
    Record-TestResult -TestName "Create Plan" -Method "POST" -Endpoint "/asset/stocktaking/plan" -Success $result.Success -ResponseTime $result.ResponseTime -ErrorMessage $result.ErrorMessage

    if ($result.Success -and $result.Data.data) {
        $script:planId = $result.Data.data
        Write-Host "  Created Plan ID: $script:planId" -ForegroundColor Green

        # 1.3 Get plan details
        $result = Invoke-ApiRequest -Method "GET" -Uri "$baseUrl/asset/stocktaking/plan/$script:planId" -Headers $headers
        Record-TestResult -TestName "Get Plan Details" -Method "GET" -Endpoint "/asset/stocktaking/plan/{planId}" -Success $result.Success -ResponseTime $result.ResponseTime -ErrorMessage $result.ErrorMessage

        # 1.4 Start plan
        $result = Invoke-ApiRequest -Method "POST" -Uri "$baseUrl/asset/stocktaking/plan/start/$script:planId" -Headers $headers
        Record-TestResult -TestName "Start Plan" -Method "POST" -Endpoint "/asset/stocktaking/plan/start/{planId}" -Success $result.Success -ResponseTime $result.ResponseTime -ErrorMessage $result.ErrorMessage
    }

    Write-Host ""
}

# Stocktaking Task Tests
function Test-StocktakingTask {
    Write-Host "=== Stocktaking Task API Tests ===" -ForegroundColor Blue

    if (-not $script:planId) {
        Write-Host "Skip task tests - missing plan ID" -ForegroundColor Yellow
        return
    }

    # 2.1 Query task list
    $body = @{
        planId = $script:planId
        assignedUserId = 1
        status = 1
        pageNum = 1
        pageSize = 10
    } | ConvertTo-Json

    $result = Invoke-ApiRequest -Method "POST" -Uri "$baseUrl/asset/stocktaking/task/list" -Headers $headers -Body $body
    Record-TestResult -TestName "Query Task List" -Method "POST" -Endpoint "/asset/stocktaking/task/list" -Success $result.Success -ResponseTime $result.ResponseTime -ErrorMessage $result.ErrorMessage

    # 2.2 Create task
    $createTaskBody = @{
        planId = $script:planId
        taskName = "PowerShell Test Task"
        assignedUserId = 2
        expectedCount = 50
        distributionConfig = @{
            distributionType = 1
            maxAssetCount = 20
            assignedUserIds = @(2, 3)
            autoAssign = $true
            priority = 2
        }
    } | ConvertTo-Json -Depth 3

    $result = Invoke-ApiRequest -Method "POST" -Uri "$baseUrl/asset/stocktaking/task" -Headers $headers -Body $createTaskBody
    Record-TestResult -TestName "Create Task" -Method "POST" -Endpoint "/asset/stocktaking/task" -Success $result.Success -ResponseTime $result.ResponseTime -ErrorMessage $result.ErrorMessage

    if ($result.Success -and $result.Data.data) {
        $script:taskId = $result.Data.data
        Write-Host "  Created Task ID: $script:taskId" -ForegroundColor Green

        # 2.3 Start task
        $result = Invoke-ApiRequest -Method "POST" -Uri "$baseUrl/asset/stocktaking/task/start/$script:taskId" -Headers $headers
        Record-TestResult -TestName "Start Task" -Method "POST" -Endpoint "/asset/stocktaking/task/start/{taskId}" -Success $result.Success -ResponseTime $result.ResponseTime -ErrorMessage $result.ErrorMessage
    }

    Write-Host ""
}

# Stocktaking Record Tests
function Test-StocktakingRecord {
    Write-Host "=== Stocktaking Record API Tests ===" -ForegroundColor Blue

    if (-not $script:taskId) {
        Write-Host "Skip record tests - missing task ID" -ForegroundColor Yellow
        return
    }

    # 3.1 Query record list
    $body = @{
        taskId = $script:taskId
        foundStatus = 1
        pageNum = 1
        pageSize = 10
    } | ConvertTo-Json

    $result = Invoke-ApiRequest -Method "POST" -Uri "$baseUrl/asset/stocktaking/record/list" -Headers $headers -Body $body
    Record-TestResult -TestName "Query Record List" -Method "POST" -Endpoint "/asset/stocktaking/record/list" -Success $result.Success -ResponseTime $result.ResponseTime -ErrorMessage $result.ErrorMessage

    # 3.2 Create record
    $createRecordBody = @{
        taskId = $script:taskId
        assetId = "PS_TEST001"
        assetCode = "PST001"
        foundStatus = 1
        actualLocation = "PowerShell Test Location"
        actualStatus = 1
        inventoryTime = "2025-01-15 10:30:00"
        remark = "PowerShell automation test record"
        scanInfo = @{
            scanType = 1
            scanContent = "QR_PS_TEST001"
            scanTime = "2025-01-15 10:30:00"
            deviceInfo = "PowerShell Test Device"
        }
    } | ConvertTo-Json -Depth 3

    $result = Invoke-ApiRequest -Method "POST" -Uri "$baseUrl/asset/stocktaking/record" -Headers $headers -Body $createRecordBody
    Record-TestResult -TestName "Create Record" -Method "POST" -Endpoint "/asset/stocktaking/record" -Success $result.Success -ResponseTime $result.ResponseTime -ErrorMessage $result.ErrorMessage

    if ($result.Success -and $result.Data.data) {
        $script:recordId = $result.Data.data
        Write-Host "  Created Record ID: $script:recordId" -ForegroundColor Green
    }

    Write-Host ""
}

# Stocktaking Difference Tests
function Test-StocktakingDifference {
    Write-Host "=== Stocktaking Difference API Tests ===" -ForegroundColor Blue

    if (-not $script:planId) {
        Write-Host "Skip difference tests - missing plan ID" -ForegroundColor Yellow
        return
    }

    # 4.1 Query difference list
    $body = @{
        planId = $script:planId
        diffType = 2
        handleStatus = 1
        pageNum = 1
        pageSize = 10
    } | ConvertTo-Json

    $result = Invoke-ApiRequest -Method "POST" -Uri "$baseUrl/asset/stocktaking/difference/list" -Headers $headers -Body $body
    Record-TestResult -TestName "Query Difference List" -Method "POST" -Endpoint "/asset/stocktaking/difference/list" -Success $result.Success -ResponseTime $result.ResponseTime -ErrorMessage $result.ErrorMessage

    Write-Host ""
}

# Stocktaking Report Tests
function Test-StocktakingReport {
    Write-Host "=== Stocktaking Report API Tests ===" -ForegroundColor Blue

    if (-not $script:planId) {
        Write-Host "Skip report tests - missing plan ID" -ForegroundColor Yellow
        return
    }

    # 5.1 Generate summary report
    $result = Invoke-ApiRequest -Method "GET" -Uri "$baseUrl/asset/stocktaking/report/summary/$script:planId" -Headers $headers
    Record-TestResult -TestName "Generate Summary Report" -Method "GET" -Endpoint "/asset/stocktaking/report/summary/{planId}" -Success $result.Success -ResponseTime $result.ResponseTime -ErrorMessage $result.ErrorMessage

    # 5.2 Generate chart data
    $result = Invoke-ApiRequest -Method "GET" -Uri "$baseUrl/asset/stocktaking/report/chart/$script:planId" -Headers $headers
    Record-TestResult -TestName "Generate Chart Data" -Method "GET" -Endpoint "/asset/stocktaking/report/chart/{planId}" -Success $result.Success -ResponseTime $result.ResponseTime -ErrorMessage $result.ErrorMessage

    Write-Host ""
}

# Generate test report
function Generate-TestReport {
    Write-Host "=== Test Report ===" -ForegroundColor Yellow

    $totalTests = $testResults.Count
    $passedTests = ($testResults | Where-Object { $_.Success }).Count
    $failedTests = $totalTests - $passedTests
    $successRate = if ($totalTests -gt 0) { [math]::Round(($passedTests / $totalTests) * 100, 2) } else { 0 }
    $avgResponseTime = if ($totalTests -gt 0) { [math]::Round(($testResults | Measure-Object ResponseTime -Average).Average, 2) } else { 0 }

    Write-Host "Total Tests: $totalTests" -ForegroundColor Cyan
    Write-Host "Passed: $passedTests" -ForegroundColor Green
    Write-Host "Failed: $failedTests" -ForegroundColor Red
    Write-Host "Success Rate: $successRate%" -ForegroundColor Cyan
    Write-Host "Average Response Time: ${avgResponseTime}ms" -ForegroundColor Cyan
    Write-Host ""

    # Detailed results
    Write-Host "Detailed Test Results:" -ForegroundColor Yellow
    $testResults | Format-Table TestName, Success, ResponseTime, ErrorMessage -AutoSize

    # Export to CSV file
    $reportPath = "doc/StocktakingApiTestReport_$(Get-Date -Format 'yyyyMMdd_HHmmss').csv"
    $testResults | Export-Csv -Path $reportPath -NoTypeInformation -Encoding UTF8
    Write-Host "Test report exported to: $reportPath" -ForegroundColor Green
}

# Execute tests
Start-StocktakingApiTest
