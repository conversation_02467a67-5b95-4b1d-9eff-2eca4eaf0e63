# 资产盘点接口测试脚本 (PowerShell版本)
# 作者: AI Assistant
# 日期: 2025-01-15
# 描述: 用于测试资产盘点相关接口的PowerShell脚本

# 配置参数
$baseUrl = "http://localhost:8080"
$token = "your_jwt_token_here"  # 请替换为实际的JWT Token
$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

# 测试结果记录
$testResults = @()

# 辅助函数：记录测试结果
function Record-TestResult {
    param(
        [string]$TestName,
        [string]$Method,
        [string]$Endpoint,
        [bool]$Success,
        [double]$ResponseTime,
        [string]$ErrorMessage = ""
    )
    
    $result = [PSCustomObject]@{
        TestName = $TestName
        Method = $Method
        Endpoint = $Endpoint
        Success = $Success
        ResponseTime = $ResponseTime
        ErrorMessage = $ErrorMessage
        Timestamp = Get-Date
    }
    
    $script:testResults += $result
    
    $status = if ($Success) { "✓ 通过" } else { "✗ 失败" }
    Write-Host "$TestName - $status (${ResponseTime}ms)" -ForegroundColor $(if ($Success) { "Green" } else { "Red" })
    if ($ErrorMessage) {
        Write-Host "  错误: $ErrorMessage" -ForegroundColor Red
    }
}

# 辅助函数：发送HTTP请求
function Invoke-ApiRequest {
    param(
        [string]$Method,
        [string]$Uri,
        [hashtable]$Headers,
        [string]$Body = $null
    )
    
    try {
        $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
        
        $params = @{
            Uri = $Uri
            Method = $Method
            Headers = $Headers
        }
        
        if ($Body) {
            $params.Body = $Body
        }
        
        $response = Invoke-RestMethod @params
        $stopwatch.Stop()
        
        return @{
            Success = $true
            Data = $response
            ResponseTime = $stopwatch.ElapsedMilliseconds
            ErrorMessage = ""
        }
    }
    catch {
        $stopwatch.Stop()
        return @{
            Success = $false
            Data = $null
            ResponseTime = $stopwatch.ElapsedMilliseconds
            ErrorMessage = $_.Exception.Message
        }
    }
}

# 主测试函数
function Start-StocktakingApiTest {
    Write-Host "=== 开始资产盘点接口测试 ===" -ForegroundColor Yellow
    Write-Host "测试时间: $(Get-Date)" -ForegroundColor Cyan
    Write-Host "服务地址: $baseUrl" -ForegroundColor Cyan
    Write-Host ""
    
    # 1. 盘点计划接口测试
    Test-StocktakingPlan
    
    # 2. 盘点任务接口测试
    Test-StocktakingTask
    
    # 3. 盘点记录接口测试
    Test-StocktakingRecord
    
    # 4. 盘点差异接口测试
    Test-StocktakingDifference
    
    # 5. 盘点报告接口测试
    Test-StocktakingReport
    
    # 生成测试报告
    Generate-TestReport
}

# 盘点计划测试
function Test-StocktakingPlan {
    Write-Host "=== 盘点计划接口测试 ===" -ForegroundColor Blue
    
    # 1.1 查询盘点计划列表
    $body = @{
        planName = "测试盘点"
        planType = 1
        status = 1
        pageNum = 1
        pageSize = 10
    } | ConvertTo-Json
    
    $result = Invoke-ApiRequest -Method "POST" -Uri "$baseUrl/asset/stocktaking/plan/list" -Headers $headers -Body $body
    Record-TestResult -TestName "查询盘点计划列表" -Method "POST" -Endpoint "/asset/stocktaking/plan/list" -Success $result.Success -ResponseTime $result.ResponseTime -ErrorMessage $result.ErrorMessage
    
    # 1.2 创建盘点计划
    $createPlanBody = @{
        planName = "PowerShell测试盘点计划"
        planType = 1
        startDate = "2025-01-15"
        endDate = "2025-01-30"
        responsibleUserId = 1
        remark = "PowerShell自动化测试"
        scopeDetail = @{
            deptIds = @(100, 101)
            categoryIds = @(1, 2)
            includeSubDept = $true
            minValue = 1000.0
            maxValue = 100000.0
        }
    } | ConvertTo-Json -Depth 3
    
    $result = Invoke-ApiRequest -Method "POST" -Uri "$baseUrl/asset/stocktaking/plan" -Headers $headers -Body $createPlanBody
    Record-TestResult -TestName "创建盘点计划" -Method "POST" -Endpoint "/asset/stocktaking/plan" -Success $result.Success -ResponseTime $result.ResponseTime -ErrorMessage $result.ErrorMessage
    
    if ($result.Success -and $result.Data.data) {
        $script:planId = $result.Data.data
        Write-Host "  创建的计划ID: $script:planId" -ForegroundColor Green
        
        # 1.3 获取盘点计划详情
        $result = Invoke-ApiRequest -Method "GET" -Uri "$baseUrl/asset/stocktaking/plan/$script:planId" -Headers $headers
        Record-TestResult -TestName "获取盘点计划详情" -Method "GET" -Endpoint "/asset/stocktaking/plan/{planId}" -Success $result.Success -ResponseTime $result.ResponseTime -ErrorMessage $result.ErrorMessage
        
        # 1.4 启动盘点计划
        $result = Invoke-ApiRequest -Method "POST" -Uri "$baseUrl/asset/stocktaking/plan/start/$script:planId" -Headers $headers
        Record-TestResult -TestName "启动盘点计划" -Method "POST" -Endpoint "/asset/stocktaking/plan/start/{planId}" -Success $result.Success -ResponseTime $result.ResponseTime -ErrorMessage $result.ErrorMessage
    }
    
    Write-Host ""
}

# 盘点任务测试
function Test-StocktakingTask {
    Write-Host "=== 盘点任务接口测试 ===" -ForegroundColor Blue
    
    if (-not $script:planId) {
        Write-Host "跳过任务测试 - 缺少计划ID" -ForegroundColor Yellow
        return
    }
    
    # 2.1 查询盘点任务列表
    $body = @{
        planId = $script:planId
        assignedUserId = 1
        status = 1
        pageNum = 1
        pageSize = 10
    } | ConvertTo-Json
    
    $result = Invoke-ApiRequest -Method "POST" -Uri "$baseUrl/asset/stocktaking/task/list" -Headers $headers -Body $body
    Record-TestResult -TestName "查询盘点任务列表" -Method "POST" -Endpoint "/asset/stocktaking/task/list" -Success $result.Success -ResponseTime $result.ResponseTime -ErrorMessage $result.ErrorMessage
    
    # 2.2 创建盘点任务
    $createTaskBody = @{
        planId = $script:planId
        taskName = "PowerShell测试任务"
        assignedUserId = 2
        expectedCount = 50
        distributionConfig = @{
            distributionType = 1
            maxAssetCount = 20
            assignedUserIds = @(2, 3)
            autoAssign = $true
            priority = 2
        }
    } | ConvertTo-Json -Depth 3
    
    $result = Invoke-ApiRequest -Method "POST" -Uri "$baseUrl/asset/stocktaking/task" -Headers $headers -Body $createTaskBody
    Record-TestResult -TestName "创建盘点任务" -Method "POST" -Endpoint "/asset/stocktaking/task" -Success $result.Success -ResponseTime $result.ResponseTime -ErrorMessage $result.ErrorMessage
    
    if ($result.Success -and $result.Data.data) {
        $script:taskId = $result.Data.data
        Write-Host "  创建的任务ID: $script:taskId" -ForegroundColor Green
        
        # 2.3 开始执行任务
        $result = Invoke-ApiRequest -Method "POST" -Uri "$baseUrl/asset/stocktaking/task/start/$script:taskId" -Headers $headers
        Record-TestResult -TestName "开始执行任务" -Method "POST" -Endpoint "/asset/stocktaking/task/start/{taskId}" -Success $result.Success -ResponseTime $result.ResponseTime -ErrorMessage $result.ErrorMessage
    }
    
    Write-Host ""
}

# 盘点记录测试
function Test-StocktakingRecord {
    Write-Host "=== 盘点记录接口测试 ===" -ForegroundColor Blue
    
    if (-not $script:taskId) {
        Write-Host "跳过记录测试 - 缺少任务ID" -ForegroundColor Yellow
        return
    }
    
    # 3.1 查询盘点记录列表
    $body = @{
        taskId = $script:taskId
        foundStatus = 1
        pageNum = 1
        pageSize = 10
    } | ConvertTo-Json
    
    $result = Invoke-ApiRequest -Method "POST" -Uri "$baseUrl/asset/stocktaking/record/list" -Headers $headers -Body $body
    Record-TestResult -TestName "查询盘点记录列表" -Method "POST" -Endpoint "/asset/stocktaking/record/list" -Success $result.Success -ResponseTime $result.ResponseTime -ErrorMessage $result.ErrorMessage
    
    # 3.2 创建盘点记录
    $createRecordBody = @{
        taskId = $script:taskId
        assetId = "PS_TEST001"
        assetCode = "PST001"
        foundStatus = 1
        actualLocation = "PowerShell测试位置"
        actualStatus = 1
        inventoryTime = "2025-01-15 10:30:00"
        remark = "PowerShell自动化测试记录"
        scanInfo = @{
            scanType = 1
            scanContent = "QR_PS_TEST001"
            scanTime = "2025-01-15 10:30:00"
            deviceInfo = "PowerShell测试设备"
        }
    } | ConvertTo-Json -Depth 3
    
    $result = Invoke-ApiRequest -Method "POST" -Uri "$baseUrl/asset/stocktaking/record" -Headers $headers -Body $createRecordBody
    Record-TestResult -TestName "创建盘点记录" -Method "POST" -Endpoint "/asset/stocktaking/record" -Success $result.Success -ResponseTime $result.ResponseTime -ErrorMessage $result.ErrorMessage
    
    if ($result.Success -and $result.Data.data) {
        $script:recordId = $result.Data.data
        Write-Host "  创建的记录ID: $script:recordId" -ForegroundColor Green
    }
    
    Write-Host ""
}

# 盘点差异测试
function Test-StocktakingDifference {
    Write-Host "=== 盘点差异接口测试 ===" -ForegroundColor Blue
    
    if (-not $script:planId) {
        Write-Host "跳过差异测试 - 缺少计划ID" -ForegroundColor Yellow
        return
    }
    
    # 4.1 查询差异列表
    $body = @{
        planId = $script:planId
        diffType = 2
        handleStatus = 1
        pageNum = 1
        pageSize = 10
    } | ConvertTo-Json
    
    $result = Invoke-ApiRequest -Method "POST" -Uri "$baseUrl/asset/stocktaking/difference/list" -Headers $headers -Body $body
    Record-TestResult -TestName "查询盘点差异列表" -Method "POST" -Endpoint "/asset/stocktaking/difference/list" -Success $result.Success -ResponseTime $result.ResponseTime -ErrorMessage $result.ErrorMessage
    
    Write-Host ""
}

# 盘点报告测试
function Test-StocktakingReport {
    Write-Host "=== 盘点报告接口测试 ===" -ForegroundColor Blue
    
    if (-not $script:planId) {
        Write-Host "跳过报告测试 - 缺少计划ID" -ForegroundColor Yellow
        return
    }
    
    # 5.1 生成汇总报告
    $result = Invoke-ApiRequest -Method "GET" -Uri "$baseUrl/asset/stocktaking/report/summary/$script:planId" -Headers $headers
    Record-TestResult -TestName "生成汇总报告" -Method "GET" -Endpoint "/asset/stocktaking/report/summary/{planId}" -Success $result.Success -ResponseTime $result.ResponseTime -ErrorMessage $result.ErrorMessage
    
    # 5.2 生成图表数据
    $result = Invoke-ApiRequest -Method "GET" -Uri "$baseUrl/asset/stocktaking/report/chart/$script:planId" -Headers $headers
    Record-TestResult -TestName "生成图表数据" -Method "GET" -Endpoint "/asset/stocktaking/report/chart/{planId}" -Success $result.Success -ResponseTime $result.ResponseTime -ErrorMessage $result.ErrorMessage
    
    Write-Host ""
}

# 生成测试报告
function Generate-TestReport {
    Write-Host "=== 测试报告 ===" -ForegroundColor Yellow
    
    $totalTests = $testResults.Count
    $passedTests = ($testResults | Where-Object { $_.Success }).Count
    $failedTests = $totalTests - $passedTests
    $successRate = if ($totalTests -gt 0) { [math]::Round(($passedTests / $totalTests) * 100, 2) } else { 0 }
    $avgResponseTime = if ($totalTests -gt 0) { [math]::Round(($testResults | Measure-Object ResponseTime -Average).Average, 2) } else { 0 }
    
    Write-Host "测试总数: $totalTests" -ForegroundColor Cyan
    Write-Host "通过数量: $passedTests" -ForegroundColor Green
    Write-Host "失败数量: $failedTests" -ForegroundColor Red
    Write-Host "成功率: $successRate%" -ForegroundColor Cyan
    Write-Host "平均响应时间: ${avgResponseTime}ms" -ForegroundColor Cyan
    Write-Host ""
    
    # 详细结果
    Write-Host "详细测试结果:" -ForegroundColor Yellow
    $testResults | Format-Table TestName, Success, ResponseTime, ErrorMessage -AutoSize
    
    # 导出到CSV文件
    $reportPath = "doc/资产盘点接口测试报告_$(Get-Date -Format 'yyyyMMdd_HHmmss').csv"
    $testResults | Export-Csv -Path $reportPath -NoTypeInformation -Encoding UTF8
    Write-Host "测试报告已导出到: $reportPath" -ForegroundColor Green
}

# 执行测试
Start-StocktakingApiTest
