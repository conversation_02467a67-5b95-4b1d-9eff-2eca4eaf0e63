package com.jingfang.asset_stocktaking.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 资产盘点差异实体类
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@TableName(value = "asset_stocktaking_difference")
public class AssetStocktakingDifference implements Serializable {

    /**
     * 差异ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String diffId;

    /**
     * 盘点计划ID
     */
    private String planId;

    /**
     * 资产ID
     */
    private String assetId;

    /**
     * 差异类型：1-盘盈，2-盘亏，3-状态差异，4-位置差异
     */
    private Integer diffType;

    /**
     * 账面信息（JSON格式）
     */
    private String bookValue;

    /**
     * 实际信息（JSON格式）
     */
    private String actualValue;

    /**
     * 差异原因
     */
    private String diffReason;

    /**
     * 处理状态：1-待处理，2-处理中，3-已处理
     */
    private Integer handleStatus;

    /**
     * 处理建议
     */
    private String handleSuggestion;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 差异类型常量
     */
    public static final class DiffType {
        /** 盘盈 */
        public static final int SURPLUS = 1;
        /** 盘亏 */
        public static final int DEFICIT = 2;
        /** 状态差异 */
        public static final int STATUS_DIFF = 3;
        /** 位置差异 */
        public static final int LOCATION_DIFF = 4;
    }

    /**
     * 处理状态常量
     */
    public static final class HandleStatus {
        /** 待处理 */
        public static final int PENDING = 1;
        /** 处理中 */
        public static final int PROCESSING = 2;
        /** 已处理 */
        public static final int PROCESSED = 3;
    }

    /**
     * 判断是否为盘盈
     * 
     * @return true-盘盈，false-非盘盈
     */
    public boolean isSurplus() {
        return DiffType.SURPLUS == this.diffType;
    }

    /**
     * 判断是否为盘亏
     * 
     * @return true-盘亏，false-非盘亏
     */
    public boolean isDeficit() {
        return DiffType.DEFICIT == this.diffType;
    }

    /**
     * 判断是否为状态差异
     * 
     * @return true-状态差异，false-非状态差异
     */
    public boolean isStatusDiff() {
        return DiffType.STATUS_DIFF == this.diffType;
    }

    /**
     * 判断是否为位置差异
     * 
     * @return true-位置差异，false-非位置差异
     */
    public boolean isLocationDiff() {
        return DiffType.LOCATION_DIFF == this.diffType;
    }

    /**
     * 判断是否已处理
     * 
     * @return true-已处理，false-未处理
     */
    public boolean isProcessed() {
        return HandleStatus.PROCESSED == this.handleStatus;
    }

    /**
     * 获取差异类型描述
     * 
     * @return 差异类型描述
     */
    public String getDiffTypeDesc() {
        if (diffType == null) {
            return "未知";
        }
        switch (diffType) {
            case DiffType.SURPLUS:
                return "盘盈";
            case DiffType.DEFICIT:
                return "盘亏";
            case DiffType.STATUS_DIFF:
                return "状态差异";
            case DiffType.LOCATION_DIFF:
                return "位置差异";
            default:
                return "未知";
        }
    }
}
