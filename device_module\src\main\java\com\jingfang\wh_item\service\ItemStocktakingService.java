package com.jingfang.wh_item.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.wh_item.module.dto.ItemStocktakingDto;
import com.jingfang.wh_item.module.dto.ItemStocktakingDetailUpdateDto;
import com.jingfang.wh_item.module.request.ItemStocktakingSearchRequest;
import com.jingfang.wh_item.module.vo.ItemStocktakingDetailVo;
import com.jingfang.wh_item.module.vo.ItemStocktakingVo;
import com.jingfang.wh_item.module.vo.StocktakingProgressVo;
import com.jingfang.wh_item.module.vo.PersonalProgressVo;

import java.math.BigDecimal;
import java.util.List;

public interface ItemStocktakingService {

    /**
     * 创建库存盘点计划
     */
    String createStocktaking(ItemStocktakingDto dto, String username);

    /**
     * 生成盘点明细
     */
    boolean generateStocktakingDetails(String stocktakingId);

    /**
     * 开始盘点
     */
    boolean startStocktaking(String stocktakingId, String username);

    /**
     * 录入盘点结果
     */
    boolean recordStocktakingResult(String detailId, BigDecimal actualQuantity, String differenceReason, String username);

    /**
     * 完成盘点
     */
    boolean completeStocktaking(String stocktakingId, String username);

    /**
     * 审核盘点结果
     */
    boolean auditStocktaking(String stocktakingId, boolean approved, String auditRemark, String username);

    /**
     * 应用盘点差异到库存
     */
    boolean applyStocktakingDifferences(String stocktakingId, String username);
    
    /**
     * 查询盘点列表
     */
    IPage<ItemStocktakingVo> selectStocktakingList(ItemStocktakingSearchRequest request);
    
    /**
     * 获取盘点详情
     */
    ItemStocktakingVo getStocktakingDetail(String stocktakingId);
    
    /**
     * 查询盘点明细列表
     */
    List<ItemStocktakingDetailVo> selectStocktakingDetailList(String stocktakingId);
    
    /**
     * 删除盘点计划
     */
    boolean deleteStocktaking(String stocktakingId);
    
    /**
     * 更新盘点计划
     */
    boolean updateStocktaking(ItemStocktakingDto dto, String username);

    // ==================== 微信小程序专用接口 ====================

    /**
     * 获取用户的盘点任务列表
     * @param username 用户名
     * @return 盘点任务列表
     */
    List<ItemStocktakingVo> getMyTasks(String username);

    /**
     * 获取盘点进度
     * @param stocktakingId 盘点单ID
     * @return 盘点进度信息
     */
    StocktakingProgressVo getStocktakingProgress(String stocktakingId);

    /**
     * 获取个人盘点进度
     * @param username 用户名
     * @return 个人进度信息
     */
    PersonalProgressVo getMyProgress(String username);

    /**
     * 获取个人盘点记录
     * @param username 用户名
     * @param timeRange 时间范围（today/week/month/custom）
     * @param startDate 开始日期（timeRange为custom时使用）
     * @param endDate 结束日期（timeRange为custom时使用）
     * @return 个人盘点记录列表
     */
    List<ItemStocktakingDetailVo> getMyRecords(String username, String timeRange, String startDate, String endDate);

    /**
     * 获取物品盘点历史
     * @param itemId 物品ID
     * @return 物品盘点历史列表
     */
    List<ItemStocktakingDetailVo> getItemHistory(String itemId);

    /**
     * 更新盘点明细（包括照片）
     * @param detailId 明细ID
     * @param dto 更新信息
     * @param username 操作用户
     * @return 更新结果
     */
    boolean updateStocktakingDetail(String detailId, ItemStocktakingDetailUpdateDto dto, String username);

    /**
     * 根据物品ID和仓库ID查找盘点明细
     * @param stocktakingId 盘点单ID
     * @param itemId 物品ID
     * @param warehouseId 仓库ID
     * @return 盘点明细
     */
    ItemStocktakingDetailVo getStocktakingDetailByItem(String stocktakingId, String itemId, Integer warehouseId);
}
