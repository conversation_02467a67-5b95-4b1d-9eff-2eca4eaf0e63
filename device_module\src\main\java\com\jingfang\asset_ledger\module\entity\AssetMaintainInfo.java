package com.jingfang.asset_ledger.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 资产维保信息
 * @TableName asset_maintenance
 */
@TableName(value ="asset_maintenance")
@Data
public class AssetMaintainInfo implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long maintainId;

    /**
     * 
     */
    private String assetId;

    /**
     * 维保厂商
     */
    private String maintainVendor;

    /**
     * 
     */
    private Date startTime;

    /**
     * 
     */
    private Date endTime;

    /**
     * 维保状态
     */
    private Integer maintainStatus;

    /**
     * 联系方式
     */
    private String contactNumber;

    /**
     * 负责人
     */
    private Long managerId;

    /**
     * 维保方式
     */
    private Integer maintainMethod;

    /**
     * 备注
     */
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}