package com.jingfang.asset_disposal.module.dto;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 处置审批DTO
 */
@Data
public class DisposalApprovalDto {
    
    /**
     * 处置单ID
     */
    @NotBlank(message = "处置单ID不能为空")
    private String disposalId;
    
    /**
     * 审批结果(2-通过, 3-拒绝)
     */
    @NotNull(message = "审批结果不能为空")
    private Integer approvalStatus;
    
    /**
     * 审批意见
     */
    private String approvalComment;
} 