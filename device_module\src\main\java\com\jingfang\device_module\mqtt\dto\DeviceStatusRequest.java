package com.jingfang.device_module.mqtt.dto;

import lombok.Data;

import java.util.List;

/**
 * MQTT设备状态查询请求DTO
 */
@Data
public class DeviceStatusRequest {
    
    /**
     * 请求ID，用于匹配请求和响应
     */
    private String id;
    
    /**
     * 版本号
     */
    private String version;
    
    /**
     * 确认标志，固定为1
     */
    private Integer ack;
    
    /**
     * 参数列表，固定为空
     */
    private List<Object> params;
    
    public DeviceStatusRequest() {
        this.version = "V1.0";
        this.ack = 1;
        this.params = List.of();
    }
    
    public DeviceStatusRequest(String id) {
        this();
        this.id = id;
    }
}
