package com.jingfang.wh_item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.wh_item.module.entity.ItemBaseInfo;
import com.jingfang.wh_item.module.request.ItemSearchRequest;
import com.jingfang.wh_item.module.request.ItemStockStatisticsRequest;
import com.jingfang.wh_item.module.request.ItemStockViewRequest;
import com.jingfang.wh_item.module.vo.ItemDetailVo;
import com.jingfang.wh_item.module.vo.ItemInventoryVo;
import com.jingfang.wh_item.module.vo.ItemStockStatisticsVo;
import com.jingfang.wh_item.module.vo.ItemStockViewVo;
import com.jingfang.wh_item.module.vo.ItemVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物品基本信息Mapper接口
 */
@Mapper
public interface ItemBaseInfoMapper extends BaseMapper<ItemBaseInfo> {
    
    /**
     * 查询物品列表
     * 
     * @param page 分页参数
     * @param request 查询条件
     * @return 物品列表
     */
    IPage<ItemVo> selectItemList(IPage<ItemVo> page, @Param("request") ItemSearchRequest request);
    
    /**
     * 查询物品基本信息（不包含库存）
     * 
     * @param itemId 物品ID
     * @return 物品基本信息
     */
    ItemDetailVo selectItemBaseInfoById(@Param("itemId") String itemId);
    
    /**
     * 查询物品库存列表
     * 
     * @param itemId 物品ID
     * @return 库存列表
     */
    List<ItemInventoryVo> selectItemInventoryList(@Param("itemId") String itemId);
    
    /**
     * 查询物品的仓库ID列表
     * 
     * @param itemId 物品ID
     * @return 仓库ID列表
     */
    List<Integer> selectWarehouseIdsByItemId(@Param("itemId") String itemId);
    
    /**
     * 物品库存统计查询
     * 
     * @param page 分页参数
     * @param request 查询条件
     * @return 库存统计列表
     */
    IPage<ItemStockStatisticsVo> selectItemStockStatistics(IPage<ItemStockStatisticsVo> page, @Param("request") ItemStockStatisticsRequest request);
    
    /**
     * 物品库存统计查询（无分页）
     * 
     * @param request 查询条件
     * @return 库存统计列表
     */
    List<ItemStockStatisticsVo> selectItemStockStatisticsWithoutPage(@Param("request") ItemStockStatisticsRequest request);
    
    /**
     * 查询库存不足的物品列表（库存下限告警）
     * 查询总库存低于企业级安全库存的物品
     * 
     * @return 库存不足的物品列表
     */
    List<ItemVo> selectLowStockItems();
    
    /**
     * 库存查看 - 按仓库展示
     * 
     * @param page 分页参数
     * @param request 查询条件
     * @return 库存查看列表
     */
    IPage<ItemStockViewVo> selectItemStockViewByWarehouse(IPage<ItemStockViewVo> page, @Param("request") ItemStockViewRequest request);
    
    /**
     * 库存查看 - 按物品展示
     * 
     * @param page 分页参数
     * @param request 查询条件
     * @return 库存查看列表
     */
    IPage<ItemStockViewVo> selectItemStockViewByItem(IPage<ItemStockViewVo> page, @Param("request") ItemStockViewRequest request);
    
    /**
     * 查询物品的库存详情列表（用于按物品展示模式）
     * 
     * @param itemId 物品ID
     * @return 库存详情列表
     */
    List<ItemStockViewVo.ItemStockDetailVo> selectItemStockDetails(@Param("itemId") String itemId);
} 