-- ========================================
-- 设备参数表MQTT网关适配修改脚本
-- 适用于使用MQTT网关设备进行参数采集的场景
-- 执行前请备份现有数据
-- ========================================

-- 1. 备份现有设备参数数据
CREATE TABLE IF NOT EXISTS device_param_backup AS SELECT * FROM device_param;

-- 2. 修改device_param表结构，适配MQTT网关方式
-- 删除不再需要的Modbus相关字段，添加MQTT相关字段

-- 2.1 添加新字段（如果不存在）
ALTER TABLE device_param 
ADD COLUMN IF NOT EXISTS `mqtt_property_name` varchar(100) COMMENT 'MQTT属性名称，用于从网关获取数据' AFTER `param_name`;

ALTER TABLE device_param 
ADD COLUMN IF NOT EXISTS `param_description` varchar(255) COMMENT '参数描述' AFTER `mqtt_property_name`;

ALTER TABLE device_param 
ADD COLUMN IF NOT EXISTS `is_alert_param` tinyint(1) DEFAULT 0 COMMENT '是否为告警参数 0-运行参数 1-告警参数' AFTER `param_description`;

ALTER TABLE device_param 
ADD COLUMN IF NOT EXISTS `alert_threshold_min` double COMMENT '告警阈值下限' AFTER `range_end`;

ALTER TABLE device_param 
ADD COLUMN IF NOT EXISTS `alert_threshold_max` double COMMENT '告警阈值上限' AFTER `alert_threshold_min`;

ALTER TABLE device_param 
ADD COLUMN IF NOT EXISTS `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `del_flag`;

ALTER TABLE device_param 
ADD COLUMN IF NOT EXISTS `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `created_time`;

-- 2.2 修改现有字段注释，使其更适合MQTT场景
ALTER TABLE device_param 
MODIFY COLUMN `param_key` varchar(50) COMMENT '参数键值，用于系统内部标识';

ALTER TABLE device_param 
MODIFY COLUMN `param_name` varchar(100) COMMENT '参数显示名称';

ALTER TABLE device_param 
MODIFY COLUMN `param_unit` varchar(20) COMMENT '参数单位（如：°C、%、V等）';

ALTER TABLE device_param 
MODIFY COLUMN `data_type` int COMMENT '数据类型 0-整型 1-浮点型 2-布尔型 3-字符串型';

ALTER TABLE device_param 
MODIFY COLUMN `category_id` int COMMENT '参数分类 1-温度 2-湿度 3-压力 4-电压 5-电流 6-状态 99-其他';

ALTER TABLE device_param 
MODIFY COLUMN `range_start` double COMMENT '正常运行范围下限';

ALTER TABLE device_param 
MODIFY COLUMN `range_end` double COMMENT '正常运行范围上限';

-- 2.3 将不再使用的Modbus字段设为可空，但保留以便回退
ALTER TABLE device_param 
MODIFY COLUMN `storage_type` int NULL COMMENT '存储类型（已废弃，保留用于数据迁移）';

ALTER TABLE device_param 
MODIFY COLUMN `param_address` int NULL COMMENT '参数地址（已废弃，保留用于数据迁移）';

-- 3. 创建设备参数分类字典表（如果不存在）
CREATE TABLE IF NOT EXISTS `device_param_category` (
  `category_id` int NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_name` varchar(50) NOT NULL COMMENT '分类名称',
  `category_code` varchar(20) NOT NULL COMMENT '分类编码',
  `category_icon` varchar(50) DEFAULT NULL COMMENT '分类图标',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 0-禁用 1-启用',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`category_id`),
  UNIQUE KEY `uk_category_code` (`category_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备参数分类表';

-- 4. 插入默认参数分类数据
INSERT IGNORE INTO `device_param_category` (`category_id`, `category_name`, `category_code`, `category_icon`, `sort_order`, `status`, `remark`) VALUES
(1, '温度参数', 'TEMPERATURE', 'el-icon-thermometer', 1, 1, '温度相关参数'),
(2, '湿度参数', 'HUMIDITY', 'el-icon-water', 2, 1, '湿度相关参数'),
(3, '压力参数', 'PRESSURE', 'el-icon-odometer', 3, 1, '压力相关参数'),
(4, '电压参数', 'VOLTAGE', 'el-icon-lightning', 4, 1, '电压相关参数'),
(5, '电流参数', 'CURRENT', 'el-icon-connection', 5, 1, '电流相关参数'),
(6, '状态参数', 'STATUS', 'el-icon-info', 6, 1, '设备状态相关参数'),
(7, '流量参数', 'FLOW', 'el-icon-sort', 7, 1, '流量相关参数'),
(8, '转速参数', 'SPEED', 'el-icon-refresh', 8, 1, '转速相关参数'),
(99, '其他参数', 'OTHER', 'el-icon-more', 99, 1, '其他类型参数');

-- 5. 创建设备参数模板表（可选，用于快速配置常用设备参数）
CREATE TABLE IF NOT EXISTS `device_param_template` (
  `template_id` bigint NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `device_type` int NOT NULL COMMENT '适用设备类型',
  `param_config` json COMMENT '参数配置JSON',
  `description` varchar(255) DEFAULT NULL COMMENT '模板描述',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`template_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备参数配置模板表';

-- 6. 添加索引优化查询性能
ALTER TABLE device_param ADD INDEX IF NOT EXISTS `idx_device_id` (`device_id`);
ALTER TABLE device_param ADD INDEX IF NOT EXISTS `idx_mqtt_property_name` (`mqtt_property_name`);
ALTER TABLE device_param ADD INDEX IF NOT EXISTS `idx_category_id` (`category_id`);
ALTER TABLE device_param ADD INDEX IF NOT EXISTS `idx_is_alert_param` (`is_alert_param`);

-- 7. 更新现有数据，设置默认值
UPDATE device_param SET 
  `is_alert_param` = CASE 
    WHEN `storage_type` IN (2, 3) THEN 1  -- 原来的线圈和离散存储设为告警参数
    ELSE 0 
  END,
  `mqtt_property_name` = CONCAT('param_', `param_id`),  -- 临时设置，需要根据实际情况修改
  `param_description` = `param_name`
WHERE `mqtt_property_name` IS NULL;

-- 8. 验证数据完整性
SELECT 
  COUNT(*) as total_params,
  COUNT(CASE WHEN mqtt_property_name IS NOT NULL THEN 1 END) as with_mqtt_name,
  COUNT(CASE WHEN is_alert_param = 1 THEN 1 END) as alert_params,
  COUNT(CASE WHEN is_alert_param = 0 THEN 1 END) as normal_params
FROM device_param 
WHERE del_flag = 0;

-- 9. 显示修改后的表结构
DESCRIBE device_param;

-- ========================================
-- 执行完成后的注意事项：
-- 1. 需要手动更新mqtt_property_name字段为实际的MQTT属性名称
-- 2. 根据实际需求调整参数分类
-- 3. 更新相关的Java实体类和服务代码
-- 4. 测试MQTT参数采集功能
-- ========================================
