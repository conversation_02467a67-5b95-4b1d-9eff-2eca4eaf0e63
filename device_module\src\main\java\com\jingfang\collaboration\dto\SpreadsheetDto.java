package com.jingfang.collaboration.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 表格数据传输对象
 */
@Data
public class SpreadsheetDto {
    
    /**
     * 表格ID
     */
    private String id;
    
    /**
     * 表格标题
     */
    @NotBlank(message = "表格标题不能为空")
    @Size(max = 100, message = "表格标题长度不能超过100个字符")
    private String title;
    
    /**
     * 表格描述
     */
    @Size(max = 500, message = "表格描述长度不能超过500个字符")
    private String description;
    
    /**
     * 表格数据（JSON格式）
     */
    private String data;
    
    /**
     * 状态（0正常 1停用）
     */
    private String status;
    
    /**
     * 是否公开（0私有 1公开）
     */
    private String isPublic;
    
    /**
     * 分享密码
     */
    private String sharePassword;
    
    /**
     * 分享过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date shareExpireTime;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 协作者列表
     */
    private List<CollaboratorDto> collaborators;
}
