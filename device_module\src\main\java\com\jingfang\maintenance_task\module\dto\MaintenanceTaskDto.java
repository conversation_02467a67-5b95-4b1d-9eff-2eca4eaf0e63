package com.jingfang.maintenance_task.module.dto;

import com.jingfang.maintenance_task.module.entity.MaintenanceTask;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 维护任务DTO
 */
@Data
public class MaintenanceTaskDto {
    
    /**
     * 维护任务ID
     */
    private String taskId;
    
    /**
     * 维护计划ID
     */
    private String planId;
    
    /**
     * 任务标题
     */
    @NotBlank(message = "任务标题不能为空")
    private String taskTitle;
    
    /**
     * 资产ID
     */
    private String assetId;
    
    /**
     * 资产名称（用于显示）
     */
    private String assetName;
    
    /**
     * 维护事项描述
     */
    private String maintenanceItems;
    
    /**
     * 计划执行时间
     */
    private Date scheduledTime;
    
    /**
     * 实际开始时间
     */
    private Date actualStartTime;
    
    /**
     * 实际完成时间
     */
    private Date actualEndTime;
    
    /**
     * 负责人类型(1-个人, 2-部门)
     */
    private Integer responsibleType;
    
    /**
     * 负责人ID（用户ID或部门ID）
     */
    private Long responsibleId;
    
    /**
     * 负责人名称（用于显示）
     */
    private String responsibleName;
    
    /**
     * 实际执行人ID
     */
    private Long executorId;
    
    /**
     * 执行人名称（用于显示）
     */
    private String executorName;
    
    /**
     * 任务状态(1-待执行, 2-执行中, 3-草稿, 4-待审核, 5-审核通过, 6-审核不通过, 7-已完成, 8-已取消)
     */
    private Integer status;
    
    /**
     * 任务优先级(1-低, 2-中, 3-高, 4-紧急)
     */
    private Integer priority;
    
    /**
     * 检查结果
     */
    private String checkResult;
    
    /**
     * 维护结果描述
     */
    private String resultDescription;
    
    /**
     * 问题描述
     */
    private String problemDescription;
    
    /**
     * 解决方案
     */
    private String solution;
    
    /**
     * 审核意见
     */
    private String reviewComment;
    
    /**
     * 委派原因
     */
    private String delegateReason;
    
    /**
     * 备注说明
     */
    private String remark;
    
    /**
     * 备品备件使用记录列表
     */
    private List<MaintenanceTaskPartDto> partList;
    
    /**
     * 维护任务备品备件使用记录DTO
     */
    @Data
    public static class MaintenanceTaskPartDto {
        
        /**
         * 记录ID
         */
        private String recordId;
        
        /**
         * 备品备件ID
         */
        @NotBlank(message = "备品备件ID不能为空")
        private String partId;
        
        /**
         * 备品备件名称（用于显示）
         */
        private String partName;
        
        /**
         * 规格型号
         */
        private String specModel;
        
        /**
         * 单位
         */
        private String unit;
        
        /**
         * 计划使用数量
         */
        private BigDecimal plannedQuantity;
        
        /**
         * 实际使用数量
         */
        private BigDecimal actualQuantity;
        
        /**
         * 当前库存数量
         */
        private BigDecimal currentStock;
        
        /**
         * 使用状态(1-计划使用, 2-已使用, 3-未使用)
         */
        private Integer useStatus;
        
        /**
         * 备注说明
         */
        private String remark;
    }
    
    /**
     * 转换为实体类
     */
    public MaintenanceTask toEntity() {
        MaintenanceTask task = new MaintenanceTask();
        task.setTaskId(this.taskId);
        task.setPlanId(this.planId);
        task.setTaskTitle(this.taskTitle);
        task.setAssetId(this.assetId);
        task.setMaintenanceItems(this.maintenanceItems);
        task.setScheduledTime(this.scheduledTime);
        task.setActualStartTime(this.actualStartTime);
        task.setActualEndTime(this.actualEndTime);
        task.setResponsibleType(this.responsibleType);
        task.setResponsibleId(this.responsibleId);
        task.setExecutorId(this.executorId);
        task.setStatus(this.status);
        task.setPriority(this.priority);
        task.setCheckResult(this.checkResult);
        task.setResultDescription(this.resultDescription);
        task.setProblemDescription(this.problemDescription);
        task.setSolution(this.solution);
        task.setReviewComment(this.reviewComment);
        task.setDelegateReason(this.delegateReason);
        task.setRemark(this.remark);
        return task;
    }
} 