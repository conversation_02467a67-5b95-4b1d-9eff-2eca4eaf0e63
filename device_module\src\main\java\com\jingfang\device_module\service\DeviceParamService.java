package com.jingfang.device_module.service;

import com.jingfang.device_module.module.dto.DeviceAddDto;
import com.jingfang.device_module.module.dto.NormalParamAddDto;
import com.jingfang.device_module.module.entity.DeviceParam;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jingfang.device_module.module.vo.DeviceParamVo;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【device_param】的数据库操作Service
* @createDate 2025-03-25 13:58:50
*/
public interface DeviceParamService extends IService<DeviceParam> {

    boolean addDeviceParamTypeOne(NormalParamAddDto dto);

    List<DeviceParamVo> showDeviceParamTypeOne(Long deviceId);

    boolean addDeviceParamTypeTwo(DeviceAddDto dto);

    List<DeviceParamVo> showDeviceParamTypeTwo(Long deviceId);

    /**
     * 获取设备告警信息（新接口）
     * @param deviceId 设备ID
     * @return 设备告警信息列表
     */
    List<DeviceParamVo> getDeviceAlertInfo(Long deviceId);

    /**
     * 获取设备控制参数
     * @param deviceId 设备ID
     * @return 设备控制参数列表
     */
    List<DeviceParamVo> getDeviceControlParams(Long deviceId);

    /**
     * 写入设备参数
     * @param deviceId 设备ID
     * @param paramName 参数名称
     * @param paramValue 参数值
     * @return 写入结果
     */
    boolean writeDeviceParam(Long deviceId, String paramName, String paramValue);

    /**
     * 批量写入设备参数
     * @param deviceId 设备ID
     * @param paramMap 参数映射（参数名 -> 参数值）
     * @return 写入结果
     */
    boolean writeDeviceParams(Long deviceId, Map<String, String> paramMap);

    void modbusConnectTest();
}
