<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.asset_part_relation.mapper.AssetPartRelationMapper">

    <!-- 查询资产关联的备品备件列表 -->
    <select id="selectAssetPartRelationList" resultType="com.jingfang.asset_part_relation.module.vo.AssetPartRelationVo">
        SELECT 
            t1.relation_id,
            t1.asset_id,
            t2.asset_name,
            t1.part_id,
            t3.item_name as part_name,
            t3.item_code as part_code,
            t3.spec_model,
            t3.unit,
            t1.relation_type,
            CASE t1.relation_type 
                WHEN 1 THEN '必需' 
                WHEN 2 THEN '推荐' 
                WHEN 3 THEN '可选' 
                ELSE '未知' 
            END as relation_type_name,
            t1.suggested_quantity,
            COALESCE(t4.total_quantity, 0) as current_quantity,
            COALESCE(t4.min_stock_status, 1) as stock_status,
            CASE COALESCE(t4.min_stock_status, 1) 
                WHEN 1 THEN '正常' 
                WHEN 2 THEN '不足' 
                WHEN 3 THEN '过剩' 
                ELSE '未知' 
            END as stock_status_name,
            t5.part_category,
            CASE t5.part_category 
                WHEN 1 THEN '关键' 
                WHEN 2 THEN '常用' 
                WHEN 3 THEN '次要' 
                ELSE '未知' 
            END as part_category_name,
            t5.replacement_cycle,
            t1.remark,
            t1.create_time
        FROM 
            asset_part_relation t1
            LEFT JOIN asset_ledger t2 ON t1.asset_id = t2.asset_id AND t2.del_flag = 0
            LEFT JOIN item_base_info t3 ON t1.part_id = t3.item_id AND t3.deleted = 0 AND t3.item_type = 2
            LEFT JOIN (
                SELECT 
                    item_id,
                    SUM(current_quantity) as total_quantity,
                    MAX(stock_status) as min_stock_status
                FROM item_inventory 
                GROUP BY item_id
            ) t4 ON t1.part_id = t4.item_id
            LEFT JOIN item_part_attr t5 ON t1.part_id = t5.item_id
        WHERE 
            t1.deleted = 0
        <if test="request.assetId != null and request.assetId != ''">
            AND t1.asset_id = #{request.assetId}
        </if>
        <if test="request.assetName != null and request.assetName != ''">
            AND t2.asset_name LIKE CONCAT('%', #{request.assetName}, '%')
        </if>
        <if test="request.partId != null and request.partId != ''">
            AND t1.part_id = #{request.partId}
        </if>
        <if test="request.partName != null and request.partName != ''">
            AND t3.item_name LIKE CONCAT('%', #{request.partName}, '%')
        </if>
        <if test="request.partCode != null and request.partCode != ''">
            AND t3.item_code LIKE CONCAT('%', #{request.partCode}, '%')
        </if>
        <if test="request.relationType != null">
            AND t1.relation_type = #{request.relationType}
        </if>
        <if test="request.partCategory != null">
            AND t5.part_category = #{request.partCategory}
        </if>
        <if test="request.stockStatus != null">
            AND t4.min_stock_status = #{request.stockStatus}
        </if>
        ORDER BY t1.create_time DESC
    </select>

    <!-- 根据资产ID查询关联的备品备件列表 -->
    <select id="selectPartsByAssetId" resultType="com.jingfang.asset_part_relation.module.vo.AssetPartRelationVo">
        SELECT 
            t1.relation_id,
            t1.asset_id,
            t1.part_id,
            t3.item_name as part_name,
            t3.item_code as part_code,
            t3.spec_model,
            t3.unit,
            t1.relation_type,
            CASE t1.relation_type 
                WHEN 1 THEN '必需' 
                WHEN 2 THEN '推荐' 
                WHEN 3 THEN '可选' 
                ELSE '未知' 
            END as relation_type_name,
            t1.suggested_quantity,
            COALESCE(t4.total_quantity, 0) as current_quantity,
            COALESCE(t4.min_stock_status, 1) as stock_status,
            CASE COALESCE(t4.min_stock_status, 1) 
                WHEN 1 THEN '正常' 
                WHEN 2 THEN '不足' 
                WHEN 3 THEN '过剩' 
                ELSE '未知' 
            END as stock_status_name,
            t5.part_category,
            CASE t5.part_category 
                WHEN 1 THEN '关键' 
                WHEN 2 THEN '常用' 
                WHEN 3 THEN '次要' 
                ELSE '未知' 
            END as part_category_name,
            t5.replacement_cycle,
            t1.remark,
            t1.create_time
        FROM 
            asset_part_relation t1
            LEFT JOIN item_base_info t3 ON t1.part_id = t3.item_id AND t3.deleted = 0 AND t3.item_type = 2
            LEFT JOIN (
                SELECT 
                    item_id,
                    SUM(current_quantity) as total_quantity,
                    MAX(stock_status) as min_stock_status
                FROM item_inventory 
                GROUP BY item_id
            ) t4 ON t1.part_id = t4.item_id
            LEFT JOIN item_part_attr t5 ON t1.part_id = t5.item_id
        WHERE 
            t1.deleted = 0
            AND t1.asset_id = #{assetId}
        ORDER BY t1.relation_type ASC, t1.create_time DESC
    </select>

    <!-- 根据备品备件ID查询关联的资产列表 -->
    <select id="selectAssetsByPartId" resultType="com.jingfang.asset_part_relation.module.vo.AssetPartRelationVo">
        SELECT 
            t1.relation_id,
            t1.asset_id,
            t2.asset_name,
            t1.part_id,
            t1.relation_type,
            CASE t1.relation_type 
                WHEN 1 THEN '必需' 
                WHEN 2 THEN '推荐' 
                WHEN 3 THEN '可选' 
                ELSE '未知' 
            END as relation_type_name,
            t1.suggested_quantity,
            t1.remark,
            t1.create_time
        FROM 
            asset_part_relation t1
            LEFT JOIN asset_ledger t2 ON t1.asset_id = t2.asset_id AND t2.del_flag = 0
        WHERE 
            t1.deleted = 0
            AND t1.part_id = #{partId}
        ORDER BY t1.relation_type ASC, t1.create_time DESC
    </select>

    <!-- 检查资产和备品备件是否已关联 -->
    <select id="checkRelationExists" resultType="int">
        SELECT COUNT(1)
        FROM asset_part_relation
        WHERE deleted = 0
          AND asset_id = #{assetId}
          AND part_id = #{partId}
    </select>

    <!-- 批量删除资产的所有备品备件关联 -->
    <update id="deleteByAssetId">
        UPDATE asset_part_relation 
        SET deleted = 1, 
            update_time = NOW(), 
            update_by = #{updateBy}
        WHERE deleted = 0 
          AND asset_id = #{assetId}
    </update>

    <!-- 批量删除备品备件的所有资产关联 -->
    <update id="deleteByPartId">
        UPDATE asset_part_relation 
        SET deleted = 1, 
            update_time = NOW(), 
            update_by = #{updateBy}
        WHERE deleted = 0 
          AND part_id = #{partId}
    </update>

</mapper> 