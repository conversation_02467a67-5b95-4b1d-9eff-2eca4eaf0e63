package com.jingfang.asset_disposal.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 资产处置审批记录表
 * @TableName asset_disposal_approval
 */
@TableName(value = "asset_disposal_approval")
@Data
public class AssetDisposalApproval implements Serializable {
    
    /**
     * 审批记录ID
     */
    @TableId(type = IdType.INPUT)
    private String approvalId;
    
    /**
     * 处置单ID
     */
    private String disposalId;
    
    /**
     * 审批层级
     */
    private Integer approvalLevel;
    
    /**
     * 审批人ID
     */
    private Long approverId;
    
    /**
     * 审批状态(1-待审批, 2-通过, 3-拒绝)
     */
    private Integer approvalStatus;
    
    /**
     * 审批时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date approvalTime;
    
    /**
     * 审批意见
     */
    private String approvalComment;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
} 