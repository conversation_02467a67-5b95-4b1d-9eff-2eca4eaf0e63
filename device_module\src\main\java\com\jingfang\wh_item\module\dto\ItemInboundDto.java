package com.jingfang.wh_item.module.dto;

import com.jingfang.wh_item.module.entity.ItemInbound;
import com.jingfang.wh_item.module.entity.ItemInboundDetail;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

@Data
public class ItemInboundDto {
    /**
     * 入库单主信息
     */
    private ItemInbound main;

    /**
     * 入库明细列表
     */
    @NotNull(message = "入库明细不能为空")
    @Size(min = 1, message = "至少需要一条入库明细")
    private List<ItemInboundDetail> details;
}