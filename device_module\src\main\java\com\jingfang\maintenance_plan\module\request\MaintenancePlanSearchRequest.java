package com.jingfang.maintenance_plan.module.request;

import lombok.Data;

import java.util.Date;

/**
 * 维护计划查询请求
 */
@Data
public class MaintenancePlanSearchRequest {
    
    /**
     * 当前页码
     */
    private Integer pageNum = 1;
    
    /**
     * 每页大小
     */
    private Integer pageSize = 10;
    
    /**
     * 计划名称（模糊查询）
     */
    private String planName;
    
    /**
     * 资产ID
     */
    private String assetId;
    
    /**
     * 资产名称（模糊查询）
     */
    private String assetName;
    
    /**
     * 维护周期类型(1-按天, 2-按周, 3-按月, 4-按年)
     */
    private Integer cycleType;
    
    /**
     * 负责人类型(1-个人, 2-部门)
     */
    private Integer responsibleType;
    
    /**
     * 负责人ID
     */
    private Long responsibleId;
    
    /**
     * 计划状态(1-启用, 0-停用)
     */
    private Integer status;
    
    /**
     * 下次维护时间开始
     */
    private Date nextMaintenanceTimeStart;
    
    /**
     * 下次维护时间结束
     */
    private Date nextMaintenanceTimeEnd;
    
    /**
     * 创建时间开始
     */
    private Date createTimeStart;
    
    /**
     * 创建时间结束
     */
    private Date createTimeEnd;
} 