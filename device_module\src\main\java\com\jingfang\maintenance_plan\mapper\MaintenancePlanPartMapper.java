package com.jingfang.maintenance_plan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jingfang.maintenance_plan.module.entity.MaintenancePlanPart;
import com.jingfang.maintenance_plan.module.vo.MaintenancePlanVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 维护计划备品备件关联Mapper接口
 */
@Mapper
public interface MaintenancePlanPartMapper extends BaseMapper<MaintenancePlanPart> {
    
    /**
     * 根据维护计划ID查询关联的备品备件
     * 
     * @param planId 维护计划ID
     * @return 备品备件列表
     */
    List<MaintenancePlanVo.MaintenancePlanPartVo> selectPartsByPlanId(@Param("planId") String planId);
    
    /**
     * 根据备品备件ID查询关联的维护计划
     * 
     * @param partId 备品备件ID
     * @return 维护计划列表
     */
    List<String> selectPlanIdsByPartId(@Param("partId") String partId);
    
    /**
     * 批量删除维护计划的备品备件关联
     * 
     * @param planId 维护计划ID
     * @param updateBy 更新人
     * @return 影响行数
     */
    int deleteByPlanId(@Param("planId") String planId, @Param("updateBy") String updateBy);
    
    /**
     * 检查维护计划和备品备件是否已关联
     * 
     * @param planId 维护计划ID
     * @param partId 备品备件ID
     * @return 是否存在关联
     */
    boolean checkRelationExists(@Param("planId") String planId, @Param("partId") String partId);
} 