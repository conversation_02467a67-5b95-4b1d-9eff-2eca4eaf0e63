package com.jingfang.asset_inbound.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 资产入库明细表
 * @TableName asset_inbound_detail
 */
@TableName(value ="asset_inbound_detail")
@Data
public class AssetInboundDetail implements Serializable {
    /**
     * 明细ID
     */
    @TableId(type = IdType.AUTO)
    private Long detailId;

    /**
     * 入库单ID
     */
    private String inboundId;

    /**
     * 资产编号
     */
    private String assetId;

    /**
     * 资产名称
     */
    private String assetName;

    /**
     * 资产分类ID
     */
    private Integer categoryId;

    /**
     * 资产状态
     */
    private Integer assetStatus;

    /**
     * 规格型号
     */
    private String specModel;

    /**
     * 品牌信息
     */
    private String assetBrand;

    /**
     * 资产用途
     */
    private String assetPurpose;

    /**
     * 计量单位
     */
    private String assetUnit;

    /**
     * 存放位置
     */
    private Integer storageLocation;

    /**
     * 详细地址
     */
    private String detailLocation;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 维保厂商
     */
    private String maintainVendor;

    /**
     * 维保开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 维保到期日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 维保状态
     */
    private Integer maintainStatus;

    /**
     * 联系方式
     */
    private String contactNumber;

    /**
     * 负责人ID
     */
    private Long managerId;

    /**
     * 维保方式
     */
    private Integer maintainMethod;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}