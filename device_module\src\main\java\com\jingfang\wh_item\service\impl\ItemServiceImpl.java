package com.jingfang.wh_item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jingfang.asset_part_relation.service.AssetPartRelationService;
import com.jingfang.common.utils.SecurityUtils;
import com.jingfang.common.utils.uuid.UUID;
import com.jingfang.wh_item.mapper.ItemBaseInfoMapper;
import com.jingfang.wh_item.mapper.ItemConsumableAttrMapper;
import com.jingfang.wh_item.mapper.ItemInventoryMapper;
import com.jingfang.wh_item.mapper.ItemPartAttrMapper;
import com.jingfang.wh_item.module.dto.ItemDto;
import com.jingfang.wh_item.module.dto.ItemRegistrationDto;
import com.jingfang.wh_item.module.dto.ItemStockAdjustmentDto;
import com.jingfang.wh_item.module.entity.ItemBaseInfo;
import com.jingfang.wh_item.module.entity.ItemConsumableAttr;
import com.jingfang.wh_item.module.entity.ItemInventory;
import com.jingfang.wh_item.module.entity.ItemPartAttr;
import com.jingfang.wh_item.module.request.ItemSearchRequest;
import com.jingfang.wh_item.module.request.ItemStockStatisticsRequest;
import com.jingfang.wh_item.module.request.ItemStockViewRequest;
import com.jingfang.wh_item.module.vo.ItemDetailVo;
import com.jingfang.wh_item.module.vo.ItemInventoryVo;
import com.jingfang.wh_item.module.vo.ItemStockStatisticsVo;
import com.jingfang.wh_item.module.vo.ItemStockViewVo;
import com.jingfang.wh_item.module.vo.ItemVo;
import com.jingfang.wh_item.service.ItemService;
import com.jingfang.wh_item.service.ItemStockSyncService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 物品管理Service实现
 */
@Slf4j
@Service
public class ItemServiceImpl implements ItemService {

    @Resource
    private ItemBaseInfoMapper itemBaseInfoMapper;
    
    @Resource
    private ItemConsumableAttrMapper consumableAttrMapper;
    
    @Resource
    private ItemPartAttrMapper partAttrMapper;
    
    @Resource
    private ItemInventoryMapper inventoryMapper;

    @Resource
    private AssetPartRelationService assetPartRelationService;

    @Resource
    private ItemStockSyncService itemStockSyncService;

    /**
     * 新增物品（包含库存信息，实际为物品单体入库）
     *
     * @param itemDto 物品信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addItem(ItemDto itemDto) {
        try {
            String itemId = UUID.fastUUID().toString(true);
            Date now = new Date();
            String username = SecurityUtils.getUsername();
            
            // 处理基本信息
            ItemBaseInfo baseInfo = itemDto.getBaseInfo();
            baseInfo.setItemId(itemId);
            baseInfo.setCreateTime(now);
            baseInfo.setCreateBy(username);
            baseInfo.setDeleted(0);
            itemBaseInfoMapper.insert(baseInfo);
            
            // 处理库存信息
            ItemInventory inventory = itemDto.getInventory();
            inventory.setInventoryId(UUID.fastUUID().toString(true));
            inventory.setItemId(itemId);
            inventory.setCreateTime(now);
            inventory.setCreateBy(username);
            
            // 根据安全库存和当前库存设置库存状态
            if (inventory.getSafetyStock() != null) {
                if (inventory.getCurrentQuantity().compareTo(inventory.getSafetyStock()) < 0) {
                    inventory.setStockStatus(2); // 不足
                } else {
                    inventory.setStockStatus(1); // 正常
                }
            } else {
                inventory.setStockStatus(1); // 正常
            }
            
            inventoryMapper.insert(inventory);
            
            // 根据物品类型处理特有属性
            if (baseInfo.getItemType() == 1) { // 消耗品
                ItemConsumableAttr attr = itemDto.getConsumableAttr();
                if (attr != null) {
                    attr.setAttrId(UUID.fastUUID().toString(true));
                    attr.setItemId(itemId);
                    attr.setCreateTime(now);
                    attr.setCreateBy(username);
                    consumableAttrMapper.insert(attr);
                }
            } else if (baseInfo.getItemType() == 2) { // 备品备件
                ItemPartAttr attr = itemDto.getPartAttr();
                if (attr != null) {
                    attr.setAttrId(UUID.fastUUID().toString(true));
                    attr.setItemId(itemId);
                    attr.setCreateTime(now);
                    attr.setCreateBy(username);
                    partAttrMapper.insert(attr);
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("新增物品失败", e);
            throw e;
        }
    }

    /**
     * 物品登记（只登记基本信息和属性，不包含库存信息）
     *
     * @param registrationDto 物品登记信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean registerItem(ItemRegistrationDto registrationDto) {
        try {
            String itemId = UUID.fastUUID().toString(true);
            Date now = new Date();
            String username = SecurityUtils.getUsername();
            
            // 处理基本信息
            ItemBaseInfo baseInfo = registrationDto.getBaseInfo();
            baseInfo.setItemId(itemId);
            baseInfo.setCreateTime(now);
            baseInfo.setCreateBy(username);
            baseInfo.setDeleted(0);
            // 物品登记时，总库存设为0，安全库存从DTO中获取
            baseInfo.setTotalStock(BigDecimal.ZERO);
            itemBaseInfoMapper.insert(baseInfo);
            
            // 根据物品类型处理特有属性
            if (baseInfo.getItemType() == 1) { // 消耗品
                ItemConsumableAttr attr = registrationDto.getConsumableAttr();
                if (attr != null) {
                    attr.setAttrId(UUID.fastUUID().toString(true));
                    attr.setItemId(itemId);
                    attr.setCreateTime(now);
                    attr.setCreateBy(username);
                    consumableAttrMapper.insert(attr);
                }
            } else if (baseInfo.getItemType() == 2) { // 备品备件
                ItemPartAttr attr = registrationDto.getPartAttr();
                if (attr != null) {
                    attr.setAttrId(UUID.fastUUID().toString(true));
                    attr.setItemId(itemId);
                    attr.setCreateTime(now);
                    attr.setCreateBy(username);
                    partAttrMapper.insert(attr);
                }
            }
            
            log.info("物品登记成功，itemId={}，物品名称={}，物品类型={}，企业级安全库存={}", 
                    itemId, baseInfo.getItemName(), baseInfo.getItemType(), baseInfo.getSafetyStock());
            
            return true;
        } catch (Exception e) {
            log.error("物品登记失败", e);
            throw e;
        }
    }

    /**
     * 修改物品
     *
     * @param itemDto 物品信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateItem(ItemDto itemDto) {
        try {
            Date now = new Date();
            String username = SecurityUtils.getUsername();
            String itemId = itemDto.getBaseInfo().getItemId();
            
            // 更新基本信息
            ItemBaseInfo baseInfo = itemDto.getBaseInfo();
            baseInfo.setUpdateTime(now);
            baseInfo.setUpdateBy(username);
            itemBaseInfoMapper.updateById(baseInfo);
            
            // 更新库存信息
            ItemInventory inventory = itemDto.getInventory();
            inventory.setUpdateTime(now);
            inventory.setUpdateBy(username);
            
            // 根据安全库存和当前库存设置库存状态
            if (inventory.getSafetyStock() != null) {
                if (inventory.getCurrentQuantity().compareTo(inventory.getSafetyStock()) < 0) {
                    inventory.setStockStatus(2); // 不足
                } else {
                    inventory.setStockStatus(1); // 正常
                }
            } else {
                inventory.setStockStatus(1); // 正常
            }
            
            LambdaQueryWrapper<ItemInventory> inventoryWrapper = new LambdaQueryWrapper<>();
            inventoryWrapper.eq(ItemInventory::getItemId, itemId);
            inventoryMapper.update(inventory, inventoryWrapper);
            
            // 更新特有属性
            if (baseInfo.getItemType() == 1) { // 消耗品
                ItemConsumableAttr attr = itemDto.getConsumableAttr();
                if (attr != null) {
                    attr.setUpdateTime(now);
                    attr.setUpdateBy(username);
                    
                    LambdaQueryWrapper<ItemConsumableAttr> attrWrapper = new LambdaQueryWrapper<>();
                    attrWrapper.eq(ItemConsumableAttr::getItemId, itemId);
                    consumableAttrMapper.update(attr, attrWrapper);
                }
            } else if (baseInfo.getItemType() == 2) { // 备品备件
                ItemPartAttr attr = itemDto.getPartAttr();
                if (attr != null) {
                    attr.setUpdateTime(now);
                    attr.setUpdateBy(username);
                    
                    LambdaQueryWrapper<ItemPartAttr> attrWrapper = new LambdaQueryWrapper<>();
                    attrWrapper.eq(ItemPartAttr::getItemId, itemId);
                    partAttrMapper.update(attr, attrWrapper);
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("修改物品失败", e);
            throw e;
        }
    }

    /**
     * 删除物品
     *
     * @param itemId 物品ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteItem(String itemId) {
        try {
            // 逻辑删除
            ItemBaseInfo baseInfo = new ItemBaseInfo();
            baseInfo.setItemId(itemId);
            baseInfo.setDeleted(1);
            baseInfo.setUpdateTime(new Date());
            baseInfo.setUpdateBy(SecurityUtils.getUsername());
            itemBaseInfoMapper.updateById(baseInfo);
            return true;
        } catch (Exception e) {
            log.error("删除物品失败", e);
            throw e;
        }
    }

    /**
     * 批量删除物品
     *
     * @param itemIds 需要删除的物品ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteItems(List<String> itemIds) {
        try {
            if (itemIds != null && !itemIds.isEmpty()) {
                for (String itemId : itemIds) {
                    deleteItem(itemId);
                }
            }
            return true;
        } catch (Exception e) {
            log.error("批量删除物品失败", e);
            throw e;
        }
    }

    /**
     * 查询物品列表
     *
     * @param request 查询条件
     * @return 物品列表
     */
    @Override
    public IPage<ItemVo> selectItemList(ItemSearchRequest request) {
        Page<ItemVo> page = new Page<>(request.getPageNum(), request.getPageSize());
        IPage<ItemVo> result = itemBaseInfoMapper.selectItemList(page, request);
        
        // 为每个物品填充仓库ID列表
        if (result.getRecords() != null && !result.getRecords().isEmpty()) {
            for (ItemVo item : result.getRecords()) {
                List<Integer> warehouseIds = itemBaseInfoMapper.selectWarehouseIdsByItemId(item.getItemId());
                item.setWarehouseIds(warehouseIds);
            }
        }
        
        return result;
    }

    /**
     * 查询物品详情
     *
     * @param itemId 物品ID
     * @return 物品详情
     */
    @Override
    public ItemDetailVo getItemDetail(String itemId) {
        // 1. 查询物品基本信息（包含总库存和企业级安全库存）
        ItemDetailVo detail = itemBaseInfoMapper.selectItemBaseInfoById(itemId);
        if (detail == null) {
            return null;
        }
        
        // 2. 查询库存信息列表（各仓库的详细库存）
        List<ItemInventoryVo> inventoryList = itemBaseInfoMapper.selectItemInventoryList(itemId);
        detail.setInventoryList(inventoryList);
        
        // 3. 如果是备品备件类型，查询关联的资产列表
        if (detail.getItemType() != null && detail.getItemType() == 2) {
            try {
                detail.setRelatedAssets(assetPartRelationService.selectAssetsByPartId(itemId));
            } catch (Exception e) {
                log.warn("查询备品备件{}关联的资产失败: {}", itemId, e.getMessage());
                detail.setRelatedAssets(new ArrayList<>());
            }
        }
        
        return detail;
    }

    /**
     * 更新物品库存
     *
     * @param itemId 物品ID
     * @param warehouseId 仓库ID
     * @param quantity 变动数量（正数增加，负数减少）
     * @param operationType 操作类型（1-入库，2-出库，3-盘点）
     * @param operationId 关联单据ID
     * @param remark 备注
     * @param shelfLocation 货架位置
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateItemStock(String itemId, Integer warehouseId, int quantity, int operationType, String operationId, String remark, String shelfLocation) {
        try {
            // 参数校验
            if (warehouseId == null) {
                log.error("仓库ID不能为空，itemId={}", itemId);
                return false;
            }
            
            // 查询指定仓库的库存记录
            LambdaQueryWrapper<ItemInventory> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ItemInventory::getItemId, itemId);
            wrapper.eq(ItemInventory::getWarehouseId, warehouseId);
            ItemInventory inventory = inventoryMapper.selectOne(wrapper);
            
            if (inventory == null) {
                // 如果指定仓库的库存记录不存在，则创建新的库存记录
                log.info("物品在仓库[{}]的库存记录不存在，创建新的库存记录，itemId={}", warehouseId, itemId);
                
                // 首先验证物品基本信息是否存在
                ItemBaseInfo baseInfo = itemBaseInfoMapper.selectById(itemId);
                if (baseInfo == null) {
                    log.error("物品基本信息不存在，无法创建库存记录，itemId={}", itemId);
                    return false;
                }
                
                // 创建新的库存记录
                inventory = new ItemInventory();
                inventory.setInventoryId(UUID.fastUUID().toString(true));
                inventory.setItemId(itemId);
                inventory.setWarehouseId(warehouseId);
                inventory.setCurrentQuantity(BigDecimal.ZERO); // 初始库存为0
                inventory.setSafetyStock(BigDecimal.ZERO); // 默认安全库存为0
                inventory.setStockStatus(1); // 默认状态为正常
                inventory.setShelfLocation(shelfLocation != null ? shelfLocation : ""); // 设置货架位置
                inventory.setCreateTime(new Date());
                inventory.setCreateBy(SecurityUtils.getUsername());
                inventory.setUpdateTime(new Date());
                inventory.setUpdateBy(SecurityUtils.getUsername());
                
                // 插入新的库存记录
                inventoryMapper.insert(inventory);
                log.info("成功创建物品在仓库[{}]的库存记录，itemId={}，货架位置={}", warehouseId, itemId, shelfLocation);
            }
            
            // 记录操作前库存
            BigDecimal beforeQuantity = inventory.getCurrentQuantity();
            
            // 更新库存
            BigDecimal changeQuantity = new BigDecimal(quantity);
            BigDecimal afterQuantity = beforeQuantity.add(changeQuantity);
            
            // 库存不能为负数（出库时需要检查）
            if (afterQuantity.compareTo(BigDecimal.ZERO) < 0) {
                log.error("仓库[{}]库存不足，无法操作，itemId={}，当前库存={}，变动数量={}", 
                        warehouseId, itemId, beforeQuantity, changeQuantity);
                return false;
            }
            
            // 更新库存状态
            int stockStatus = 1; // 默认正常
            if (inventory.getSafetyStock() != null && inventory.getSafetyStock().compareTo(BigDecimal.ZERO) > 0) {
                if (afterQuantity.compareTo(inventory.getSafetyStock()) < 0) {
                    stockStatus = 2; // 库存不足
                }
            }
            
            // 更新库存信息
            inventory.setCurrentQuantity(afterQuantity);
            inventory.setStockStatus(stockStatus);
            // 如果提供了新的货架位置，则更新货架位置
            if (shelfLocation != null && !shelfLocation.trim().isEmpty()) {
                inventory.setShelfLocation(shelfLocation);
            }
            inventory.setUpdateTime(new Date());
            inventory.setUpdateBy(SecurityUtils.getUsername());
            inventoryMapper.updateById(inventory);
            
            log.info("库存更新成功，itemId={}，仓库ID={}，操作前库存={}，变动数量={}，操作后库存={}，货架位置={}", 
                    itemId, warehouseId, beforeQuantity, changeQuantity, afterQuantity, inventory.getShelfLocation());
            
            // 记录库存操作日志（此处可以调用库存操作日志的服务）
            // TODO: 记录库存操作日志
            
            // 同步更新物品总库存
            try {
                itemStockSyncService.syncTotalStock(itemId);
                log.info("同步物品总库存成功，itemId={}", itemId);
            } catch (Exception e) {
                log.error("同步物品总库存失败，itemId={}", itemId, e);
                // 不抛出异常，避免影响主要的库存更新流程
            }
            
            return true;
        } catch (Exception e) {
            log.error("更新物品库存失败", e);
            throw e;
        }
    }
    
    /**
     * 检查物品是否存在
     *
     * @param itemId 物品ID
     * @return 是否存在
     */
    @Override
    public boolean existsById(String itemId) {
        try {
            LambdaQueryWrapper<ItemBaseInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ItemBaseInfo::getItemId, itemId);
            wrapper.eq(ItemBaseInfo::getDeleted, 0); // 只查询未删除的物品
            Long count = itemBaseInfoMapper.selectCount(wrapper);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("检查物品是否存在失败，itemId={}", itemId, e);
            return false;
        }
    }
    
    /**
     * 物品库存统计查询
     *
     * @param request 查询条件
     * @return 库存统计列表
     */
    @Override
    public IPage<ItemStockStatisticsVo> selectItemStockStatistics(ItemStockStatisticsRequest request) {
        try {
            // 设置默认统计类型
            if (request.getStatisticsType() == null) {
                request.setStatisticsType(1); // 默认按仓库统计
            }
            
            // 直接查询全部数据，不使用分页
            List<ItemStockStatisticsVo> records = itemBaseInfoMapper.selectItemStockStatisticsWithoutPage(request);
            
            // 为每条记录设置仓库名称（通过数据字典获取）
            if (records != null && !records.isEmpty()) {
                for (ItemStockStatisticsVo item : records) {
                    if (item.getWarehouseId() != null) {
                        item.setWarehouseName("仓库" + item.getWarehouseId());
                    }
                }
            }
            
            // 手动构造分页结果
            Page<ItemStockStatisticsVo> page = new Page<>();
            page.setRecords(records);
            page.setTotal(records.size());
            page.setSize(records.size());
            page.setCurrent(1);
            page.setPages(1);
            
            log.info("库存统计查询完成，统计类型={}，查询条件={}，结果数量={}", 
                    request.getStatisticsType(), request, records.size());
            
            return page;
        } catch (Exception e) {
            log.error("物品库存统计查询失败", e);
            throw new RuntimeException("库存统计查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询库存不足的物品列表（库存下限告警）
     * 查询总库存低于企业级安全库存的物品
     *
     * @return 库存不足的物品列表
     */
    @Override
    public List<ItemVo> selectLowStockItems() {
        try {
            List<ItemVo> lowStockItems = itemBaseInfoMapper.selectLowStockItems();
            
            // 为每个物品填充仓库ID列表
            if (lowStockItems != null && !lowStockItems.isEmpty()) {
                for (ItemVo item : lowStockItems) {
                    List<Integer> warehouseIds = itemBaseInfoMapper.selectWarehouseIdsByItemId(item.getItemId());
                    item.setWarehouseIds(warehouseIds);
                }
            }
            
            log.info("查询库存不足物品完成，共找到{}个库存不足的物品", 
                    lowStockItems != null ? lowStockItems.size() : 0);
            
            return lowStockItems != null ? lowStockItems : new ArrayList<>();
        } catch (Exception e) {
            log.error("查询库存不足物品失败", e);
            throw new RuntimeException("查询库存不足物品失败: " + e.getMessage());
        }
    }

    /**
     * 库存查看（支持按仓库和按物品两种展示模式）
     *
     * @param request 查询条件
     * @return 库存查看列表
     */
    @Override
    public IPage<ItemStockViewVo> selectItemStockView(ItemStockViewRequest request) {
        try {
            // 设置默认展示模式
            if (request.getViewMode() == null) {
                request.setViewMode(1); // 默认按仓库展示
            }
            
            Page<ItemStockViewVo> page = new Page<>(request.getPageNum(), request.getPageSize());
            IPage<ItemStockViewVo> result;
            
            if (request.getViewMode() == 1) {
                // 按仓库展示：显示每个物品在每个仓库的库存情况
                result = itemBaseInfoMapper.selectItemStockViewByWarehouse(page, request);
            } else {
                // 按物品展示：汇总每个物品在所有仓库的库存，并提供详细的仓库分布
                result = itemBaseInfoMapper.selectItemStockViewByItem(page, request);
                
                // 为每个物品填充库存详情列表
                if (result.getRecords() != null && !result.getRecords().isEmpty()) {
                    for (ItemStockViewVo item : result.getRecords()) {
                        List<ItemStockViewVo.ItemStockDetailVo> stockDetails = 
                            itemBaseInfoMapper.selectItemStockDetails(item.getItemId());
                        item.setStockDetails(stockDetails);
                    }
                }
            }
            
            // 为每条记录设置仓库名称（通过数据字典获取）
            if (result.getRecords() != null && !result.getRecords().isEmpty()) {
                for (ItemStockViewVo item : result.getRecords()) {
                    if (item.getWarehouseId() != null) {
                        item.setWarehouseName("仓库" + item.getWarehouseId());
                    }
                    // 为库存详情也设置仓库名称
                    if (item.getStockDetails() != null) {
                        for (ItemStockViewVo.ItemStockDetailVo detail : item.getStockDetails()) {
                            detail.setWarehouseName("仓库" + detail.getWarehouseId());
                        }
                    }
                }
            }
            
            log.info("库存查看查询完成，展示模式={}，查询条件={}，结果数量={}", 
                    request.getViewMode(), request, result.getRecords().size());
            
            return result;
        } catch (Exception e) {
            log.error("库存查看查询失败", e);
            throw new RuntimeException("库存查看查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 调整物品库存数量
     *
     * @param adjustmentDto 库存调整信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean adjustItemStock(ItemStockAdjustmentDto adjustmentDto) {
        try {
            String itemId = adjustmentDto.getItemId();
            Integer warehouseId = adjustmentDto.getWarehouseId();
            BigDecimal newQuantity = adjustmentDto.getNewQuantity();
            
            // 参数校验
            if (itemId == null || itemId.trim().isEmpty()) {
                log.error("物品ID不能为空");
                return false;
            }
            if (warehouseId == null) {
                log.error("仓库ID不能为空");
                return false;
            }
            if (newQuantity == null || newQuantity.compareTo(BigDecimal.ZERO) < 0) {
                log.error("调整后的库存数量不能为空且不能为负数");
                return false;
            }
            
            // 查询指定仓库的库存记录
            LambdaQueryWrapper<ItemInventory> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ItemInventory::getItemId, itemId);
            wrapper.eq(ItemInventory::getWarehouseId, warehouseId);
            ItemInventory inventory = inventoryMapper.selectOne(wrapper);
            
            if (inventory == null) {
                log.error("未找到物品在指定仓库的库存记录，itemId={}，warehouseId={}", itemId, warehouseId);
                return false;
            }
            
            // 记录调整前的库存
            BigDecimal oldQuantity = inventory.getCurrentQuantity();
            
            // 更新库存数量
            inventory.setCurrentQuantity(newQuantity);
            
            // 更新库存状态
            int stockStatus = 1; // 默认正常
            if (inventory.getSafetyStock() != null && inventory.getSafetyStock().compareTo(BigDecimal.ZERO) > 0) {
                if (newQuantity.compareTo(inventory.getSafetyStock()) < 0) {
                    stockStatus = 2; // 库存不足
                } else if (newQuantity.compareTo(inventory.getSafetyStock().multiply(new BigDecimal("2"))) > 0) {
                    stockStatus = 3; // 库存过剩
                }
            }
            inventory.setStockStatus(stockStatus);
            
            // 如果提供了新的货架位置，则更新货架位置
            if (adjustmentDto.getShelfLocation() != null && !adjustmentDto.getShelfLocation().trim().isEmpty()) {
                inventory.setShelfLocation(adjustmentDto.getShelfLocation());
            }
            
            inventory.setUpdateTime(new Date());
            inventory.setUpdateBy(SecurityUtils.getUsername());
            inventoryMapper.updateById(inventory);
            
            log.info("库存调整成功，itemId={}，仓库ID={}，调整前库存={}，调整后库存={}，调整原因={}，货架位置={}", 
                    itemId, warehouseId, oldQuantity, newQuantity, adjustmentDto.getReason(), inventory.getShelfLocation());
            
            // 同步更新物品总库存
            try {
                itemStockSyncService.syncTotalStock(itemId);
                log.info("同步物品总库存成功，itemId={}", itemId);
            } catch (Exception e) {
                log.error("同步物品总库存失败，itemId={}", itemId, e);
                // 不抛出异常，避免影响主要的库存调整流程
            }
            
            // 记录库存操作日志（此处可以调用库存操作日志的服务）
            // TODO: 记录库存调整日志
            
            return true;
        } catch (Exception e) {
            log.error("调整物品库存失败", e);
            throw e;
        }
    }
    
    @Override
    public boolean checkStockSufficient(String itemId, Integer warehouseId, int quantity) {
        try {
            // 参数校验
            if (itemId == null || itemId.trim().isEmpty()) {
                log.error("物品ID不能为空");
                return false;
            }
            if (warehouseId == null) {
                log.error("仓库ID不能为空");
                return false;
            }
            if (quantity <= 0) {
                log.error("检查数量必须大于0");
                return false;
            }
            
            // 查询指定仓库的库存记录
            LambdaQueryWrapper<ItemInventory> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ItemInventory::getItemId, itemId);
            wrapper.eq(ItemInventory::getWarehouseId, warehouseId);
            ItemInventory inventory = inventoryMapper.selectOne(wrapper);
            
            if (inventory == null) {
                log.warn("物品[{}]在仓库[{}]没有库存记录", itemId, warehouseId);
                return false;
            }
            
            BigDecimal currentQuantity = inventory.getCurrentQuantity();
            if (currentQuantity == null) {
                currentQuantity = BigDecimal.ZERO;
            }
            
            // 检查库存是否充足
            boolean sufficient = currentQuantity.compareTo(new BigDecimal(quantity)) >= 0;
            
            if (!sufficient) {
                log.info("库存不足：物品[{}]在仓库[{}]当前库存[{}]，需要[{}]", 
                        itemId, warehouseId, currentQuantity, quantity);
            }
            
            return sufficient;
        } catch (Exception e) {
            log.error("检查库存是否充足失败，itemId={}，warehouseId={}，quantity={}", 
                    itemId, warehouseId, quantity, e);
            return false;
        }
    }

    // ==================== 微信小程序专用接口实现 ====================

    @Override
    public ItemDetailVo getItemByCode(String itemCode) {
        try {
            log.info("根据物品条码查询物品信息，条码：{}", itemCode);

            // 根据物品条码查询物品基本信息
            LambdaQueryWrapper<ItemBaseInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ItemBaseInfo::getItemCode, itemCode)
                   .eq(ItemBaseInfo::getDeleted, 0);

            ItemBaseInfo baseInfo = itemBaseInfoMapper.selectOne(wrapper);

            if (baseInfo == null) {
                log.warn("未找到物品条码对应的物品信息，条码：{}", itemCode);
                return null;
            }

            // 获取物品详情
            ItemDetailVo detailVo = getItemDetail(baseInfo.getItemId());

            log.info("根据物品条码查询物品信息成功，条码：{}，物品ID：{}", itemCode, baseInfo.getItemId());
            return detailVo;

        } catch (Exception e) {
            log.error("根据物品条码查询物品信息失败，条码：{}", itemCode, e);
            return null;
        }
    }
}