<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.asset_disposal.mapper.AssetDisposalExecutionMapper">

    <resultMap id="BaseResultMap" type="com.jingfang.asset_disposal.module.entity.AssetDisposalExecution">
        <id property="executionId" column="execution_id" jdbcType="VARCHAR"/>
        <result property="disposalId" column="disposal_id" jdbcType="VARCHAR"/>
        <result property="executorId" column="executor_id" jdbcType="BIGINT"/>
        <result property="executionTime" column="execution_time" jdbcType="TIMESTAMP"/>
        <result property="actualDisposalValue" column="actual_disposal_value" jdbcType="DECIMAL"/>
        <result property="disposalCertificate" column="disposal_certificate" jdbcType="VARCHAR"/>
        <result property="executionDescription" column="execution_description" jdbcType="LONGVARCHAR"/>
        <result property="executionStatus" column="execution_status" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 根据处置单ID查询执行记录 -->
    <select id="selectExecutionByDisposalId" resultType="com.jingfang.asset_disposal.module.vo.AssetDisposalExecutionVo">
        SELECT 
            e.execution_id,
            e.disposal_id,
            e.executor_id,
            su.nick_name as executor_name,
            e.execution_time,
            e.actual_disposal_value,
            e.disposal_certificate,
            e.execution_description,
            e.execution_status,
            CASE e.execution_status
                WHEN 1 THEN '执行中'
                WHEN 2 THEN '已完成'
                WHEN 3 THEN '异常'
                ELSE '未知'
            END as execution_status_name,
            e.create_time
        FROM asset_disposal_execution e
        LEFT JOIN sys_user su ON e.executor_id = su.user_id
        WHERE e.disposal_id = #{disposalId}
    </select>

</mapper> 