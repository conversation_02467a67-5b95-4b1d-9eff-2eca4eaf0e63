<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.wechat.mapper.SysWxUserMapper">

    <resultMap type="com.jingfang.wechat.module.entity.SysWxUser" id="SysWxUserResult">
        <id     property="wxUserId"     column="wx_user_id"     />
        <result property="openid"       column="openid"         />
        <result property="unionid"      column="unionid"        />
        <result property="nickName"     column="nick_name"      />
        <result property="avatarUrl"    column="avatar_url"     />
        <result property="gender"       column="gender"         />
        <result property="country"      column="country"        />
        <result property="province"     column="province"       />
        <result property="city"         column="city"           />
        <result property="createTime"   column="create_time"    />
        <result property="updateTime"   column="update_time"    />
    </resultMap>

    <sql id="selectWxUserVo">
        select wx_user_id, openid, unionid, nick_name, avatar_url, gender, country, province, city, create_time, update_time
        from sys_wx_user
    </sql>

    <select id="selectWxUserById" parameterType="Long" resultMap="SysWxUserResult">
        <include refid="selectWxUserVo"/>
        where wx_user_id = #{wxUserId}
    </select>

    <select id="selectWxUserByOpenid" parameterType="String" resultMap="SysWxUserResult">
        <include refid="selectWxUserVo"/>
        where openid = #{openid}
    </select>

    <insert id="insertWxUser" parameterType="com.jingfang.wechat.module.entity.SysWxUser" useGeneratedKeys="true" keyProperty="wxUserId">
        insert into sys_wx_user (
        <if test="openid != null and openid != ''">openid,</if>
        <if test="unionid != null and unionid != ''">unionid,</if>
        <if test="nickName != null and nickName != ''">nick_name,</if>
        <if test="avatarUrl != null and avatarUrl != ''">avatar_url,</if>
        <if test="gender != null and gender != ''">gender,</if>
        <if test="country != null and country != ''">country,</if>
        <if test="province != null and province != ''">province,</if>
        <if test="city != null and city != ''">city,</if>
        <if test="createTime != null">create_time,</if>
        <if test="updateTime != null">update_time</if>
        ) values (
        <if test="openid != null and openid != ''">#{openid},</if>
        <if test="unionid != null and unionid != ''">#{unionid},</if>
        <if test="nickName != null and nickName != ''">#{nickName},</if>
        <if test="avatarUrl != null and avatarUrl != ''">#{avatarUrl},</if>
        <if test="gender != null and gender != ''">#{gender},</if>
        <if test="country != null and country != ''">#{country},</if>
        <if test="province != null and province != ''">#{province},</if>
        <if test="city != null and city != ''">#{city},</if>
        <if test="createTime != null">#{createTime},</if>
        <if test="updateTime != null">#{updateTime}</if>
        )
    </insert>

    <update id="updateWxUser" parameterType="com.jingfang.wechat.module.entity.SysWxUser">
        update sys_wx_user
        <set>
            <if test="unionid != null">unionid = #{unionid},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="avatarUrl != null">avatar_url = #{avatarUrl},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="country != null">country = #{country},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
        where wx_user_id = #{wxUserId}
    </update>

</mapper>