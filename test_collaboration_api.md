# 协作模块API测试指南

## 修复内容总结

### 🔧 已修复的问题
1. **添加了缺失的SQL实现** - `selectUserInvitations`方法
2. **更新了实体类和VO** - 添加了表格标题、描述、邀请消息字段
3. **完善了API接口** - 添加了用户邀请列表和退出协作接口
4. **修复了前端API调用** - 所有页面现在都使用真实数据

### 📋 新增的API接口

#### 1. 查询用户的协作邀请列表
```
GET /collaboration/spreadsheet/invitations
参数：
- pageNum: 页码（默认1）
- pageSize: 页大小（默认10）
- spreadsheetTitle: 表格标题（可选）
- status: 邀请状态（可选，0待接受 1已接受 2已拒绝）
```

#### 2. 退出协作
```
POST /collaboration/spreadsheet/{id}/leave
参数：
- id: 表格ID（路径参数）
```

#### 3. 查询用户可访问的表格列表
```
GET /collaboration/spreadsheet/accessible
参数：
- pageNum: 页码（默认1）
- pageSize: 页大小（默认10）
- title: 表格标题（可选）
- status: 状态（可选）
```

### 🗄️ 数据库更新

如果需要完整的邀请消息功能，请执行以下SQL：
```sql
-- 添加邀请消息字段
ALTER TABLE `collaboration_spreadsheet_collaborator` 
ADD COLUMN `message` varchar(500) DEFAULT NULL COMMENT '邀请消息' 
AFTER `status`;
```

**注意**：当前版本为了兼容性，邀请消息暂时存储在`remark`字段中。

### 🧪 测试步骤

1. **启动后端服务**
   ```bash
   cd device_monitor-admin
   mvn spring-boot:run
   ```

2. **启动前端服务**
   ```bash
   cd device_monitor-ui
   npm run dev
   ```

3. **测试页面访问**
   - 在线表格：`http://localhost/collaboration/spreadsheet`
   - 我的协作：`http://localhost/collaboration/my-collaboration`
   - 协作邀请：`http://localhost/collaboration/invitations`

4. **测试功能流程**
   - 创建表格 → 邀请协作者 → 查看邀请列表 → 接受/拒绝邀请 → 管理协作者权限

### 🔍 故障排除

如果遇到以下错误：
- `Invalid bound statement (not found)` - 检查Mapper XML文件是否正确加载
- `Column not found` - 检查数据库表结构是否包含所需字段
- `Permission denied` - 检查用户权限配置

### 📝 后续优化建议

1. **数据库优化**：执行数据库更新脚本，添加专门的message字段
2. **权限细化**：为不同操作配置更细粒度的权限控制
3. **通知系统**：集成站内消息和邮件通知功能
4. **性能优化**：添加缓存和索引优化查询性能
