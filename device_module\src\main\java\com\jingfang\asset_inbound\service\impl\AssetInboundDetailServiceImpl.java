package com.jingfang.asset_inbound.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingfang.asset_inbound.module.entity.AssetInboundDetail;
import com.jingfang.asset_inbound.service.AssetInboundDetailService;
import com.jingfang.asset_inbound.mapper.AssetInboundDetailMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【asset_inbound_detail(资产入库明细表)】的数据库操作Service实现
* @createDate 2025-05-07 14:08:37
*/
@Service
public class AssetInboundDetailServiceImpl extends ServiceImpl<AssetInboundDetailMapper, AssetInboundDetail>
    implements AssetInboundDetailService{

}




