package com.jingfang.asset_disposal.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingfang.asset_disposal.mapper.*;
import com.jingfang.asset_disposal.module.dto.*;
import com.jingfang.asset_disposal.module.entity.*;
import com.jingfang.asset_disposal.module.request.AssetDisposalSearchRequest;
import com.jingfang.asset_disposal.module.vo.*;
import com.jingfang.asset_disposal.service.AssetDisposalService;
import com.jingfang.asset_ledger.module.entity.AssetBaseInfo;
import com.jingfang.asset_ledger.service.AssetBaseInfoService;
import com.jingfang.common.utils.BusinessCodeGenerator;
import com.jingfang.common.utils.SecurityUtils;
import com.jingfang.common.utils.uuid.IdUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 资产处置服务实现类
 */
@Slf4j
@Service
public class AssetDisposalServiceImpl extends ServiceImpl<AssetDisposalMapper, AssetDisposal> 
        implements AssetDisposalService {
    
    @Resource
    private AssetDisposalMapper disposalMapper;
    
    @Resource
    private AssetDisposalApprovalMapper approvalMapper;
    
    @Resource
    private AssetDisposalExecutionMapper executionMapper;
    
    @Resource
    private AssetDisposalAttachmentMapper attachmentMapper;
    
    @Resource
    private AssetBaseInfoService assetBaseInfoService;
    
    @Resource
    private BusinessCodeGenerator codeGenerator;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createDisposalRequest(AssetDisposalDto dto) {
        try {
            AssetDisposal disposal = new AssetDisposal();
            BeanUtils.copyProperties(dto, disposal);
            
            // 生成处置单号
            disposal.setDisposalId(codeGenerator.generateCode("ZCCP"));
            disposal.setApplicantId(SecurityUtils.getUserId());
            disposal.setApplyTime(new Date());
            disposal.setStatus(1); // 草稿状态
            disposal.setCreateTime(new Date());
            disposal.setCreateBy(SecurityUtils.getUsername());
            disposal.setDeleted(0);
            
            // 保存主记录
            this.save(disposal);
            
            // 保存附件
            if (dto.getAttachmentList() != null && !dto.getAttachmentList().isEmpty()) {
                saveAttachments(disposal.getDisposalId(), dto.getAttachmentList());
            }
            
            return true;
        } catch (Exception e) {
            log.error("创建资产处置申请失败", e);
            return false;
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submitForApproval(String disposalId) {
        try {
            // 更新状态为待审核
            AssetDisposal disposal = new AssetDisposal();
            disposal.setDisposalId(disposalId);
            disposal.setStatus(2);
            disposal.setUpdateTime(new Date());
            disposal.setUpdateBy(SecurityUtils.getUsername());
            
            boolean result = this.updateById(disposal);
            
            if (result) {
                // 生成审批流程
                generateApprovalFlow(disposalId);
            }
            
            return result;
        } catch (Exception e) {
            log.error("提交审核失败", e);
            return false;
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean approveDisposal(DisposalApprovalDto dto) {
        try {
            // 更新审批记录
            AssetDisposalApproval approval = new AssetDisposalApproval();
            approval.setDisposalId(dto.getDisposalId());
            approval.setApproverId(SecurityUtils.getUserId());
            approval.setApprovalStatus(dto.getApprovalStatus());
            approval.setApprovalComment(dto.getApprovalComment());
            approval.setApprovalTime(new Date());
            
            approvalMapper.updateApprovalStatus(approval);
            
            // 判断是否所有审批都通过
            if (dto.getApprovalStatus() == 2) { // 通过
                int pendingCount = approvalMapper.countPendingApprovals(dto.getDisposalId());
                if (pendingCount == 0) {
                    // 更新处置单状态为审核通过
                    updateDisposalStatus(dto.getDisposalId(), 4);
                }
            } else if (dto.getApprovalStatus() == 3) { // 拒绝
                // 直接更新为审核拒绝
                updateDisposalStatus(dto.getDisposalId(), 5);
            }
            
            return true;
        } catch (Exception e) {
            log.error("审批处置申请失败", e);
            return false;
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean executeDisposal(String disposalId, AssetDisposalExecutionDto dto) {
        try {
            // 1. 保存执行记录
            AssetDisposalExecution execution = new AssetDisposalExecution();
            BeanUtils.copyProperties(dto, execution);
            execution.setExecutionId(IdUtils.fastSimpleUUID());
            execution.setDisposalId(disposalId);
            execution.setExecutorId(SecurityUtils.getUserId());
            execution.setExecutionTime(new Date());
            execution.setCreateTime(new Date());
            
            executionMapper.insert(execution);
            
            // 2. 更新处置单状态
            updateDisposalStatus(disposalId, 7); // 已完成
            
            // 3. 更新资产状态
            AssetDisposal disposal = this.getById(disposalId);
            updateAssetStatus(disposal);
            
            return true;
        } catch (Exception e) {
            log.error("执行资产处置失败", e);
            return false;
        }
    }
    
    @Override
    public IPage<AssetDisposalVo> selectDisposalList(AssetDisposalSearchRequest request) {
        IPage<AssetDisposalVo> page = new Page<>(request.getPageNum(), request.getPageSize());
        return disposalMapper.selectDisposalList(page, request);
    }
    
    @Override
    public AssetDisposalDetailVo getDisposalDetail(String disposalId) {
        AssetDisposalDetailVo detailVo = new AssetDisposalDetailVo();
        
        // 获取处置信息
        AssetDisposalVo disposalInfo = disposalMapper.selectDisposalById(disposalId);
        detailVo.setDisposalInfo(disposalInfo);
        
        // 获取资产信息
        if (disposalInfo != null && disposalInfo.getAssetId() != null) {
            AssetBaseInfo asset = assetBaseInfoService.getById(disposalInfo.getAssetId());
            if (asset != null) {
                // 这里需要将AssetBaseInfo转换为AssetDetailBaseInfoVo，或者直接设置
                detailVo.setAssetInfo(assetBaseInfoService.selectBaseInfoById(disposalInfo.getAssetId()));
            }
        }
        
        // 获取审批记录
        List<AssetDisposalApprovalVo> approvalRecords = approvalMapper.selectApprovalsByDisposalId(disposalId);
        detailVo.setApprovalRecords(approvalRecords);
        
        // 获取执行记录
        AssetDisposalExecutionVo executionRecord = executionMapper.selectExecutionByDisposalId(disposalId);
        detailVo.setExecutionRecord(executionRecord);
        
        // 获取附件列表
        List<AssetDisposalAttachmentDto> attachmentList = attachmentMapper.selectAttachmentsByDisposalId(disposalId);
        detailVo.setAttachmentList(attachmentList);
        
        return detailVo;
    }
    
    @Override
    public List<AssetDisposalVo> getPendingApprovalList(Long userId) {
        return disposalMapper.selectPendingApprovalList(userId);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelDisposal(String disposalId, String reason) {
        try {
            AssetDisposal disposal = new AssetDisposal();
            disposal.setDisposalId(disposalId);
            disposal.setStatus(8); // 已取消
            disposal.setUpdateTime(new Date());
            disposal.setUpdateBy(SecurityUtils.getUsername());
            
            return this.updateById(disposal);
        } catch (Exception e) {
            log.error("取消处置申请失败", e);
            return false;
        }
    }
    
    @Override
    public Map<String, Object> getDisposalStatistics() {
        List<Map<String, Object>> statistics = disposalMapper.selectDisposalStatistics();
        Map<String, Object> result = new HashMap<>();
        
        for (Map<String, Object> stat : statistics) {
            String status = String.valueOf(stat.get("status"));
            Object count = stat.get("count");
            result.put("status_" + status, count);
        }
        
        return result;
    }
    
    /**
     * 保存附件
     */
    private void saveAttachments(String disposalId, List<AssetDisposalAttachmentDto> attachmentList) {
        attachmentMapper.batchInsertAttachments(disposalId, attachmentList);
    }
    
    /**
     * 生成审批流程
     */
    private void generateApprovalFlow(String disposalId) {
        // 简化版本：只设置一级审批
        List<AssetDisposalApproval> approvalList = new ArrayList<>();
        
        AssetDisposalApproval approval = new AssetDisposalApproval();
        approval.setApprovalId(IdUtils.fastSimpleUUID());
        approval.setDisposalId(disposalId);
        approval.setApprovalLevel(1);
        approval.setApproverId(1L); // 这里应该根据实际业务规则确定审批人
        approval.setApprovalStatus(1); // 待审批
        approval.setCreateTime(new Date());
        
        approvalList.add(approval);
        
        approvalMapper.batchInsertApprovals(approvalList);
    }
    
    /**
     * 更新处置状态
     */
    private void updateDisposalStatus(String disposalId, Integer status) {
        disposalMapper.updateDisposalStatus(disposalId, status);
    }
    
    /**
     * 更新资产状态
     */
    private void updateAssetStatus(AssetDisposal disposal) {
        // 根据处置类型更新资产状态
        AssetBaseInfo asset = new AssetBaseInfo();
        asset.setAssetId(disposal.getAssetId());
        asset.setUpdateTime(new Date());
        
        switch (disposal.getDisposalType()) {
            case 1: // 报废
                asset.setAssetStatus(3); // 报废状态
                break;
            case 2: // 调拨
                asset.setDeptId(disposal.getTransferDeptId());
                break;
            case 3: // 出售
                asset.setAssetStatus(3); // 出售后也设为报废状态
                break;
        }
        
        assetBaseInfoService.updateById(asset);
    }
} 