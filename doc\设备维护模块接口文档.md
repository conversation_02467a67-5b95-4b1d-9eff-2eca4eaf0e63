# 设备维护模块接口文档

## 概述

设备维护模块是基于RuoYi框架开发的设备维护管理系统，主要包含维护计划管理和维护任务管理两大核心功能。该模块提供了完整的设备维护生命周期管理，从维护计划的制定到维护任务的执行、审核和完成。

## 模块结构

设备维护模块包含以下4个控制器：

1. **MaintenancePlanController** - 维护计划控制器
2. **MaintenanceTaskController** - 维护任务控制器  
3. **MaintenanceHelperController** - 维护计划辅助控制器
4. **MaintenanceTaskHelperController** - 维护任务辅助控制器

## 1. 维护计划控制器 (MaintenancePlanController)

**基础路径**: `/maintenance/plan`

### 1.1 核心功能接口

#### 新增维护计划
- **接口**: `POST /maintenance/plan`
- **权限**: `maintenance:plan:add`
- **参数**: MaintenancePlanDto
- **功能**: 创建新的维护计划

#### 修改维护计划
- **接口**: `PUT /maintenance/plan`
- **权限**: `maintenance:plan:edit`
- **参数**: MaintenancePlanDto
- **功能**: 更新现有维护计划信息

#### 删除维护计划
- **接口**: `DELETE /maintenance/plan/{planIds}`
- **权限**: `maintenance:plan:remove`
- **参数**: planIds (支持批量删除，逗号分隔)
- **功能**: 删除指定的维护计划

#### 查询维护计划列表
- **接口**: `POST /maintenance/plan/list`
- **权限**: `maintenance:plan:list`
- **参数**: MaintenancePlanSearchRequest
- **功能**: 分页查询维护计划列表

#### 查询维护计划详情
- **接口**: `GET /maintenance/plan/{planId}`
- **权限**: `maintenance:plan:query`
- **参数**: planId
- **功能**: 根据ID查询维护计划详细信息

### 1.2 状态管理接口

#### 启用/停用维护计划
- **接口**: `PUT /maintenance/plan/status/{planId}/{status}`
- **权限**: `maintenance:plan:edit`
- **参数**: planId, status (1-启用, 0-停用)
- **功能**: 更新维护计划的启用状态

### 1.3 查询类接口

#### 查询即将到期的维护计划
- **接口**: `GET /maintenance/plan/upcoming/{days}`
- **权限**: `maintenance:plan:query`
- **参数**: days (提前天数)
- **功能**: 查询指定天数内即将到期的维护计划

#### 查询已过期的维护计划
- **接口**: `GET /maintenance/plan/overdue`
- **权限**: `maintenance:plan:query`
- **功能**: 查询所有已过期的维护计划

#### 根据资产ID查询维护计划
- **接口**: `GET /maintenance/plan/asset/{assetId}`
- **权限**: `maintenance:plan:query`
- **参数**: assetId
- **功能**: 查询指定资产的所有维护计划

#### 根据负责人查询维护计划
- **接口**: `GET /maintenance/plan/responsible/{responsibleType}/{responsibleId}`
- **权限**: `maintenance:plan:query`
- **参数**: responsibleType (1-个人, 2-部门), responsibleId
- **功能**: 查询指定负责人的维护计划

## 2. 维护任务控制器 (MaintenanceTaskController)

**基础路径**: `/maintenance/task`

### 2.1 基础CRUD接口

#### 分页查询维护任务列表
- **接口**: `GET /maintenance/task/list`
- **权限**: `maintenance:task:list`
- **参数**: MaintenanceTaskSearchRequest
- **功能**: 分页查询维护任务列表

#### 获取维护任务详细信息
- **接口**: `GET /maintenance/task/{taskId}`
- **权限**: `maintenance:task:query`
- **参数**: taskId
- **功能**: 根据ID查询维护任务详情

#### 新增维护任务
- **接口**: `POST /maintenance/task`
- **权限**: `maintenance:task:add`
- **参数**: MaintenanceTaskDto
- **功能**: 创建新的维护任务

#### 修改维护任务
- **接口**: `PUT /maintenance/task`
- **权限**: `maintenance:task:edit`
- **参数**: MaintenanceTaskDto
- **功能**: 更新维护任务信息

#### 删除维护任务
- **接口**: `DELETE /maintenance/task/{taskIds}`
- **权限**: `maintenance:task:remove`
- **参数**: taskIds (数组，支持批量删除)
- **功能**: 删除指定的维护任务

### 2.2 任务执行流程接口

#### 开始执行任务
- **接口**: `POST /maintenance/task/start/{taskId}`
- **权限**: `maintenance:task:execute`
- **参数**: taskId
- **功能**: 开始执行指定的维护任务

#### 保存为草稿
- **接口**: `POST /maintenance/task/draft`
- **权限**: `maintenance:task:execute`
- **参数**: MaintenanceTaskDto
- **功能**: 将任务保存为草稿状态

#### 提交任务结果
- **接口**: `POST /maintenance/task/submit`
- **权限**: `maintenance:task:execute`
- **参数**: MaintenanceTaskDto
- **功能**: 提交任务执行结果

#### 审核任务
- **接口**: `POST /maintenance/task/review`
- **权限**: `maintenance:task:review`
- **参数**: taskId, reviewResult, reviewComment
- **功能**: 对提交的任务进行审核

#### 委派任务
- **接口**: `POST /maintenance/task/delegate`
- **权限**: `maintenance:task:delegate`
- **参数**: taskId, responsibleType, responsibleId, delegateReason
- **功能**: 将任务委派给其他人员或部门

#### 取消任务
- **接口**: `POST /maintenance/task/cancel`
- **权限**: `maintenance:task:cancel`
- **参数**: taskId, cancelReason
- **功能**: 取消指定的维护任务

### 2.3 任务查询接口

#### 查询我的任务
- **接口**: `GET /maintenance/task/myTasks`
- **权限**: `maintenance:task:myTasks`
- **参数**: statusList (可选，任务状态列表)
- **功能**: 查询当前用户的维护任务

#### 查询待审核任务
- **接口**: `GET /maintenance/task/pendingReview`
- **权限**: `maintenance:task:review`
- **功能**: 查询所有待审核的维护任务

#### 查询即将到期的任务
- **接口**: `GET /maintenance/task/upcoming`
- **权限**: `maintenance:task:list`
- **参数**: days (默认7天)
- **功能**: 查询即将到期的维护任务

#### 查询已逾期的任务
- **接口**: `GET /maintenance/task/overdue`
- **权限**: `maintenance:task:list`
- **功能**: 查询所有已逾期的维护任务

#### 根据维护计划ID查询任务列表
- **接口**: `GET /maintenance/task/plan/{planId}`
- **权限**: `maintenance:task:list`
- **参数**: planId
- **功能**: 查询指定维护计划下的所有任务

### 2.4 统计和生成接口

#### 获取任务统计信息
- **接口**: `GET /maintenance/task/statistics`
- **权限**: `maintenance:task:list`
- **功能**: 获取维护任务的统计数据

#### 手动生成维护任务
- **接口**: `POST /maintenance/task/generate`
- **权限**: `maintenance:task:generate`
- **功能**: 手动触发维护任务生成

#### 根据维护计划生成任务
- **接口**: `POST /maintenance/task/generate/{planId}`
- **权限**: `maintenance:task:generate`
- **参数**: planId
- **功能**: 根据指定维护计划生成任务

#### 导出维护任务列表
- **接口**: `POST /maintenance/task/export`
- **权限**: `maintenance:task:export`
- **参数**: MaintenanceTaskSearchRequest
- **功能**: 导出维护任务数据到Excel

## 3. 维护计划辅助控制器 (MaintenanceHelperController)

**基础路径**: `/maintenance/helper`

#### 根据资产ID获取关联的备品备件列表
- **接口**: `GET /maintenance/helper/asset/{assetId}/parts`
- **权限**: `maintenance:plan:query`
- **参数**: assetId
- **功能**: 获取指定资产关联的备品备件列表，用于维护计划中选择备品备件

#### 检查备品备件是否与资产关联
- **接口**: `GET /maintenance/helper/check/asset/{assetId}/part/{partId}`
- **权限**: `maintenance:plan:query`
- **参数**: assetId, partId
- **功能**: 验证备品备件与资产的关联关系

## 4. 维护任务辅助控制器 (MaintenanceTaskHelperController)

**基础路径**: `/maintenance/task/helper`

### 4.1 基础数据接口

#### 获取用户列表
- **接口**: `GET /maintenance/task/helper/users`
- **权限**: `maintenance:task:list`
- **功能**: 获取系统用户列表，用于任务分配

#### 获取部门列表
- **接口**: `GET /maintenance/task/helper/depts`
- **权限**: `maintenance:task:list`
- **功能**: 获取系统部门列表，用于任务分配

### 4.2 选项数据接口

#### 获取任务状态选项
- **接口**: `GET /maintenance/task/helper/statusOptions`
- **功能**: 获取任务状态选项
- **返回值**: 
  - 1: 待执行
  - 2: 执行中
  - 3: 草稿
  - 4: 待审核
  - 5: 审核通过
  - 6: 审核不通过
  - 7: 已完成
  - 8: 已取消

#### 获取优先级选项
- **接口**: `GET /maintenance/task/helper/priorityOptions`
- **功能**: 获取任务优先级选项
- **返回值**:
  - 1: 低
  - 2: 中
  - 3: 高
  - 4: 紧急

#### 获取负责人类型选项
- **接口**: `GET /maintenance/task/helper/responsibleTypeOptions`
- **功能**: 获取负责人类型选项
- **返回值**:
  - 1: 个人
  - 2: 部门

#### 获取使用状态选项
- **接口**: `GET /maintenance/task/helper/useStatusOptions`
- **功能**: 获取备品备件使用状态选项
- **返回值**:
  - 1: 计划使用
  - 2: 已使用
  - 3: 未使用

## 权限说明

设备维护模块使用RuoYi框架的权限控制机制，主要权限包括：

- `maintenance:plan:*` - 维护计划相关权限
- `maintenance:task:*` - 维护任务相关权限

具体权限细分：
- `add` - 新增权限
- `edit` - 编辑权限
- `remove` - 删除权限
- `list` - 列表查询权限
- `query` - 详情查询权限
- `execute` - 任务执行权限
- `review` - 任务审核权限
- `delegate` - 任务委派权限
- `cancel` - 任务取消权限
- `generate` - 任务生成权限
- `export` - 数据导出权限
- `myTasks` - 查看个人任务权限

## 业务流程

### 维护计划流程
1. 创建维护计划 → 2. 启用计划 → 3. 系统自动生成维护任务

### 维护任务流程
1. 任务创建 → 2. 开始执行 → 3. 保存草稿/提交结果 → 4. 审核 → 5. 完成

### 任务状态流转
- 待执行 → 执行中 → 草稿/待审核 → 审核通过/审核不通过 → 已完成
- 任务可在任何阶段被取消或委派

## 注意事项

1. 所有接口都基于RuoYi框架的权限控制机制
2. 接口返回统一使用AjaxResult格式
3. 支持批量操作的接口使用逗号分隔或数组参数
4. 日期相关查询支持灵活的时间范围设置
5. 导出功能使用RuoYi框架的ExcelUtil工具类
6. 所有操作都有详细的日志记录
