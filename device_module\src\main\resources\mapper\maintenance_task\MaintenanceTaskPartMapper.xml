<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.maintenance_task.module.mapper.MaintenanceTaskPartMapper">

    <resultMap id="MaintenanceTaskPartVoResult" type="com.jingfang.maintenance_task.module.vo.MaintenanceTaskVo$MaintenanceTaskPartVo">
        <id property="recordId" column="record_id"/>
        <result property="taskId" column="task_id"/>
        <result property="partId" column="part_id"/>
        <result property="partName" column="part_name"/>
        <result property="specModel" column="spec_model"/>
        <result property="unit" column="unit"/>
        <result property="plannedQuantity" column="planned_quantity"/>
        <result property="actualQuantity" column="actual_quantity"/>
        <result property="currentStock" column="current_stock"/>
        <result property="useStatus" column="use_status"/>
        <result property="useStatusName" column="use_status_name"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectMaintenanceTaskPartVo">
        SELECT 
            mtp.record_id,
            mtp.task_id,
            mtp.part_id,
            wi.item_name as part_name,
            wi.spec_model,
            wi.unit,
            mtp.planned_quantity,
            mtp.actual_quantity,
            wi.current_stock,
            mtp.use_status,
            CASE mtp.use_status 
                WHEN 1 THEN '计划使用'
                WHEN 2 THEN '已使用'
                WHEN 3 THEN '未使用'
                ELSE '未知'
            END as use_status_name,
            mtp.remark,
            mtp.create_time
        FROM maintenance_task_part mtp
        LEFT JOIN warehouse_item wi ON mtp.part_id = wi.item_id
        WHERE mtp.deleted = 0
    </sql>

    <select id="selectPartsByTaskId" resultMap="MaintenanceTaskPartVoResult">
        <include refid="selectMaintenanceTaskPartVo"/>
        AND mtp.task_id = #{taskId}
        ORDER BY mtp.create_time ASC
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO maintenance_task_part (
            record_id, task_id, part_id, planned_quantity, actual_quantity,
            use_status, remark, create_time, create_by, deleted
        ) VALUES
        <foreach collection="partList" item="part" separator=",">
            (
                #{part.recordId}, #{part.taskId}, #{part.partId}, #{part.plannedQuantity}, #{part.actualQuantity},
                #{part.useStatus}, #{part.remark}, #{part.createTime}, #{part.createBy}, #{part.deleted}
            )
        </foreach>
    </insert>

    <delete id="deleteByTaskId">
        UPDATE maintenance_task_part 
        SET deleted = 1, update_time = NOW()
        WHERE task_id = #{taskId}
    </delete>

    <update id="updateUseStatus">
        UPDATE maintenance_task_part 
        SET use_status = #{useStatus}, 
            update_time = NOW(), 
            update_by = #{updateBy}
        WHERE record_id = #{recordId}
    </update>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="partList" item="part" separator=";">
            UPDATE maintenance_task_part 
            SET planned_quantity = #{part.plannedQuantity},
                actual_quantity = #{part.actualQuantity},
                use_status = #{part.useStatus},
                remark = #{part.remark},
                update_time = #{part.updateTime},
                update_by = #{part.updateBy}
            WHERE record_id = #{part.recordId}
        </foreach>
    </update>

</mapper> 