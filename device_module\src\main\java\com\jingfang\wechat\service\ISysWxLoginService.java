package com.jingfang.wechat.service;

import com.jingfang.common.core.domain.AjaxResult;

import java.util.Map;

/**
 * 微信登录服务接口
 */
public interface ISysWxLoginService {
    /**
     * 微信小程序登录
     *
     * @param code 微信临时登录凭证
     * @param userInfo 微信用户信息
     * @return 结果
     */
    public AjaxResult wxLogin(String code, Map<String, Object> userInfo);

    /**
     * 绑定系统账号
     *
     * @param openid 微信openid
     * @param username 系统用户名
     * @param password 系统用户密码
     * @param bindType 绑定类型
     * @return 结果
     */
    public AjaxResult bindAccount(String openid, String username, String password, String bindType);

    /**
     * 解绑系统账号
     *
     * @param openid 微信openid
     * @param userId 系统用户ID
     * @return 结果
     */
    public AjaxResult unbindAccount(String openid, Long userId);
}
