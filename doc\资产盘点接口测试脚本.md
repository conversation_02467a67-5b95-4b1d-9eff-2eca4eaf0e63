# 资产盘点接口测试脚本

## 测试环境配置

### 基础配置
- **服务器地址**: http://localhost:8080
- **认证方式**: Bearer Token
- **Content-Type**: application/json

### 测试数据准备
```json
{
  "testUserId": 1,
  "testDeptId": 100,
  "testAssetId": "ASSET001",
  "testAssetCode": "A001",
  "baseUrl": "http://localhost:8080"
}
```

## 1. 盘点计划接口测试

### 1.1 查询盘点计划列表
```bash
curl -X POST "${baseUrl}/asset/stocktaking/plan/list" \
  -H "Authorization: Bearer ${token}" \
  -H "Content-Type: application/json" \
  -d '{
    "planName": "测试盘点",
    "planType": 1,
    "status": 1,
    "pageNum": 1,
    "pageSize": 10
  }'
```

**预期结果**: 返回盘点计划分页列表，包含计划基本信息

### 1.2 创建盘点计划
```bash
curl -X POST "${baseUrl}/asset/stocktaking/plan" \
  -H "Authorization: Bearer ${token}" \
  -H "Content-Type: application/json" \
  -d '{
    "planName": "2025年第一季度全盘",
    "planType": 1,
    "startDate": "2025-01-15",
    "endDate": "2025-01-30",
    "responsibleUserId": 1,
    "remark": "季度例行盘点",
    "scopeDetail": {
      "deptIds": [100, 101],
      "categoryIds": [1, 2],
      "includeSubDept": true,
      "minValue": 1000.0,
      "maxValue": 100000.0
    }
  }'
```

**预期结果**: 创建成功，返回计划ID

### 1.3 获取盘点计划详情
```bash
curl -X GET "${baseUrl}/asset/stocktaking/plan/{planId}" \
  -H "Authorization: Bearer ${token}"
```

**预期结果**: 返回完整的计划信息，包括范围详情

### 1.4 更新盘点计划
```bash
curl -X PUT "${baseUrl}/asset/stocktaking/plan" \
  -H "Authorization: Bearer ${token}" \
  -H "Content-Type: application/json" \
  -d '{
    "planId": "{planId}",
    "planName": "2025年第一季度全盘(修订版)",
    "planType": 1,
    "startDate": "2025-01-15",
    "endDate": "2025-02-05",
    "responsibleUserId": 1,
    "remark": "延期至2月5日完成"
  }'
```

**预期结果**: 更新成功

### 1.5 启动盘点计划
```bash
curl -X POST "${baseUrl}/asset/stocktaking/plan/start/{planId}" \
  -H "Authorization: Bearer ${token}"
```

**预期结果**: 计划状态变更为执行中

### 1.6 完成盘点计划
```bash
curl -X POST "${baseUrl}/asset/stocktaking/plan/complete/{planId}" \
  -H "Authorization: Bearer ${token}"
```

**预期结果**: 计划状态变更为已完成

### 1.7 导出盘点计划
```bash
curl -X POST "${baseUrl}/asset/stocktaking/plan/export" \
  -H "Authorization: Bearer ${token}" \
  -H "Content-Type: application/json" \
  -d '{
    "planType": 1,
    "status": 3
  }' \
  --output "盘点计划导出.xlsx"
```

**预期结果**: 下载Excel文件

## 2. 盘点任务接口测试

### 2.1 查询盘点任务列表
```bash
curl -X POST "${baseUrl}/asset/stocktaking/task/list" \
  -H "Authorization: Bearer ${token}" \
  -H "Content-Type: application/json" \
  -d '{
    "planId": "{planId}",
    "assignedUserId": 1,
    "status": 1,
    "pageNum": 1,
    "pageSize": 10
  }'
```

**预期结果**: 返回任务分页列表

### 2.2 创建盘点任务
```bash
curl -X POST "${baseUrl}/asset/stocktaking/task" \
  -H "Authorization: Bearer ${token}" \
  -H "Content-Type: application/json" \
  -d '{
    "planId": "{planId}",
    "taskName": "办公区域盘点任务",
    "assignedUserId": 2,
    "expectedCount": 50,
    "distributionConfig": {
      "distributionType": 1,
      "maxAssetCount": 20,
      "assignedUserIds": [2, 3],
      "autoAssign": true,
      "priority": 2
    }
  }'
```

**预期结果**: 创建成功，返回任务ID

### 2.3 获取任务详情
```bash
curl -X GET "${baseUrl}/asset/stocktaking/task/{taskId}" \
  -H "Authorization: Bearer ${token}"
```

**预期结果**: 返回完整任务信息

### 2.4 开始执行任务
```bash
curl -X POST "${baseUrl}/asset/stocktaking/task/start/{taskId}" \
  -H "Authorization: Bearer ${token}"
```

**预期结果**: 任务状态变更为执行中

### 2.5 完成任务
```bash
curl -X POST "${baseUrl}/asset/stocktaking/task/complete/{taskId}" \
  -H "Authorization: Bearer ${token}"
```

**预期结果**: 任务状态变更为已完成

## 3. 盘点记录接口测试

### 3.1 查询盘点记录列表
```bash
curl -X POST "${baseUrl}/asset/stocktaking/record/list" \
  -H "Authorization: Bearer ${token}" \
  -H "Content-Type: application/json" \
  -d '{
    "taskId": "{taskId}",
    "foundStatus": 1,
    "pageNum": 1,
    "pageSize": 10
  }'
```

**预期结果**: 返回记录分页列表

### 3.2 创建盘点记录
```bash
curl -X POST "${baseUrl}/asset/stocktaking/record" \
  -H "Authorization: Bearer ${token}" \
  -H "Content-Type: application/json" \
  -d '{
    "taskId": "{taskId}",
    "assetId": "ASSET001",
    "assetCode": "A001",
    "foundStatus": 1,
    "actualLocation": "办公室A101",
    "actualStatus": 1,
    "inventoryTime": "2025-01-15 10:30:00",
    "remark": "资产状态良好",
    "scanInfo": {
      "scanType": 1,
      "scanContent": "QR_ASSET001",
      "scanTime": "2025-01-15 10:30:00",
      "deviceInfo": "移动设备扫码"
    }
  }'
```

**预期结果**: 创建成功，返回记录ID

### 3.3 批量创建盘点记录
```bash
curl -X POST "${baseUrl}/asset/stocktaking/record/batch" \
  -H "Authorization: Bearer ${token}" \
  -H "Content-Type: application/json" \
  -d '{
    "taskId": "{taskId}",
    "batchRecords": [
      {
        "assetId": "ASSET002",
        "assetCode": "A002",
        "foundStatus": 1,
        "actualLocation": "办公室A102",
        "actualStatus": 1,
        "remark": "正常"
      },
      {
        "assetId": "ASSET003",
        "assetCode": "A003",
        "foundStatus": 0,
        "remark": "未找到资产"
      }
    ]
  }'
```

**预期结果**: 批量创建成功

### 3.4 获取记录详情
```bash
curl -X GET "${baseUrl}/asset/stocktaking/record/{recordId}" \
  -H "Authorization: Bearer ${token}"
```

**预期结果**: 返回完整记录信息

### 3.5 更新盘点记录
```bash
curl -X PUT "${baseUrl}/asset/stocktaking/record" \
  -H "Authorization: Bearer ${token}" \
  -H "Content-Type: application/json" \
  -d '{
    "recordId": "{recordId}",
    "taskId": "{taskId}",
    "assetId": "ASSET001",
    "foundStatus": 1,
    "actualLocation": "办公室A101-更新",
    "actualStatus": 1,
    "remark": "位置信息已更新"
  }'
```

**预期结果**: 更新成功

### 3.6 扫码盘点
```bash
curl -X POST "${baseUrl}/asset/stocktaking/record/scan" \
  -H "Authorization: Bearer ${token}" \
  -H "Content-Type: application/json" \
  -d '{
    "taskId": "{taskId}",
    "scanContent": "QR_ASSET004",
    "scanType": 1,
    "actualLocation": "仓库B区",
    "remark": "扫码盘点"
  }'
```

**预期结果**: 扫码盘点成功

### 3.7 查询任务进度
```bash
curl -X GET "${baseUrl}/asset/stocktaking/record/progress/{taskId}" \
  -H "Authorization: Bearer ${token}"
```

**预期结果**: 返回任务完成进度统计

### 3.8 查询异常记录
```bash
curl -X GET "${baseUrl}/asset/stocktaking/record/abnormal/{taskId}" \
  -H "Authorization: Bearer ${token}"
```

**预期结果**: 返回异常记录列表

### 3.9 查询未盘点资产
```bash
curl -X GET "${baseUrl}/asset/stocktaking/record/missing/{taskId}" \
  -H "Authorization: Bearer ${token}"
```

**预期结果**: 返回未盘点的资产列表

### 3.10 导出盘点记录
```bash
curl -X POST "${baseUrl}/asset/stocktaking/record/export" \
  -H "Authorization: Bearer ${token}" \
  -H "Content-Type: application/json" \
  -d '{
    "taskId": "{taskId}",
    "foundStatus": 1
  }' \
  --output "盘点记录导出.xlsx"
```

**预期结果**: 下载Excel文件

## 4. 盘点差异接口测试

### 4.1 查询差异列表
```bash
curl -X POST "${baseUrl}/asset/stocktaking/difference/list" \
  -H "Authorization: Bearer ${token}" \
  -H "Content-Type: application/json" \
  -d '{
    "planId": "{planId}",
    "diffType": 2,
    "handleStatus": 1,
    "pageNum": 1,
    "pageSize": 10
  }'
```

**预期结果**: 返回差异分页列表

### 4.2 创建盘点差异
```bash
curl -X POST "${baseUrl}/asset/stocktaking/difference" \
  -H "Authorization: Bearer ${token}" \
  -H "Content-Type: application/json" \
  -d '{
    "planId": "{planId}",
    "assetId": "ASSET005",
    "diffType": 2,
    "diffReason": "资产丢失",
    "handleStatus": 1,
    "handleSuggestion": "建议核销处理",
    "bookValue": {
      "assetName": "办公电脑",
      "assetCode": "A005",
      "assetStatus": 1,
      "location": "办公室A103",
      "deptName": "技术部",
      "managerName": "张三",
      "assetValue": 5000.0
    },
    "actualValue": {
      "foundStatus": 0,
      "inventoryTime": "2025-01-15 14:00:00",
      "inventoryUser": "李四"
    }
  }'
```

**预期结果**: 创建成功，返回差异ID

### 4.3 获取差异详情
```bash
curl -X GET "${baseUrl}/asset/stocktaking/difference/{diffId}" \
  -H "Authorization: Bearer ${token}"
```

**预期结果**: 返回完整差异信息

### 4.4 处理差异
```bash
curl -X POST "${baseUrl}/asset/stocktaking/difference/handle/{diffId}" \
  -H "Authorization: Bearer ${token}" \
  -H "Content-Type: application/json" \
  -d '{
    "handleType": 3,
    "handleUserId": 1,
    "handleTime": "2025-01-16 09:00:00",
    "handleResult": "已核销处理",
    "needApproval": true
  }'
```

**预期结果**: 处理成功

### 4.5 批量处理差异
```bash
curl -X POST "${baseUrl}/asset/stocktaking/difference/batch/handle" \
  -H "Authorization: Bearer ${token}" \
  -H "Content-Type: application/json" \
  -d '{
    "diffIds": ["{diffId1}", "{diffId2}"],
    "handleInfo": {
      "handleType": 1,
      "handleUserId": 1,
      "handleResult": "批量台账更新"
    }
  }'
```

**预期结果**: 批量处理成功

### 4.6 查询差异统计
```bash
curl -X GET "${baseUrl}/asset/stocktaking/difference/statistics/{planId}" \
  -H "Authorization: Bearer ${token}"
```

**预期结果**: 返回差异统计信息

### 4.7 导出差异列表
```bash
curl -X POST "${baseUrl}/asset/stocktaking/difference/export" \
  -H "Authorization: Bearer ${token}" \
  -H "Content-Type: application/json" \
  -d '{
    "planId": "{planId}",
    "diffType": 2
  }' \
  --output "盘点差异导出.xlsx"
```

**预期结果**: 下载Excel文件

## 5. 盘点报告接口测试

### 5.1 生成汇总报告
```bash
curl -X GET "${baseUrl}/asset/stocktaking/report/summary/{planId}" \
  -H "Authorization: Bearer ${token}"
```

**预期结果**: 返回完整的盘点汇总报告

### 5.2 生成详细报告
```bash
curl -X GET "${baseUrl}/asset/stocktaking/report/detail/{planId}" \
  -H "Authorization: Bearer ${token}"
```

**预期结果**: 返回详细盘点报告

### 5.3 生成部门统计报告
```bash
curl -X GET "${baseUrl}/asset/stocktaking/report/dept/{planId}" \
  -H "Authorization: Bearer ${token}"
```

**预期结果**: 返回按部门统计的报告

### 5.4 生成图表数据
```bash
curl -X GET "${baseUrl}/asset/stocktaking/report/chart/{planId}" \
  -H "Authorization: Bearer ${token}"
```

**预期结果**: 返回图表展示数据

### 5.5 生成对比报告
```bash
curl -X POST "${baseUrl}/asset/stocktaking/report/comparison" \
  -H "Authorization: Bearer ${token}" \
  -H "Content-Type: application/json" \
  -d '["planId1", "planId2", "planId3"]'
```

**预期结果**: 返回多计划对比报告

### 5.6 生成趋势分析报告
```bash
curl -X GET "${baseUrl}/asset/stocktaking/report/trend?startDate=2024-01-01&endDate=2025-01-31" \
  -H "Authorization: Bearer ${token}"
```

**预期结果**: 返回趋势分析报告

## 6. 综合测试场景

### 6.1 完整盘点流程测试
```bash
#!/bin/bash

# 设置基础变量
baseUrl="http://localhost:8080"
token="your_jwt_token_here"

echo "=== 开始完整盘点流程测试 ==="

# 1. 创建盘点计划
echo "1. 创建盘点计划..."
planResponse=$(curl -s -X POST "${baseUrl}/asset/stocktaking/plan" \
  -H "Authorization: Bearer ${token}" \
  -H "Content-Type: application/json" \
  -d '{
    "planName": "自动化测试盘点计划",
    "planType": 1,
    "startDate": "2025-01-15",
    "endDate": "2025-01-30",
    "responsibleUserId": 1,
    "remark": "自动化测试"
  }')

planId=$(echo $planResponse | jq -r '.data')
echo "计划ID: $planId"

# 2. 启动计划
echo "2. 启动盘点计划..."
curl -s -X POST "${baseUrl}/asset/stocktaking/plan/start/${planId}" \
  -H "Authorization: Bearer ${token}"

# 3. 创建任务
echo "3. 创建盘点任务..."
taskResponse=$(curl -s -X POST "${baseUrl}/asset/stocktaking/task" \
  -H "Authorization: Bearer ${token}" \
  -H "Content-Type: application/json" \
  -d '{
    "planId": "'$planId'",
    "taskName": "自动化测试任务",
    "assignedUserId": 2,
    "expectedCount": 10
  }')

taskId=$(echo $taskResponse | jq -r '.data')
echo "任务ID: $taskId"

# 4. 开始执行任务
echo "4. 开始执行任务..."
curl -s -X POST "${baseUrl}/asset/stocktaking/task/start/${taskId}" \
  -H "Authorization: Bearer ${token}"

# 5. 创建盘点记录
echo "5. 创建盘点记录..."
curl -s -X POST "${baseUrl}/asset/stocktaking/record" \
  -H "Authorization: Bearer ${token}" \
  -H "Content-Type: application/json" \
  -d '{
    "taskId": "'$taskId'",
    "assetId": "TEST001",
    "assetCode": "T001",
    "foundStatus": 1,
    "actualLocation": "测试位置",
    "actualStatus": 1,
    "inventoryTime": "2025-01-15 10:00:00"
  }'

# 6. 完成任务
echo "6. 完成任务..."
curl -s -X POST "${baseUrl}/asset/stocktaking/task/complete/${taskId}" \
  -H "Authorization: Bearer ${token}"

# 7. 完成计划
echo "7. 完成计划..."
curl -s -X POST "${baseUrl}/asset/stocktaking/plan/complete/${planId}" \
  -H "Authorization: Bearer ${token}"

# 8. 生成报告
echo "8. 生成报告..."
curl -s -X GET "${baseUrl}/asset/stocktaking/report/summary/${planId}" \
  -H "Authorization: Bearer ${token}"

echo "=== 完整盘点流程测试完成 ==="
```

### 6.2 异常情况测试
```bash
#!/bin/bash

echo "=== 异常情况测试 ==="

# 测试无效参数
echo "1. 测试创建计划时缺少必填参数..."
curl -X POST "${baseUrl}/asset/stocktaking/plan" \
  -H "Authorization: Bearer ${token}" \
  -H "Content-Type: application/json" \
  -d '{
    "planName": "",
    "planType": null
  }'

# 测试不存在的资源
echo "2. 测试查询不存在的计划..."
curl -X GET "${baseUrl}/asset/stocktaking/plan/INVALID_ID" \
  -H "Authorization: Bearer ${token}"

# 测试权限验证
echo "3. 测试无权限访问..."
curl -X GET "${baseUrl}/asset/stocktaking/plan/list" \
  -H "Content-Type: application/json"

echo "=== 异常情况测试完成 ==="
```

## 7. 性能测试

### 7.1 并发创建记录测试
```bash
#!/bin/bash

echo "=== 并发性能测试 ==="

# 并发创建100条盘点记录
for i in {1..100}; do
  {
    curl -s -X POST "${baseUrl}/asset/stocktaking/record" \
      -H "Authorization: Bearer ${token}" \
      -H "Content-Type: application/json" \
      -d '{
        "taskId": "'$taskId'",
        "assetId": "PERF'$i'",
        "assetCode": "P'$i'",
        "foundStatus": 1,
        "actualLocation": "性能测试位置'$i'",
        "actualStatus": 1,
        "inventoryTime": "2025-01-15 10:00:00"
      }'
  } &
done

wait
echo "并发创建记录测试完成"
```

### 7.2 大数据量查询测试
```bash
# 查询大量数据
time curl -X POST "${baseUrl}/asset/stocktaking/record/list" \
  -H "Authorization: Bearer ${token}" \
  -H "Content-Type: application/json" \
  -d '{
    "pageNum": 1,
    "pageSize": 1000
  }'
```

## 8. 测试结果验证

### 8.1 数据一致性验证
```bash
#!/bin/bash

echo "=== 数据一致性验证 ==="

# 验证计划状态
planStatus=$(curl -s -X GET "${baseUrl}/asset/stocktaking/plan/${planId}" \
  -H "Authorization: Bearer ${token}" | jq -r '.data.status')

if [ "$planStatus" = "4" ]; then
  echo "✓ 计划状态正确 (已完成)"
else
  echo "✗ 计划状态异常: $planStatus"
fi

# 验证任务进度
progress=$(curl -s -X GET "${baseUrl}/asset/stocktaking/record/progress/${taskId}" \
  -H "Authorization: Bearer ${token}" | jq -r '.data.completionRate')

echo "任务完成进度: $progress%"

# 验证记录数量
recordCount=$(curl -s -X POST "${baseUrl}/asset/stocktaking/record/list" \
  -H "Authorization: Bearer ${token}" \
  -H "Content-Type: application/json" \
  -d '{"taskId": "'$taskId'"}' | jq -r '.total')

echo "记录总数: $recordCount"
```

### 8.2 接口响应时间验证
```bash
#!/bin/bash

echo "=== 接口响应时间验证 ==="

# 测试各接口响应时间
endpoints=(
  "GET /asset/stocktaking/plan/list"
  "GET /asset/stocktaking/task/list"
  "GET /asset/stocktaking/record/list"
  "GET /asset/stocktaking/difference/list"
)

for endpoint in "${endpoints[@]}"; do
  method=$(echo $endpoint | cut -d' ' -f1)
  path=$(echo $endpoint | cut -d' ' -f2)

  response_time=$(curl -o /dev/null -s -w "%{time_total}" \
    -X $method "${baseUrl}${path}" \
    -H "Authorization: Bearer ${token}" \
    -H "Content-Type: application/json" \
    -d '{"pageNum": 1, "pageSize": 10}')

  echo "$endpoint: ${response_time}s"
done
```

## 9. 测试报告模板

### 9.1 测试执行记录
```
测试日期: 2025-01-15
测试环境: 开发环境
测试人员: [测试人员姓名]

| 测试模块 | 测试用例 | 执行结果 | 响应时间 | 备注 |
|---------|---------|---------|---------|------|
| 盘点计划 | 创建计划 | ✓ 通过 | 0.2s | - |
| 盘点计划 | 查询列表 | ✓ 通过 | 0.1s | - |
| 盘点计划 | 更新计划 | ✓ 通过 | 0.15s | - |
| 盘点任务 | 创建任务 | ✓ 通过 | 0.3s | - |
| 盘点任务 | 执行任务 | ✓ 通过 | 0.1s | - |
| 盘点记录 | 创建记录 | ✓ 通过 | 0.25s | - |
| 盘点记录 | 批量创建 | ✓ 通过 | 0.5s | - |
| 盘点差异 | 创建差异 | ✓ 通过 | 0.2s | - |
| 盘点差异 | 处理差异 | ✓ 通过 | 0.3s | - |
| 盘点报告 | 生成报告 | ✓ 通过 | 1.2s | - |
```

### 9.2 问题记录
```
| 问题编号 | 问题描述 | 严重程度 | 状态 | 备注 |
|---------|---------|---------|------|------|
| BUG001 | 创建计划时日期验证异常 | 中 | 已修复 | - |
| BUG002 | 批量处理差异响应慢 | 低 | 待优化 | - |
```

## 10. 使用说明

### 10.1 环境准备
1. 确保服务已启动并可访问
2. 获取有效的JWT Token
3. 准备测试数据（用户、部门、资产等）
4. 安装curl和jq工具

### 10.2 执行步骤
1. 修改脚本中的baseUrl和token变量
2. 根据实际环境调整测试数据
3. 按模块顺序执行测试脚本
4. 记录测试结果和问题
5. 生成测试报告

### 10.3 注意事项
- 测试前备份数据库
- 使用独立的测试环境
- 及时清理测试数据
- 关注接口权限设置
- 监控系统性能指标
