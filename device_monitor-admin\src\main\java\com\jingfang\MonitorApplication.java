package com.jingfang;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 启动程序
 * 
 * <AUTHOR>
 */
@EnableAsync
@ComponentScan(basePackages = {"com.jingfang","com.jingfang.flowable"})
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
public class MonitorApplication
{
    public static void main(String[] args)
    {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication.run(MonitorApplication.class, args);
        System.out.println("启动成功");

    }
}
