package com.jingfang.wh_item.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.wh_item.module.dto.ItemOutboundDto;
import com.jingfang.wh_item.module.request.ItemOutboundSearchRequest;
import com.jingfang.wh_item.module.vo.ItemOutboundVo;

public interface ItemOutboundService {

    /**
     * 新增物品出库单
     */
    String addItemOutbound(ItemOutboundDto outboundDto, String username);

    /**
     * 编辑物品出库单
     */
    void updateItemOutbound(String outboundId, ItemOutboundDto outboundDto, String username);

    /**
     * 获取出库单详情
     */
    ItemOutboundVo getOutboundDetail(String outboundId);

    /**
     * 查询出库单列表
     */
    IPage<ItemOutboundVo> selectOutboundList(ItemOutboundSearchRequest request);

    /**
     * 提交出库单待确认
     */
    void submitOutbound(String outboundId, String username);

    /**
     * 经手人确认出库单
     */
    void handleOutbound(String outboundId, String remark, String username);

    /**
     * 审核出库单
     */
    void auditOutbound(String outboundId, Integer status, String remark, String username);

    /**
     * 删除出库单
     */
    void deleteOutbound(String[] outboundIds, String username);
} 