<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.wh_item.mapper.ItemOutboundDetailMapper">

    <resultMap id="BaseResultMap" type="com.jingfang.wh_item.module.entity.ItemOutboundDetail">
        <id property="detailId" column="detail_id" jdbcType="BIGINT"/>
        <result property="outboundId" column="outbound_id" jdbcType="VARCHAR"/>
        <result property="itemId" column="item_id" jdbcType="VARCHAR"/>
        <result property="batchNo" column="batch_no" jdbcType="VARCHAR"/>
        <result property="productionDate" column="production_date" jdbcType="DATE"/>
        <result property="expiryDate" column="expiry_date" jdbcType="DATE"/>
        <result property="quantity" column="quantity" jdbcType="DECIMAL"/>
        <result property="unitPrice" column="unit_price" jdbcType="DECIMAL"/>
        <result property="amount" column="amount" jdbcType="DECIMAL"/>
        <result property="warehouseId" column="warehouse_id" jdbcType="INTEGER"/>
        <result property="shelfLocation" column="shelf_location" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="ItemOutboundDetailVoResultMap" type="com.jingfang.wh_item.module.vo.ItemOutboundDetailVo">
        <id property="detailId" column="detail_id" jdbcType="BIGINT"/>
        <result property="outboundId" column="outbound_id" jdbcType="VARCHAR"/>
        <result property="itemId" column="item_id" jdbcType="VARCHAR"/>
        <result property="itemName" column="item_name" jdbcType="VARCHAR"/>
        <result property="itemCode" column="item_code" jdbcType="VARCHAR"/>
        <result property="itemType" column="item_type" jdbcType="INTEGER"/>
        <result property="itemTypeName" column="item_type_name" jdbcType="VARCHAR"/>
        <result property="specModel" column="spec_model" jdbcType="VARCHAR"/>
        <result property="unit" column="unit" jdbcType="VARCHAR"/>
        <result property="imageUrl" column="image_url" jdbcType="VARCHAR"/>
        <result property="batchNo" column="batch_no" jdbcType="VARCHAR"/>
        <result property="productionDate" column="production_date" jdbcType="DATE"/>
        <result property="expiryDate" column="expiry_date" jdbcType="DATE"/>
        <result property="quantity" column="quantity" jdbcType="DECIMAL"/>
        <result property="unitPrice" column="unit_price" jdbcType="DECIMAL"/>
        <result property="amount" column="amount" jdbcType="DECIMAL"/>
        <result property="warehouseId" column="warehouse_id" jdbcType="INTEGER"/>
        <result property="warehouseName" column="warehouse_name" jdbcType="VARCHAR"/>
        <result property="shelfLocation" column="shelf_location" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        detail_id, outbound_id, item_id,
        batch_no, production_date, expiry_date, quantity, unit_price, amount,
        warehouse_id, shelf_location, remark, create_time, update_time
    </sql>

    <!-- 根据出库单ID查询明细列表（包含物品基本信息） -->
    <select id="selectDetailsByOutboundId" parameterType="string" resultMap="ItemOutboundDetailVoResultMap">
        SELECT 
            d.detail_id, d.outbound_id, d.item_id,
            i.item_name, i.item_code, i.item_type, i.spec_model, i.unit, i.image_url,
            CASE i.item_type 
                WHEN 1 THEN '消耗品' 
                WHEN 2 THEN '备品备件' 
                ELSE '未知' 
            END as item_type_name,
            d.batch_no, d.production_date, d.expiry_date, 
            d.quantity, d.unit_price, d.amount,
            d.warehouse_id,
            CASE d.warehouse_id 
                WHEN 1 THEN '主仓库'
                WHEN 2 THEN '备件仓库'
                WHEN 3 THEN '临时仓库'
                ELSE CONCAT('仓库', d.warehouse_id)
            END as warehouse_name,
            d.shelf_location, d.remark
        FROM 
            item_outbound_detail d
            LEFT JOIN item_base_info i ON d.item_id = i.item_id
        WHERE 
            d.outbound_id = #{outboundId}
        ORDER BY d.detail_id
    </select>

</mapper> 