# 设备监控系统登录功能技术文档

## 📋 文档信息

| 项目名称 | 设备监控系统 |
|---------|-------------|
| 框架版本 | RuoYi 3.x |
| 文档版本 | v1.0 |
| 创建日期 | 2025-01-07 |
| 更新日期 | 2025-01-07 |

## 📚 目录

- [🎯 概述](#-概述)
- [🏗️ 技术架构](#️-技术架构)
- [🔐 后端实现](#-后端实现)
  - [1. 登录接口](#1-登录接口)
  - [2. 核心类说明](#2-核心类说明)
  - [3. 登录验证流程](#3-登录验证流程)
  - [4. 安全配置](#4-安全配置)
- [🖥️ 前端实现](#️-前端实现)
  - [1. 登录页面](#1-登录页面)
  - [2. API接口](#2-api接口)
  - [3. 状态管理](#3-状态管理)
  - [4. 路由守卫](#4-路由守卫)
  - [5. HTTP拦截器](#5-http拦截器)
- [🔧 配置说明](#-配置说明)
- [🚀 部署说明](#-部署说明)
- [🔍 测试说明](#-测试说明)
- [🔒 安全特性](#-安全特性)
- [🔧 扩展功能](#-扩展功能)
- [🔍 监控与日志](#-监控与日志)
- [🐛 常见问题](#-常见问题)
- [📊 性能指标](#-性能指标)
- [📞 技术支持](#-技术支持)

## 🎯 概述

本系统基于RuoYi框架实现了完整的用户认证体系，采用**JWT Token + Spring Security + Redis缓存**的技术方案，支持Web端和微信小程序多端登录。

### 核心特性
- ✅ **安全认证**: JWT Token + Spring Security双重保障
- ✅ **多端支持**: Web端、微信小程序、移动端适配
- ✅ **验证码防护**: 图形验证码 + 数学运算验证码
- ✅ **会话管理**: Redis缓存 + 自动刷新机制
- ✅ **权限控制**: RBAC角色权限管理
- ✅ **安全防护**: IP黑名单、防重复提交、密码加密
- ✅ **监控日志**: 登录日志、操作日志、在线用户监控

## 🚀 快速开始

### 开发环境搭建
1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd device_monitor
   ```

2. **后端启动**
   ```bash
   # 启动Redis
   redis-server

   # 导入数据库
   mysql -u root -p < sql/device_monitor.sql

   # 启动后端服务
   cd device_monitor-admin
   mvn spring-boot:run
   ```

3. **前端启动**
   ```bash
   cd device_monitor-ui
   npm install
   npm run dev
   ```

4. **访问系统**
   - 前端地址: http://localhost:80
   - 后端地址: http://localhost:8080
   - 默认账号: admin / admin123

### 核心文件位置
```
device_monitor/
├── device_monitor-admin/
│   └── src/main/java/com/jingfang/web/controller/system/
│       └── SysLoginController.java          # 登录控制器
├── device_monitor-framework/
│   └── src/main/java/com/jingfang/framework/
│       ├── web/service/
│       │   ├── SysLoginService.java         # 登录服务
│       │   ├── TokenService.java            # Token服务
│       │   └── UserDetailsServiceImpl.java  # 用户详情服务
│       ├── security/filter/
│       │   └── JwtAuthenticationTokenFilter.java # JWT过滤器
│       └── config/
│           └── SecurityConfig.java          # 安全配置
└── device_monitor-ui/
    └── src/
        ├── views/login.vue                  # 登录页面
        ├── api/login.js                     # 登录API
        ├── store/modules/user.js            # 用户状态管理
        ├── permission.js                    # 路由守卫
        └── utils/
            ├── auth.js                      # Token工具
            └── request.js                   # HTTP拦截器
```

## 🏗️ 技术架构

### 核心技术栈
- **后端框架**: Spring Boot + Spring Security
- **认证方式**: JWT Token
- **缓存系统**: Redis
- **前端框架**: Vue.js + Element UI
- **状态管理**: Vuex
- **路由管理**: Vue Router

### 架构图
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   前端页面   │───▶│  路由守卫    │───▶│  HTTP拦截器  │
└─────────────┘    └─────────────┘    └─────────────┘
       │                                      │
       ▼                                      ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  登录API    │───▶│ 登录控制器   │───▶│  登录服务    │
└─────────────┘    └─────────────┘    └─────────────┘
                                             │
                                             ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    Redis    │◀───│ Token服务   │◀───│Spring Security│
└─────────────┘    └─────────────┘    └─────────────┘
```

## 🔐 后端实现

### 1. 登录接口

**接口地址**: `POST /login`

**请求参数**:
```json
{
  "username": "admin",      // 用户名
  "password": "admin123",   // 密码
  "code": "1234",          // 验证码
  "uuid": "uuid-string"    // 验证码唯一标识
}
```

**响应数据**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "token": "eyJhbGciOiJIUzUxMiJ9..."
}
```

### 2. 核心类说明

#### SysLoginController
- **路径**: `device_monitor-admin/src/main/java/com/jingfang/web/controller/system/SysLoginController.java`
- **功能**: 处理登录请求，调用登录服务生成Token

#### SysLoginService
- **路径**: `device_monitor-framework/src/main/java/com/jingfang/framework/web/service/SysLoginService.java`
- **功能**: 登录核心业务逻辑
- **主要方法**:
  - `login()`: 登录验证主流程
  - `validateCaptcha()`: 验证码校验
  - `loginPreCheck()`: 登录前置校验

#### TokenService
- **路径**: `device_monitor-framework/src/main/java/com/jingfang/framework/web/service/TokenService.java`
- **功能**: JWT Token管理
- **主要方法**:
  - `createToken()`: 创建Token
  - `verifyToken()`: 验证Token
  - `refreshToken()`: 刷新Token

#### UserDetailsServiceImpl
- **路径**: `device_monitor-framework/src/main/java/com/jingfang/framework/web/service/UserDetailsServiceImpl.java`
- **功能**: Spring Security用户详情服务
- **主要方法**:
  - `loadUserByUsername()`: 根据用户名加载用户信息

### 3. 登录验证流程

```mermaid
sequenceDiagram
    participant C as 客户端
    participant LC as 登录控制器
    participant LS as 登录服务
    participant US as 用户服务
    participant TS as Token服务
    participant R as Redis

    C->>LC: POST /login
    LC->>LS: login(username, password, code, uuid)
    LS->>LS: validateCaptcha() 验证码校验
    LS->>LS: loginPreCheck() 前置校验
    LS->>US: authenticate() Spring Security认证
    US->>US: loadUserByUsername() 加载用户信息
    US-->>LS: 返回认证结果
    LS->>TS: createToken() 生成Token
    TS->>R: 缓存用户信息
    TS-->>LS: 返回Token
    LS-->>LC: 返回Token
    LC-->>C: 返回登录结果
```

### 4. 安全配置

#### SecurityConfig
- **路径**: `device_monitor-framework/src/main/java/com/jingfang/framework/config/SecurityConfig.java`
- **配置项**:
  - 密码加密器: BCryptPasswordEncoder
  - 白名单路径: `/login`, `/register`, `/captchaImage`
  - JWT过滤器: JwtAuthenticationTokenFilter

#### JwtAuthenticationTokenFilter
- **路径**: `device_monitor-framework/src/main/java/com/jingfang/framework/security/filter/JwtAuthenticationTokenFilter.java`
- **功能**: 每次请求验证JWT Token，设置Spring Security上下文

## 🖥️ 前端实现

### 1. 登录页面

**文件路径**: `device_monitor-ui/src/views/login.vue`

**主要功能**:
- 用户名密码输入
- 图形验证码显示
- 记住密码功能
- 登录状态管理

### 2. API接口

**文件路径**: `device_monitor-ui/src/api/login.js`

**主要接口**:
```javascript
// 登录
export function login(username, password, code, uuid)

// 获取用户信息
export function getInfo()

// 退出登录
export function logout()

// 获取验证码
export function getCodeImg()
```

### 3. 状态管理

**文件路径**: `device_monitor-ui/src/store/modules/user.js`

**主要Actions**:
- `Login`: 执行登录操作
- `GetInfo`: 获取用户信息
- `LogOut`: 退出登录

### 4. 路由守卫

**文件路径**: `device_monitor-ui/src/permission.js`

**守卫逻辑**:
1. 检查Token是否存在
2. 已登录用户访问登录页跳转首页
3. 未登录用户重定向到登录页
4. 动态加载用户权限路由

### 5. HTTP拦截器

**文件路径**: `device_monitor-ui/src/utils/request.js`

**拦截功能**:
- 请求拦截: 自动添加Authorization头
- 响应拦截: 处理401未授权状态
- 错误处理: 统一错误提示

## 🔧 配置说明

### 1. Token配置

**配置文件**: `application.yml`
```yaml
# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30
```

### 2. 验证码配置

**配置类**: `CaptchaConfig.java`
- 支持字符验证码和数学运算验证码
- 可通过系统参数控制开关
- 验证码存储在Redis中，有效期5分钟

### 3. Redis配置

**用途**:
- 验证码缓存: `captcha_codes:uuid`
- 用户Token缓存: `login_tokens:token`
- 在线用户缓存: `sys_user_online:token`

## 🚀 部署说明

### 1. 环境要求
- JDK 8+
- Redis 3.0+
- MySQL 5.7+
- Node.js 12+

### 2. 配置步骤

1. **数据库配置**
   ```yaml
   spring:
     datasource:
       url: ******************************************
       username: root
       password: password
   ```

2. **Redis配置**
   ```yaml
   spring:
     redis:
       host: localhost
       port: 6379
       password: 
       database: 0
   ```

3. **前端配置**
   ```javascript
   // .env.development
   VUE_APP_BASE_API = 'http://localhost:8080'
   VUE_APP_TITLE = '设备监控系统'
   ```

## 📡 API接口文档

### 1. 登录相关接口

#### 1.1 用户登录
- **接口地址**: `POST /login`
- **请求参数**:
  ```json
  {
    "username": "admin",      // 用户名，必填
    "password": "admin123",   // 密码，必填
    "code": "1234",          // 验证码，可选
    "uuid": "uuid-string"    // 验证码UUID，可选
  }
  ```
- **响应示例**:
  ```json
  {
    "code": 200,
    "msg": "操作成功",
    "token": "eyJhbGciOiJIUzUxMiJ9..."
  }
  ```

#### 1.2 获取验证码
- **接口地址**: `GET /captchaImage`
- **响应示例**:
  ```json
  {
    "code": 200,
    "msg": "操作成功",
    "captchaEnabled": true,
    "uuid": "uuid-string",
    "img": "base64-image-data"
  }
  ```

#### 1.3 获取用户信息
- **接口地址**: `GET /getInfo`
- **请求头**: `Authorization: Bearer {token}`
- **响应示例**:
  ```json
  {
    "code": 200,
    "msg": "操作成功",
    "user": {
      "userId": 1,
      "userName": "admin",
      "nickName": "管理员",
      "email": "<EMAIL>",
      "phonenumber": "15888888888",
      "sex": "1",
      "avatar": "",
      "status": "0"
    },
    "roles": ["admin"],
    "permissions": ["*:*:*"]
  }
  ```

#### 1.4 用户登出
- **接口地址**: `POST /logout`
- **请求头**: `Authorization: Bearer {token}`
- **响应示例**:
  ```json
  {
    "code": 200,
    "msg": "退出成功"
  }
  ```

#### 1.5 获取路由信息
- **接口地址**: `GET /getRouters`
- **请求头**: `Authorization: Bearer {token}`
- **响应示例**:
  ```json
  {
    "code": 200,
    "msg": "操作成功",
    "data": [
      {
        "name": "System",
        "path": "/system",
        "component": "Layout",
        "meta": {
          "title": "系统管理",
          "icon": "system"
        },
        "children": [...]
      }
    ]
  }
  ```

### 2. 错误码说明

| 错误码 | 说明 | 处理方式 |
|-------|------|---------|
| 200 | 操作成功 | 正常处理 |
| 401 | 未授权 | 跳转登录页 |
| 403 | 权限不足 | 提示权限不足 |
| 500 | 服务器错误 | 提示系统异常 |
| 601 | 警告信息 | 显示警告提示 |

### 3. 微信小程序接口

#### 3.1 微信登录
- **接口地址**: `POST /wx/login`
- **请求参数**:
  ```json
  {
    "code": "wx-login-code",
    "userInfo": {
      "nickName": "用户昵称",
      "avatarUrl": "头像地址"
    }
  }
  ```

#### 3.2 绑定系统账号
- **接口地址**: `POST /wx/bind`
- **请求参数**:
  ```json
  {
    "openid": "wx-openid",
    "username": "admin",
    "password": "admin123"
  }
  ```

## 🔍 测试说明

### 1. 默认账号
- **管理员**: admin / admin123
- **普通用户**: test / test123

### 2. 测试用例

#### 登录成功测试
```bash
curl -X POST http://localhost:8080/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123",
    "code": "1234",
    "uuid": "test-uuid"
  }'
```

#### Token验证测试
```bash
curl -X GET http://localhost:8080/getInfo \
  -H "Authorization: Bearer your-token-here"
```

### 3. 前端测试
```javascript
// 登录测试
this.$store.dispatch('Login', {
  username: 'admin',
  password: 'admin123',
  code: '1234',
  uuid: 'test-uuid'
}).then(() => {
  console.log('登录成功');
}).catch(error => {
  console.error('登录失败:', error);
});

// Token验证测试
this.$store.dispatch('GetInfo').then(res => {
  console.log('用户信息:', res.user);
}).catch(error => {
  console.error('获取用户信息失败:', error);
});
```

## 🔒 安全特性

### 1. 密码安全
- **加密算法**: BCrypt哈希加密
- **密码强度**: 支持长度限制（5-20位）
- **密码存储**: 前端记住密码使用RSA加密存储

### 2. 验证码机制
- **类型支持**: 字符验证码、数学运算验证码
- **防刷机制**: Redis缓存，5分钟有效期
- **可配置**: 通过系统参数控制开关

### 3. 会话安全
- **Token机制**: JWT + UUID双重标识
- **自动刷新**: 20分钟内活动自动延期
- **单点登录**: 支持踢出其他设备登录

### 4. 访问控制
- **IP黑名单**: 支持IP地址黑名单限制
- **防重复提交**: 1秒内相同请求自动拦截
- **权限控制**: 基于RBAC的细粒度权限管理

## 🔧 扩展功能

### 1. 微信小程序登录
- **文件路径**: `device_module/src/main/java/com/jingfang/wechat/service/impl/SysWxLoginServiceImpl.java`
- **功能**: 支持微信登录和系统账号绑定
- **接口**: `/wx/login`, `/wx/bind`

### 2. 第三方登录扩展
系统预留了第三方登录接口，可扩展支持：
- QQ登录
- 微博登录
- 钉钉登录
- 企业微信登录

### 3. 多端适配
- **Web端**: 标准浏览器登录
- **移动端**: 响应式设计适配
- **小程序**: 微信小程序专用登录
- **APP**: 支持Electron桌面应用

## 🔍 监控与日志

### 1. 登录日志
- **表名**: `sys_logininfor`
- **记录内容**: 登录时间、IP地址、浏览器、操作系统、登录结果
- **查询接口**: `/monitor/logininfor/list`

### 2. 在线用户监控
- **表名**: `sys_user_online`
- **功能**: 实时查看在线用户，支持强制下线
- **管理接口**: `/monitor/online/list`, `/monitor/online/forceLogout`

### 3. 操作日志
- **表名**: `sys_oper_log`
- **记录范围**: 所有用户操作行为
- **查询接口**: `/monitor/operlog/list`

## 🐛 常见问题

### 1. 登录失败排查

#### 验证码问题
```bash
# 检查Redis中验证码
redis-cli
> get captcha_codes:your-uuid
```

#### 用户状态检查
```sql
-- 检查用户状态
SELECT user_name, status, del_flag FROM sys_user WHERE user_name = 'admin';
-- status: 0正常 1停用
-- del_flag: 0正常 2删除
```

#### 密码验证
```java
// 密码加密验证（开发调试用）
BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
boolean matches = encoder.matches("plainPassword", "encodedPassword");
```

### 2. Token问题排查

#### Token格式检查
```javascript
// JWT Token解析（前端调试）
const token = 'your-jwt-token';
const payload = JSON.parse(atob(token.split('.')[1]));
console.log(payload);
```

#### Redis缓存检查
```bash
# 检查用户Token缓存
redis-cli
> get login_tokens:your-token
> ttl login_tokens:your-token  # 查看过期时间
```

### 3. 前端问题排查

#### 网络请求检查
```javascript
// 检查请求头
console.log('Token:', getToken());
console.log('Request Headers:', config.headers);
```

#### 路由跳转调试
```javascript
// 在permission.js中添加调试日志
console.log('Current Route:', to.path);
console.log('Has Token:', !!getToken());
console.log('User Roles:', store.getters.roles);
```

### 4. 性能优化建议

#### Redis连接池配置
```yaml
spring:
  redis:
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
```

#### Token缓存优化
```java
// 建议Token过期时间设置
private int expireTime = 30; // 30分钟
private static final int MILLIS_MINUTE_TWENTY = 20 * 60 * 1000; // 20分钟自动刷新
```

## 📊 性能指标

### 1. 响应时间
- **登录接口**: < 500ms
- **Token验证**: < 100ms
- **用户信息获取**: < 200ms

### 2. 并发能力
- **登录并发**: 支持1000+并发登录
- **Token验证**: 支持10000+并发验证
- **Redis缓存**: 支持100000+读写操作

### 3. 缓存命中率
- **用户信息缓存**: > 95%
- **权限信息缓存**: > 90%
- **验证码缓存**: 100%

## 📞 技术支持

### 开发团队联系方式
- **技术负责人**: [姓名] - [邮箱]
- **前端开发**: [姓名] - [邮箱]
- **后端开发**: [姓名] - [邮箱]

### 相关文档链接
- **RuoYi官方文档**: http://doc.ruoyi.vip
- **Spring Security文档**: https://spring.io/projects/spring-security
- **Vue.js官方文档**: https://vuejs.org
- **Element UI文档**: https://element.eleme.cn
- **Redis官方文档**: https://redis.io/documentation

### 问题反馈
- **Bug报告**: 请提供详细的错误日志和复现步骤
- **功能建议**: 请描述具体需求和使用场景
- **性能问题**: 请提供相关的性能数据和环境信息

---

**文档维护**: 开发团队
**创建时间**: 2025-01-07
**最后更新**: 2025-01-07
**文档版本**: v1.0
