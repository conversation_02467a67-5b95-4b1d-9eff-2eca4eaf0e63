package com.jingfang.wh_item.module.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 物品库存统计VO
 */
@Data
public class ItemStockStatisticsVo implements Serializable {
    
    /**
     * 物品ID
     */
    private String itemId;
    
    /**
     * 物品名称
     */
    private String itemName;
    
    /**
     * 物品编码
     */
    private String itemCode;
    
    /**
     * 物品类型(1-消耗品, 2-备品备件)
     */
    private Integer itemType;
    
    /**
     * 物品类型名称
     */
    private String itemTypeName;
    
    /**
     * 规格型号
     */
    private String specModel;
    
    /**
     * 单位
     */
    private String unit;
    
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    
    /**
     * 仓库名称（通过数据字典获取）
     */
    private String warehouseName;
    
    /**
     * 货架位置/库位
     */
    private String shelfLocation;
    
    /**
     * 当前库存数量
     */
    private BigDecimal currentQuantity;
    
    /**
     * 安全库存
     */
    private BigDecimal safetyStock;
    
    /**
     * 库存状态(1-正常, 2-不足, 3-过剩)
     */
    private Integer stockStatus;
    
    /**
     * 库存状态名称
     */
    private String stockStatusName;
    
    private static final long serialVersionUID = 1L;
} 