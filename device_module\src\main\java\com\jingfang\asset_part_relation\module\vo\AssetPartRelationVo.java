package com.jingfang.asset_part_relation.module.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 资产备品备件关联VO
 */
@Data
public class AssetPartRelationVo implements Serializable {
    
    /**
     * 关联ID
     */
    private String relationId;
    
    /**
     * 资产ID
     */
    private String assetId;
    
    /**
     * 资产名称
     */
    private String assetName;
    
    /**
     * 备品备件ID
     */
    private String partId;
    
    /**
     * 备品备件名称
     */
    private String partName;
    
    /**
     * 备品备件编码
     */
    private String partCode;
    
    /**
     * 规格型号
     */
    private String specModel;
    
    /**
     * 单位
     */
    private String unit;
    
    /**
     * 关联类型(1-必需, 2-推荐, 3-可选)
     */
    private Integer relationType;
    
    /**
     * 关联类型名称
     */
    private String relationTypeName;
    
    /**
     * 建议库存数量
     */
    private Integer suggestedQuantity;
    
    /**
     * 当前总库存数量
     */
    private BigDecimal currentQuantity;
    
    /**
     * 库存状态(1-正常, 2-不足, 3-过剩)
     */
    private Integer stockStatus;
    
    /**
     * 库存状态名称
     */
    private String stockStatusName;
    
    /**
     * 备件分类(1-关键, 2-常用, 3-次要)
     */
    private Integer partCategory;
    
    /**
     * 备件分类名称
     */
    private String partCategoryName;
    
    /**
     * 建议更换周期(天)
     */
    private Integer replacementCycle;
    
    /**
     * 备注说明
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    private static final long serialVersionUID = 1L;
} 