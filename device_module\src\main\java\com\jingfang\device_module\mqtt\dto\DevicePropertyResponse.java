package com.jingfang.device_module.mqtt.dto;

import lombok.Data;

import java.util.List;

/**
 * MQTT设备属性查询响应DTO
 */
@Data
public class DevicePropertyResponse {
    
    /**
     * 响应ID，对应请求ID
     */
    private String id;
    
    /**
     * 版本号
     */
    private String version;
    
    /**
     * 响应代码，0表示成功
     */
    private Integer code;
    
    /**
     * 参数列表
     */
    private List<DevicePropertyParam> params;
    
    /**
     * 设备属性参数
     */
    @Data
    public static class DevicePropertyParam {
        /**
         * 客户端ID（设备IP地址）
         */
        private String clientID;
        
        /**
         * 属性列表
         */
        private List<PropertyValue> properties;
    }
    
    /**
     * 属性值信息
     */
    @Data
    public static class PropertyValue {
        /**
         * 属性名称
         */
        private String name;
        
        /**
         * 属性值
         */
        private Object value;
        
        /**
         * 时间戳
         */
        private Long timestamp;
    }
}
