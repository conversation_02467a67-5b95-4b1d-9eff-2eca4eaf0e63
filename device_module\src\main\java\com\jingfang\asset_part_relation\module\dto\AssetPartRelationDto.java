package com.jingfang.asset_part_relation.module.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 资产备品备件关联DTO
 */
@Data
public class AssetPartRelationDto implements Serializable {
    
    /**
     * 关联ID（编辑时使用）
     */
    private String relationId;
    
    /**
     * 资产ID
     */
    private String assetId;
    
    /**
     * 备品备件ID
     */
    private String partId;
    
    /**
     * 关联类型(1-必需, 2-推荐, 3-可选)
     */
    private Integer relationType;
    
    /**
     * 建议库存数量
     */
    private Integer suggestedQuantity;
    
    /**
     * 备注说明
     */
    private String remark;
    
    private static final long serialVersionUID = 1L;
}

/**
 * 批量关联DTO
 */
@Data
class BatchAssetPartRelationDto implements Serializable {
    
    /**
     * 资产ID
     */
    private String assetId;
    
    /**
     * 备品备件关联列表
     */
    private List<AssetPartRelationDto> relations;
    
    private static final long serialVersionUID = 1L;
} 