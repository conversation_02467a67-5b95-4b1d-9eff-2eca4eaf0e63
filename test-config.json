{"testConfig": {"baseUrl": "http://localhost:8080", "token": "your_jwt_token_here", "timeout": 30, "retryCount": 3, "testData": {"testItemCode": "TEST001", "testStocktakingId": "test_stocktaking_id", "testItemId": "test_item_id", "testDetailId": "test_detail_id", "testWarehouseId": 1}, "expectedResponses": {"myTasks": {"code": 200, "msg": "操作成功", "dataFields": ["stocktakingId", "stocktakingCode", "stocktakingName", "status", "totalItems"]}, "itemByCode": {"code": 200, "msg": "查询成功", "dataFields": ["itemId", "itemName", "itemCode", "specModel", "unit"]}, "stocktakingDetail": {"code": 200, "msg": "操作成功", "dataFields": ["detailId", "stocktakingId", "itemId", "warehouseId", "bookQuantity"]}, "updateDetail": {"code": 200, "msg": "更新盘点明细成功"}, "progress": {"code": 200, "msg": "操作成功", "dataFields": ["totalItems", "completedItems", "completionRate"]}, "myProgress": {"code": 200, "msg": "操作成功", "dataFields": ["totalAssigned", "completed", "completionRate"]}, "myRecords": {"code": 200, "msg": "操作成功", "dataFields": ["detailId", "itemName", "itemCode", "checkTime"]}, "itemHistory": {"code": 200, "msg": "操作成功", "dataFields": ["detailId", "stocktakingId", "itemName", "checkTime"]}}, "errorScenarios": {"itemNotFound": {"code": 500, "msg": "未找到对应的物品信息"}, "detailNotFound": {"code": 500, "msg": "未找到对应的盘点明细"}, "updateFailed": {"code": 500, "msg": "更新盘点明细失败"}}}}