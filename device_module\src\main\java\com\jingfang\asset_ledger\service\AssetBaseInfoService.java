package com.jingfang.asset_ledger.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.asset_ledger.module.entity.AssetBaseInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jingfang.asset_ledger.module.request.AssetSearchRequest;
import com.jingfang.asset_ledger.module.vo.AssetBaseInfoVo;
import com.jingfang.asset_ledger.module.vo.AssetDetailBaseInfoVo;

/**
* <AUTHOR>
* @description 针对表【asset_ledger】的数据库操作Service
* @createDate 2025-04-16 16:41:33
*/
public interface AssetBaseInfoService extends IService<AssetBaseInfo> {

    void add(AssetBaseInfo baseInfo);

    IPage<AssetBaseInfoVo> selectAssetList(AssetSearchRequest request);
    
    /**
     * 重载方法，支持传入自定义Page对象
     */
    IPage<AssetBaseInfoVo> selectAssetList(AssetSearchRequest request, IPage<AssetBaseInfoVo> page);

    AssetDetailBaseInfoVo selectBaseInfoById(String assetId);

    void edit(AssetBaseInfo baseInfo);

    int deleteBaseInfo(String assetId);
}
