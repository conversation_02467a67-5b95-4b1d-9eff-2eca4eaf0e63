package com.jingfang.asset_disposal.module.vo;

import lombok.Data;
import java.util.Date;

/**
 * 资产处置审批VO
 */
@Data
public class AssetDisposalApprovalVo {
    
    /**
     * 审批记录ID
     */
    private String approvalId;
    
    /**
     * 处置单ID
     */
    private String disposalId;
    
    /**
     * 审批层级
     */
    private Integer approvalLevel;
    
    /**
     * 审批人ID
     */
    private Long approverId;
    
    /**
     * 审批人姓名
     */
    private String approverName;
    
    /**
     * 审批状态(1-待审批, 2-通过, 3-拒绝)
     */
    private Integer approvalStatus;
    
    /**
     * 审批状态名称
     */
    private String approvalStatusName;
    
    /**
     * 审批时间
     */
    private Date approvalTime;
    
    /**
     * 审批意见
     */
    private String approvalComment;
    
    /**
     * 创建时间
     */
    private Date createTime;
} 