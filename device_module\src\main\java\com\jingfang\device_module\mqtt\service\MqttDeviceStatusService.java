package com.jingfang.device_module.mqtt.service;

import com.alibaba.fastjson2.JSON;
import com.jingfang.device_module.mqtt.dto.DeviceStatusRequest;
import com.jingfang.device_module.mqtt.dto.DeviceStatusResponse;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;


/**
 * MQTT设备状态查询服务
 */
@Slf4j
@Service
public class MqttDeviceStatusService {

    @Value("${mqtt.server-uri:tcp://***************:10883}")
    private String serverUri;

    @Value("${mqtt.client-id:device_monitor_client}")
    private String clientId;

    @Value("${mqtt.username:admin}")
    private String username;

    @Value("${mqtt.password:429498517}")
    private String password;

    @Value("${mqtt.device.status.request.topic:/sys/thing/node/status/get/device_monitor_client}")
    private String requestTopic;

    @Value("${mqtt.device.status.response.topic:/sys/thing/node/status/get_reply/device_monitor_client}")
    private String responseTopic;

    @Value("${mqtt.device.status.timeout:10}")
    private int timeoutSeconds;

    private MqttClient mqttClient;

    // 存储待处理的请求
    private final Map<String, CompletableFuture<DeviceStatusResponse>> pendingRequests = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        try {
            log.info("开始初始化MQTT设备状态服务...");
            log.info("MQTT配置 - 服务器: {}, 客户端ID: {}, 用户名: {}", serverUri, clientId, username);

            mqttClient = new MqttClient(serverUri, clientId + "_status");

            MqttConnectOptions options = new MqttConnectOptions();
            options.setUserName(username);
            options.setPassword(password.toCharArray());
            options.setCleanSession(true);
            options.setConnectionTimeout(30);
            options.setKeepAliveInterval(60);
            options.setAutomaticReconnect(true);

            mqttClient.connect(options);
            log.info("MQTT客户端连接成功");

            // 订阅响应主题
            mqttClient.subscribe(responseTopic, (topic, message) -> {
                String payload = new String(message.getPayload());
                log.info("收到MQTT设备状态响应 - 主题: {}, 内容: {}", topic, payload);

                try {
                    DeviceStatusResponse response = JSON.parseObject(payload, DeviceStatusResponse.class);
                    if (response != null && response.getId() != null) {
                        log.info("解析MQTT响应成功，请求ID: {}, 响应代码: {}", response.getId(), response.getCode());
                        CompletableFuture<DeviceStatusResponse> future = pendingRequests.remove(response.getId());
                        if (future != null) {
                            future.complete(response);
                            log.info("成功完成请求ID: {} 的Future", response.getId());
                        } else {
                            log.warn("未找到请求ID: {} 对应的Future", response.getId());
                        }
                    } else {
                        log.warn("MQTT响应解析失败或缺少ID字段: {}", payload);
                    }
                } catch (Exception e) {
                    log.error("处理MQTT设备状态响应失败", e);
                }
            });

            log.info("已订阅响应主题: {}", responseTopic);
            log.info("MQTT设备状态服务初始化完成");
        } catch (Exception e) {
            log.error("MQTT设备状态服务初始化失败", e);
        }
    }

    @PreDestroy
    public void destroy() {
        try {
            if (mqttClient != null && mqttClient.isConnected()) {
                mqttClient.disconnect();
                mqttClient.close();
            }
        } catch (Exception e) {
            log.error("关闭MQTT客户端失败", e);
        }
    }

    /**
     * 查询设备状态
     * @return 设备状态响应
     */
    public CompletableFuture<DeviceStatusResponse> queryDeviceStatus() {
        String requestId = UUID.randomUUID().toString();
        DeviceStatusRequest request = new DeviceStatusRequest(requestId);

        CompletableFuture<DeviceStatusResponse> future = new CompletableFuture<>();
        pendingRequests.put(requestId, future);

        log.info("当前待处理请求数量: {}", pendingRequests.size());

        try {
            String jsonMessage = JSON.toJSONString(request);
            log.info("准备发送MQTT设备状态查询请求到主题 {}: {}", requestTopic, jsonMessage);

            // 检查MQTT客户端状态
            if (mqttClient == null || !mqttClient.isConnected()) {
                throw new RuntimeException("MQTT客户端未连接");
            }

            MqttMessage message = new MqttMessage(jsonMessage.getBytes());
            message.setQos(1);

            mqttClient.publish(requestTopic, message);
            log.info("MQTT设备状态查询请求已发送，请求ID: {}", requestId);

            // 设置超时处理
            CompletableFuture.delayedExecutor(timeoutSeconds, TimeUnit.SECONDS).execute(() -> {
                CompletableFuture<DeviceStatusResponse> timeoutFuture = pendingRequests.remove(requestId);
                if (timeoutFuture != null && !timeoutFuture.isDone()) {
                    log.warn("MQTT设备状态查询超时，请求ID: {}", requestId);
                    timeoutFuture.completeExceptionally(new RuntimeException("MQTT设备状态查询超时"));
                }
            });

        } catch (Exception e) {
            pendingRequests.remove(requestId);
            future.completeExceptionally(e);
            log.error("发送MQTT设备状态查询请求失败，请求ID: {}", requestId, e);
        }

        return future;
    }

    /**
     * 测试MQTT连接状态
     */
    public boolean testMqttConnection() {
        try {
            log.info("测试MQTT连接状态...");
            log.info("MQTT客户端状态: {}", mqttClient != null && mqttClient.isConnected() ? "已连接" : "未连接");
            log.info("请求主题: {}", requestTopic);
            log.info("响应主题: {}", responseTopic);
            log.info("超时时间: {} 秒", timeoutSeconds);
            return mqttClient != null && mqttClient.isConnected();
        } catch (Exception e) {
            log.error("测试MQTT连接状态失败", e);
            return false;
        }
    }
}
