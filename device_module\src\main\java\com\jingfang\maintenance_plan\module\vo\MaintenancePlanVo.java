package com.jingfang.maintenance_plan.module.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 维护计划VO
 */
@Data
public class MaintenancePlanVo {
    
    /**
     * 维护计划ID
     */
    private String planId;
    
    /**
     * 计划名称
     */
    private String planName;
    
    /**
     * 资产ID
     */
    private String assetId;
    
    /**
     * 资产名称
     */
    private String assetName;
    
    /**
     * 资产编号
     */
    private String assetCode;
    
    /**
     * 维护事项描述
     */
    private String maintenanceItems;
    
    /**
     * 维护周期类型(1-按天, 2-按周, 3-按月, 4-按年)
     */
    private Integer cycleType;
    
    /**
     * 维护周期类型名称
     */
    private String cycleTypeName;
    
    /**
     * 维护周期值
     */
    private Integer cycleValue;
    
    /**
     * 维护周期描述
     */
    private String cycleDescription;
    
    /**
     * 负责人类型(1-个人, 2-部门)
     */
    private Integer responsibleType;
    
    /**
     * 负责人类型名称
     */
    private String responsibleTypeName;
    
    /**
     * 负责人ID（用户ID或部门ID）
     */
    private Long responsibleId;
    
    /**
     * 负责人名称
     */
    private String responsibleName;
    
    /**
     * 计划状态(1-启用, 0-停用)
     */
    private Integer status;
    
    /**
     * 计划状态名称
     */
    private String statusName;
    
    /**
     * 下次维护时间
     */
    private Date nextMaintenanceTime;
    
    /**
     * 备注说明
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 关联的备品备件列表
     */
    private List<MaintenancePlanPartVo> partList;
    
    /**
     * 维护计划备品备件VO
     */
    @Data
    public static class MaintenancePlanPartVo {
        
        /**
         * 关联ID
         */
        private String relationId;
        
        /**
         * 备品备件ID
         */
        private String partId;
        
        /**
         * 备品备件名称
         */
        private String partName;
        
        /**
         * 备品备件编码
         */
        private String partCode;
        
        /**
         * 规格型号
         */
        private String specModel;
        
        /**
         * 单位
         */
        private String unit;
        
        /**
         * 每次维护所需数量
         */
        private BigDecimal requiredQuantity;
        
        /**
         * 当前库存数量
         */
        private BigDecimal currentStock;
        
        /**
         * 备注说明
         */
        private String remark;
    }
} 