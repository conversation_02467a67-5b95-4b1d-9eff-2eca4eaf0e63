package com.jingfang.device_module.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jingfang.device_module.module.entity.DeviceInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jingfang.device_module.request.DeviceInfoSearchRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【device_info】的数据库操作Mapper
* @createDate 2025-03-20 15:41:39
* @Entity com.jingfang.device_module.module.entity.DeviceInfo
*/
public interface DeviceInfoMapper extends BaseMapper<DeviceInfo> {


    int addPictureUrl(@Param("deviceId") Long deviceId,@Param("url") String pictureUrl);

    List<String> getDevicePictureUrls(Long deviceId);

    int deletePictureUrl(@Param("deviceId") Long deviceId,@Param("url") String pictureUrl);
}




