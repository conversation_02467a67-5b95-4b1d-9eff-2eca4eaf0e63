-- ========================================
-- 设备参数配置示例（包含新字段）
-- 演示 rw_type 和 param_type 字段的使用
-- ========================================

-- 清理示例数据（如果存在）
DELETE FROM device_param WHERE param_name LIKE 'EXAMPLE_%';

-- ========================================
-- 示例1：温湿度传感器参数配置
-- ========================================

-- 一般参数（只读）
INSERT INTO device_param (
    device_id, param_name, param_unit, 
    range_start, range_end, 
    rw_type, param_type, del_flag
) VALUES 
-- 温度传感器（只读，一般参数）
(1, 'EXAMPLE_环境温度', '°C', -10.0, 50.0, 0, 0, 0),
-- 湿度传感器（只读，一般参数）
(1, 'EXAMPLE_环境湿度', '%', 0.0, 100.0, 0, 0, 0),
-- 压力传感器（只读，一般参数）
(1, 'EXAMPLE_系统压力', 'kPa', 0.0, 1000.0, 0, 0, 0);

-- 设定值参数（读写）
INSERT INTO device_param (
    device_id, param_name, param_unit, 
    range_start, range_end, 
    rw_type, param_type, del_flag
) VALUES 
-- 温度设定值（读写，一般参数）
(1, 'EXAMPLE_温度设定值', '°C', 10.0, 40.0, 2, 0, 0),
-- 湿度设定值（读写，一般参数）
(1, 'EXAMPLE_湿度设定值', '%', 30.0, 80.0, 2, 0, 0);

-- 控制参数（控制类型）
INSERT INTO device_param (
    device_id, param_name, param_unit,
    range_start, range_end,
    rw_type, param_type, del_flag
) VALUES
-- 启动命令（只写，控制参数）
(1, 'EXAMPLE_启动命令', '', NULL, NULL, 1, 3, 0),
-- 停止命令（只写，控制参数）
(1, 'EXAMPLE_停止命令', '', NULL, NULL, 1, 3, 0),
-- 复位命令（只写，控制参数）
(1, 'EXAMPLE_复位命令', '', NULL, NULL, 1, 3, 0),
-- 工作模式设置（读写，控制参数）
(1, 'EXAMPLE_工作模式', '', 0.0, 3.0, 2, 3, 0),
-- 运行状态控制（读写，控制参数）
(1, 'EXAMPLE_运行状态', '', 0.0, 1.0, 2, 3, 0);

-- 告警参数（只读）
INSERT INTO device_param (
    device_id, param_name, param_unit, 
    range_start, range_end, 
    rw_type, param_type, del_flag
) VALUES 
-- 高温告警（只读，告警参数）
(1, 'EXAMPLE_高温告警', '', NULL, NULL, 0, 1, 0),
-- 低压告警（只读，告警参数）
(1, 'EXAMPLE_低压告警', '', NULL, NULL, 0, 1, 0),
-- 通信故障（只读，告警参数）
(1, 'EXAMPLE_通信故障', '', NULL, NULL, 0, 1, 0),
-- 系统异常（只读，告警参数）
(1, 'EXAMPLE_系统异常', '', NULL, NULL, 0, 1, 0);

-- ========================================
-- 示例2：电机控制器参数配置
-- ========================================

-- 运行状态参数（只读，一般参数）
INSERT INTO device_param (
    device_id, param_name, param_unit, 
    range_start, range_end, 
    rw_type, param_type, del_flag
) VALUES 
(2, 'EXAMPLE_电机转速', 'rpm', 0.0, 3000.0, 0, 0, 0),
(2, 'EXAMPLE_电机电流', 'A', 0.0, 50.0, 0, 0, 0),
(2, 'EXAMPLE_电机电压', 'V', 0.0, 500.0, 0, 0, 0),
(2, 'EXAMPLE_电机温度', '°C', 0.0, 120.0, 0, 0, 0);

-- 控制参数（读写，控制参数）
INSERT INTO device_param (
    device_id, param_name, param_unit,
    range_start, range_end,
    rw_type, param_type, del_flag
) VALUES
(2, 'EXAMPLE_转速设定值', 'rpm', 0.0, 3000.0, 2, 3, 0),
(2, 'EXAMPLE_加速时间', 's', 1.0, 60.0, 2, 3, 0),
(2, 'EXAMPLE_减速时间', 's', 1.0, 60.0, 2, 3, 0),
(2, 'EXAMPLE_工作模式', '', 0.0, 3.0, 2, 3, 0),
-- 电机控制指令（只写，控制参数）
(2, 'EXAMPLE_电机启动', '', NULL, NULL, 1, 3, 0),
(2, 'EXAMPLE_电机停止', '', NULL, NULL, 1, 3, 0);

-- 电机告警参数（只读，告警参数）
INSERT INTO device_param (
    device_id, param_name, param_unit, 
    range_start, range_end, 
    rw_type, param_type, del_flag
) VALUES 
(2, 'EXAMPLE_过载告警', '', NULL, NULL, 0, 1, 0),
(2, 'EXAMPLE_过热告警', '', NULL, NULL, 0, 1, 0),
(2, 'EXAMPLE_欠压告警', '', NULL, NULL, 0, 1, 0),
(2, 'EXAMPLE_编码器故障', '', NULL, NULL, 0, 1, 0);

-- ========================================
-- 示例3：流量计参数配置
-- ========================================

-- 测量参数（只读，一般参数）
INSERT INTO device_param (
    device_id, param_name, param_unit, 
    range_start, range_end, 
    rw_type, param_type, del_flag
) VALUES 
(3, 'EXAMPLE_瞬时流量', 'L/min', 0.0, 1000.0, 0, 0, 0),
(3, 'EXAMPLE_累计流量', 'L', 0.0, 999999.0, 0, 0, 0),
(3, 'EXAMPLE_流体温度', '°C', -20.0, 150.0, 0, 0, 0),
(3, 'EXAMPLE_流体密度', 'kg/m³', 500.0, 2000.0, 0, 0, 0);

-- 校准参数（读写，控制参数）
INSERT INTO device_param (
    device_id, param_name, param_unit,
    range_start, range_end,
    rw_type, param_type, del_flag
) VALUES
(3, 'EXAMPLE_流量系数', '', 0.1, 10.0, 2, 3, 0),
(3, 'EXAMPLE_零点校正', '', -100.0, 100.0, 2, 3, 0),
(3, 'EXAMPLE_满量程校正', '', 0.5, 2.0, 2, 3, 0),
-- 流量计控制指令（只写，控制参数）
(3, 'EXAMPLE_校准命令', '', NULL, NULL, 1, 3, 0),
(3, 'EXAMPLE_复位命令', '', NULL, NULL, 1, 3, 0);

-- 流量计告警参数（只读，告警参数）
INSERT INTO device_param (
    device_id, param_name, param_unit, 
    range_start, range_end, 
    rw_type, param_type, del_flag
) VALUES 
(3, 'EXAMPLE_流量超限告警', '', NULL, NULL, 0, 1, 0),
(3, 'EXAMPLE_传感器故障', '', NULL, NULL, 0, 1, 0),
(3, 'EXAMPLE_信号异常', '', NULL, NULL, 0, 1, 0);

-- ========================================
-- 查询示例
-- ========================================

-- 查询所有一般参数（运行参数）
SELECT
    device_id,
    param_name,
    param_unit,
    CASE rw_type
        WHEN 0 THEN '只读'
        WHEN 1 THEN '只写'
        WHEN 2 THEN '读写'
        ELSE '未知'
    END AS rw_type_desc,
    CASE param_type
        WHEN 0 THEN '一般参数'
        WHEN 1 THEN '告警参数'
        WHEN 3 THEN '控制参数'
        ELSE '未知'
    END AS param_type_desc
FROM device_param
WHERE param_type = 0 AND del_flag = 0
ORDER BY device_id, param_name;

-- 查询所有告警参数
SELECT 
    device_id,
    param_name,
    CASE rw_type 
        WHEN 0 THEN '只读'
        WHEN 1 THEN '只写'
        WHEN 2 THEN '读写'
        ELSE '未知'
    END AS rw_type_desc
FROM device_param
WHERE param_type = 1 AND del_flag = 0
ORDER BY device_id, param_name;

-- 查询所有控制参数
SELECT
    device_id,
    param_name,
    CASE rw_type
        WHEN 0 THEN '只读'
        WHEN 1 THEN '只写'
        WHEN 2 THEN '读写'
        ELSE '未知'
    END AS rw_type_desc
FROM device_param
WHERE param_type = 3 AND del_flag = 0
ORDER BY device_id, param_name;

-- 统计各类型参数数量
SELECT
    device_id,
    SUM(CASE WHEN param_type = 0 THEN 1 ELSE 0 END) AS normal_params,
    SUM(CASE WHEN param_type = 1 THEN 1 ELSE 0 END) AS alert_params,
    SUM(CASE WHEN param_type = 3 THEN 1 ELSE 0 END) AS control_params,
    SUM(CASE WHEN rw_type = 0 THEN 1 ELSE 0 END) AS readonly_params,
    SUM(CASE WHEN rw_type = 1 THEN 1 ELSE 0 END) AS writeonly_params,
    SUM(CASE WHEN rw_type = 2 THEN 1 ELSE 0 END) AS readwrite_params,
    COUNT(*) AS total_params
FROM device_param
WHERE del_flag = 0
GROUP BY device_id
ORDER BY device_id;
